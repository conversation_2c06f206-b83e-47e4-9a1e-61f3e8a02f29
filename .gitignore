# 环境变量文件（包含敏感信息）
.env

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# 虚拟环境
venv/
env/
ENV/
env.bak/
venv.bak/
tianjin/
xuni/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# 日志文件
logs/
*.log

# 临时文件
temp/
tmp/
*.tmp

# 调试和分析文件
detection/debug_test/
detection/debug_test/*.txt
analysis_*.txt

# 其他临时文本文件
temp_*.txt
test_*.txt
debug_*.txt

# 但保留重要的文档文件
!README.txt
!requirements.txt
!LICENSE.txt
!CHANGELOG.txt
!INSTALL.txt
!USAGE.txt

# 模型文件（如果很大）
# *.pt
# *.pth

# 截图和视频
captures/
*.mp4
*.avi

# 系统文件
.DS_Store
Thumbs.db

# 配置文件备份
*.bak
*.backup
