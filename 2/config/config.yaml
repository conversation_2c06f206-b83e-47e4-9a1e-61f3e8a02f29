# 火焰和烟雾检测配置文件
# 注意：所有路径都使用相对路径，确保跨平台兼容性

# 模型配置
model:
  # 模型权重文件路径 (相对于项目根目录)
  weights_path: "models/fire_detection/fire.rknn"
  # 置信度阈值
  confidence_threshold: 0.1
  # IoU阈值
  iou_threshold: 0.45
  # 设备选择 ('cpu', 'cuda', 'auto')
  device: "auto"
  # 输入图像尺寸
  img_size: 640

# 检测类别
classes:
  fire: 0
  smoke: 1

# 类别名称映射
class_names:
  0: "fire"
  1: "smoke"

# 颜色配置 (BGR格式)
colors:
  fire: [0, 0, 255]    # 红色
  smoke: [128, 128, 128]  # 灰色

# 输入配置
input:
  # 输入类型 ('image', 'video', 'camera', 'rtsp')
  type: "image"
  # 输入源路径 (相对于项目根目录)
  source: "2/data/input/"
  # 摄像头ID (当type为camera时)
  camera_id: 0
  # RTSP流地址 (当type为rtsp时)
  rtsp_url: ""

# 输出配置
output:
  # 是否保存结果
  save_results: true
  # 输出目录 (相对于项目根目录)
  output_dir: "2/data/output/"
  # 是否显示结果
  show_results: true
  # 是否保存检测框坐标
  save_txt: true
  # 是否保存置信度
  save_conf: true

# 日志配置
logging:
  # 日志级别 ('DEBUG', 'INFO', 'WARNING', 'ERROR')
  level: "INFO"
  # 日志文件路径 (相对于项目根目录)
  log_file: "2/logs/detection.log"
  # 是否在控制台显示日志
  console_output: true

# 报警配置
alert:
  # 是否启用报警
  enabled: true
  # 连续检测到目标的帧数阈值
  frame_threshold: 3
  # 报警冷却时间(秒)
  cooldown_time: 10
  # 报警方式 ('console', 'email', 'webhook')
  methods: ["console"]
  
# 邮件报警配置 (当alert.methods包含email时)
email:
  smtp_server: ""
  smtp_port: 587
  username: ""
  password: ""
  from_email: ""
  to_emails: []

# Webhook报警配置 (当alert.methods包含webhook时)
webhook:
  url: ""
  headers: {}
  
# 跟踪配置
tracking:
  # 数据导出路径（避免与主系统冲突，相对于项目根目录）
  output_path: "data_exports/modular_fire_smoke_analysis.xlsx"
  # 最大消失帧数
  max_disappeared: 10
  # 最大跟踪距离
  max_distance: 100

# 分析配置
analysis:
  # 是否提取几何特征
  extract_geometric_features: true
  # 是否提取图像特征
  extract_image_features: true
  # 是否提取运动特征
  extract_motion_features: true
  # 闪烁分析窗口大小
  flicker_window_size: 15

# 性能配置
performance:
  # 最大处理帧率
  max_fps: 30
  # 是否使用多线程
  use_threading: true
  # 线程数量 (0表示自动)
  num_threads: 0
