"""
特征分析模块
提供多维特征提取和闪烁频率分析功能
"""

import cv2
import numpy as np
import logging
from typing import List, Dict, Tuple, Optional
from collections import defaultdict
from skimage import measure
import scipy.signal


class FeatureAnalysisEngine:
    """特征分析引擎 - 多维特征提取"""
    
    def __init__(self, config: Dict = None):
        """
        初始化特征分析引擎
        
        Args:
            config: 分析配置字典
        """
        self.config = config or {}
        self.logger = logging.getLogger("FeatureAnalysisEngine")
        if not self.logger.handlers:
            # 如果没有处理器，添加一个控制台处理器
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
        
        # 分析开关
        self.extract_geometric = self.config.get('extract_geometric_features', True)
        self.extract_image = self.config.get('extract_image_features', True)
        self.extract_motion = self.config.get('extract_motion_features', True)
        
        # 闪烁分析器
        flicker_window_size = self.config.get('flicker_window_size', 15)
        self.flicker_analyzer = FlickerAnalyzer(window_size=flicker_window_size)
    
    def extract_geometric_features(self, bbox: List[int]) -> Dict:
        """
        提取几何特征
        
        Args:
            bbox: 检测框 [x1, y1, x2, y2]
            
        Returns:
            几何特征字典
        """
        if not self.extract_geometric:
            return {}
        
        try:
            x1, y1, x2, y2 = bbox
            width = x2 - x1
            height = y2 - y1
            
            # 基本几何特征
            area = width * height
            aspect_ratio = width / height if height > 0 else 0
            perimeter = 2 * (width + height)
            compactness = (4 * np.pi * area) / (perimeter ** 2) if perimeter > 0 else 0
            
            # 形状特征
            diagonal = np.sqrt(width**2 + height**2)
            rectangularity = area / (width * height) if (width * height) > 0 else 0
            
            return {
                'area': float(area),
                'width': float(width),
                'height': float(height),
                'aspect_ratio': float(aspect_ratio),
                'perimeter': float(perimeter),
                'compactness': float(compactness),
                'diagonal': float(diagonal),
                'rectangularity': float(rectangularity)
            }
            
        except Exception as e:
            self.logger.error(f"几何特征提取失败: {e}")
            return {}
    
    def extract_image_features(self, image: np.ndarray, bbox: List[int]) -> Dict:
        """
        提取图像特征
        
        Args:
            image: 输入图像
            bbox: 检测框 [x1, y1, x2, y2]
            
        Returns:
            图像特征字典
        """
        if not self.extract_image:
            return {}
        
        try:
            x1, y1, x2, y2 = bbox
            
            # 确保坐标在图像范围内
            h, w = image.shape[:2]
            x1, y1 = max(0, x1), max(0, y1)
            x2, y2 = min(w, x2), min(h, y2)
            
            if x2 <= x1 or y2 <= y1:
                return self._get_empty_image_features()
            
            # 提取区域
            region = image[y1:y2, x1:x2]
            
            # 转换为灰度图
            if len(region.shape) == 3:
                gray_region = cv2.cvtColor(region, cv2.COLOR_BGR2GRAY)
            else:
                gray_region = region
            
            # 强度特征
            mean_intensity = np.mean(gray_region)
            std_intensity = np.std(gray_region)
            min_intensity = np.min(gray_region)
            max_intensity = np.max(gray_region)
            intensity_range = max_intensity - min_intensity
            
            # 纹理特征
            texture_energy = self._calculate_texture_energy(gray_region)
            
            # 边缘特征
            edge_density = self._calculate_edge_density(gray_region)
            
            # 颜色特征（如果是彩色图像）
            color_features = {}
            if len(region.shape) == 3:
                color_features = self._extract_color_features(region)
            
            features = {
                'mean_intensity': float(mean_intensity),
                'std_intensity': float(std_intensity),
                'min_intensity': float(min_intensity),
                'max_intensity': float(max_intensity),
                'intensity_range': float(intensity_range),
                'texture_energy': float(texture_energy),
                'edge_density': float(edge_density)
            }
            
            features.update(color_features)
            return features
            
        except Exception as e:
            self.logger.error(f"图像特征提取失败: {e}")
            return self._get_empty_image_features()
    
    def _calculate_texture_energy(self, gray_region: np.ndarray) -> float:
        """计算纹理能量"""
        try:
            if gray_region.size < 4:  # 区域太小
                return 0.0
            
            # 使用灰度共生矩阵计算纹理特征
            # 限制灰度级别以提高计算效率
            levels = min(256, int(np.max(gray_region)) + 1)
            glcm = measure.graycomatrix(
                gray_region.astype(np.uint8), 
                distances=[1], 
                angles=[0], 
                levels=levels, 
                symmetric=True, 
                normed=True
            )
            texture_energy = measure.graycoprops(glcm, 'energy')[0, 0]
            return texture_energy
        except:
            return 0.0
    
    def _calculate_edge_density(self, gray_region: np.ndarray) -> float:
        """计算边缘密度"""
        try:
            if gray_region.size < 4:
                return 0.0
            
            edges = cv2.Canny(gray_region, 50, 150)
            edge_density = np.sum(edges > 0) / edges.size
            return edge_density
        except:
            return 0.0
    
    def _extract_color_features(self, color_region: np.ndarray) -> Dict:
        """提取颜色特征"""
        try:
            # BGR通道统计
            b_mean, g_mean, r_mean = np.mean(color_region, axis=(0, 1))
            b_std, g_std, r_std = np.std(color_region, axis=(0, 1))
            
            # HSV颜色空间
            hsv_region = cv2.cvtColor(color_region, cv2.COLOR_BGR2HSV)
            h_mean, s_mean, v_mean = np.mean(hsv_region, axis=(0, 1))
            
            return {
                'b_mean': float(b_mean),
                'g_mean': float(g_mean),
                'r_mean': float(r_mean),
                'b_std': float(b_std),
                'g_std': float(g_std),
                'r_std': float(r_std),
                'h_mean': float(h_mean),
                's_mean': float(s_mean),
                'v_mean': float(v_mean)
            }
        except:
            return {}
    
    def _get_empty_image_features(self) -> Dict:
        """获取空的图像特征"""
        return {
            'mean_intensity': 0.0,
            'std_intensity': 0.0,
            'min_intensity': 0.0,
            'max_intensity': 0.0,
            'intensity_range': 0.0,
            'texture_energy': 0.0,
            'edge_density': 0.0
        }
    
    def extract_motion_features(self, trajectory: List[Tuple[int, int]], fps: float = 30.0) -> Dict:
        """
        提取运动特征
        
        Args:
            trajectory: 轨迹点列表
            fps: 视频帧率
            
        Returns:
            运动特征字典
        """
        if not self.extract_motion or len(trajectory) < 2:
            return self._get_empty_motion_features()
        
        try:
            # 运动方向
            movement_direction = self._calculate_movement_direction(trajectory)
            
            # 移动速度
            movement_speed = self._calculate_movement_speed(trajectory, fps)
            
            # 扩散面积
            spread_area = self._calculate_spread_area(trajectory)
            
            # 轨迹稳定性
            trajectory_stability = self._calculate_trajectory_stability(trajectory)
            
            # 轨迹长度
            trajectory_length = len(trajectory)
            
            # 总移动距离
            total_distance = self._calculate_total_distance(trajectory)
            
            return {
                'movement_direction': float(movement_direction),
                'movement_speed': float(movement_speed),
                'spread_area': float(spread_area),
                'trajectory_stability': float(trajectory_stability),
                'trajectory_length': int(trajectory_length),
                'total_distance': float(total_distance)
            }
            
        except Exception as e:
            self.logger.error(f"运动特征提取失败: {e}")
            return self._get_empty_motion_features()
    
    def _calculate_movement_direction(self, trajectory: List[Tuple[int, int]]) -> float:
        """计算主要运动方向（角度）"""
        if len(trajectory) < 2:
            return 0.0
        
        start_point = np.array(trajectory[0])
        end_point = np.array(trajectory[-1])
        
        direction_vector = end_point - start_point
        angle = np.arctan2(direction_vector[1], direction_vector[0])
        return np.degrees(angle)
    
    def _calculate_movement_speed(self, trajectory: List[Tuple[int, int]], fps: float) -> float:
        """计算平均移动速度（像素/秒）"""
        if len(trajectory) < 2:
            return 0.0
        
        total_distance = self._calculate_total_distance(trajectory)
        time_duration = len(trajectory) / fps
        return total_distance / time_duration if time_duration > 0 else 0.0
    
    def _calculate_total_distance(self, trajectory: List[Tuple[int, int]]) -> float:
        """计算总移动距离"""
        if len(trajectory) < 2:
            return 0.0
        
        total_distance = 0.0
        for i in range(1, len(trajectory)):
            distance = np.linalg.norm(np.array(trajectory[i]) - np.array(trajectory[i-1]))
            total_distance += distance
        
        return total_distance
    
    def _calculate_spread_area(self, trajectory: List[Tuple[int, int]]) -> float:
        """计算轨迹覆盖的区域面积"""
        if len(trajectory) < 3:
            return 0.0
        
        points = np.array(trajectory)
        x_min, y_min = points.min(axis=0)
        x_max, y_max = points.max(axis=0)
        
        return (x_max - x_min) * (y_max - y_min)
    
    def _calculate_trajectory_stability(self, trajectory: List[Tuple[int, int]]) -> float:
        """计算轨迹稳定性（0-1，1表示最稳定）"""
        if len(trajectory) < 3:
            return 1.0
        
        # 计算相邻点之间的距离变化
        distances = []
        for i in range(1, len(trajectory)):
            distance = np.linalg.norm(np.array(trajectory[i]) - np.array(trajectory[i-1]))
            distances.append(distance)
        
        if len(distances) == 0:
            return 1.0
        
        # 使用距离变化的标准差来评估稳定性
        mean_distance = np.mean(distances)
        std_distance = np.std(distances)
        
        if mean_distance == 0:
            return 1.0
        
        # 稳定性 = 1 - (标准差 / 平均值)，限制在0-1范围内
        stability = 1.0 - min(std_distance / mean_distance, 1.0)
        return max(0.0, stability)
    
    def _get_empty_motion_features(self) -> Dict:
        """获取空的运动特征"""
        return {
            'movement_direction': 0.0,
            'movement_speed': 0.0,
            'spread_area': 0.0,
            'trajectory_stability': 1.0,
            'trajectory_length': 0,
            'total_distance': 0.0
        }
    
    def analyze_flicker(self, object_id: int, intensity_value: float, fps: float = 30.0) -> Dict:
        """
        分析闪烁特征
        
        Args:
            object_id: 目标ID
            intensity_value: 当前强度值
            fps: 视频帧率
            
        Returns:
            闪烁分析结果
        """
        # 更新闪烁分析器
        self.flicker_analyzer.update(object_id, intensity_value)
        
        # 计算闪烁特征
        flicker_frequency = self.flicker_analyzer.calculate_flicker_frequency(object_id, fps)
        flicker_intensity = self.flicker_analyzer.calculate_flicker_intensity(object_id)
        
        return {
            'flicker_frequency': float(flicker_frequency),
            'flicker_intensity': float(flicker_intensity)
        }
    
    def extract_comprehensive_features(self, image: np.ndarray, bbox: List[int], 
                                     trajectory: List[Tuple[int, int]] = None,
                                     object_id: int = None, fps: float = 30.0) -> Dict:
        """
        提取综合特征
        
        Args:
            image: 输入图像
            bbox: 检测框
            trajectory: 轨迹数据
            object_id: 目标ID
            fps: 视频帧率
            
        Returns:
            综合特征字典
        """
        features = {}
        
        # 几何特征
        if self.extract_geometric:
            geometric_features = self.extract_geometric_features(bbox)
            features.update(geometric_features)
        
        # 图像特征
        if self.extract_image:
            image_features = self.extract_image_features(image, bbox)
            features.update(image_features)
            
            # 闪烁分析（基于平均强度）
            if object_id is not None and 'mean_intensity' in image_features:
                flicker_features = self.analyze_flicker(
                    object_id, image_features['mean_intensity'], fps
                )
                features.update(flicker_features)
        
        # 运动特征
        if self.extract_motion and trajectory:
            motion_features = self.extract_motion_features(trajectory, fps)
            features.update(motion_features)
        
        return features


class FlickerAnalyzer:
    """闪烁分析器"""
    
    def __init__(self, window_size: int = 15):
        """
        初始化闪烁分析器
        
        Args:
            window_size: 分析窗口大小
        """
        self.window_size = window_size
        self.intensity_history = {}  # 存储每个目标的强度历史
        self.logger = logging.getLogger("FlickerAnalyzer")
        if not self.logger.handlers:
            # 如果没有处理器，添加一个控制台处理器
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
    
    def update(self, object_id: int, mean_intensity: float):
        """
        更新目标的强度历史
        
        Args:
            object_id: 目标ID
            mean_intensity: 平均强度值
        """
        if object_id not in self.intensity_history:
            self.intensity_history[object_id] = []
        
        self.intensity_history[object_id].append(mean_intensity)
        
        # 保持窗口大小
        if len(self.intensity_history[object_id]) > self.window_size:
            self.intensity_history[object_id] = self.intensity_history[object_id][-self.window_size:]
    
    def calculate_flicker_frequency(self, object_id: int, fps: float = 30.0) -> float:
        """
        计算闪烁频率
        
        Args:
            object_id: 目标ID
            fps: 视频帧率
            
        Returns:
            闪烁频率 (Hz)
        """
        if object_id not in self.intensity_history:
            return 0.0
        
        intensities = self.intensity_history[object_id]
        if len(intensities) < 3:
            return 0.0
        
        try:
            # 计算强度变化的峰值
            intensities = np.array(intensities)
            
            # 去除趋势
            detrended = intensities - np.linspace(intensities[0], intensities[-1], len(intensities))
            
            # 找到峰值
            peaks, _ = scipy.signal.find_peaks(np.abs(detrended), height=np.std(detrended) * 0.5)
            
            if len(peaks) < 2:
                return 0.0
            
            # 计算平均峰值间隔
            peak_intervals = np.diff(peaks)
            avg_interval = np.mean(peak_intervals)
            
            # 转换为频率
            frequency = fps / (2 * avg_interval) if avg_interval > 0 else 0.0
            
            return frequency
            
        except Exception as e:
            self.logger.error(f"闪烁频率计算失败: {e}")
            return 0.0
    
    def calculate_flicker_intensity(self, object_id: int) -> float:
        """
        计算闪烁强度 (标准差/均值)
        
        Args:
            object_id: 目标ID
            
        Returns:
            闪烁强度
        """
        if object_id not in self.intensity_history:
            return 0.0
        
        intensities = self.intensity_history[object_id]
        if len(intensities) < 2:
            return 0.0
        
        try:
            intensities = np.array(intensities)
            mean_val = np.mean(intensities)
            std_val = np.std(intensities)
            
            return std_val / mean_val if mean_val > 0 else 0.0
            
        except Exception as e:
            self.logger.error(f"闪烁强度计算失败: {e}")
            return 0.0
    
    def reset(self):
        """重置分析器状态"""
        self.intensity_history = {}
        self.logger.info("闪烁分析器状态已重置")
