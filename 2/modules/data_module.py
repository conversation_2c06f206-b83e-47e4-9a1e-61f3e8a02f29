"""
数据导出模块
提供Excel导出和统计分析数据结构
"""

import pandas as pd
import numpy as np
import logging
import time
from pathlib import Path
from typing import Dict, List, Optional, Any
from collections import defaultdict


class DataExportEngine:
    """数据导出引擎 - Excel和统计分析"""
    
    def __init__(self, output_path: str = "detection_analysis.xlsx"):
        """
        初始化数据导出引擎
        
        Args:
            output_path: Excel输出文件路径
        """
        self.output_path = Path(output_path)
        self.logger = logging.getLogger("DataExportEngine")
        if not self.logger.handlers:
            # 如果没有处理器，添加一个控制台处理器
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
        
        # 数据存储
        self.fire_data = []
        self.smoke_data = []
        self.frame_count = 0
        self.start_time = time.time()
        
        # 统计信息
        self.statistics = {
            'total_frames': 0,
            'total_fire_detections': 0,
            'total_smoke_detections': 0,
            'unique_fire_objects': set(),
            'unique_smoke_objects': set(),
            'processing_start_time': self.start_time
        }
    
    def add_detection_data(self, frame_data: Dict):
        """
        添加检测数据
        
        Args:
            frame_data: 帧数据字典，包含检测结果和特征
        """
        frame_num = frame_data.get('frame_num', 0)
        timestamp = frame_data.get('timestamp', 0.0)
        fire_objects = frame_data.get('fire_objects', {})
        smoke_objects = frame_data.get('smoke_objects', {})
        image = frame_data.get('image')
        fps = frame_data.get('fps', 30.0)
        fire_trajectories = frame_data.get('fire_trajectories', {})
        smoke_trajectories = frame_data.get('smoke_trajectories', {})
        
        self.frame_count = frame_num
        self.statistics['total_frames'] = frame_num
        
        # 处理火焰数据
        self._process_fire_data(
            frame_num, timestamp, fire_objects, image, fps, fire_trajectories
        )
        
        # 处理烟雾数据
        self._process_smoke_data(
            frame_num, timestamp, smoke_objects, image, fps, smoke_trajectories
        )
    
    def _process_fire_data(self, frame_num: int, timestamp: float, 
                          fire_objects: Dict, image: Any, fps: float,
                          fire_trajectories: Dict):
        """处理火焰数据"""
        for obj_id, obj_data in fire_objects.items():
            self.statistics['unique_fire_objects'].add(obj_id)
            self.statistics['total_fire_detections'] += 1
            
            # 提取基本信息
            bbox = obj_data['bbox']
            centroid = obj_data['centroid']  # 底部中心点（火焰根部）
            confidence = obj_data['confidence']
            
            # 计算几何特征
            geometric_features = self._calculate_geometric_features(bbox)
            
            # 提取图像特征（如果有图像数据）
            image_features = {}
            if image is not None:
                image_features = self._extract_image_features(image, bbox)
            
            # 计算轨迹特征
            trajectory_features = {}
            if fire_trajectories and obj_id in fire_trajectories:
                trajectory = fire_trajectories[obj_id]
                trajectory_features = self._calculate_trajectory_features(trajectory, fps)
            
            # 构建数据记录
            fire_record = {
                'frame_num': frame_num,
                'timestamp': timestamp,
                'object_id': obj_id,
                'centroid_x': centroid[0],
                'centroid_y': centroid[1],
                'bbox_x1': bbox[0],
                'bbox_y1': bbox[1],
                'bbox_x2': bbox[2],
                'bbox_y2': bbox[3],
                'confidence': confidence,
                'duration': obj_data.get('frame_count', 1) / fps,
                **geometric_features,
                **image_features,
                **trajectory_features
            }
            
            self.fire_data.append(fire_record)
    
    def _process_smoke_data(self, frame_num: int, timestamp: float,
                           smoke_objects: Dict, image: Any, fps: float,
                           smoke_trajectories: Dict):
        """处理烟雾数据"""
        for obj_id, obj_data in smoke_objects.items():
            self.statistics['unique_smoke_objects'].add(obj_id)
            self.statistics['total_smoke_detections'] += 1
            
            # 提取基本信息
            bbox = obj_data['bbox']
            centroid = obj_data['centroid']  # 底部中心点（烟雾根部）
            confidence = obj_data['confidence']
            
            # 计算几何特征
            geometric_features = self._calculate_geometric_features(bbox)
            
            # 提取图像特征（如果有图像数据）
            image_features = {}
            if image is not None:
                image_features = self._extract_image_features(image, bbox)
            
            # 计算轨迹特征
            trajectory_features = {}
            if smoke_trajectories and obj_id in smoke_trajectories:
                trajectory = smoke_trajectories[obj_id]
                trajectory_features = self._calculate_trajectory_features(trajectory, fps)
            
            # 构建数据记录
            smoke_record = {
                'frame_num': frame_num,
                'timestamp': timestamp,
                'object_id': obj_id,
                'centroid_x': centroid[0],
                'centroid_y': centroid[1],
                'bbox_x1': bbox[0],
                'bbox_y1': bbox[1],
                'bbox_x2': bbox[2],
                'bbox_y2': bbox[3],
                'confidence': confidence,
                'duration': obj_data.get('frame_count', 1) / fps,
                **geometric_features,
                **image_features,
                **trajectory_features
            }
            
            self.smoke_data.append(smoke_record)
    
    def _calculate_geometric_features(self, bbox: List[int]) -> Dict:
        """计算几何特征"""
        x1, y1, x2, y2 = bbox
        width = x2 - x1
        height = y2 - y1
        area = width * height
        aspect_ratio = width / height if height > 0 else 0
        perimeter = 2 * (width + height)
        compactness = (4 * np.pi * area) / (perimeter ** 2) if perimeter > 0 else 0
        
        return {
            'area': area,
            'width': width,
            'height': height,
            'aspect_ratio': aspect_ratio,
            'perimeter': perimeter,
            'compactness': compactness
        }
    
    def _extract_image_features(self, image: Any, bbox: List[int]) -> Dict:
        """提取图像特征（简化版本）"""
        try:
            import cv2
            
            x1, y1, x2, y2 = bbox
            h, w = image.shape[:2]
            x1, y1 = max(0, x1), max(0, y1)
            x2, y2 = min(w, x2), min(h, y2)
            
            if x2 <= x1 or y2 <= y1:
                return self._get_empty_image_features()
            
            # 提取区域
            region = image[y1:y2, x1:x2]
            
            # 转换为灰度图
            if len(region.shape) == 3:
                gray_region = cv2.cvtColor(region, cv2.COLOR_BGR2GRAY)
            else:
                gray_region = region
            
            # 计算基本统计特征
            mean_intensity = np.mean(gray_region)
            std_intensity = np.std(gray_region)
            
            # 计算边缘密度
            edges = cv2.Canny(gray_region, 50, 150)
            edge_density = np.sum(edges > 0) / edges.size
            
            return {
                'mean_intensity': float(mean_intensity),
                'std_intensity': float(std_intensity),
                'edge_density': float(edge_density),
                'texture_energy': 0.0  # 简化版本
            }
            
        except Exception as e:
            self.logger.error(f"图像特征提取失败: {e}")
            return self._get_empty_image_features()
    
    def _get_empty_image_features(self) -> Dict:
        """获取空的图像特征"""
        return {
            'mean_intensity': 0.0,
            'std_intensity': 0.0,
            'edge_density': 0.0,
            'texture_energy': 0.0
        }
    
    def _calculate_trajectory_features(self, trajectory: List, fps: float) -> Dict:
        """计算轨迹特征"""
        if len(trajectory) < 2:
            return {
                'movement_direction': 0.0,
                'movement_speed': 0.0,
                'spread_area': 0.0,
                'trajectory_stability': 1.0,
                'trajectory_length': len(trajectory)
            }
        
        # 运动方向
        start_point = np.array(trajectory[0])
        end_point = np.array(trajectory[-1])
        direction_vector = end_point - start_point
        movement_direction = np.degrees(np.arctan2(direction_vector[1], direction_vector[0]))
        
        # 移动速度
        total_distance = 0.0
        for i in range(1, len(trajectory)):
            distance = np.linalg.norm(np.array(trajectory[i]) - np.array(trajectory[i-1]))
            total_distance += distance
        
        time_duration = len(trajectory) / fps
        movement_speed = total_distance / time_duration if time_duration > 0 else 0.0
        
        # 扩散面积
        points = np.array(trajectory)
        x_min, y_min = points.min(axis=0)
        x_max, y_max = points.max(axis=0)
        spread_area = (x_max - x_min) * (y_max - y_min)
        
        # 轨迹稳定性
        distances = []
        for i in range(1, len(trajectory)):
            distance = np.linalg.norm(np.array(trajectory[i]) - np.array(trajectory[i-1]))
            distances.append(distance)
        
        if len(distances) > 0:
            mean_distance = np.mean(distances)
            std_distance = np.std(distances)
            trajectory_stability = 1.0 - min(std_distance / mean_distance, 1.0) if mean_distance > 0 else 1.0
            trajectory_stability = max(0.0, trajectory_stability)
        else:
            trajectory_stability = 1.0
        
        return {
            'movement_direction': float(movement_direction),
            'movement_speed': float(movement_speed),
            'spread_area': float(spread_area),
            'trajectory_stability': float(trajectory_stability),
            'trajectory_length': len(trajectory)
        }
    
    def export_to_excel(self) -> str:
        """
        导出数据到Excel文件
        
        Returns:
            导出文件路径
        """
        try:
            # 确保输出目录存在
            self.output_path.parent.mkdir(parents=True, exist_ok=True)
            
            with pd.ExcelWriter(self.output_path, engine='openpyxl') as writer:
                # 火焰数据
                if self.fire_data:
                    fire_df = pd.DataFrame(self.fire_data)
                    fire_df.to_excel(writer, sheet_name='火焰数据', index=False)

                    # 火焰统计 - 使用新的详细统计格式
                    fire_stats_df = self._calculate_statistics(fire_df, '火焰')
                    fire_stats_df.to_excel(writer, sheet_name='火焰统计', index=False)
                
                # 烟雾数据
                if self.smoke_data:
                    smoke_df = pd.DataFrame(self.smoke_data)
                    smoke_df.to_excel(writer, sheet_name='烟雾数据', index=False)

                    # 烟雾统计 - 使用新的详细统计格式
                    smoke_stats_df = self._calculate_statistics(smoke_df, '烟雾')
                    smoke_stats_df.to_excel(writer, sheet_name='烟雾统计', index=False)
                
                # 综合分析 - 使用新的标准格式
                comprehensive_df = self._calculate_comprehensive_statistics()
                comprehensive_df.to_excel(writer, sheet_name='综合分析', index=False)
            
            self.logger.info(f"数据已导出到: {self.output_path}")
            return str(self.output_path)
            
        except Exception as e:
            self.logger.error(f"Excel导出失败: {e}")
            raise
    
    def _calculate_statistics(self, df: pd.DataFrame, object_type: str) -> pd.DataFrame:
        """计算统计信息 - 按目标ID分组的详细统计"""
        if df.empty:
            return pd.DataFrame()

        stats_data = []

        # 按目标ID分组统计
        for obj_id in df['object_id'].unique():
            obj_data = df[df['object_id'] == obj_id]

            # 计算面积变化
            area_values = obj_data['area'].values
            area_change_rate = self._calculate_change_rate(area_values)

            # 计算形态变化
            aspect_ratio_values = obj_data['aspect_ratio'].values
            shape_stability = 1 - np.std(aspect_ratio_values) / np.mean(aspect_ratio_values) if np.mean(aspect_ratio_values) > 0 else 0

            # 计算强度变化
            intensity_values = obj_data['mean_intensity'].values if 'mean_intensity' in obj_data.columns else np.zeros(len(obj_data))
            intensity_change_rate = self._calculate_change_rate(intensity_values)

            # 计算闪烁特征
            flicker_intensity = np.std(intensity_values) / np.mean(intensity_values) if np.mean(intensity_values) > 0 else 0

            # 获取持续时间
            duration_seconds = obj_data['duration'].iloc[-1] if 'duration' in obj_data.columns else len(obj_data) / 30.0

            stats_record = {
                '目标类型': object_type,
                '目标ID': obj_id,
                '持续帧数': len(obj_data),
                '持续时间(秒)': duration_seconds,
                '平均面积': np.mean(area_values),
                '最大面积': np.max(area_values),
                '最小面积': np.min(area_values),
                '面积变化率': area_change_rate,
                '平均宽高比': np.mean(aspect_ratio_values),
                '形态稳定性': shape_stability,
                '平均强度': np.mean(intensity_values),
                '强度变化率': intensity_change_rate,
                '闪烁强度': flicker_intensity,
                '平均置信度': np.mean(obj_data['confidence']),
                '平均紧凑度': np.mean(obj_data['compactness']) if 'compactness' in obj_data.columns else 0.0,
                '平均边缘密度': np.mean(obj_data['edge_density']) if 'edge_density' in obj_data.columns else 0.0,
                '主要运动方向': np.mean(obj_data['movement_direction']) if 'movement_direction' in obj_data.columns else 0.0,
                '平均移动速度': np.mean(obj_data['movement_speed']) if 'movement_speed' in obj_data.columns else 0.0,
                '最大扩散面积': np.max(obj_data['spread_area']) if 'spread_area' in obj_data.columns else 0.0,
                '轨迹稳定性': np.mean(obj_data['trajectory_stability']) if 'trajectory_stability' in obj_data.columns else 1.0,
                '最大轨迹长度': np.max(obj_data['trajectory_length']) if 'trajectory_length' in obj_data.columns else len(obj_data)
            }
            stats_data.append(stats_record)

        return pd.DataFrame(stats_data)

    def _calculate_change_rate(self, values: np.ndarray) -> float:
        """计算变化率"""
        if len(values) < 2:
            return 0.0

        # 计算相邻值的变化率
        changes = np.abs(np.diff(values))
        mean_value = np.mean(values)

        if mean_value == 0:
            return 0.0

        return np.mean(changes) / mean_value

    def _calculate_comprehensive_statistics(self) -> pd.DataFrame:
        """计算综合统计信息 - 标准格式"""
        summary_data = []

        # 总体统计
        total_fire_objects = len(self.statistics['unique_fire_objects'])
        total_smoke_objects = len(self.statistics['unique_smoke_objects'])

        summary_data.append({
            '分析项目': '检测到的火焰目标数量',
            '数值': total_fire_objects,
            '单位': '个'
        })

        summary_data.append({
            '分析项目': '检测到的烟雾目标数量',
            '数值': total_smoke_objects,
            '单位': '个'
        })

        summary_data.append({
            '分析项目': '总处理帧数',
            '数值': self.statistics['total_frames'],
            '单位': '帧'
        })

        # 火焰分析
        if self.fire_data:
            fire_df = pd.DataFrame(self.fire_data)
            avg_fire_area = np.mean(fire_df['area'])
            max_fire_area = np.max(fire_df['area'])
            avg_fire_intensity = np.mean(fire_df['mean_intensity']) if 'mean_intensity' in fire_df.columns else 0

            summary_data.extend([
                {'分析项目': '火焰平均面积', '数值': round(avg_fire_area, 2), '单位': '像素²'},
                {'分析项目': '火焰最大面积', '数值': round(max_fire_area, 2), '单位': '像素²'},
                {'分析项目': '火焰平均强度', '数值': round(avg_fire_intensity, 2), '单位': '灰度值'}
            ])

        # 烟雾分析
        if self.smoke_data:
            smoke_df = pd.DataFrame(self.smoke_data)
            avg_smoke_area = np.mean(smoke_df['area'])
            max_smoke_area = np.max(smoke_df['area'])
            avg_smoke_intensity = np.mean(smoke_df['mean_intensity']) if 'mean_intensity' in smoke_df.columns else 0

            summary_data.extend([
                {'分析项目': '烟雾平均面积', '数值': round(avg_smoke_area, 2), '单位': '像素²'},
                {'分析项目': '烟雾最大面积', '数值': round(max_smoke_area, 2), '单位': '像素²'},
                {'分析项目': '烟雾平均强度', '数值': round(avg_smoke_intensity, 2), '单位': '灰度值'}
            ])

        return pd.DataFrame(summary_data)
    
    def get_statistics(self) -> Dict:
        """
        获取当前统计信息
        
        Returns:
            统计信息字典
        """
        processing_time = time.time() - self.start_time
        
        return {
            'total_frames': self.statistics['total_frames'],
            'total_fire_detections': self.statistics['total_fire_detections'],
            'total_smoke_detections': self.statistics['total_smoke_detections'],
            'unique_fire_objects': len(self.statistics['unique_fire_objects']),
            'unique_smoke_objects': len(self.statistics['unique_smoke_objects']),
            'fire_data_count': len(self.fire_data),
            'smoke_data_count': len(self.smoke_data),
            'processing_time': processing_time,
            'average_fps': self.statistics['total_frames'] / processing_time if processing_time > 0 else 0
        }
    
    def reset(self):
        """重置数据"""
        self.fire_data = []
        self.smoke_data = []
        self.frame_count = 0
        self.start_time = time.time()
        self.statistics = {
            'total_frames': 0,
            'total_fire_detections': 0,
            'total_smoke_detections': 0,
            'unique_fire_objects': set(),
            'unique_smoke_objects': set(),
            'processing_start_time': self.start_time
        }
        self.logger.info("数据导出引擎已重置")
    
    def save_current_data(self, backup_path: Optional[str] = None) -> str:
        """
        保存当前数据到备份文件
        
        Args:
            backup_path: 备份文件路径
            
        Returns:
            备份文件路径
        """
        if backup_path is None:
            timestamp = time.strftime('%Y%m%d_%H%M%S')
            backup_path = self.output_path.parent / f"backup_{timestamp}.xlsx"
        
        original_path = self.output_path
        self.output_path = Path(backup_path)
        
        try:
            result_path = self.export_to_excel()
            self.logger.info(f"数据已备份到: {result_path}")
            return result_path
        finally:
            self.output_path = original_path
