"""
火焰烟雾检测模块 - RKNN版本
提供单权重和双权重检测功能的纯功能模块，无GUI依赖
使用RKNN推理引擎替换PyTorch/Ultralytics实现
"""

import cv2
import numpy as np
from typing import List, Tuple, Dict, Optional, Union
import logging
from pathlib import Path
import time
import os

# 导入RKNN推理引擎
try:
    from detection.core.rknn_inference_engine import RKNNInferenceEngine
    RKNN_AVAILABLE = True
except ImportError:
    RKNN_AVAILABLE = False
    print("⚠️ RKNN推理引擎不可用，火焰烟雾检测功能将不可用")

# 保持向后兼容性 - 如果需要PyTorch版本，可以取消注释
# import torch
# from ultralytics import YOLO


class FireSmokeDetectionEngine:
    """火焰烟雾检测引擎 - RKNN版本"""

    def __init__(self, config_dict: Dict):
        """
        从配置字典初始化RKNN检测引擎

        Args:
            config_dict: 配置字典，包含模型路径、阈值等参数
        """
        # 初始化日志（必须在其他初始化之前）
        self.logger = logging.getLogger("FireSmokeDetectionEngine")
        if not self.logger.handlers:
            # 如果没有处理器，添加一个控制台处理器
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)

        self.config = config_dict

        # RKNN推理引擎
        self.single_engine = None
        self.fire_engine = None
        self.smoke_engine = None

        # 检测参数
        self.confidence_threshold = config_dict.get('confidence_threshold', 0.5)
        self.iou_threshold = config_dict.get('iou_threshold', 0.45)
        self.input_size = config_dict.get('input_size', (640, 640))

        # 设备配置（保持兼容性，实际使用NPU）
        self.device = self._get_device(config_dict.get('device', 'npu'))

        # 类别配置
        self.class_names = config_dict.get('class_names', {0: 'fire', 1: 'smoke'})
        self.colors = config_dict.get('colors', {'fire': [0, 0, 255], 'smoke': [128, 128, 128]})

        # 双权重配置
        self.smoke_class_mapping = config_dict.get('smoke_class_mapping', {0: 1, 1: 0})

        # 统计信息
        self.detection_count = 0
        self.total_inference_time = 0.0
        self.last_error = None
        self.total_fire_detections = 0
        self.total_smoke_detections = 0
    
    def _get_device(self, device_config: str) -> str:
        """获取计算设备 - RKNN版本"""
        if device_config == 'auto':
            # RKNN默认使用NPU
            device = 'npu'
            self.logger.info("使用NPU (瑞芯微神经处理单元)")
        elif device_config in ['cuda', 'gpu']:
            # 将GPU配置映射到NPU
            device = 'npu'
            self.logger.info("GPU配置已映射到NPU")
        elif device_config == 'cpu':
            # 保持CPU配置（用于调试）
            device = 'cpu'
            self.logger.info("使用CPU (调试模式)")
        else:
            device = device_config
            self.logger.info(f"使用指定设备: {device}")
        return device

    def _convert_model_path(self, model_path: str, model_type: str) -> str:
        """
        转换模型路径为RKNN格式

        Args:
            model_path: 原始模型路径
            model_type: 模型类型 ('fire_smoke', 'fire', 'smoke')

        Returns:
            RKNN模型路径
        """
        if model_path.endswith('.pt') or model_path.endswith('.pth'):
            # PyTorch模型路径转换为RKNN路径
            base_name = os.path.splitext(os.path.basename(model_path))[0]
            rknn_path = f"models/fire_detection/{base_name}.rknn"
            self.logger.info(f"自动转换模型路径: {model_path} -> {rknn_path}")
            return rknn_path
        elif model_path.endswith('.rknn'):
            # 已经是RKNN格式
            return model_path
        else:
            # 默认添加.rknn扩展名
            return f"{model_path}.rknn"
    
    def load_single_model(self, model_path: str) -> bool:
        """
        加载单权重RKNN模型

        Args:
            model_path: RKNN模型文件路径

        Returns:
            是否加载成功
        """
        if not RKNN_AVAILABLE:
            self.last_error = "RKNN推理引擎不可用"
            self.logger.error(self.last_error)
            return False

        # 处理模型路径兼容性
        rknn_model_path = self._convert_model_path(model_path, 'fire_smoke')

        if not Path(rknn_model_path).exists():
            self.last_error = f"RKNN模型文件不存在: {rknn_model_path}"
            self.logger.error(self.last_error)
            return False

        try:
            self.single_engine = RKNNInferenceEngine(
                model_path=rknn_model_path,
                input_size=self.input_size,
                confidence_threshold=self.confidence_threshold,
                iou_threshold=self.iou_threshold,
                class_names=list(self.class_names.values())
            )

            if self.single_engine.is_initialized:
                self.logger.info(f"成功加载单权重RKNN模型: {rknn_model_path}")
                return True
            else:
                self.last_error = "RKNN单模型初始化失败"
                self.logger.error(self.last_error)
                return False

        except Exception as e:
            self.last_error = f"加载单权重RKNN模型失败: {e}"
            self.logger.error(self.last_error)
            return False
    
    def load_dual_models(self, fire_model_path: str, smoke_model_path: str) -> bool:
        """
        加载双权重RKNN模型

        Args:
            fire_model_path: 火焰检测RKNN模型路径
            smoke_model_path: 烟雾检测RKNN模型路径

        Returns:
            是否加载成功
        """
        if not RKNN_AVAILABLE:
            self.last_error = "RKNN推理引擎不可用"
            self.logger.error(self.last_error)
            return False

        # 处理模型路径兼容性
        fire_rknn_path = self._convert_model_path(fire_model_path, 'fire')
        smoke_rknn_path = self._convert_model_path(smoke_model_path, 'smoke')

        if not Path(fire_rknn_path).exists():
            self.last_error = f"火焰检测RKNN模型文件不存在: {fire_rknn_path}"
            self.logger.error(self.last_error)
            return False

        if not Path(smoke_rknn_path).exists():
            self.last_error = f"烟雾检测RKNN模型文件不存在: {smoke_rknn_path}"
            self.logger.error(self.last_error)
            return False

        try:
            # 加载火焰检测RKNN引擎
            self.fire_engine = RKNNInferenceEngine(
                model_path=fire_rknn_path,
                input_size=self.input_size,
                confidence_threshold=self.confidence_threshold,
                iou_threshold=self.iou_threshold,
                class_names=['fire']
            )

            if self.fire_engine.is_initialized:
                self.logger.info(f"成功加载火焰检测RKNN模型: {fire_rknn_path}")
            else:
                self.last_error = "火焰检测RKNN引擎初始化失败"
                self.logger.error(self.last_error)
                return False

            # 加载烟雾检测RKNN引擎
            self.smoke_engine = RKNNInferenceEngine(
                model_path=smoke_rknn_path,
                input_size=self.input_size,
                confidence_threshold=self.confidence_threshold,
                iou_threshold=self.iou_threshold,
                class_names=['smoke']
            )

            if self.smoke_engine.is_initialized:
                self.logger.info(f"成功加载烟雾检测RKNN模型: {smoke_rknn_path}")
                return True
            else:
                self.last_error = "烟雾检测RKNN引擎初始化失败"
                self.logger.error(self.last_error)
                return False

        except Exception as e:
            self.last_error = f"加载双权重RKNN模型失败: {e}"
            self.logger.error(self.last_error)
            return False
    
    def detect_single_model(self, image: np.ndarray) -> List[Dict]:
        """
        使用单权重RKNN模型进行检测

        Args:
            image: 输入图像 (BGR格式)

        Returns:
            检测结果列表
        """
        if self.single_engine is None or not self.single_engine.is_initialized:
            self.last_error = "单权重RKNN模型未加载"
            return []

        start_time = time.time()

        try:
            # 使用RKNN推理引擎执行检测
            detections, _ = self.single_engine.detect(image)

            # 转换为兼容格式
            formatted_detections = []
            for det in detections:
                class_id = det['class_id']
                class_name = det['class_name']

                formatted_det = {
                    'class_id': class_id,
                    'class_name': class_name,
                    'confidence': det['confidence'],
                    'bbox': [int(x) for x in det['bbox']],  # [x1, y1, x2, y2]
                    'source_model': 'single'
                }
                formatted_detections.append(formatted_det)

                # 更新统计
                if class_name == 'fire':
                    self.total_fire_detections += 1
                elif class_name == 'smoke':
                    self.total_smoke_detections += 1

            # 更新统计信息
            self.detection_count += 1
            self.total_inference_time += time.time() - start_time

            return formatted_detections

        except Exception as e:
            self.last_error = f"单权重RKNN检测失败: {e}"
            self.logger.error(self.last_error)
            return []
    
    def detect_dual_model(self, image: np.ndarray) -> List[Dict]:
        """
        使用双权重RKNN模型进行检测

        Args:
            image: 输入图像 (BGR格式)

        Returns:
            检测结果列表
        """
        if ((self.fire_engine is None or not self.fire_engine.is_initialized) and
            (self.smoke_engine is None or not self.smoke_engine.is_initialized)):
            self.last_error = "双权重RKNN模型未完全加载"
            return []

        start_time = time.time()
        all_detections = []

        try:
            # 使用火焰RKNN引擎检测火焰
            if self.fire_engine and self.fire_engine.is_initialized:
                fire_detections, _ = self.fire_engine.detect(image)
                for det in fire_detections:
                    formatted_det = {
                        'class_id': 0,  # 火焰类别ID
                        'class_name': 'fire',
                        'confidence': det['confidence'],
                        'bbox': [int(x) for x in det['bbox']],
                        'source_model': 'fire'
                    }
                    all_detections.append(formatted_det)
                    self.total_fire_detections += 1

            # 使用烟雾RKNN引擎检测烟雾
            if self.smoke_engine and self.smoke_engine.is_initialized:
                smoke_detections, _ = self.smoke_engine.detect(image)
                for det in smoke_detections:
                    formatted_det = {
                        'class_id': 1,  # 烟雾类别ID
                        'class_name': 'smoke',
                        'confidence': det['confidence'],
                        'bbox': [int(x) for x in det['bbox']],
                        'source_model': 'smoke'
                    }
                    all_detections.append(formatted_det)
                    self.total_smoke_detections += 1

            # 更新统计信息
            self.detection_count += 1
            self.total_inference_time += time.time() - start_time

            return all_detections

        except Exception as e:
            self.last_error = f"双权重RKNN检测失败: {e}"
            self.logger.error(self.last_error)
            return []
    
    def visualize_detections(self, image: np.ndarray, detections: List[Dict]) -> np.ndarray:
        """
        可视化检测结果

        Args:
            image: 输入图像
            detections: 检测结果列表

        Returns:
            标注后的图像
        """
        result_image = image.copy()

        for det in detections:
            x1, y1, x2, y2 = det['bbox']
            class_name = det['class_name']
            confidence = det['confidence']

            # 获取颜色
            color = self.colors.get(class_name, [255, 255, 255])
            color_bgr = (int(color[2]), int(color[1]), int(color[0]))  # RGB to BGR

            # 绘制边界框
            cv2.rectangle(result_image, (x1, y1), (x2, y2), color_bgr, 2)

            # 绘制标签
            label = f"{class_name}: {confidence:.2f}"
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]

            # 标签背景
            cv2.rectangle(result_image, (x1, y1 - label_size[1] - 10),
                         (x1 + label_size[0], y1), color_bgr, -1)

            # 标签文字
            cv2.putText(result_image, label, (x1, y1 - 5),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

        return result_image
    
    def set_thresholds(self, confidence: float, iou: float):
        """
        动态调整检测阈值
        
        Args:
            confidence: 置信度阈值 (0.0-1.0)
            iou: IoU阈值 (0.0-1.0)
        """
        self.confidence_threshold = max(0.0, min(1.0, confidence))
        self.iou_threshold = max(0.0, min(1.0, iou))
        self.logger.info(f"更新检测阈值: confidence={self.confidence_threshold:.2f}, iou={self.iou_threshold:.2f}")
    
    def get_statistics(self) -> Dict:
        """
        获取检测统计信息

        Returns:
            统计信息字典
        """
        avg_inference_time = (
            self.total_inference_time / self.detection_count
            if self.detection_count > 0 else 0.0
        )

        stats = {
            'detection_count': self.detection_count,
            'total_inference_time': self.total_inference_time,
            'average_inference_time': avg_inference_time,
            'current_confidence_threshold': self.confidence_threshold,
            'current_iou_threshold': self.iou_threshold,
            'device': self.device,
            'last_error': self.last_error,
            'engine_type': 'RKNN',  # 标识使用RKNN引擎
            'total_fire_detections': self.total_fire_detections,
            'total_smoke_detections': self.total_smoke_detections
        }

        # 添加RKNN引擎统计信息
        if self.single_engine:
            stats['single_engine_stats'] = self.single_engine.get_statistics()

        if self.fire_engine:
            stats['fire_engine_stats'] = self.fire_engine.get_statistics()

        if self.smoke_engine:
            stats['smoke_engine_stats'] = self.smoke_engine.get_statistics()

        return stats
    
    def reset_statistics(self):
        """重置统计信息"""
        self.detection_count = 0
        self.total_inference_time = 0.0
        self.total_fire_detections = 0
        self.total_smoke_detections = 0
        self.last_error = None
        self.logger.info("检测统计信息已重置")

    def cleanup(self):
        """清理RKNN资源"""
        if hasattr(self, 'single_engine') and self.single_engine:
            self.single_engine.cleanup()
            self.single_engine = None

        if hasattr(self, 'fire_engine') and self.fire_engine:
            self.fire_engine.cleanup()
            self.fire_engine = None

        if hasattr(self, 'smoke_engine') and self.smoke_engine:
            self.smoke_engine.cleanup()
            self.smoke_engine = None

        if hasattr(self, 'logger'):
            self.logger.info("RKNN火焰烟雾检测器资源已清理")

    def __del__(self):
        """析构函数 - 确保资源被正确释放"""
        try:
            self.cleanup()
        except Exception:
            # 忽略析构函数中的异常
            pass


# 向后兼容性适配器
class PyTorchFireSmokeDetectionEngineAdapter:
    """
    PyTorch火焰烟雾检测引擎适配器
    为需要PyTorch版本的代码提供兼容性支持
    """

    def __init__(self, config_dict: Dict):
        """初始化适配器，自动使用RKNN版本"""
        self.logger = logging.getLogger("PyTorchFireSmokeDetectionEngineAdapter")
        self.logger.warning("正在使用PyTorch适配器，实际使用RKNN引擎")

        # 创建RKNN版本的检测引擎
        self.engine = FireSmokeDetectionEngine(config_dict)

        # 转发所有属性
        self.config = self.engine.config
        self.confidence_threshold = self.engine.confidence_threshold
        self.iou_threshold = self.engine.iou_threshold
        self.device = self.engine.device

    def load_single_model(self, model_path: str) -> bool:
        """转发到RKNN引擎"""
        return self.engine.load_single_model(model_path)

    def load_dual_models(self, fire_model_path: str, smoke_model_path: str) -> bool:
        """转发到RKNN引擎"""
        return self.engine.load_dual_models(fire_model_path, smoke_model_path)

    def detect_single_model(self, image: np.ndarray) -> List[Dict]:
        """转发到RKNN引擎"""
        return self.engine.detect_single_model(image)

    def detect_dual_model(self, image: np.ndarray) -> List[Dict]:
        """转发到RKNN引擎"""
        return self.engine.detect_dual_model(image)

    def visualize_detections(self, image: np.ndarray, detections: List[Dict]) -> np.ndarray:
        """转发到RKNN引擎"""
        return self.engine.visualize_detections(image, detections)

    def set_thresholds(self, confidence: float, iou: float):
        """转发到RKNN引擎"""
        self.engine.set_thresholds(confidence, iou)
        self.confidence_threshold = confidence
        self.iou_threshold = iou

    def get_statistics(self) -> Dict:
        """转发到RKNN引擎"""
        return self.engine.get_statistics()

    def reset_statistics(self):
        """转发到RKNN引擎"""
        self.engine.reset_statistics()

    def cleanup(self):
        """转发到RKNN引擎"""
        self.engine.cleanup()

    def __del__(self):
        """析构函数"""
        self.cleanup()
