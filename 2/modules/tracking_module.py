"""
目标跟踪模块
实现底部中心点跟踪算法，目标关联和生命周期管理
"""

import numpy as np
import time
import logging
from typing import List, Dict, Tuple, Optional
from collections import defaultdict


class ObjectTrackingEngine:
    """目标跟踪引擎 - 底部中心点跟踪"""
    
    def __init__(self, max_disappeared: int = 10, max_distance: float = 100):
        """
        初始化跟踪引擎
        
        Args:
            max_disappeared: 目标消失的最大帧数
            max_distance: 匹配的最大距离（像素）
        """
        self.max_disappeared = max_disappeared
        self.max_distance = max_distance
        
        # 跟踪状态
        self.next_object_id = 0
        self.objects = {}  # 存储跟踪的目标
        self.disappeared = {}  # 记录目标消失的帧数
        
        # 轨迹记录
        self.trajectories = {}  # 存储目标轨迹
        self.max_trajectory_length = 30  # 最大轨迹长度
        
        # 统计信息
        self.total_objects_tracked = 0
        self.active_objects_count = 0
        
        self.logger = logging.getLogger("ObjectTrackingEngine")
        if not self.logger.handlers:
            # 如果没有处理器，添加一个控制台处理器
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
    
    def register_object(self, centroid: Tuple[int, int], bbox: List[int], 
                       confidence: float, class_name: str) -> int:
        """
        注册新目标
        
        Args:
            centroid: 底部中心点坐标 (x, y)
            bbox: 检测框 [x1, y1, x2, y2]
            confidence: 置信度
            class_name: 类别名称
            
        Returns:
            分配的目标ID
        """
        object_id = self.next_object_id
        
        self.objects[object_id] = {
            'centroid': centroid,  # 底部中心点（根部位置）
            'bbox': bbox,
            'confidence': confidence,
            'class_name': class_name,
            'first_seen': time.time(),
            'last_seen': time.time(),
            'frame_count': 1,
            'total_distance': 0.0  # 累计移动距离
        }
        
        self.disappeared[object_id] = 0
        self.trajectories[object_id] = [centroid]
        
        self.next_object_id += 1
        self.total_objects_tracked += 1
        self.active_objects_count += 1
        
        self.logger.debug(f"注册新目标 {object_id}: {class_name} at {centroid}")
        return object_id
    
    def deregister_object(self, object_id: int):
        """
        注销目标

        Args:
            object_id: 目标ID
        """
        if object_id in self.objects:
            del self.objects[object_id]
            del self.disappeared[object_id]
            # 清理轨迹记录（修复轨迹累积问题）
            if object_id in self.trajectories:
                del self.trajectories[object_id]
            self.active_objects_count -= 1
            self.logger.debug(f"注销目标 {object_id}，已清理轨迹")
    
    def update_tracks(self, detections: List[Dict]) -> Dict[int, Dict]:
        """
        更新跟踪状态
        
        Args:
            detections: 检测结果列表，每个元素包含 bbox, confidence, class_name
            
        Returns:
            更新后的跟踪目标字典
        """
        if len(detections) == 0:
            # 没有检测到目标，增加所有目标的消失计数
            self._handle_no_detections()
            return self.objects
        
        # 计算检测结果的底部中心点（火焰和烟雾的根部）
        input_centroids = []
        for detection in detections:
            centroid = self._calculate_bottom_centroid(detection['bbox'])
            input_centroids.append(centroid)
        
        # 如果没有现有目标，注册所有检测结果
        if len(self.objects) == 0:
            for i, detection in enumerate(detections):
                centroid = input_centroids[i]
                self.register_object(
                    centroid, 
                    detection['bbox'], 
                    detection['confidence'],
                    detection['class_name']
                )
        else:
            # 执行目标关联
            self._associate_detections(detections, input_centroids)
        
        return self.objects
    
    def _calculate_bottom_centroid(self, bbox: List[int]) -> Tuple[int, int]:
        """
        计算检测框的底部中心点（火焰和烟雾的根部位置）
        
        Args:
            bbox: 检测框 [x1, y1, x2, y2]
            
        Returns:
            底部中心点坐标 (cx, cy)
        """
        x1, y1, x2, y2 = bbox
        cx = int((x1 + x2) / 2)  # 水平中心
        cy = int(y2)  # 底部（根部位置）
        return (cx, cy)
    
    def _handle_no_detections(self):
        """处理没有检测到目标的情况"""
        for object_id in list(self.disappeared.keys()):
            self.disappeared[object_id] += 1
            if self.disappeared[object_id] > self.max_disappeared:
                self.deregister_object(object_id)
    
    def _associate_detections(self, detections: List[Dict], input_centroids: List[Tuple[int, int]]):
        """
        关联检测结果与现有目标
        
        Args:
            detections: 检测结果列表
            input_centroids: 检测结果的底部中心点列表
        """
        # 获取现有目标的中心点和ID
        object_centroids = [obj['centroid'] for obj in self.objects.values()]
        object_ids = list(self.objects.keys())
        
        # 计算距离矩阵
        D = self._compute_distance_matrix(object_centroids, input_centroids)
        
        # 找到最佳匹配
        matches = self._find_best_matches(D, object_ids, detections, input_centroids)
        
        # 处理未匹配的目标和检测
        self._handle_unmatched(D, matches, object_ids, detections, input_centroids)
    
    def _compute_distance_matrix(self, object_centroids: List[Tuple[int, int]], 
                                input_centroids: List[Tuple[int, int]]) -> np.ndarray:
        """
        计算目标中心点与检测中心点的距离矩阵
        
        Args:
            object_centroids: 现有目标中心点列表
            input_centroids: 检测结果中心点列表
            
        Returns:
            距离矩阵
        """
        if len(object_centroids) == 0 or len(input_centroids) == 0:
            return np.array([])
        
        # 使用欧几里得距离
        D = np.linalg.norm(
            np.array(object_centroids)[:, np.newaxis] - np.array(input_centroids), 
            axis=2
        )
        return D
    
    def _find_best_matches(self, D: np.ndarray, object_ids: List[int], 
                          detections: List[Dict], input_centroids: List[Tuple[int, int]]) -> Dict:
        """
        找到最佳匹配
        
        Args:
            D: 距离矩阵
            object_ids: 目标ID列表
            detections: 检测结果列表
            input_centroids: 检测中心点列表
            
        Returns:
            匹配信息字典
        """
        matches = {
            'used_row_indices': set(),
            'used_col_indices': set()
        }
        
        if D.size == 0:
            return matches
        
        # 找到最小距离的匹配
        rows = D.min(axis=1).argsort()
        cols = D.argmin(axis=1)[rows]
        
        # 更新匹配的目标
        for (row, col) in zip(rows, cols):
            if row in matches['used_row_indices'] or col in matches['used_col_indices']:
                continue
            
            if D[row, col] > self.max_distance:
                continue
            
            object_id = object_ids[row]
            detection = detections[col]
            new_centroid = input_centroids[col]
            
            # 更新目标信息
            self._update_object(object_id, detection, new_centroid)
            
            matches['used_row_indices'].add(row)
            matches['used_col_indices'].add(col)
        
        return matches
    
    def _update_object(self, object_id: int, detection: Dict, new_centroid: Tuple[int, int]):
        """
        更新目标信息
        
        Args:
            object_id: 目标ID
            detection: 检测结果
            new_centroid: 新的中心点
        """
        old_centroid = self.objects[object_id]['centroid']
        
        # 计算移动距离
        distance = np.linalg.norm(np.array(new_centroid) - np.array(old_centroid))
        
        # 更新目标信息
        self.objects[object_id].update({
            'centroid': new_centroid,
            'bbox': detection['bbox'],
            'confidence': detection['confidence'],
            'last_seen': time.time(),
            'frame_count': self.objects[object_id]['frame_count'] + 1,
            'total_distance': self.objects[object_id]['total_distance'] + distance
        })
        
        # 重置消失计数
        self.disappeared[object_id] = 0
        
        # 更新轨迹
        self._update_trajectory(object_id, new_centroid)
    
    def _update_trajectory(self, object_id: int, centroid: Tuple[int, int]):
        """
        更新目标轨迹
        
        Args:
            object_id: 目标ID
            centroid: 新的中心点
        """
        if object_id not in self.trajectories:
            self.trajectories[object_id] = []
        
        self.trajectories[object_id].append(centroid)
        
        # 保持轨迹长度限制
        if len(self.trajectories[object_id]) > self.max_trajectory_length:
            self.trajectories[object_id] = self.trajectories[object_id][-self.max_trajectory_length:]
    
    def _handle_unmatched(self, D: np.ndarray, matches: Dict, object_ids: List[int], 
                         detections: List[Dict], input_centroids: List[Tuple[int, int]]):
        """
        处理未匹配的目标和检测
        
        Args:
            D: 距离矩阵
            matches: 匹配信息
            object_ids: 目标ID列表
            detections: 检测结果列表
            input_centroids: 检测中心点列表
        """
        if D.size == 0:
            # 没有现有目标，注册所有检测
            for i, detection in enumerate(detections):
                centroid = input_centroids[i]
                self.register_object(
                    centroid, 
                    detection['bbox'], 
                    detection['confidence'],
                    detection['class_name']
                )
            return
        
        unused_rows = set(range(0, D.shape[0])).difference(matches['used_row_indices'])
        unused_cols = set(range(0, D.shape[1])).difference(matches['used_col_indices'])
        
        if D.shape[0] >= D.shape[1]:
            # 更多目标than检测，增加消失计数
            for row in unused_rows:
                object_id = object_ids[row]
                self.disappeared[object_id] += 1
                if self.disappeared[object_id] > self.max_disappeared:
                    self.deregister_object(object_id)
        else:
            # 更多检测than目标，注册新目标
            for col in unused_cols:
                detection = detections[col]
                centroid = input_centroids[col]
                self.register_object(
                    centroid, 
                    detection['bbox'], 
                    detection['confidence'],
                    detection['class_name']
                )
    
    def get_trajectories(self) -> Dict[int, List[Tuple[int, int]]]:
        """
        获取所有目标的轨迹数据
        
        Returns:
            轨迹字典，键为目标ID，值为轨迹点列表
        """
        return dict(self.trajectories)
    
    def get_active_objects(self) -> Dict[int, Dict]:
        """
        获取当前活跃的目标
        
        Returns:
            活跃目标字典
        """
        return dict(self.objects)
    
    def get_statistics(self) -> Dict:
        """
        获取跟踪统计信息
        
        Returns:
            统计信息字典
        """
        return {
            'total_objects_tracked': self.total_objects_tracked,
            'active_objects_count': self.active_objects_count,
            'next_object_id': self.next_object_id,
            'max_disappeared': self.max_disappeared,
            'max_distance': self.max_distance,
            'trajectory_count': len(self.trajectories)
        }
    
    def reset(self):
        """重置跟踪器状态"""
        self.next_object_id = 0
        self.objects = {}
        self.disappeared = {}
        self.trajectories = {}
        self.total_objects_tracked = 0
        self.active_objects_count = 0
        self.logger.info("跟踪器状态已重置")
    
    def set_parameters(self, max_disappeared: int = None, max_distance: float = None):
        """
        设置跟踪参数
        
        Args:
            max_disappeared: 最大消失帧数
            max_distance: 最大匹配距离
        """
        if max_disappeared is not None:
            self.max_disappeared = max_disappeared
        if max_distance is not None:
            self.max_distance = max_distance
        
        self.logger.info(f"跟踪参数已更新: max_disappeared={self.max_disappeared}, max_distance={self.max_distance}")


class TrajectoryAnalyzer:
    """轨迹分析器"""
    
    @staticmethod
    def calculate_movement_direction(trajectory: List[Tuple[int, int]]) -> float:
        """
        计算主要运动方向（角度）
        
        Args:
            trajectory: 轨迹点列表
            
        Returns:
            运动方向角度（度）
        """
        if len(trajectory) < 2:
            return 0.0
        
        start_point = np.array(trajectory[0])
        end_point = np.array(trajectory[-1])
        
        direction_vector = end_point - start_point
        angle = np.arctan2(direction_vector[1], direction_vector[0])
        return np.degrees(angle)
    
    @staticmethod
    def calculate_movement_speed(trajectory: List[Tuple[int, int]], fps: float = 30.0) -> float:
        """
        计算平均移动速度（像素/秒）
        
        Args:
            trajectory: 轨迹点列表
            fps: 视频帧率
            
        Returns:
            平均移动速度
        """
        if len(trajectory) < 2:
            return 0.0
        
        total_distance = 0.0
        for i in range(1, len(trajectory)):
            distance = np.linalg.norm(np.array(trajectory[i]) - np.array(trajectory[i-1]))
            total_distance += distance
        
        time_duration = len(trajectory) / fps
        return total_distance / time_duration if time_duration > 0 else 0.0
    
    @staticmethod
    def calculate_spread_area(trajectory: List[Tuple[int, int]]) -> float:
        """
        计算轨迹覆盖的区域面积
        
        Args:
            trajectory: 轨迹点列表
            
        Returns:
            覆盖区域面积
        """
        if len(trajectory) < 3:
            return 0.0
        
        points = np.array(trajectory)
        x_min, y_min = points.min(axis=0)
        x_max, y_max = points.max(axis=0)
        
        return (x_max - x_min) * (y_max - y_min)
    
    @staticmethod
    def calculate_trajectory_stability(trajectory: List[Tuple[int, int]]) -> float:
        """
        计算轨迹稳定性（0-1，1表示最稳定）
        
        Args:
            trajectory: 轨迹点列表
            
        Returns:
            轨迹稳定性评分
        """
        if len(trajectory) < 3:
            return 1.0
        
        # 计算相邻点之间的距离变化
        distances = []
        for i in range(1, len(trajectory)):
            distance = np.linalg.norm(np.array(trajectory[i]) - np.array(trajectory[i-1]))
            distances.append(distance)
        
        if len(distances) == 0:
            return 1.0
        
        # 使用距离变化的标准差来评估稳定性
        mean_distance = np.mean(distances)
        std_distance = np.std(distances)
        
        if mean_distance == 0:
            return 1.0
        
        # 稳定性 = 1 - (标准差 / 平均值)，限制在0-1范围内
        stability = 1.0 - min(std_distance / mean_distance, 1.0)
        return max(0.0, stability)

    def clear_all_trajectories(self):
        """清理所有轨迹数据"""
        self.trajectories.clear()
        self.logger.info("已清理所有轨迹数据")

    def cleanup_old_trajectories(self, max_age_seconds: float = 30.0):
        """清理过期的轨迹数据"""
        current_time = time.time()

        # 找出需要清理的目标ID
        ids_to_remove = []
        for obj_id, obj_data in self.objects.items():
            if current_time - obj_data.get('last_seen', current_time) > max_age_seconds:
                ids_to_remove.append(obj_id)

        # 清理过期目标
        for obj_id in ids_to_remove:
            self.deregister_object(obj_id)

        if ids_to_remove:
            self.logger.info(f"清理了 {len(ids_to_remove)} 个过期目标的轨迹")
