# PC端验证策略指南

## 概述

通过分层测试和模拟（Mocking）的方法，在PC上验证绝大部分的逻辑代码，最大限度地减少在板子上调试的工作量。

## 核心策略：关注逻辑，模拟硬件

我们的目标是在PC上验证所有不依赖NPU硬件的部分，包括：
- Qt界面的响应和布局
- 多线程架构是否能正常工作
- 视频读取、图像预处理和后处理的逻辑是否正确
- 核心业务分析逻辑是否正确

## 文件结构

```
项目根目录/
├── mock_ai_worker.py              # 模拟AI Worker
├── platform_detector.py          # 平台检测器
├── test_pc_simulation.py         # PC端模拟测试
├── start_with_platform_detection.py  # 智能启动器
├── ai_processing_migrator.py      # AI处理迁移器（已修改）
└── PC_VERIFICATION_GUIDE.md      # 本指南
```

## 使用方法

### 1. 自动平台检测启动

```bash
# 自动检测平台并启动相应模式
python start_with_platform_detection.py
```

### 2. 强制模拟模式（PC端测试）

```bash
# 强制使用模拟AI Worker
python start_with_platform_detection.py --force-mock
```

### 3. 强制真实模式（目标设备）

```bash
# 强制使用真实AI Worker
python start_with_platform_detection.py --force-real
```

### 4. 运行PC端模拟测试

```bash
# 运行完整的模拟测试套件
python start_with_platform_detection.py --test
```

### 5. 查看平台信息

```bash
# 仅显示平台检测信息
python start_with_platform_detection.py --info
```

## 平台检测逻辑

系统会自动检测以下条件来判断运行环境：

1. **操作系统和架构**：Linux + aarch64/arm64
2. **NPU硬件指示器**：
   - `/dev/davinci0` - 华为昇腾NPU设备文件
   - `/dev/davinci_manager`
   - `/usr/local/Ascend` - 昇腾软件栈
   - `/opt/npu` - 其他NPU安装路径

3. **环境变量控制**：
   - `FORCE_MOCK_AI=1` - 强制模拟模式
   - `FORCE_REAL_AI=1` - 强制真实模式

## 模拟AI Worker功能

### 完全兼容的接口
模拟AI Worker与真实AI Worker具有完全相同的接口：

- **方法**：`initialize_detectors()`, `add_task()`, `pause_processing()` 等
- **信号**：`human_detection_finished`, `fire_smoke_detection_finished` 等
- **数据结构**：`AITaskData`, `AIResultData`

### 模拟功能特性

1. **真实的处理延迟**：模拟20-100ms的推理时间
2. **随机检测结果**：70%概率生成检测结果
3. **错误模拟**：2%概率模拟处理错误
4. **可视化标注**：生成带有检测框和标签的图像
5. **统计信息**：提供与真实版本一致的性能统计

### 检测类型支持

- **人体检测**：生成1-3个随机人体检测框
- **火焰烟雾检测**：生成火焰或烟雾检测框
- **热源检测**：生成温度点检测结果

## 测试验证流程

### 1. 接口完整性验证
```python
from platform_detector import get_ai_worker_instance, validate_ai_worker_interface

ai_worker = get_ai_worker_instance()
is_valid = validate_ai_worker_interface(ai_worker)
```

### 2. 功能测试
```python
# 运行完整的功能测试
python test_pc_simulation.py
```

测试包括：
- 人体检测任务提交和结果接收
- 火焰烟雾检测任务处理
- 热源检测功能验证
- 错误处理机制测试
- 性能统计验证

### 3. Qt界面测试
在PC端可以完整测试：
- 界面布局和响应
- 多线程数据交换
- 信号槽机制
- 图像显示和更新

## 开发工作流

### PC端开发阶段
1. 使用模拟模式开发和调试界面逻辑
2. 验证多线程架构
3. 测试业务逻辑流程
4. 进行单元测试和集成测试

### 目标设备部署阶段
1. 将代码部署到目标设备
2. 系统自动检测并切换到真实模式
3. 仅需验证NPU推理结果
4. 进行最终的端到端测试

## 环境变量配置

```bash
# 强制模拟模式
export FORCE_MOCK_AI=1

# 强制真实模式  
export FORCE_REAL_AI=1

# 详细日志输出
export LOG_LEVEL=DEBUG
```

## 故障排除

### 1. 导入错误
如果遇到模块导入错误，检查：
- Python路径设置
- 依赖项安装
- 文件权限

### 2. 平台检测错误
如果平台检测不准确：
- 使用 `--force-mock` 或 `--force-real` 参数
- 检查环境变量设置
- 查看详细日志输出

### 3. 测试失败
如果测试失败：
- 检查Qt应用程序环境
- 验证信号槽连接
- 查看错误日志

## 性能对比

| 指标 | PC端模拟 | 目标设备 |
|------|----------|----------|
| 启动时间 | < 5秒 | 10-30秒 |
| 推理延迟 | 20-100ms | 实际NPU延迟 |
| 内存占用 | 较低 | 较高 |
| 调试便利性 | 极高 | 中等 |

## 最佳实践

1. **优先PC端验证**：在PC端完成90%以上的开发和测试
2. **接口一致性**：确保模拟和真实版本接口完全一致
3. **渐进式测试**：从单元测试到集成测试再到端到端测试
4. **自动化测试**：使用测试脚本进行回归测试
5. **文档同步**：及时更新接口文档和测试用例

## 扩展功能

### 添加新的检测类型
1. 在 `mock_ai_worker.py` 中添加新的模拟方法
2. 在 `ai_worker.py` 中添加对应的真实实现
3. 更新接口验证列表
4. 添加相应的测试用例

### 自定义模拟行为
```python
# 修改模拟配置
mock_config = {
    'processing_delay_range': (0.05, 0.2),  # 调整延迟范围
    'error_probability': 0.05,              # 调整错误概率
    'detection_probability': 0.8,           # 调整检测概率
}
```

## 总结

这个PC端验证策略让您能够：
- 在PC上快速开发和调试
- 验证完整的业务逻辑
- 减少在目标设备上的调试时间
- 提高开发效率和代码质量

通过这种分层测试方法，您可以确信在PC端验证通过的代码在目标设备上也能正常工作。
