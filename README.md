# 🔥 海康威视双光谱热成像监控系统

## 📋 项目简介

这是一个基于海康威视双光谱摄像头的智能热成像监控系统，集成了热源检测、人体检测、火焰烟雾检测、实时温度监测和Qt图形界面等功能。

## ✨ 核心功能

- 🌡️ **实时温度监测** - 精确的热成像温度检测和分析
- 🔥 **热源检测** - 智能热源识别和跟踪，支持多热源检测
- 👤 **人体检测** - 基于YOLO的人体识别，蓝色框标识
- 🚨 **火焰烟雾检测** - 本地训练的火焰烟雾检测模型

- 🖥️ **Qt图形界面** - 专业的桌面应用界面
- 📊 **数据统计分析** - 热源变化率、面积统计等指标
- 🎯 **双光配准** - 可见光与热成像画面精确对齐

## 🏗️ 模块化架构

### 📁 目录结构

```
tjxf/
├── main.py                     # 主程序入口
├── system_initializer.py       # 系统初始化器
├── requirements.txt            # 依赖库列表
├── manual_annotations.json     # 手动标注数据
├── config/                     # 配置模块
│   ├── __init__.py
│   ├── camera_config.py        # 摄像头配置
│   ├── detection_config.py     # 检测配置

│   ├── system_config.py        # 系统配置
│   ├── chinese_text_config.py  # 中文文本配置
│   ├── fire_detection_config.py # 火焰检测配置
│   ├── heat_detection_config.py # 热源检测配置
│   ├── human_detection_config.py # 人体检测配置
│   ├── fire_detection_renderer.py # 火焰检测渲染器
│   ├── heat_detection_renderer.py # 热源检测渲染器
│   ├── thermal_dimension_manager.py # 热成像尺寸管理
│   ├── dual_camera_alignment.json # 双光配准配置
│   └── heat_area_config.json   # 热源区域配置
├── core/                       # 核心功能模块
│   ├── __init__.py
│   ├── video_capture.py        # 视频捕获模块
│   ├── frame_processor.py      # 帧处理器
│   └── performance_monitor.py  # 性能监控
├── detection/                  # 检测模块
│   ├── __init__.py
│   ├── core/                   # 核心检测器
│   ├── analysis/               # 分析模块
│   ├── processors/             # 处理器
│   ├── utils/                  # 检测工具
│   ├── debug/                  # 调试模块
│   └── json_output/            # JSON输出
├── qt_ui/                      # Qt用户界面
│   ├── main_window.py          # 主窗口界面
│   ├── qt_system_adapter.py    # Qt系统适配器
│   ├── qt_sidebar.py           # 侧边栏
│   ├── enhanced_sidebar.py     # 增强侧边栏
│   ├── qt_toolbar.py           # 工具栏
│   ├── qt_event_handler.py     # 事件处理器
│   ├── qt_mouse_handler.py     # 鼠标处理器
│   ├── qt_overlay_renderer.py  # 覆盖层渲染器
│   ├── metrics_manager.py      # 指标管理器
│   ├── sidebar_settings_dialog.py # 侧边栏设置对话框
│   └── toolbar_callbacks.py    # 工具栏回调
├── ui/                         # 通用UI模块
│   ├── __init__.py
│   └── control_handler.py      # 控制处理器
├── temperature/                # 温度相关模块
│   ├── __init__.py
│   ├── readers/                # 温度读取器
│   └── calculators/            # 温度计算器

├── tools/                      # 核心工具
│   ├── dual_camera_integration.py  # 双光配准
│   ├── coordinate_converter.py     # 坐标转换
│   ├── depth_aware_alignment.py    # 深度感知对齐
│   ├── depth_estimator.py          # 深度估计器
│   ├── depth_visualizer.py         # 深度可视化
│   ├── depth_zone_config.py        # 深度区域配置
│   ├── hikvision_sdk_alignment.py  # 海康SDK对齐
│   ├── hikvision_sdk_core.py       # 海康SDK核心
│   └── hikvision_sdk_structures.py # 海康SDK结构
├── utils/                      # 工具模块
│   ├── __init__.py
│   ├── logger.py               # 日志管理
│   ├── chinese_text_renderer.py # 中文文本渲染器
│   └── safe_text_renderer.py   # 安全文本渲染器
├── models/                     # 模型文件
│   ├── fire_detection/         # 火焰检测模型
│   ├── heat_detection/         # 热源检测模型
│   └── human_detection/        # 人体检测模型
├── docs/                       # 文档目录
│   ├── 系统架构设计文档.md     # 系统架构文档
│   ├── API接口文档.md          # API接口文档
│   └── 开发指南.md             # 开发指南
├── tianjin/                    # Python虚拟环境
├── logs/                       # 日志目录
└── captures/                   # 截图目录
```

## 🚀 快速开始

### 1. 环境要求

- Python 3.8+
- Windows 10/11 (推荐)
- 海康威视双光谱摄像头
- 网络连接

### 2. 安装依赖

#### Windows环境
```bash
# 安装所有依赖库
pip install -r requirements.txt

# 或手动安装主要依赖
pip install PyQt5 opencv-python ultralytics torch torchvision
```

#### Linux环境（边缘计算单元部署）
```bash
# 🚀 新版自动部署脚本（推荐）
chmod +x deploy_linux.sh
./deploy_linux.sh

# 启动系统
./start_linux.sh

# 或作为系统服务运行
sudo systemctl start thermal_camera
```

**详细Linux部署说明**: 请参考 [Linux部署指南.md](Linux部署指南.md)

#### 旧版Linux安装方式（兼容性）
```bash
# 一键部署（旧版）
chmod +x setup_linux.sh
./setup_linux.sh

# 或分步安装
chmod +x scripts/setup_linux_environment.sh
./scripts/setup_linux_environment.sh

# 仅安装系统依赖
chmod +x scripts/install_linux_dependencies.sh
./scripts/install_linux_dependencies.sh

# 修复依赖问题
chmod +x scripts/quick_fix_dependencies.sh
./scripts/quick_fix_dependencies.sh
```

### 3. 配置系统

#### 🔐 环境变量配置（推荐）

为了安全起见，系统支持使用环境变量管理敏感配置信息。

1. **复制配置模板**：
   ```bash
   cp .env.example .env
   ```

2. **编辑环境变量文件**：
   编辑 `.env` 文件，配置您的实际参数：



3. **安装环境变量依赖**：
   ```bash
   pip install python-dotenv
   ```



#### 📝 传统配置方式

如果不使用环境变量，您也可以直接编辑配置文件：

##### 摄像头配置
编辑 `config/camera_config.py` 文件：

```python
@dataclass
class CameraConfig:
    ip: str = '************'        # 摄像头IP地址
    username: str = 'admin'          # 用户名
    password: str = 'your_password'  # 密码
    visible_stream: str = 'rtsp://...'  # 可见光流
    thermal_stream: str = 'rtsp://...'  # 热成像流
```





#### 🔒 安全注意事项

- ⚠️ **不要将 `.env` 文件提交到版本控制系统**
- 🔐 **生产环境建议使用环境变量而非配置文件**

- 📁 **确保 `.env` 文件权限设置正确**

### 4. 运行系统

#### Windows环境
```bash
# 运行主程序
python main.py
```

#### Linux环境
```bash
# 如果使用了虚拟环境
source thermal_detection_env/bin/activate
python main.py

# 或使用激活脚本
./activate_env.sh
python main.py

# 如果未使用虚拟环境
python3 main.py
```

## 🐧 Linux环境特别说明

### 支持的Linux发行版
- ✅ Ubuntu 18.04+ / Debian 10+
- ✅ CentOS 7+ / RHEL 7+
- ✅ Fedora 30+
- ✅ Arch Linux / Manjaro
- ✅ 其他基于上述发行版的系统

### 依赖验证
如果遇到问题，可以使用以下工具：

```bash
# 验证依赖安装
python scripts/verify_dependencies.py
```

## 🎮 使用说明

### Qt界面功能

- **实时监控** - 双画面显示（可见光+热成像）
- **热源检测** - 自动识别和标记热源（红色框）
- **人体检测** - 人体识别和标记（蓝色框）
- **火焰烟雾检测** - 火灾预警检测
- **温度测量** - 鼠标悬停显示实时温度
- **数据统计** - 侧边栏显示检测统计信息


### 检测标识说明

- 🔴 **红色框** - 热源检测结果
- 🔵 **蓝色框** - 人体检测结果
- 🟡 **黄色框** - 火焰检测结果
- 🟣 **紫色框** - 烟雾检测结果

### 数据输出

系统提供以下数据统计：
- 全局指标：热源总数、面积统计、变化率等
- 单热源指标：位置、尺寸、温度、变化率等
- 检测统计：人体数量、火焰烟雾检测结果

## 🔧 模块说明

### 配置模块 (config/)

- **camera_config.py**: 摄像头连接参数、RTSP流地址、显示配置
- **detection_config.py**: 检测算法参数、阈值设置

- **system_config.py**: 系统级配置、文件路径、日志设置

### 核心模块 (core/)

- **video_capture.py**: 低延迟视频捕获，支持双摄像头同步
- **frame_processor.py**: 帧处理和图像预处理
- **performance_monitor.py**: 系统性能监控和优化

### 检测模块 (detection/)

- **core/**: 核心检测算法和检测器基类
- **analysis/**: 检测结果分析模块，包括指标计算、变化率分析等
- **processors/**: 检测数据处理器，负责检测结果的后处理
- **utils/**: 检测相关的工具函数和辅助类
- **debug/**: 调试模块，用于检测算法的调试和可视化
- **json_output/**: JSON格式的检测结果输出

### 温度模块 (temperature/)

- **readers/**: 各种温度读取器实现
  - `base_reader.py`: 温度读取器基类和管理器
  - `real_temp_reader.py`: 海康威视ISAPI真实温度读取器
- **calculators/**: 温度计算算法
  - `simple_calculator.py`: 基于图像分析的温度计算

### Qt界面模块 (qt_ui/)

- **main_window.py**: 主窗口界面设计和控制
- **qt_system_adapter.py**: Qt系统适配器，连接后端和界面
- **qt_sidebar.py**: 侧边栏基础功能
- **enhanced_sidebar.py**: 增强侧边栏，包含更多统计信息
- **qt_toolbar.py**: 工具栏界面和控制
- **qt_event_handler.py**: Qt事件处理器
- **qt_mouse_handler.py**: 鼠标事件处理
- **qt_overlay_renderer.py**: 覆盖层渲染器，用于检测结果显示
- **metrics_manager.py**: 界面指标管理器
- **sidebar_settings_dialog.py**: 侧边栏设置对话框
- **toolbar_callbacks.py**: 工具栏回调函数



### 工具模块 (utils/)

- **logger.py**: 统一日志管理、系统信息记录
- **chinese_text_renderer.py**: 中文文本渲染器，支持中文字符显示
- **safe_text_renderer.py**: 安全文本渲染器，防止文本渲染错误

### UI模块 (ui/)

- **control_handler.py**: 通用控制处理器，处理用户界面交互

### 核心工具 (tools/)

- **dual_camera_integration.py**: 双光配准算法
- **coordinate_converter.py**: 坐标系转换工具
- **depth_aware_alignment.py**: 深度感知对齐算法
- **depth_estimator.py**: 深度估计器
- **depth_visualizer.py**: 深度可视化工具
- **depth_zone_config.py**: 深度区域配置
- **hikvision_sdk_alignment.py**: 海康威视SDK对齐工具
- **hikvision_sdk_core.py**: 海康威视SDK核心功能
- **hikvision_sdk_structures.py**: 海康威视SDK数据结构

### 文档模块 (docs/)

- **系统架构设计文档.md**: 详细的系统架构和模块设计说明
- **API接口文档.md**: ISAPI接口等API文档
- **开发指南.md**: 扩展开发指南和代码规范

## 🎯 技术特性

### 智能检测算法

- **多目标检测**: 同时支持热源、人体、火焰、烟雾检测
- **本地模型**: 使用本地训练的火焰烟雾检测模型，无需联网
- **实时处理**: 高效的图像处理和检测算法
- **精确配准**: 可见光与热成像画面精确对齐

### 数据处理与传输

- **实时统计**: 热源数量、面积、温度变化率等指标
- **数据导出**: 标准化的数据格式，支持Excel导出
- **变化率分析**: 基于时序数据的趋势分析
- **多格式输出**: JSON格式的结构化数据输出

### 系统架构优势

- **模块化设计**: 每个模块职责单一，易于维护和扩展
- **配置驱动**: 集中的配置管理，支持不同部署环境
- **异常处理**: 完善的错误处理和恢复机制
- **性能优化**: 多线程处理，低延迟视频流

## 📦 部署打包

### 生成可执行文件

```bash
# 安装打包工具
pip install pyinstaller

# 生成单文件可执行程序
pyinstaller --onefile --windowed main.py

# 或使用详细配置
pyinstaller --onefile --windowed --add-data "config;config" --add-data "models;models" main.py
```

### 部署要求

- Windows 10/11 系统
- 海康威视双光谱摄像头
- 网络连接（用于摄像头通信）
- 显卡支持（推荐用于深度学习模型加速）

## 🐛 故障排除

### 常见问题

1. **摄像头连接失败**
   - 检查IP地址和认证信息
   - 确认网络连通性
   - 验证RTSP流地址
   - 检查防火墙设置

2. **检测模型加载失败**
   - 确认models目录下有相应的权重文件
   - 检查YOLO模型版本兼容性
   - 验证火焰烟雾检测模型路径

3. **网络连接问题**
   - 检查摄像头网络连接
   - 验证RTSP流地址
   - 确认网络连通性和防火墙设置

4. **性能问题**
   - 调整检测频率和分辨率
   - 检查GPU加速是否可用
   - 优化检测阈值参数

5. **温度数据异常**
   - 检查ISAPI接口是否可用
   - 确认摄像头支持温度数据输出
   - 查看日志文件获取详细错误信息



### 日志文件

系统日志保存在 `logs/` 目录下，包含详细的运行信息和错误记录。

## 📝 开发指南

### 添加新的检测算法

1. 在 `detection/` 目录下创建新的检测器
2. 继承相应的基类并实现检测接口
3. 在配置文件中添加相关参数
4. 在主程序中集成新的检测器

### 扩展数据导出格式

1. 修改 `data_export/` 模块中的导出格式
2. 更新配置文件中的导出参数
3. 确保数据格式与需求兼容

### 自定义界面功能

1. 修改 `qt_ui/` 目录下的界面文件
2. 添加新的控件和事件处理
3. 更新系统适配器以支持新功能

## 📊 性能指标

- **检测精度**: 热源检测准确率 > 95%
- **处理速度**: 实时处理 25-30 FPS
- **响应时间**: 检测结果显示延迟 < 100ms
- **内存占用**: 典型运行内存 < 2GB

## 📄 许可证

本项目仅供学习和研究使用。请遵守相关法律法规和设备使用协议。
