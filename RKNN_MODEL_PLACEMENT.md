# RKNN模型文件放置指南

## 📁 模型文件目录结构

将您的RKNN模型文件按照以下结构放置：

```
项目根目录/
├── models/
│   ├── human_detection/
│   │   └── yolov8n_human.rknn          # 人体检测模型
│   └── fs/
│       └── models/
│           └── fire_detection/
│               ├── fire.rknn           # 火焰检测模型
│               └── smoke.rknn          # 烟雾检测模型
```

## 🎯 具体放置位置

### 1. 人体检测模型
**文件路径**: `models/human_detection/yolov8n_human.rknn`
- 用于检测图像中的人体目标
- 基于YOLOv8n架构
- 支持RKNN推理引擎

### 2. 火焰检测模型
**文件路径**: `models/fs/models/fire_detection/fire.rknn`
- 用于检测火焰目标
- 与烟雾检测模型配合使用

### 3. 烟雾检测模型
**文件路径**: `models/fs/models/fire_detection/smoke.rknn`
- 用于检测烟雾目标
- 与火焰检测模型配合使用

## 🚀 部署到目标设备

### 1. 准备U盘传输
将整个项目文件夹复制到U盘，确保包含：
- 所有源代码文件
- RKNN模型文件（按上述结构放置）
- 配置文件
- 依赖项说明

### 2. 目标设备要求
- **操作系统**: Linux (ARM64)
- **NPU支持**: 瑞芯微RK3588或类似芯片
- **Python环境**: Python 3.8+
- **必需库**: rknnlite, opencv-python, PyQt5等

### 3. 自动平台检测
项目会自动检测运行环境：
- **目标设备**: 自动使用真实AI Worker和RKNN推理
- **PC端**: 自动使用模拟AI Worker进行测试

## 📋 验证模型文件

### 检查文件是否存在
```bash
# 在项目根目录执行
ls -la models/human_detection/yolov8n_human.rknn
ls -la models/fs/models/fire_detection/fire.rknn
ls -la models/fs/models/fire_detection/smoke.rknn
```

### 检查文件大小
RKNN模型文件通常有以下特征：
- 文件扩展名: `.rknn`
- 文件大小: 通常几MB到几十MB
- 二进制格式: 无法直接用文本编辑器打开

## 🔧 启动命令

### 在目标设备上启动
```bash
# 自动检测模式（推荐）
python start_with_platform_detection.py

# 强制使用真实AI Worker
python start_with_platform_detection.py --force-real

# 直接启动主程序
python main.py
```

### 验证模型加载
启动后查看日志，应该看到：
```
✅ RKNN推理引擎初始化成功
✅ 人体检测器初始化成功
✅ 火焰烟雾检测器初始化成功
```

## ⚠️ 常见问题

### 1. 模型文件不存在
**错误信息**: `模型文件不存在: models/xxx.rknn`
**解决方案**: 检查文件路径和文件名是否正确

### 2. RKNN库未安装
**错误信息**: `rknnlite库不可用`
**解决方案**: 在目标设备上安装rknnlite
```bash
pip install rknnlite
```

### 3. 权限问题
**错误信息**: `Permission denied`
**解决方案**: 确保模型文件有读取权限
```bash
chmod 644 models/**/*.rknn
```

### 4. NPU设备不可用
**错误信息**: `NPU设备初始化失败`
**解决方案**: 
- 检查设备是否支持NPU
- 确认NPU驱动已正确安装
- 检查设备文件权限

## 📊 性能优化建议

### 1. 模型文件优化
- 使用量化后的RKNN模型以提高推理速度
- 根据实际需求选择合适的模型精度

### 2. 内存管理
- 监控模型加载时的内存使用
- 必要时调整批处理大小

### 3. 推理优化
- 启用NPU加速
- 优化输入数据预处理流程

## 🎉 部署检查清单

在传输到目标设备前，请确认：

- [ ] 所有RKNN模型文件已放置在正确位置
- [ ] 文件路径与代码中的配置一致
- [ ] 模型文件完整且未损坏
- [ ] 项目代码包含最新的PC端验证策略
- [ ] 配置文件已正确设置
- [ ] 依赖项列表已准备

## 📞 技术支持

如果在部署过程中遇到问题：

1. **检查日志**: 查看详细的错误信息
2. **验证环境**: 确认目标设备满足要求
3. **测试模型**: 使用简单的推理脚本验证模型
4. **回退方案**: 可以先在PC端使用模拟模式验证逻辑

---

**祝您部署顺利！** 🚀
