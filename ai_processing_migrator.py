#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI处理逻辑迁移器
将主线程中的AI推理和分析逻辑迁移到AI_Worker线程中
"""

import sys
import time
import uuid
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
import numpy as np
from PyQt5.QtCore import QObject, pyqtSignal, QTimer, QMutex
from PyQt5.QtWidgets import QApplication

from platform_detector import get_ai_worker_instance, validate_ai_worker_interface
from utils.logger import get_logger

# 动态导入数据类型（确保与选择的AI Worker兼容）
try:
    from ai_worker import AITaskData, AIResultData
except ImportError:
    from mock_ai_worker import AITaskData, AIResultData


@dataclass
class MigrationConfig:
    """迁移配置"""
    enable_human_detection: bool = True
    enable_fire_smoke_detection: bool = True
    enable_heat_detection: bool = True
    max_queue_size: int = 50
    processing_timeout: float = 5.0  # 处理超时时间（秒）
    result_cache_size: int = 100
    auto_cleanup_interval: int = 60  # 自动清理间隔（秒）


class AIProcessingMigrator(QObject):
    """AI处理逻辑迁移器"""
    
    # 信号定义
    human_detection_result = pyqtSignal(dict)  # 人体检测结果
    fire_smoke_detection_result = pyqtSignal(dict)  # 火焰烟雾检测结果
    heat_detection_result = pyqtSignal(dict)  # 热源检测结果
    processing_status_changed = pyqtSignal(str, bool)  # 处理状态变化
    error_occurred = pyqtSignal(str, str)  # 错误发生
    
    def __init__(self, config: MigrationConfig = None, parent=None):
        """
        初始化AI处理迁移器
        
        Args:
            config: 迁移配置
            parent: 父对象
        """
        super().__init__(parent)
        
        self.config = config or MigrationConfig()
        self.logger = get_logger("AIProcessingMigrator")
        
        # AI工作线程 - 使用平台检测器自动选择
        self.ai_worker = get_ai_worker_instance(self)

        # 验证AI Worker接口
        if not validate_ai_worker_interface(self.ai_worker):
            self.logger.error("AI Worker接口验证失败")
            raise RuntimeError("AI Worker接口不完整")
        
        # 连接信号槽
        self._connect_signals()
        
        # 任务管理
        self.pending_tasks = {}  # task_id -> task_info
        self.result_cache = {}   # task_id -> result
        self.task_mutex = QMutex()
        
        # 性能监控
        self.performance_stats = {
            'total_tasks': 0,
            'completed_tasks': 0,
            'failed_tasks': 0,
            'average_processing_time': 0.0,
            'queue_utilization': 0.0
        }
        
        # 自动清理定时器
        self.cleanup_timer = QTimer()
        self.cleanup_timer.timeout.connect(self._cleanup_expired_tasks)
        self.cleanup_timer.start(self.config.auto_cleanup_interval * 1000)
        
        self.logger.info("AI处理迁移器初始化完成")
    
    def _connect_signals(self):
        """连接信号槽"""
        # 连接AI工作线程的信号
        self.ai_worker.human_detection_finished.connect(self._on_human_detection_finished)
        self.ai_worker.fire_smoke_detection_finished.connect(self._on_fire_smoke_detection_finished)
        self.ai_worker.heat_detection_finished.connect(self._on_heat_detection_finished)
        self.ai_worker.processing_error.connect(self._on_processing_error)
        self.ai_worker.statistics_updated.connect(self._on_statistics_updated)
    
    def initialize_detectors(self, 
                           human_detector=None,
                           fire_smoke_detector=None,
                           heat_detector=None):
        """
        初始化AI检测器
        
        Args:
            human_detector: 人体检测器实例
            fire_smoke_detector: 火焰烟雾检测器实例
            heat_detector: 热源检测器实例
        """
        self.ai_worker.initialize_detectors(
            human_detector=human_detector,
            fire_smoke_detector=fire_smoke_detector,
            heat_detector=heat_detector
        )
        
        # 启动AI工作线程
        if not self.ai_worker.isRunning():
            self.ai_worker.start()
            self.logger.info("AI工作线程已启动")
    
    def migrate_human_detection(self, frame: np.ndarray, 
                              callback: Optional[Callable] = None,
                              metadata: Optional[Dict] = None) -> str:
        """
        迁移人体检测逻辑到工作线程
        
        Args:
            frame: 输入图像帧
            callback: 结果回调函数
            metadata: 附加元数据
            
        Returns:
            任务ID
        """
        if not self.config.enable_human_detection:
            self.logger.warning("人体检测已禁用")
            return ""
        
        task_id = str(uuid.uuid4())
        
        # 创建任务数据
        task_data = AITaskData(
            task_id=task_id,
            task_type='human_detection',
            frame_data=frame.copy(),
            timestamp=time.time(),
            metadata=metadata or {}
        )
        
        # 记录任务信息
        self.task_mutex.lock()
        try:
            self.pending_tasks[task_id] = {
                'task_type': 'human_detection',
                'callback': callback,
                'start_time': time.time(),
                'metadata': metadata
            }
        finally:
            self.task_mutex.unlock()
        
        # 提交任务到工作线程
        success = self.ai_worker.add_task(task_data)
        if not success:
            self.logger.error(f"提交人体检测任务失败: {task_id}")
            self._cleanup_task(task_id)
            return ""
        
        self.performance_stats['total_tasks'] += 1
        self.logger.debug(f"人体检测任务已提交: {task_id}")
        
        return task_id
    
    def migrate_fire_smoke_detection(self, frame: np.ndarray,
                                   detection_mode: str = 'single',
                                   callback: Optional[Callable] = None,
                                   metadata: Optional[Dict] = None) -> str:
        """
        迁移火焰烟雾检测逻辑到工作线程
        
        Args:
            frame: 输入图像帧
            detection_mode: 检测模式 ('single' 或 'dual')
            callback: 结果回调函数
            metadata: 附加元数据
            
        Returns:
            任务ID
        """
        if not self.config.enable_fire_smoke_detection:
            self.logger.warning("火焰烟雾检测已禁用")
            return ""
        
        task_id = str(uuid.uuid4())
        
        # 创建任务数据
        task_metadata = metadata or {}
        task_metadata['detection_mode'] = detection_mode
        
        task_data = AITaskData(
            task_id=task_id,
            task_type='fire_smoke_detection',
            frame_data=frame.copy(),
            timestamp=time.time(),
            metadata=task_metadata
        )
        
        # 记录任务信息
        self.task_mutex.lock()
        try:
            self.pending_tasks[task_id] = {
                'task_type': 'fire_smoke_detection',
                'callback': callback,
                'start_time': time.time(),
                'metadata': task_metadata
            }
        finally:
            self.task_mutex.unlock()
        
        # 提交任务到工作线程
        success = self.ai_worker.add_task(task_data)
        if not success:
            self.logger.error(f"提交火焰烟雾检测任务失败: {task_id}")
            self._cleanup_task(task_id)
            return ""
        
        self.performance_stats['total_tasks'] += 1
        self.logger.debug(f"火焰烟雾检测任务已提交: {task_id}")
        
        return task_id
    
    def migrate_heat_detection(self, frame: np.ndarray,
                             callback: Optional[Callable] = None,
                             metadata: Optional[Dict] = None) -> str:
        """
        迁移热源检测逻辑到工作线程
        
        Args:
            frame: 输入图像帧
            callback: 结果回调函数
            metadata: 附加元数据
            
        Returns:
            任务ID
        """
        if not self.config.enable_heat_detection:
            self.logger.warning("热源检测已禁用")
            return ""
        
        task_id = str(uuid.uuid4())
        
        # 创建任务数据
        task_data = AITaskData(
            task_id=task_id,
            task_type='heat_detection',
            frame_data=frame.copy(),
            timestamp=time.time(),
            metadata=metadata or {}
        )
        
        # 记录任务信息
        self.task_mutex.lock()
        try:
            self.pending_tasks[task_id] = {
                'task_type': 'heat_detection',
                'callback': callback,
                'start_time': time.time(),
                'metadata': metadata
            }
        finally:
            self.task_mutex.unlock()
        
        # 提交任务到工作线程
        success = self.ai_worker.add_task(task_data)
        if not success:
            self.logger.error(f"提交热源检测任务失败: {task_id}")
            self._cleanup_task(task_id)
            return ""
        
        self.performance_stats['total_tasks'] += 1
        self.logger.debug(f"热源检测任务已提交: {task_id}")
        
        return task_id
    
    def _on_human_detection_finished(self, result_data: AIResultData):
        """处理人体检测完成信号"""
        self._handle_detection_result(result_data, 'human_detection')
    
    def _on_fire_smoke_detection_finished(self, result_data: AIResultData):
        """处理火焰烟雾检测完成信号"""
        self._handle_detection_result(result_data, 'fire_smoke_detection')
    
    def _on_heat_detection_finished(self, result_data: AIResultData):
        """处理热源检测完成信号"""
        self._handle_detection_result(result_data, 'heat_detection')
    
    def _handle_detection_result(self, result_data: AIResultData, detection_type: str):
        """处理检测结果"""
        task_id = result_data.task_id
        
        self.task_mutex.lock()
        try:
            # 获取任务信息
            task_info = self.pending_tasks.get(task_id)
            if not task_info:
                self.logger.warning(f"未找到任务信息: {task_id}")
                return
            
            # 缓存结果
            self.result_cache[task_id] = result_data
            
            # 调用回调函数
            callback = task_info.get('callback')
            if callback:
                try:
                    callback(result_data)
                except Exception as e:
                    self.logger.error(f"回调函数执行失败: {e}")
            
            # 发送信号
            result_dict = {
                'task_id': task_id,
                'detections': result_data.detections,
                'annotated_frame': result_data.annotated_frame,
                'processing_time': result_data.processing_time,
                'timestamp': result_data.timestamp,
                'metadata': result_data.metadata
            }
            
            if detection_type == 'human_detection':
                self.human_detection_result.emit(result_dict)
            elif detection_type == 'fire_smoke_detection':
                self.fire_smoke_detection_result.emit(result_dict)
            elif detection_type == 'heat_detection':
                self.heat_detection_result.emit(result_dict)
            
            # 更新统计
            self.performance_stats['completed_tasks'] += 1
            
            # 清理任务
            self._cleanup_task(task_id)
            
        finally:
            self.task_mutex.unlock()
    
    def _on_processing_error(self, task_id: str, error_message: str):
        """处理错误信号"""
        self.logger.error(f"AI处理错误 [{task_id}]: {error_message}")
        
        self.task_mutex.lock()
        try:
            # 更新统计
            self.performance_stats['failed_tasks'] += 1
            
            # 清理任务
            self._cleanup_task(task_id)
            
        finally:
            self.task_mutex.unlock()
        
        # 发送错误信号
        self.error_occurred.emit(task_id, error_message)
    
    def _on_statistics_updated(self, stats: Dict):
        """处理统计信息更新"""
        # 更新性能统计
        if 'average_processing_time' in stats:
            self.performance_stats['average_processing_time'] = stats['average_processing_time']
        if 'queue_size' in stats:
            total_capacity = self.config.max_queue_size
            self.performance_stats['queue_utilization'] = stats['queue_size'] / total_capacity
    
    def _cleanup_task(self, task_id: str):
        """清理任务"""
        if task_id in self.pending_tasks:
            del self.pending_tasks[task_id]
        
        # 清理过期的结果缓存
        if len(self.result_cache) > self.config.result_cache_size:
            # 删除最旧的结果
            oldest_task = min(self.result_cache.keys(), 
                             key=lambda k: self.result_cache[k].timestamp)
            del self.result_cache[oldest_task]
    
    def _cleanup_expired_tasks(self):
        """清理过期任务"""
        current_time = time.time()
        expired_tasks = []
        
        self.task_mutex.lock()
        try:
            for task_id, task_info in self.pending_tasks.items():
                if current_time - task_info['start_time'] > self.config.processing_timeout:
                    expired_tasks.append(task_id)
            
            for task_id in expired_tasks:
                self.logger.warning(f"任务超时，自动清理: {task_id}")
                self._cleanup_task(task_id)
                self.performance_stats['failed_tasks'] += 1
                
        finally:
            self.task_mutex.unlock()
    
    def get_performance_stats(self) -> Dict:
        """获取性能统计信息"""
        return self.performance_stats.copy()
    
    def get_queue_status(self) -> Dict:
        """获取队列状态"""
        return {
            'pending_tasks': len(self.pending_tasks),
            'cached_results': len(self.result_cache),
            'worker_queue_size': self.ai_worker.get_queue_size(),
            'worker_running': self.ai_worker.isRunning()
        }
    
    def shutdown(self):
        """关闭迁移器"""
        self.logger.info("正在关闭AI处理迁移器...")
        
        # 停止定时器
        self.cleanup_timer.stop()
        
        # 停止AI工作线程
        self.ai_worker.stop()
        self.ai_worker.wait(5000)  # 等待5秒
        
        # 清理资源
        self.pending_tasks.clear()
        self.result_cache.clear()
        
        self.logger.info("AI处理迁移器已关闭")
