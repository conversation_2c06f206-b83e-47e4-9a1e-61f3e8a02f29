#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI工作线程模块
创建QThread工作线程类，处理所有AI计算密集型操作
实现异步AI处理和线程安全数据交换
"""

import cv2
import numpy as np
import time
import queue
import threading
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from PyQt5.QtCore import QThread, pyqtSignal, QMutex, QWaitCondition

from utils.logger import get_logger


@dataclass
class AITaskData:
    """AI任务数据结构"""
    task_id: str
    task_type: str  # 'human_detection', 'fire_smoke_detection', 'heat_detection'
    frame_data: np.ndarray
    timestamp: float
    metadata: Dict[str, Any]


@dataclass
class AIResultData:
    """AI结果数据结构"""
    task_id: str
    task_type: str
    detections: List[Dict]
    annotated_frame: Optional[np.ndarray]
    processing_time: float
    timestamp: float
    metadata: Dict[str, Any]


class AI_Worker(QThread):
    """AI工作线程类"""
    
    # Qt信号定义
    human_detection_finished = pyqtSignal(object)  # AIResultData
    fire_smoke_detection_finished = pyqtSignal(object)  # AIResultData
    heat_detection_finished = pyqtSignal(object)  # AIResultData
    processing_error = pyqtSignal(str, str)  # task_id, error_message
    statistics_updated = pyqtSignal(dict)  # 统计信息
    
    def __init__(self, parent=None):
        """
        初始化AI工作线程
        
        Args:
            parent: 父对象
        """
        super().__init__(parent)
        
        self.logger = get_logger("AI_Worker")
        
        # 线程控制
        self.running = False
        self.paused = False
        
        # 任务队列
        self.task_queue = queue.Queue(maxsize=100)  # 限制队列大小防止内存溢出
        
        # 线程同步
        self.mutex = QMutex()
        self.condition = QWaitCondition()
        
        # AI检测器实例
        self.human_detector = None
        self.fire_smoke_detector = None
        self.heat_detector = None
        
        # 统计信息
        self.statistics = {
            'total_tasks_processed': 0,
            'human_detection_count': 0,
            'fire_smoke_detection_count': 0,
            'heat_detection_count': 0,
            'total_processing_time': 0.0,
            'average_processing_time': 0.0,
            'queue_size': 0,
            'error_count': 0,
            'last_update_time': time.time()
        }
        
        # 性能监控
        self.performance_window = []  # 滑动窗口记录处理时间
        self.max_window_size = 100
        
        self.logger.info("AI工作线程初始化完成")
    
    def initialize_detectors(self, 
                           human_detector=None,
                           fire_smoke_detector=None,
                           heat_detector=None):
        """
        初始化AI检测器
        
        Args:
            human_detector: 人体检测器实例
            fire_smoke_detector: 火焰烟雾检测器实例
            heat_detector: 热源检测器实例
        """
        self.mutex.lock()
        try:
            self.human_detector = human_detector
            self.fire_smoke_detector = fire_smoke_detector
            self.heat_detector = heat_detector
            
            detector_status = []
            if human_detector:
                detector_status.append("人体检测器")
            if fire_smoke_detector:
                detector_status.append("火焰烟雾检测器")
            if heat_detector:
                detector_status.append("热源检测器")
            
            self.logger.info(f"AI检测器初始化完成: {', '.join(detector_status)}")
            
        finally:
            self.mutex.unlock()
    
    def add_task(self, task_data: AITaskData) -> bool:
        """
        添加AI任务到队列
        
        Args:
            task_data: AI任务数据
            
        Returns:
            是否成功添加
        """
        try:
            # 检查队列是否已满
            if self.task_queue.full():
                self.logger.warning("AI任务队列已满，丢弃最旧的任务")
                try:
                    self.task_queue.get_nowait()  # 移除最旧的任务
                except queue.Empty:
                    pass
            
            # 添加新任务
            self.task_queue.put_nowait(task_data)
            
            # 更新统计信息
            self.statistics['queue_size'] = self.task_queue.qsize()
            
            # 唤醒工作线程
            self.condition.wakeOne()
            
            return True
            
        except Exception as e:
            self.logger.error(f"添加AI任务失败: {e}")
            return False
    
    def add_human_detection_task(self, 
                               frame: np.ndarray,
                               task_id: Optional[str] = None,
                               metadata: Optional[Dict] = None) -> bool:
        """
        添加人体检测任务
        
        Args:
            frame: 输入图像帧
            task_id: 任务ID
            metadata: 元数据
            
        Returns:
            是否成功添加
        """
        if task_id is None:
            task_id = f"human_{int(time.time() * 1000)}"
        
        task_data = AITaskData(
            task_id=task_id,
            task_type='human_detection',
            frame_data=frame.copy(),
            timestamp=time.time(),
            metadata=metadata or {}
        )
        
        return self.add_task(task_data)
    
    def add_fire_smoke_detection_task(self, 
                                    frame: np.ndarray,
                                    task_id: Optional[str] = None,
                                    metadata: Optional[Dict] = None) -> bool:
        """
        添加火焰烟雾检测任务
        
        Args:
            frame: 输入图像帧
            task_id: 任务ID
            metadata: 元数据
            
        Returns:
            是否成功添加
        """
        if task_id is None:
            task_id = f"fire_smoke_{int(time.time() * 1000)}"
        
        task_data = AITaskData(
            task_id=task_id,
            task_type='fire_smoke_detection',
            frame_data=frame.copy(),
            timestamp=time.time(),
            metadata=metadata or {}
        )
        
        return self.add_task(task_data)
    
    def add_heat_detection_task(self, 
                              frame: np.ndarray,
                              task_id: Optional[str] = None,
                              metadata: Optional[Dict] = None) -> bool:
        """
        添加热源检测任务
        
        Args:
            frame: 输入图像帧
            task_id: 任务ID
            metadata: 元数据
            
        Returns:
            是否成功添加
        """
        if task_id is None:
            task_id = f"heat_{int(time.time() * 1000)}"
        
        task_data = AITaskData(
            task_id=task_id,
            task_type='heat_detection',
            frame_data=frame.copy(),
            timestamp=time.time(),
            metadata=metadata or {}
        )
        
        return self.add_task(task_data)
    
    def pause_processing(self):
        """暂停处理"""
        self.mutex.lock()
        try:
            self.paused = True
            self.logger.info("AI处理已暂停")
        finally:
            self.mutex.unlock()
    
    def resume_processing(self):
        """恢复处理"""
        self.mutex.lock()
        try:
            self.paused = False
            self.condition.wakeOne()
            self.logger.info("AI处理已恢复")
        finally:
            self.mutex.unlock()
    
    def clear_queue(self):
        """清空任务队列"""
        try:
            while not self.task_queue.empty():
                self.task_queue.get_nowait()
            
            self.statistics['queue_size'] = 0
            self.logger.info("AI任务队列已清空")
            
        except Exception as e:
            self.logger.error(f"清空任务队列失败: {e}")
    
    def run(self):
        """线程主循环"""
        self.running = True
        self.logger.info("AI工作线程开始运行")
        
        while self.running:
            try:
                # 检查是否暂停
                self.mutex.lock()
                if self.paused:
                    self.condition.wait(self.mutex)
                    self.mutex.unlock()
                    continue
                self.mutex.unlock()
                
                # 获取任务
                try:
                    task_data = self.task_queue.get(timeout=1.0)  # 1秒超时
                except queue.Empty:
                    continue
                
                # 处理任务
                self._process_task(task_data)
                
                # 更新统计信息
                self._update_statistics()
                
            except Exception as e:
                self.logger.error(f"AI工作线程异常: {e}")
                self.statistics['error_count'] += 1
        
        self.logger.info("AI工作线程已停止")
    
    def _process_task(self, task_data: AITaskData):
        """
        处理AI任务
        
        Args:
            task_data: 任务数据
        """
        start_time = time.time()
        
        try:
            if task_data.task_type == 'human_detection':
                self._process_human_detection(task_data, start_time)
            elif task_data.task_type == 'fire_smoke_detection':
                self._process_fire_smoke_detection(task_data, start_time)
            elif task_data.task_type == 'heat_detection':
                self._process_heat_detection(task_data, start_time)
            else:
                self.logger.warning(f"未知的任务类型: {task_data.task_type}")
                
        except Exception as e:
            self.logger.error(f"处理任务失败 [{task_data.task_id}]: {e}")
            self.processing_error.emit(task_data.task_id, str(e))
    
    def _process_human_detection(self, task_data: AITaskData, start_time: float):
        """处理人体检测任务"""
        if self.human_detector is None:
            self.processing_error.emit(task_data.task_id, "人体检测器未初始化")
            return
        
        try:
            # 执行人体检测
            detections, annotated_frame = self.human_detector.detect_humans(task_data.frame_data)
            
            # 创建结果数据
            result_data = AIResultData(
                task_id=task_data.task_id,
                task_type=task_data.task_type,
                detections=detections,
                annotated_frame=annotated_frame,
                processing_time=time.time() - start_time,
                timestamp=time.time(),
                metadata=task_data.metadata
            )
            
            # 发送结果信号
            self.human_detection_finished.emit(result_data)
            
            # 更新统计
            self.statistics['human_detection_count'] += 1
            
        except Exception as e:
            self.processing_error.emit(task_data.task_id, f"人体检测失败: {e}")
    
    def _process_fire_smoke_detection(self, task_data: AITaskData, start_time: float):
        """处理火焰烟雾检测任务"""
        if self.fire_smoke_detector is None:
            self.processing_error.emit(task_data.task_id, "火焰烟雾检测器未初始化")
            return
        
        try:
            # 执行火焰烟雾检测
            detection_mode = task_data.metadata.get('detection_mode', 'single')
            
            if detection_mode == 'single':
                detections = self.fire_smoke_detector.detect_single_model(task_data.frame_data)
            else:
                detections = self.fire_smoke_detector.detect_dual_model(task_data.frame_data)
            
            # 生成标注图像
            annotated_frame = self.fire_smoke_detector.visualize_detections(
                task_data.frame_data, detections
            )
            
            # 创建结果数据
            result_data = AIResultData(
                task_id=task_data.task_id,
                task_type=task_data.task_type,
                detections=detections,
                annotated_frame=annotated_frame,
                processing_time=time.time() - start_time,
                timestamp=time.time(),
                metadata=task_data.metadata
            )
            
            # 发送结果信号
            self.fire_smoke_detection_finished.emit(result_data)
            
            # 更新统计
            self.statistics['fire_smoke_detection_count'] += 1
            
        except Exception as e:
            self.processing_error.emit(task_data.task_id, f"火焰烟雾检测失败: {e}")
    
    def _process_heat_detection(self, task_data: AITaskData, start_time: float):
        """处理热源检测任务"""
        if self.heat_detector is None:
            self.processing_error.emit(task_data.task_id, "热源检测器未初始化")
            return
        
        try:
            # 执行热源检测（这里需要根据实际的热源检测器接口调整）
            # 假设热源检测器有类似的接口
            if hasattr(self.heat_detector, 'detect_heat_sources'):
                detections, vis_images = self.heat_detector.detect_heat_sources()
                annotated_frame = vis_images.get('thermal_with_detections', task_data.frame_data)
            else:
                detections = []
                annotated_frame = task_data.frame_data
            
            # 创建结果数据
            result_data = AIResultData(
                task_id=task_data.task_id,
                task_type=task_data.task_type,
                detections=detections,
                annotated_frame=annotated_frame,
                processing_time=time.time() - start_time,
                timestamp=time.time(),
                metadata=task_data.metadata
            )
            
            # 发送结果信号
            self.heat_detection_finished.emit(result_data)
            
            # 更新统计
            self.statistics['heat_detection_count'] += 1
            
        except Exception as e:
            self.processing_error.emit(task_data.task_id, f"热源检测失败: {e}")
    
    def _update_statistics(self):
        """更新统计信息"""
        self.statistics['total_tasks_processed'] += 1
        self.statistics['queue_size'] = self.task_queue.qsize()
        self.statistics['last_update_time'] = time.time()
        
        # 计算平均处理时间
        if self.statistics['total_tasks_processed'] > 0:
            self.statistics['average_processing_time'] = (
                self.statistics['total_processing_time'] / 
                self.statistics['total_tasks_processed']
            )
        
        # 定期发送统计信息更新信号
        if self.statistics['total_tasks_processed'] % 10 == 0:
            self.statistics_updated.emit(self.statistics.copy())
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        return self.statistics.copy()

    def get_queue_size(self) -> int:
        """
        获取当前队列大小

        Returns:
            队列中的任务数量
        """
        return self.task_queue.qsize()
    
    def stop(self):
        """停止线程"""
        self.running = False
        self.condition.wakeOne()
        self.logger.info("AI工作线程停止信号已发送")
    
    def cleanup(self):
        """清理资源"""
        self.stop()
        self.wait()  # 等待线程结束
        
        # 清空队列
        self.clear_queue()
        
        # 清理检测器
        if self.human_detector and hasattr(self.human_detector, 'cleanup'):
            self.human_detector.cleanup()
        
        if self.fire_smoke_detector and hasattr(self.fire_smoke_detector, 'cleanup'):
            self.fire_smoke_detector.cleanup()
        
        if self.heat_detector and hasattr(self.heat_detector, 'cleanup'):
            self.heat_detector.cleanup()
        
        self.logger.info("AI工作线程资源已清理")
    
    def __del__(self):
        """析构函数"""
        self.cleanup()
