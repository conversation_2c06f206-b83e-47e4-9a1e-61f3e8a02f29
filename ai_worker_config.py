#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI工作线程配置模块
定义AI工作线程的配置参数和设置
"""

from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict
from pathlib import Path


@dataclass
class RKNNModelConfig:
    """RKNN模型配置"""
    model_path: str
    input_size: tuple = (640, 640)
    confidence_threshold: float = 0.5
    iou_threshold: float = 0.45
    class_names: list = None
    enabled: bool = True


@dataclass
class AIWorkerConfig:
    """AI工作线程配置"""
    # 线程配置
    max_queue_size: int = 100
    processing_timeout: float = 5.0
    statistics_update_interval: int = 10
    
    # 性能配置
    max_performance_window: int = 100
    enable_performance_monitoring: bool = True
    
    # 人体检测配置
    human_detection: RKNNModelConfig = None
    
    # 火焰烟雾检测配置
    fire_smoke_detection: Dict[str, Any] = None
    
    # 热源检测配置
    heat_detection: Dict[str, Any] = None
    
    # 日志配置
    log_level: str = "INFO"
    enable_debug_logging: bool = False
    
    def __post_init__(self):
        """初始化后处理"""
        if self.human_detection is None:
            self.human_detection = RKNNModelConfig(
                model_path="models/human_detection/yolov8n_human.rknn",
                input_size=(640, 640),
                confidence_threshold=0.45,
                iou_threshold=0.45,
                class_names=['person'],
                enabled=True
            )
        
        if self.fire_smoke_detection is None:
            self.fire_smoke_detection = {
                'detection_mode': 'single',  # 'single' 或 'dual'
                'single_model_path': 'models/fire_detection/fire_smoke.rknn',
                'fire_model_path': 'models/fire_detection/fire.rknn',
                'smoke_model_path': 'models/fire_detection/smoke.rknn',
                'confidence_threshold': 0.5,
                'iou_threshold': 0.45,
                'input_size': (640, 640),
                'class_names': {0: 'fire', 1: 'smoke'},
                'colors': {'fire': [0, 0, 255], 'smoke': [128, 128, 128]},
                'enabled': True
            }
        
        if self.heat_detection is None:
            self.heat_detection = {
                'enabled': True,
                'temperature_threshold': 35.0,
                'adaptive_threshold': True,
                'debug_enabled': False
            }


class AIWorkerConfigManager:
    """AI工作线程配置管理器"""
    
    def __init__(self, config_file: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = config_file or "config/ai_worker_config.json"
        self.config = AIWorkerConfig()
        
        # 尝试加载配置文件
        self.load_config()
    
    def load_config(self) -> bool:
        """
        加载配置文件
        
        Returns:
            是否成功加载
        """
        try:
            config_path = Path(self.config_file)
            if config_path.exists():
                import json
                
                with open(config_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                
                # 更新配置
                self._update_config_from_dict(config_data)
                print(f"✅ AI工作线程配置已加载: {self.config_file}")
                return True
            else:
                print(f"⚠️ 配置文件不存在，使用默认配置: {self.config_file}")
                # 创建默认配置文件
                self.save_config()
                return False
                
        except Exception as e:
            print(f"❌ 加载AI工作线程配置失败: {e}")
            return False
    
    def save_config(self) -> bool:
        """
        保存配置文件
        
        Returns:
            是否成功保存
        """
        try:
            config_path = Path(self.config_file)
            config_path.parent.mkdir(parents=True, exist_ok=True)
            
            import json
            
            # 转换为字典
            config_dict = self._config_to_dict()
            
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config_dict, f, indent=2, ensure_ascii=False)
            
            print(f"✅ AI工作线程配置已保存: {self.config_file}")
            return True
            
        except Exception as e:
            print(f"❌ 保存AI工作线程配置失败: {e}")
            return False
    
    def _update_config_from_dict(self, config_data: Dict[str, Any]):
        """从字典更新配置"""
        # 更新基本配置
        if 'max_queue_size' in config_data:
            self.config.max_queue_size = config_data['max_queue_size']
        
        if 'processing_timeout' in config_data:
            self.config.processing_timeout = config_data['processing_timeout']
        
        if 'statistics_update_interval' in config_data:
            self.config.statistics_update_interval = config_data['statistics_update_interval']
        
        if 'max_performance_window' in config_data:
            self.config.max_performance_window = config_data['max_performance_window']
        
        if 'enable_performance_monitoring' in config_data:
            self.config.enable_performance_monitoring = config_data['enable_performance_monitoring']
        
        if 'log_level' in config_data:
            self.config.log_level = config_data['log_level']
        
        if 'enable_debug_logging' in config_data:
            self.config.enable_debug_logging = config_data['enable_debug_logging']
        
        # 更新人体检测配置
        if 'human_detection' in config_data:
            human_config = config_data['human_detection']
            self.config.human_detection = RKNNModelConfig(
                model_path=human_config.get('model_path', self.config.human_detection.model_path),
                input_size=tuple(human_config.get('input_size', self.config.human_detection.input_size)),
                confidence_threshold=human_config.get('confidence_threshold', self.config.human_detection.confidence_threshold),
                iou_threshold=human_config.get('iou_threshold', self.config.human_detection.iou_threshold),
                class_names=human_config.get('class_names', self.config.human_detection.class_names),
                enabled=human_config.get('enabled', self.config.human_detection.enabled)
            )
        
        # 更新火焰烟雾检测配置
        if 'fire_smoke_detection' in config_data:
            self.config.fire_smoke_detection.update(config_data['fire_smoke_detection'])
        
        # 更新热源检测配置
        if 'heat_detection' in config_data:
            self.config.heat_detection.update(config_data['heat_detection'])
    
    def _config_to_dict(self) -> Dict[str, Any]:
        """将配置转换为字典"""
        return {
            'max_queue_size': self.config.max_queue_size,
            'processing_timeout': self.config.processing_timeout,
            'statistics_update_interval': self.config.statistics_update_interval,
            'max_performance_window': self.config.max_performance_window,
            'enable_performance_monitoring': self.config.enable_performance_monitoring,
            'log_level': self.config.log_level,
            'enable_debug_logging': self.config.enable_debug_logging,
            'human_detection': asdict(self.config.human_detection),
            'fire_smoke_detection': self.config.fire_smoke_detection,
            'heat_detection': self.config.heat_detection
        }
    
    def get_config(self) -> AIWorkerConfig:
        """获取配置对象"""
        return self.config
    
    def update_human_detection_config(self, **kwargs):
        """更新人体检测配置"""
        for key, value in kwargs.items():
            if hasattr(self.config.human_detection, key):
                setattr(self.config.human_detection, key, value)
    
    def update_fire_smoke_detection_config(self, **kwargs):
        """更新火焰烟雾检测配置"""
        self.config.fire_smoke_detection.update(kwargs)
    
    def update_heat_detection_config(self, **kwargs):
        """更新热源检测配置"""
        self.config.heat_detection.update(kwargs)
    
    def validate_config(self) -> tuple[bool, list]:
        """
        验证配置有效性
        
        Returns:
            (是否有效, 错误列表)
        """
        errors = []
        
        # 验证基本配置
        if self.config.max_queue_size <= 0:
            errors.append("max_queue_size必须大于0")
        
        if self.config.processing_timeout <= 0:
            errors.append("processing_timeout必须大于0")
        
        if self.config.statistics_update_interval <= 0:
            errors.append("statistics_update_interval必须大于0")
        
        # 验证人体检测配置
        if self.config.human_detection.enabled:
            model_path = Path(self.config.human_detection.model_path)
            if not model_path.exists():
                errors.append(f"人体检测模型文件不存在: {model_path}")
            
            if self.config.human_detection.confidence_threshold <= 0 or self.config.human_detection.confidence_threshold > 1:
                errors.append("人体检测置信度阈值必须在(0,1]范围内")
            
            if self.config.human_detection.iou_threshold <= 0 or self.config.human_detection.iou_threshold > 1:
                errors.append("人体检测IoU阈值必须在(0,1]范围内")
        
        # 验证火焰烟雾检测配置
        if self.config.fire_smoke_detection.get('enabled', True):
            detection_mode = self.config.fire_smoke_detection.get('detection_mode', 'single')
            
            if detection_mode == 'single':
                model_path = Path(self.config.fire_smoke_detection.get('single_model_path', ''))
                if not model_path.exists():
                    errors.append(f"火焰烟雾检测单模型文件不存在: {model_path}")
            
            elif detection_mode == 'dual':
                fire_model_path = Path(self.config.fire_smoke_detection.get('fire_model_path', ''))
                smoke_model_path = Path(self.config.fire_smoke_detection.get('smoke_model_path', ''))
                
                if not fire_model_path.exists():
                    errors.append(f"火焰检测模型文件不存在: {fire_model_path}")
                
                if not smoke_model_path.exists():
                    errors.append(f"烟雾检测模型文件不存在: {smoke_model_path}")
        
        return len(errors) == 0, errors
    
    def get_model_paths(self) -> Dict[str, str]:
        """获取所有模型路径"""
        paths = {}
        
        if self.config.human_detection.enabled:
            paths['human_detection'] = self.config.human_detection.model_path
        
        if self.config.fire_smoke_detection.get('enabled', True):
            detection_mode = self.config.fire_smoke_detection.get('detection_mode', 'single')
            
            if detection_mode == 'single':
                paths['fire_smoke_single'] = self.config.fire_smoke_detection.get('single_model_path', '')
            else:
                paths['fire_detection'] = self.config.fire_smoke_detection.get('fire_model_path', '')
                paths['smoke_detection'] = self.config.fire_smoke_detection.get('smoke_model_path', '')
        
        return paths
    
    def print_config_summary(self):
        """打印配置摘要"""
        print("\n" + "="*50)
        print("AI工作线程配置摘要")
        print("="*50)
        
        print(f"队列大小: {self.config.max_queue_size}")
        print(f"处理超时: {self.config.processing_timeout}秒")
        print(f"统计更新间隔: {self.config.statistics_update_interval}")
        print(f"性能监控: {'启用' if self.config.enable_performance_monitoring else '禁用'}")
        print(f"日志级别: {self.config.log_level}")
        
        print("\n检测器配置:")
        print(f"  人体检测: {'启用' if self.config.human_detection.enabled else '禁用'}")
        if self.config.human_detection.enabled:
            print(f"    模型路径: {self.config.human_detection.model_path}")
            print(f"    置信度阈值: {self.config.human_detection.confidence_threshold}")
        
        print(f"  火焰烟雾检测: {'启用' if self.config.fire_smoke_detection.get('enabled', True) else '禁用'}")
        if self.config.fire_smoke_detection.get('enabled', True):
            detection_mode = self.config.fire_smoke_detection.get('detection_mode', 'single')
            print(f"    检测模式: {detection_mode}")
            print(f"    置信度阈值: {self.config.fire_smoke_detection.get('confidence_threshold', 0.5)}")
        
        print(f"  热源检测: {'启用' if self.config.heat_detection.get('enabled', True) else '禁用'}")
        if self.config.heat_detection.get('enabled', True):
            print(f"    温度阈值: {self.config.heat_detection.get('temperature_threshold', 35.0)}°C")
            print(f"    自适应阈值: {'启用' if self.config.heat_detection.get('adaptive_threshold', True) else '禁用'}")
        
        print("="*50)


# 默认配置实例
default_ai_worker_config = AIWorkerConfig()


def get_default_config() -> AIWorkerConfig:
    """获取默认配置"""
    return default_ai_worker_config


def create_config_manager(config_file: Optional[str] = None) -> AIWorkerConfigManager:
    """创建配置管理器"""
    return AIWorkerConfigManager(config_file)
