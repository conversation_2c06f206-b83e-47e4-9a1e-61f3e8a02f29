#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI工作线程工具模块
提供AI工作线程相关的工具函数和辅助类
"""

import cv2
import numpy as np
import time
import threading
from typing import Dict, List, Optional, Any, Tuple, Callable
from dataclasses import dataclass
from collections import deque
from PyQt5.QtCore import QObject, pyqtSignal

from utils.logger import get_logger


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, window_size: int = 100):
        """
        初始化性能监控器
        
        Args:
            window_size: 滑动窗口大小
        """
        self.window_size = window_size
        self.processing_times = deque(maxlen=window_size)
        self.fps_history = deque(maxlen=window_size)
        self.memory_usage = deque(maxlen=window_size)
        
        self.last_update_time = time.time()
        self.total_frames = 0
        
        self.logger = get_logger("PerformanceMonitor")
    
    def record_processing_time(self, processing_time: float):
        """记录处理时间"""
        self.processing_times.append(processing_time)
        self.total_frames += 1
        
        # 计算FPS
        current_time = time.time()
        if current_time - self.last_update_time > 0:
            fps = 1.0 / (current_time - self.last_update_time)
            self.fps_history.append(fps)
        
        self.last_update_time = current_time
    
    def record_memory_usage(self, memory_mb: float):
        """记录内存使用量"""
        self.memory_usage.append(memory_mb)
    
    def get_average_processing_time(self) -> float:
        """获取平均处理时间"""
        if not self.processing_times:
            return 0.0
        return sum(self.processing_times) / len(self.processing_times)
    
    def get_average_fps(self) -> float:
        """获取平均FPS"""
        if not self.fps_history:
            return 0.0
        return sum(self.fps_history) / len(self.fps_history)
    
    def get_current_fps(self) -> float:
        """获取当前FPS"""
        if not self.fps_history:
            return 0.0
        return self.fps_history[-1]
    
    def get_memory_usage(self) -> Dict[str, float]:
        """获取内存使用统计"""
        if not self.memory_usage:
            return {'current': 0.0, 'average': 0.0, 'max': 0.0}
        
        return {
            'current': self.memory_usage[-1],
            'average': sum(self.memory_usage) / len(self.memory_usage),
            'max': max(self.memory_usage)
        }
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取完整统计信息"""
        return {
            'total_frames': self.total_frames,
            'average_processing_time': self.get_average_processing_time(),
            'average_fps': self.get_average_fps(),
            'current_fps': self.get_current_fps(),
            'memory_usage': self.get_memory_usage(),
            'window_size': len(self.processing_times)
        }


class TaskScheduler:
    """任务调度器"""
    
    def __init__(self):
        """初始化任务调度器"""
        self.task_priorities = {
            'heat_detection': 1,      # 最高优先级
            'fire_smoke_detection': 2,
            'human_detection': 3      # 最低优先级
        }
        
        self.task_queues = {
            'heat_detection': deque(),
            'fire_smoke_detection': deque(),
            'human_detection': deque()
        }
        
        self.lock = threading.Lock()
        self.logger = get_logger("TaskScheduler")
    
    def add_task(self, task_type: str, task_data: Any) -> bool:
        """
        添加任务
        
        Args:
            task_type: 任务类型
            task_data: 任务数据
            
        Returns:
            是否成功添加
        """
        if task_type not in self.task_queues:
            self.logger.error(f"未知的任务类型: {task_type}")
            return False
        
        with self.lock:
            self.task_queues[task_type].append(task_data)
            return True
    
    def get_next_task(self) -> Tuple[Optional[str], Optional[Any]]:
        """
        获取下一个任务（按优先级）
        
        Returns:
            (任务类型, 任务数据)
        """
        with self.lock:
            # 按优先级顺序检查队列
            for task_type in sorted(self.task_priorities.keys(), 
                                  key=lambda x: self.task_priorities[x]):
                if self.task_queues[task_type]:
                    task_data = self.task_queues[task_type].popleft()
                    return task_type, task_data
            
            return None, None
    
    def get_queue_sizes(self) -> Dict[str, int]:
        """获取各队列大小"""
        with self.lock:
            return {task_type: len(queue) 
                   for task_type, queue in self.task_queues.items()}
    
    def clear_queues(self):
        """清空所有队列"""
        with self.lock:
            for queue in self.task_queues.values():
                queue.clear()


class ResultCache:
    """结果缓存器"""
    
    def __init__(self, max_size: int = 1000, ttl: float = 60.0):
        """
        初始化结果缓存器
        
        Args:
            max_size: 最大缓存大小
            ttl: 生存时间（秒）
        """
        self.max_size = max_size
        self.ttl = ttl
        self.cache = {}
        self.timestamps = {}
        self.lock = threading.Lock()
        
        self.logger = get_logger("ResultCache")
    
    def put(self, key: str, value: Any):
        """存储结果"""
        with self.lock:
            current_time = time.time()
            
            # 清理过期项
            self._cleanup_expired(current_time)
            
            # 如果缓存已满，移除最旧的项
            if len(self.cache) >= self.max_size:
                oldest_key = min(self.timestamps.keys(), 
                               key=lambda k: self.timestamps[k])
                del self.cache[oldest_key]
                del self.timestamps[oldest_key]
            
            # 添加新项
            self.cache[key] = value
            self.timestamps[key] = current_time
    
    def get(self, key: str) -> Optional[Any]:
        """获取结果"""
        with self.lock:
            current_time = time.time()
            
            if key not in self.cache:
                return None
            
            # 检查是否过期
            if current_time - self.timestamps[key] > self.ttl:
                del self.cache[key]
                del self.timestamps[key]
                return None
            
            return self.cache[key]
    
    def _cleanup_expired(self, current_time: float):
        """清理过期项"""
        expired_keys = [key for key, timestamp in self.timestamps.items()
                       if current_time - timestamp > self.ttl]
        
        for key in expired_keys:
            del self.cache[key]
            del self.timestamps[key]
    
    def clear(self):
        """清空缓存"""
        with self.lock:
            self.cache.clear()
            self.timestamps.clear()
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self.lock:
            return {
                'size': len(self.cache),
                'max_size': self.max_size,
                'hit_rate': 0.0,  # 需要额外跟踪命中率
                'ttl': self.ttl
            }


class FrameBuffer:
    """帧缓冲器"""
    
    def __init__(self, max_frames: int = 10):
        """
        初始化帧缓冲器
        
        Args:
            max_frames: 最大缓冲帧数
        """
        self.max_frames = max_frames
        self.frames = deque(maxlen=max_frames)
        self.timestamps = deque(maxlen=max_frames)
        self.lock = threading.Lock()
        
        self.logger = get_logger("FrameBuffer")
    
    def add_frame(self, frame: np.ndarray, timestamp: Optional[float] = None):
        """添加帧"""
        if timestamp is None:
            timestamp = time.time()
        
        with self.lock:
            self.frames.append(frame.copy())
            self.timestamps.append(timestamp)
    
    def get_latest_frame(self) -> Tuple[Optional[np.ndarray], Optional[float]]:
        """获取最新帧"""
        with self.lock:
            if not self.frames:
                return None, None
            return self.frames[-1].copy(), self.timestamps[-1]
    
    def get_frame_by_index(self, index: int) -> Tuple[Optional[np.ndarray], Optional[float]]:
        """根据索引获取帧"""
        with self.lock:
            if index < 0 or index >= len(self.frames):
                return None, None
            return self.frames[index].copy(), self.timestamps[index]
    
    def get_frame_count(self) -> int:
        """获取缓冲帧数"""
        with self.lock:
            return len(self.frames)
    
    def clear(self):
        """清空缓冲"""
        with self.lock:
            self.frames.clear()
            self.timestamps.clear()


class AIWorkerSignalManager(QObject):
    """AI工作线程信号管理器"""
    
    # 定义信号
    task_completed = pyqtSignal(str, dict)  # task_id, result
    task_failed = pyqtSignal(str, str)      # task_id, error_message
    statistics_updated = pyqtSignal(dict)   # statistics
    performance_updated = pyqtSignal(dict)  # performance_data
    
    def __init__(self, parent=None):
        """初始化信号管理器"""
        super().__init__(parent)
        self.logger = get_logger("AIWorkerSignalManager")
    
    def emit_task_completed(self, task_id: str, result: Dict[str, Any]):
        """发送任务完成信号"""
        self.task_completed.emit(task_id, result)
    
    def emit_task_failed(self, task_id: str, error_message: str):
        """发送任务失败信号"""
        self.task_failed.emit(task_id, error_message)
    
    def emit_statistics_updated(self, statistics: Dict[str, Any]):
        """发送统计信息更新信号"""
        self.statistics_updated.emit(statistics)
    
    def emit_performance_updated(self, performance_data: Dict[str, Any]):
        """发送性能数据更新信号"""
        self.performance_updated.emit(performance_data)


def create_task_id(task_type: str, timestamp: Optional[float] = None) -> str:
    """
    创建任务ID
    
    Args:
        task_type: 任务类型
        timestamp: 时间戳
        
    Returns:
        任务ID
    """
    if timestamp is None:
        timestamp = time.time()
    
    return f"{task_type}_{int(timestamp * 1000)}"


def validate_frame(frame: np.ndarray) -> bool:
    """
    验证帧数据有效性
    
    Args:
        frame: 输入帧
        
    Returns:
        是否有效
    """
    if frame is None:
        return False
    
    if not isinstance(frame, np.ndarray):
        return False
    
    if len(frame.shape) != 3:
        return False
    
    if frame.shape[2] != 3:
        return False
    
    if frame.size == 0:
        return False
    
    return True


def resize_frame_if_needed(frame: np.ndarray, 
                          max_size: Tuple[int, int] = (1920, 1080)) -> np.ndarray:
    """
    如果需要，调整帧大小
    
    Args:
        frame: 输入帧
        max_size: 最大尺寸 (width, height)
        
    Returns:
        调整后的帧
    """
    h, w = frame.shape[:2]
    max_w, max_h = max_size
    
    if w <= max_w and h <= max_h:
        return frame
    
    # 计算缩放比例
    scale = min(max_w / w, max_h / h)
    new_w = int(w * scale)
    new_h = int(h * scale)
    
    return cv2.resize(frame, (new_w, new_h), interpolation=cv2.INTER_AREA)


def get_memory_usage() -> float:
    """
    获取当前内存使用量（MB）
    
    Returns:
        内存使用量
    """
    try:
        import psutil
        process = psutil.Process()
        return process.memory_info().rss / 1024 / 1024
    except ImportError:
        return 0.0
    except Exception:
        return 0.0


def format_processing_time(processing_time: float) -> str:
    """
    格式化处理时间
    
    Args:
        processing_time: 处理时间（秒）
        
    Returns:
        格式化的时间字符串
    """
    if processing_time < 0.001:
        return f"{processing_time * 1000000:.1f}μs"
    elif processing_time < 1.0:
        return f"{processing_time * 1000:.1f}ms"
    else:
        return f"{processing_time:.2f}s"


def create_performance_monitor(window_size: int = 100) -> PerformanceMonitor:
    """创建性能监控器"""
    return PerformanceMonitor(window_size)


def create_task_scheduler() -> TaskScheduler:
    """创建任务调度器"""
    return TaskScheduler()


def create_result_cache(max_size: int = 1000, ttl: float = 60.0) -> ResultCache:
    """创建结果缓存器"""
    return ResultCache(max_size, ttl)


def create_frame_buffer(max_frames: int = 10) -> FrameBuffer:
    """创建帧缓冲器"""
    return FrameBuffer(max_frames)
