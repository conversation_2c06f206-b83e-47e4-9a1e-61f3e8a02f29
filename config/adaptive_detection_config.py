#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自适应检测配置
基于测试验证的最佳参数配置
"""

from dataclasses import dataclass
from typing import Dict, Any


@dataclass
class AdaptiveDetectionConfig:
    """自适应检测配置类"""
    
    # 基础阈值配置
    fire_confidence_threshold: float = 0.1      # 火焰置信度阈值（更宽松）
    smoke_confidence_threshold: float = 0.5     # 烟雾置信度阈值（更严格）
    iou_threshold: float = 0.45                 # IoU阈值
    
    # 火焰增强配置
    enable_fire_enhancement: bool = True        # 启用火焰增强检测
    fire_color_boost: bool = True              # 启用颜色增强
    color_enhancement_threshold: float = 0.7    # 颜色增强触发阈值
    fire_color_score_weight: float = 0.7       # 颜色特征权重
    brightness_weight: float = 0.3             # 亮度权重
    min_fire_color_score: float = 0.3          # 最小火焰颜色分数
    
    # 性能配置
    max_detections_per_frame: int = 50         # 每帧最大检测数
    enable_nms: bool = True                    # 启用非极大值抑制
    
    # 日志配置
    log_enhanced_detections: bool = True       # 记录增强检测
    log_statistics_interval: int = 100         # 统计日志间隔（帧数）
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'fire_confidence_threshold': self.fire_confidence_threshold,
            'smoke_confidence_threshold': self.smoke_confidence_threshold,
            'iou_threshold': self.iou_threshold,
            'enable_fire_enhancement': self.enable_fire_enhancement,
            'fire_color_boost': self.fire_color_boost,
            'color_enhancement_threshold': self.color_enhancement_threshold,
            'fire_color_score_weight': self.fire_color_score_weight,
            'brightness_weight': self.brightness_weight,
            'min_fire_color_score': self.min_fire_color_score,
            'max_detections_per_frame': self.max_detections_per_frame,
            'enable_nms': self.enable_nms,
            'log_enhanced_detections': self.log_enhanced_detections,
            'log_statistics_interval': self.log_statistics_interval
        }
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'AdaptiveDetectionConfig':
        """从字典创建配置"""
        return cls(**config_dict)


# 预定义配置方案
class AdaptiveDetectionProfiles:
    """自适应检测配置方案"""
    
    @staticmethod
    def get_conservative_config() -> AdaptiveDetectionConfig:
        """保守配置 - 减少误检，可能漏检一些火焰"""
        return AdaptiveDetectionConfig(
            fire_confidence_threshold=0.2,
            smoke_confidence_threshold=0.6,
            color_enhancement_threshold=0.8,
            min_fire_color_score=0.4
        )
    
    @staticmethod
    def get_balanced_config() -> AdaptiveDetectionConfig:
        """平衡配置 - 测试验证的最佳配置"""
        return AdaptiveDetectionConfig(
            fire_confidence_threshold=0.1,
            smoke_confidence_threshold=0.5,
            color_enhancement_threshold=0.7,
            min_fire_color_score=0.3
        )
    
    @staticmethod
    def get_sensitive_config() -> AdaptiveDetectionConfig:
        """敏感配置 - 最大化检测，可能有更多误检"""
        return AdaptiveDetectionConfig(
            fire_confidence_threshold=0.05,
            smoke_confidence_threshold=0.3,
            color_enhancement_threshold=0.6,
            min_fire_color_score=0.2
        )
    
    @staticmethod
    def get_production_config() -> AdaptiveDetectionConfig:
        """生产环境配置 - 基于测试结果的推荐配置"""
        return AdaptiveDetectionConfig(
            fire_confidence_threshold=0.1,      # 测试验证的最佳火焰阈值
            smoke_confidence_threshold=0.5,     # 测试验证的最佳烟雾阈值
            color_enhancement_threshold=0.7,    # 颜色增强阈值
            fire_color_score_weight=0.7,        # 颜色权重
            brightness_weight=0.3,              # 亮度权重
            min_fire_color_score=0.3,           # 最小火焰颜色分数
            enable_fire_enhancement=True,       # 启用增强（贡献54.5%火焰检测）
            fire_color_boost=True,              # 启用颜色增强
            log_enhanced_detections=True,       # 记录增强检测
            log_statistics_interval=100         # 每100帧记录统计
        )


# 默认配置
DEFAULT_ADAPTIVE_CONFIG = AdaptiveDetectionProfiles.get_production_config()


def load_adaptive_config(profile: str = "production") -> AdaptiveDetectionConfig:
    """
    加载自适应检测配置
    
    Args:
        profile: 配置方案名称 ("conservative", "balanced", "sensitive", "production")
        
    Returns:
        AdaptiveDetectionConfig: 配置对象
    """
    profiles = {
        "conservative": AdaptiveDetectionProfiles.get_conservative_config,
        "balanced": AdaptiveDetectionProfiles.get_balanced_config,
        "sensitive": AdaptiveDetectionProfiles.get_sensitive_config,
        "production": AdaptiveDetectionProfiles.get_production_config
    }
    
    if profile in profiles:
        return profiles[profile]()
    else:
        print(f"⚠️ 未知配置方案: {profile}，使用默认生产配置")
        return AdaptiveDetectionProfiles.get_production_config()


def print_config_comparison():
    """打印配置方案对比"""
    configs = {
        "保守配置": AdaptiveDetectionProfiles.get_conservative_config(),
        "平衡配置": AdaptiveDetectionProfiles.get_balanced_config(),
        "敏感配置": AdaptiveDetectionProfiles.get_sensitive_config(),
        "生产配置": AdaptiveDetectionProfiles.get_production_config()
    }
    
    print("🔧 自适应检测配置方案对比")
    print("=" * 80)
    print(f"{'配置方案':<12} {'火焰阈值':<8} {'烟雾阈值':<8} {'增强阈值':<8} {'颜色分数':<8} {'特点'}")
    print("-" * 80)
    
    for name, config in configs.items():
        features = []
        if config.enable_fire_enhancement:
            features.append("增强检测")
        if config.fire_color_boost:
            features.append("颜色增强")
        
        print(f"{name:<12} {config.fire_confidence_threshold:<8.2f} "
              f"{config.smoke_confidence_threshold:<8.2f} "
              f"{config.color_enhancement_threshold:<8.2f} "
              f"{config.min_fire_color_score:<8.2f} "
              f"{', '.join(features)}")
    
    print("=" * 80)
    print("💡 推荐:")
    print("  - 生产环境: 使用'生产配置'（基于测试验证）")
    print("  - 测试环境: 使用'敏感配置'（最大化检测）")
    print("  - 误检较多: 使用'保守配置'（减少误检）")


if __name__ == "__main__":
    print_config_comparison()
