"""
预警系统配置文件
定义四级预警的阈值和参数
"""

from config.alert_levels import AlertLevel


class AlertConfig:
    """预警配置类"""
    
    # 默认预警阈值配置
    DEFAULT_THRESHOLDS = {
        # 一级预警：检测到超过阈值的热源（热成像画面）
        AlertLevel.LEVEL_1: {
            'fire_count_min': 0,        # 最少火焰数量（不要求火焰）
            'smoke_count_min': 0,       # 最少烟雾数量（不要求烟雾）
            'heat_source_count_min': 1, # 最少热源数量（核心条件）
            'heat_source_temp_min': 35.0, # 热源最低温度阈值（核心条件）
            'total_area_min': 0,        # 最小总面积（不限制）
            'confidence_min': 0.0,      # 最小置信度（不限制，因为基于热源而非AI检测）
            'description': '检测到超过阈值的热源，温度异常'
        },
        
        # 二级预警：热源扩散（热成像画面）
        AlertLevel.LEVEL_2: {
            'fire_count_min': 0,        # 最少火焰数量（不要求火焰）
            'smoke_count_min': 0,       # 最少烟雾数量（不要求烟雾）
            'heat_source_count_min': 2, # 最少热源数量（热源增加）
            'heat_source_area_min': 300, # 热源总面积阈值（像素²）
            'heat_source_growth_rate': 1.5, # 热源面积增长率（1.5倍）
            'total_area_min': 0,        # 最小总面积（不限制）
            'confidence_min': 0.0,      # 最小置信度（不限制）
            'description': '热源数量增加或面积扩散，温度异常区域增大'
        },
        
        # 三级预警：检测到火焰
        AlertLevel.LEVEL_3: {
            'fire_count_min': 1,        # 最少火焰数量
            'smoke_count_min': 0,       # 最少烟雾数量
            'total_area_min': 200,      # 最小总面积（像素²）
            'confidence_min': 0.5,      # 最小置信度
            'description': '系统确认检测到火焰，这是严重的火灾警报'
        },
        
        # 四级预警：火焰面积增大
        AlertLevel.LEVEL_4: {
            'fire_count_min': 1,        # 最少火焰数量
            'smoke_count_min': 0,       # 最少烟雾数量
            'total_area_min': 800,      # 最小总面积（像素²）
            'area_growth_rate': 2.0,    # 火焰面积增长率（2倍）
            'confidence_min': 0.6,      # 最小置信度
            'description': '火焰面积持续增大，火势正在蔓延'
        }
    }
    
    # 预警冷却时间配置（秒）
    COOLDOWN_SETTINGS = {
        'default_cooldown': 300,        # 默认5分钟冷却
        'level_specific_cooldown': {
            AlertLevel.LEVEL_1: 300,    # 一级预警5分钟冷却
            AlertLevel.LEVEL_2: 240,    # 二级预警4分钟冷却
            AlertLevel.LEVEL_3: 180,    # 三级预警3分钟冷却
            AlertLevel.LEVEL_4: 120     # 四级预警2分钟冷却（更紧急）
        }
    }
    
    # 弹窗显示配置
    POPUP_SETTINGS = {
        'auto_close_time': 30,          # 自动关闭时间（秒）
        'stay_on_top': True,            # 保持置顶
        'modal': True,                  # 模态显示
        'center_on_screen': True,       # 居中显示
        'enable_sound': True,           # 启用声音提示
        'enable_animation': True        # 启用动画效果
    }
    
    # 预警级别优先级（数字越大优先级越高）
    ALERT_PRIORITY = {
        AlertLevel.LEVEL_1: 1,
        AlertLevel.LEVEL_2: 2,
        AlertLevel.LEVEL_3: 3,
        AlertLevel.LEVEL_4: 4
    }
    
    # 预警级别颜色配置
    ALERT_COLORS = {
        AlertLevel.LEVEL_1: {
            'primary': '#FFA500',       # 橙色
            'background': '#FFF3E0',    # 浅橙色背景
            'text': '#E65100'           # 深橙色文字
        },
        AlertLevel.LEVEL_2: {
            'primary': '#FF8C00',       # 深橙色
            'background': '#FFE0B2',    # 橙色背景
            'text': '#BF360C'           # 深橙红文字
        },
        AlertLevel.LEVEL_3: {
            'primary': '#FF4444',       # 红色
            'background': '#FFEBEE',    # 浅红色背景
            'text': '#B71C1C'           # 深红色文字
        },
        AlertLevel.LEVEL_4: {
            'primary': '#CC0000',       # 深红色
            'background': '#FFCDD2',    # 红色背景
            'text': '#8B0000'           # 暗红色文字
        }
    }
    
    # 预警消息模板
    ALERT_MESSAGES = {
        AlertLevel.LEVEL_1: {
            'title': '一级预警 - 异常热源',
            'icon': '🟡',
            'action': '记录日志，持续监控',
            'urgency': '低'
        },
        AlertLevel.LEVEL_2: {
            'title': '二级预警 - 热源扩散',
            'icon': '🟠',
            'action': '现场确认，准备应急措施',
            'urgency': '中'
        },
        AlertLevel.LEVEL_3: {
            'title': '三级预警 - 检测到火焰',
            'icon': '🔴',
            'action': '立即现场处置，启动应急预案',
            'urgency': '高'
        },
        AlertLevel.LEVEL_4: {
            'title': '四级预警 - 火势蔓延',
            'icon': '🚨',
            'action': '紧急疏散，全面灭火',
            'urgency': '紧急'
        }
    }
    
    @classmethod
    def get_threshold(cls, alert_level: str, key: str, default=None):
        """获取指定预警级别的阈值"""
        return cls.DEFAULT_THRESHOLDS.get(alert_level, {}).get(key, default)
    
    @classmethod
    def get_cooldown_time(cls, alert_level: str) -> int:
        """获取指定预警级别的冷却时间"""
        return cls.COOLDOWN_SETTINGS['level_specific_cooldown'].get(
            alert_level, 
            cls.COOLDOWN_SETTINGS['default_cooldown']
        )
    
    @classmethod
    def get_alert_priority(cls, alert_level: str) -> int:
        """获取预警级别优先级"""
        return cls.ALERT_PRIORITY.get(alert_level, 0)
    
    @classmethod
    def get_alert_color(cls, alert_level: str, color_type: str = 'primary') -> str:
        """获取预警级别颜色"""
        return cls.ALERT_COLORS.get(alert_level, {}).get(color_type, '#808080')
    
    @classmethod
    def get_alert_message(cls, alert_level: str) -> dict:
        """获取预警消息配置"""
        return cls.ALERT_MESSAGES.get(alert_level, {
            'title': '未知预警',
            'icon': '❓',
            'action': '请检查系统配置',
            'urgency': '未知'
        })
    
    @classmethod
    def validate_thresholds(cls, thresholds: dict) -> bool:
        """验证阈值配置是否有效"""
        required_keys = ['fire_count_min', 'smoke_count_min', 'total_area_min', 'confidence_min']
        
        for level, config in thresholds.items():
            if not isinstance(config, dict):
                return False
            
            for key in required_keys:
                if key not in config:
                    return False
                
                value = config[key]
                if not isinstance(value, (int, float)) or value < 0:
                    return False
        
        return True
    
    @classmethod
    def get_default_config(cls) -> dict:
        """获取默认配置"""
        return {
            'thresholds': cls.DEFAULT_THRESHOLDS.copy(),
            'cooldown': cls.COOLDOWN_SETTINGS.copy(),
            'popup': cls.POPUP_SETTINGS.copy(),
            'colors': cls.ALERT_COLORS.copy(),
            'messages': cls.ALERT_MESSAGES.copy()
        }


# 全局配置实例
alert_config = AlertConfig()
