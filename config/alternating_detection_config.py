#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交替检测配置模块
用于配置人体检测、火焰检测、烟雾检测的交替调度策略
"""

from dataclasses import dataclass
from typing import Dict, Any


@dataclass
class AlternatingDetectionConfig:
    """交替检测配置类"""
    
    # 基础配置
    enabled: bool = True  # 是否启用交替检测
    detection_interval: int = 2  # 检测间隔（帧数）
    
    # 检测策略
    strategy: str = "round_robin"  # 检测策略：round_robin（轮询）、priority（优先级）
    
    # 轮询策略配置
    round_robin_sequence: list = None  # 检测序列，默认为 ["human", "fire", "smoke", "reserved"]
    
    # 优先级策略配置
    priority_weights: Dict[str, float] = None  # 检测优先级权重
    
    # 性能优化
    cache_results: bool = True  # 是否缓存检测结果
    cache_duration: int = 5  # 缓存持续时间（帧数）
    
    # 调试配置
    debug_enabled: bool = False  # 是否启用调试输出（默认关闭，保持后台简洁）
    show_detection_type: bool = True  # 是否显示当前检测类型
    
    def __post_init__(self):
        """初始化后处理"""
        if self.round_robin_sequence is None:
            self.round_robin_sequence = ["human", "fire", "smoke", "reserved"]

        if self.priority_weights is None:
            self.priority_weights = {
                "human": 1.0,      # 人体检测优先级
                "fire": 1.5,       # 火焰检测优先级
                "smoke": 1.3,      # 烟雾检测优先级
                "reserved": 0.1    # 预留帧优先级（最低）
            }


def get_alternating_detection_config() -> AlternatingDetectionConfig:
    """获取交替检测配置"""
    return AlternatingDetectionConfig()


def get_performance_optimized_config() -> AlternatingDetectionConfig:
    """获取性能优化的交替检测配置"""
    return AlternatingDetectionConfig(
        enabled=True,
        detection_interval=3,  # 更大的间隔
        strategy="round_robin",
        cache_duration=8,  # 更长的缓存时间
        debug_enabled=False
    )


def get_accuracy_optimized_config() -> AlternatingDetectionConfig:
    """获取精度优化的交替检测配置"""
    return AlternatingDetectionConfig(
        enabled=True,
        detection_interval=1,  # 更小的间隔
        strategy="priority",
        cache_duration=3,  # 更短的缓存时间
        debug_enabled=True
    )


def get_fire_priority_config() -> AlternatingDetectionConfig:
    """获取火焰优先的交替检测配置"""
    return AlternatingDetectionConfig(
        enabled=True,
        detection_interval=2,
        strategy="priority",
        priority_weights={
            "human": 0.8,
            "fire": 2.0,       # 火焰检测优先级最高
            "smoke": 1.5,      # 烟雾检测优先级较高
            "reserved": 0.1    # 预留帧优先级最低
        },
        debug_enabled=True
    )

def get_four_frame_cycle_config() -> AlternatingDetectionConfig:
    """获取4帧循环检测配置"""
    return AlternatingDetectionConfig(
        enabled=True,
        detection_interval=1,  # 每帧切换
        strategy="round_robin",
        round_robin_sequence=["human", "fire", "smoke", "reserved"],
        cache_duration=4,  # 缓存4帧
        debug_enabled=False
    )


# 预设配置字典
PRESET_CONFIGS = {
    "default": get_alternating_detection_config,
    "performance": get_performance_optimized_config,
    "accuracy": get_accuracy_optimized_config,
    "fire_priority": get_fire_priority_config,
    "four_frame_cycle": get_four_frame_cycle_config
}


def get_config_by_preset(preset_name: str) -> AlternatingDetectionConfig:
    """根据预设名称获取配置"""
    if preset_name in PRESET_CONFIGS:
        return PRESET_CONFIGS[preset_name]()
    else:
        print(f"⚠️ 未知的预设配置: {preset_name}，使用默认配置")
        return get_alternating_detection_config()


def print_config_info(config: AlternatingDetectionConfig):
    """打印配置信息"""
    print("🔄 交替检测配置信息:")
    print(f"   启用状态: {config.enabled}")
    print(f"   检测间隔: {config.detection_interval}帧")
    print(f"   检测策略: {config.strategy}")
    
    if config.strategy == "round_robin":
        print(f"   轮询序列: {config.round_robin_sequence}")
    elif config.strategy == "priority":
        print(f"   优先级权重: {config.priority_weights}")
    
    print(f"   缓存结果: {config.cache_results}")
    print(f"   缓存时长: {config.cache_duration}帧")
    print(f"   调试模式: {config.debug_enabled}")


if __name__ == "__main__":
    # 测试配置
    print("=== 交替检测配置测试 ===")
    
    configs = [
        ("默认配置", get_alternating_detection_config()),
        ("性能优化", get_performance_optimized_config()),
        ("精度优化", get_accuracy_optimized_config()),
        ("火焰优先", get_fire_priority_config())
    ]
    
    for name, config in configs:
        print(f"\n--- {name} ---")
        print_config_info(config)
