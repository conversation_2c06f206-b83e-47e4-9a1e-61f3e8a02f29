#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
摄像头配置模块
集中管理所有摄像头相关的配置参数
"""

import os
from dataclasses import dataclass
from typing import Optional

@dataclass
class CameraConfig:
    """摄像头配置类"""
    
    # 基础连接参数
    ip: str = '************'
    username: str = 'admin'
    password: str = 'dxwx12345'
    port: int = 80          # HTTP网页端口
    sdk_port: int = 8000    # SDK设备服务端口
    
    # RTSP流地址
    visible_channel: int = 102  # 可见光子码流通道
    thermal_channel: int = 202  # 红外子码流通道
    
    # ISAPI温度接口
    thermal_api_channel: int = 2  # 温度API通道

    
    @property
    def visible_url(self) -> str:
        """可见光RTSP流地址"""
        return f'rtsp://{self.username}:{self.password}@{self.ip}:554/Streaming/Channels/{self.visible_channel}'
    
    @property
    def thermal_url(self) -> str:
        """红外RTSP流地址"""
        return f'rtsp://{self.username}:{self.password}@{self.ip}:554/Streaming/Channels/{self.thermal_channel}'
    
    @property
    def base_url(self) -> str:
        """基础HTTP URL"""
        return f"http://{self.ip}:{self.port}"
    
    @property
    def thermal_api_url(self) -> str:
        """温度API URL"""
        return f'{self.base_url}/ISAPI/Thermal/channels/{self.thermal_api_channel}/thermometry/jpegPicWithAppendData?format=json'

@dataclass
class VideoConfig:
    """视频配置类"""
    
    # 显示参数
    display_width: int = 800
    display_height: int = 600
    
    # 低延迟参数
    buffer_size: int = 1
    max_queue_size: int = 2
    
    # FPS监控
    fps_reset_interval: int = 250  # 每250帧重置一次FPS计数

@dataclass
class UIConfig:
    """用户界面配置类"""
    
    # 窗口设置
    window_name: str = 'Dual Camera - Visible & Thermal'
    
    # 文字显示
    font_face: int = 0  # cv2.FONT_HERSHEY_SIMPLEX
    font_scale: float = 0.7
    font_thickness: int = 2
    
    # 颜色设置 (BGR格式)
    visible_label_color: tuple = (0, 255, 0)    # 绿色
    thermal_label_color: tuple = (0, 255, 255)  # 黄色
    text_color: tuple = (255, 255, 255)         # 白色
    crosshair_color: tuple = (255, 255, 255)    # 白色
    temp_bg_color: tuple = (0, 0, 0)            # 黑色背景
    
    # 标签位置
    label_offset_x: int = 180  # 标签距离右边的距离
    label_offset_y: int = 30   # 标签距离顶部的距离
    
    # 温度显示
    temp_font_scale: float = 0.6
    temp_offset_x: int = 15
    temp_offset_y: int = 10

    # 热源检测标签样式
    heat_source_label_text: str = "Heat Source"
    heat_source_font_face: int = 0  # cv2.FONT_HERSHEY_SIMPLEX
    heat_source_font_scale: float = 0.5
    heat_source_font_thickness: int = 1
    heat_source_text_color: tuple = (255, 255, 255)  # 白色文字
    heat_source_bg_color: tuple = (0, 0, 255)        # 红色背景
    heat_source_label_offset: int = 5  # 标签距离边界框的偏移

def get_camera_config() -> CameraConfig:
    """获取摄像头配置"""
    config = CameraConfig()
    
    # 从环境变量读取配置（如果存在）
    config.ip = os.getenv('CAMERA_IP', config.ip)
    config.username = os.getenv('CAMERA_USERNAME', config.username)
    config.password = os.getenv('CAMERA_PASSWORD', config.password)
    
    return config

def get_video_config() -> VideoConfig:
    """获取视频配置"""
    return VideoConfig()

def get_ui_config() -> UIConfig:
    """获取UI配置"""
    return UIConfig()

# 全局配置实例
CAMERA_CONFIG = get_camera_config()
VIDEO_CONFIG = get_video_config()
UI_CONFIG = get_ui_config()
