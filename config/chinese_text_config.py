#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中文文字配置
定义所有英文文字到中文的映射
"""

# 界面标签文字映射
INTERFACE_TEXT = {
    # 视频标签
    "Visible Light": "可见光",
    "Thermal Infrared": "红外热像",
    
    # 系统状态
    "System Status": "系统状态",
    "Help & Usage": "帮助说明",
    "Press 'h' for Help": "按 'h' 查看帮助",
    "Press 'h' for Status": "按 'h' 查看状态",
    
    # 时间和性能
    "Time:": "时间：",
    "FPS:": "帧率：",
    "Temp:": "温度：",
    "Mouse:": "鼠标：",
    
    # 检测相关
    "Detection": "检测",
    "Threshold:": "阈值：",
    "Heat Sources:": "热源数量：",
    "Sources": "热源",
    "Count:": "数量：",
    "Max Temp:": "最高温度：",
    "Area:": "面积：",
    "Position:": "位置：",
    
    # 控制按钮
    "Start": "开始",
    "Stop": "停止",
    "Reset": "重置",
    "Settings": "设置",
    "Exit": "退出",
    "Save": "保存",
    "Load": "加载",
    
    # 模式切换
    "Mode:": "模式：",
    "Auto": "自动",
    "Manual": "手动",
    "Fixed": "固定",
    "Adaptive": "自适应",
    
    # 状态信息
    "Connected": "已连接",
    "Disconnected": "已断开",
    "Running": "运行中",
    "Stopped": "已停止",
    "Error": "错误",
    "Ready": "就绪",
    
    # 热源检测
    "Heat Source": "热源",
    "Temperature": "温度",
    "Celsius": "摄氏度",
    "Range": "范围",
    "Min": "最小值",
    "Max": "最大值",
    "Mean": "平均值",
    "Std": "标准差",
    "Suggest": "建议",
    "Current": "当前",
    "Detections": "检测数",
    
    # 工具栏
    "Toolbar": "工具栏",
    "Tools": "工具",
    "Options": "选项",
    "View": "视图",
    
    # 侧边栏
    "Sidebar": "侧边栏",
    "Status": "状态",
    "Help": "帮助",
    "Info": "信息",
    
    # 快捷键说明
    "Quick Keys": "快捷键",
    "h - Help/Status": "h - 帮助/状态",
    "d - Detection": "d - 检测开关",
    "t - Sidebar": "t - 侧边栏",
    "↑↓ - Scroll Sources": "↑↓ - 滚动热源",
    "q - Quit": "q - 退出",
    
    # 系统信息
    "System Info:": "系统信息：",
    "Camera:": "摄像头：",
    "Resolution:": "分辨率：",
    "Format:": "格式：",
    "Status:": "状态：",
    
    # 错误信息
    "Connection Failed": "连接失败",
    "Camera Error": "摄像头错误",
    "Network Error": "网络错误",
    "Timeout": "超时",
    "Invalid Data": "无效数据",
    
    # 单位
    "°C": "°C",
    "px": "像素",
    "ms": "毫秒",
    "fps": "帧/秒",
    
    # 区域标识
    "Area: Thermal (with temperature)": "区域：红外（显示温度）",
    "Area: Visible Light": "区域：可见光",
    "Area: Sidebar": "区域：侧边栏",
    "In Toolbar Area": "工具栏区域",
    
    # 测试相关
    "Mouse Coordinate Test": "鼠标坐标测试",
    "Mouse Cursor Alignment Test": "鼠标光标对齐测试",
    "Original": "原始",
    "Adjusted": "调整后",
    "Window": "窗口",
    "Red crosshair should align with mouse pointer": "红色十字线应与鼠标指针重合",
    "Move mouse to different areas to test alignment": "移动鼠标到不同区域测试对齐",
    "Temp: Calculating...": "温度：计算中...",
}

# 字体大小配置
FONT_SIZES = {
    "title": 24,        # 标题
    "subtitle": 20,     # 副标题
    "normal": 16,       # 正常文字
    "small": 14,        # 小字
    "tiny": 12,         # 极小字
    "label": 18,        # 标签
    "button": 16,       # 按钮
    "status": 14,       # 状态信息
    "temperature": 18,  # 温度显示
}

# 颜色配置 (BGR格式)
TEXT_COLORS = {
    "white": (255, 255, 255),       # 白色
    "black": (0, 0, 0),             # 黑色
    "red": (0, 0, 255),             # 红色
    "green": (0, 255, 0),           # 绿色
    "blue": (255, 0, 0),            # 蓝色
    "yellow": (0, 255, 255),        # 黄色
    "cyan": (255, 255, 0),          # 青色
    "magenta": (255, 0, 255),       # 洋红
    "orange": (0, 165, 255),        # 橙色
    "gray": (128, 128, 128),        # 灰色
    "light_gray": (200, 200, 200),  # 浅灰色
    "dark_gray": (64, 64, 64),      # 深灰色
}

# 背景颜色配置
BACKGROUND_COLORS = {
    "transparent": None,            # 透明
    "black": (0, 0, 0),            # 黑色
    "white": (255, 255, 255),      # 白色
    "dark": (40, 40, 40),          # 深色
    "light": (240, 240, 240),      # 浅色
    "blue_dark": (80, 40, 40),     # 深蓝色
    "green_dark": (40, 80, 40),    # 深绿色
    "red_dark": (40, 40, 80),      # 深红色
}

def get_chinese_text(english_text: str) -> str:
    """
    获取英文文字对应的中文
    
    Args:
        english_text: 英文文字
        
    Returns:
        对应的中文文字，如果没有找到则返回原文
    """
    return INTERFACE_TEXT.get(english_text, english_text)

def get_font_size(size_type: str) -> int:
    """
    获取指定类型的字体大小
    
    Args:
        size_type: 字体大小类型
        
    Returns:
        字体大小
    """
    return FONT_SIZES.get(size_type, FONT_SIZES["normal"])

def get_text_color(color_name: str) -> tuple:
    """
    获取指定名称的文字颜色
    
    Args:
        color_name: 颜色名称
        
    Returns:
        BGR颜色元组
    """
    return TEXT_COLORS.get(color_name, TEXT_COLORS["white"])

def get_background_color(color_name: str) -> tuple:
    """
    获取指定名称的背景颜色
    
    Args:
        color_name: 颜色名称
        
    Returns:
        BGR颜色元组或None（透明）
    """
    return BACKGROUND_COLORS.get(color_name, BACKGROUND_COLORS["transparent"])

# 常用文字组合
COMMON_TEXTS = {
    "fps_format": "帧率：{:.1f}",
    "temp_format": "温度：{:.1f}°C",
    "mouse_pos_format": "鼠标：({}, {})",
    "count_format": "数量：{}",
    "area_format": "面积：{}像素",
    "time_format": "%Y-%m-%d %H:%M:%S",
}
