#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
坐标系统统一管理器
负责在显示坐标系统和传感器原始坐标系统之间进行转换
"""

from typing import Tuple, Optional, Union
from dataclasses import dataclass
from .thermal_dimension_manager import ThermalDimensionManager
from .heat_detection_config import HeatDetectionConfig


@dataclass
class CoordinateSystemConfig:
    """坐标系统配置"""
    # 传感器原始尺寸（热成像传感器的真实分辨率）
    sensor_thermal_width: int = 384
    sensor_thermal_height: int = 288
    
    # 显示尺寸（Qt UI中显示的尺寸）
    display_thermal_width: int = 640
    display_thermal_height: int = 480
    display_visible_width: int = 640
    display_visible_height: int = 480
    
    # 热成像在显示中的偏移量（双画面模式）
    thermal_display_offset_x: int = 640  # 热成像显示在右侧


class CoordinateSystemManager:
    """坐标系统统一管理器"""
    
    def __init__(self, config: Optional[CoordinateSystemConfig] = None):
        """初始化坐标系统管理器"""
        self.config = config or CoordinateSystemConfig()
        self.thermal_dimension_manager = ThermalDimensionManager(HeatDetectionConfig())
        
        # 缓存的转换比例
        self._thermal_scale_x: Optional[float] = None
        self._thermal_scale_y: Optional[float] = None
        self._visible_scale_x: Optional[float] = None
        self._visible_scale_y: Optional[float] = None
    
    def update_sensor_dimensions(self, temperature_manager=None, heat_detector=None):
        """更新传感器原始尺寸"""
        try:
            # 获取真实的传感器尺寸
            height, width = self.thermal_dimension_manager.get_original_thermal_dimensions(
                temperature_manager, heat_detector
            )
            self.config.sensor_thermal_width = width
            self.config.sensor_thermal_height = height
            
            # 清除缓存的转换比例
            self._clear_scale_cache()
            
            print(f"📏 更新传感器尺寸: {width}x{height}")
            
        except Exception as e:
            print(f"⚠️ 更新传感器尺寸失败: {e}")
    
    def update_display_dimensions(self, visible_width: int, visible_height: int, 
                                thermal_width: int, thermal_height: int,
                                thermal_offset_x: int = 0):
        """更新显示尺寸"""
        self.config.display_visible_width = visible_width
        self.config.display_visible_height = visible_height
        self.config.display_thermal_width = thermal_width
        self.config.display_thermal_height = thermal_height
        self.config.thermal_display_offset_x = thermal_offset_x
        
        # 清除缓存的转换比例
        self._clear_scale_cache()
        
        print(f"📺 更新显示尺寸: 可见光={visible_width}x{visible_height}, "
              f"热成像={thermal_width}x{thermal_height}, 偏移={thermal_offset_x}")
    
    def _clear_scale_cache(self):
        """清除缓存的转换比例"""
        self._thermal_scale_x = None
        self._thermal_scale_y = None
        self._visible_scale_x = None
        self._visible_scale_y = None
    
    def _get_thermal_scale(self) -> Tuple[float, float]:
        """获取热成像坐标转换比例"""
        if self._thermal_scale_x is None or self._thermal_scale_y is None:
            self._thermal_scale_x = self.config.sensor_thermal_width / self.config.display_thermal_width
            self._thermal_scale_y = self.config.sensor_thermal_height / self.config.display_thermal_height
        return self._thermal_scale_x, self._thermal_scale_y
    
    def _get_visible_scale(self) -> Tuple[float, float]:
        """获取可见光坐标转换比例（假设传感器和显示尺寸相同）"""
        if self._visible_scale_x is None or self._visible_scale_y is None:
            # 可见光通常显示尺寸就是传感器尺寸
            self._visible_scale_x = 1.0
            self._visible_scale_y = 1.0
        return self._visible_scale_x, self._visible_scale_y
    
    def display_to_sensor_thermal(self, display_x: int, display_y: int) -> Tuple[int, int]:
        """将显示坐标转换为热成像传感器坐标"""
        scale_x, scale_y = self._get_thermal_scale()
        
        # 转换为传感器坐标
        sensor_x = int(display_x * scale_x)
        sensor_y = int(display_y * scale_y)
        
        # 边界检查
        sensor_x = max(0, min(sensor_x, self.config.sensor_thermal_width - 1))
        sensor_y = max(0, min(sensor_y, self.config.sensor_thermal_height - 1))
        
        return sensor_x, sensor_y
    
    def sensor_to_display_thermal(self, sensor_x: int, sensor_y: int) -> Tuple[int, int]:
        """将热成像传感器坐标转换为显示坐标"""
        scale_x, scale_y = self._get_thermal_scale()
        
        # 转换为显示坐标
        display_x = int(sensor_x / scale_x)
        display_y = int(sensor_y / scale_y)
        
        # 边界检查
        display_x = max(0, min(display_x, self.config.display_thermal_width - 1))
        display_y = max(0, min(display_y, self.config.display_thermal_height - 1))
        
        return display_x, display_y
    
    def display_to_sensor_visible(self, display_x: int, display_y: int) -> Tuple[int, int]:
        """将显示坐标转换为可见光传感器坐标（通常1:1）"""
        scale_x, scale_y = self._get_visible_scale()
        
        sensor_x = int(display_x * scale_x)
        sensor_y = int(display_y * scale_y)
        
        return sensor_x, sensor_y
    
    def sensor_to_display_visible(self, sensor_x: int, sensor_y: int) -> Tuple[int, int]:
        """将可见光传感器坐标转换为显示坐标（通常1:1）"""
        scale_x, scale_y = self._get_visible_scale()
        
        display_x = int(sensor_x / scale_x)
        display_y = int(sensor_y / scale_y)
        
        return display_x, display_y
    
    def widget_to_sensor_coords(self, widget_x: int, widget_y: int) -> Tuple[Optional[Tuple[int, int]], bool]:
        """
        将Qt控件坐标转换为传感器坐标
        
        Args:
            widget_x, widget_y: Qt控件中的坐标
            
        Returns:
            (sensor_coords, is_thermal): 传感器坐标和是否为热成像区域的标志
            如果坐标无效，sensor_coords为None
        """
        # 检查是否在可见光区域
        if (0 <= widget_x < self.config.display_visible_width and 
            0 <= widget_y < self.config.display_visible_height):
            sensor_coords = self.display_to_sensor_visible(widget_x, widget_y)
            return sensor_coords, False
        
        # 检查是否在热成像区域
        thermal_start_x = self.config.thermal_display_offset_x
        thermal_end_x = thermal_start_x + self.config.display_thermal_width
        
        if (thermal_start_x <= widget_x < thermal_end_x and 
            0 <= widget_y < self.config.display_thermal_height):
            # 转换为热成像区域内的相对坐标
            thermal_x = widget_x - thermal_start_x
            thermal_y = widget_y
            sensor_coords = self.display_to_sensor_thermal(thermal_x, thermal_y)
            return sensor_coords, True
        
        # 坐标不在有效区域内
        return None, False
    
    def sensor_to_widget_coords(self, sensor_x: int, sensor_y: int, is_thermal: bool) -> Tuple[int, int]:
        """
        将传感器坐标转换为Qt控件坐标
        
        Args:
            sensor_x, sensor_y: 传感器坐标
            is_thermal: 是否为热成像坐标
            
        Returns:
            widget_coords: Qt控件坐标
        """
        if is_thermal:
            # 转换为热成像显示坐标
            display_x, display_y = self.sensor_to_display_thermal(sensor_x, sensor_y)
            # 加上热成像区域的偏移
            widget_x = display_x + self.config.thermal_display_offset_x
            widget_y = display_y
            return widget_x, widget_y
        else:
            # 可见光坐标直接转换
            return self.sensor_to_display_visible(sensor_x, sensor_y)
    
    def convert_bbox_display_to_sensor(self, bbox: Tuple[int, int, int, int], 
                                     is_thermal: bool) -> Tuple[int, int, int, int]:
        """将显示边界框转换为传感器边界框"""
        x, y, w, h = bbox
        
        if is_thermal:
            # 转换热成像边界框
            x1, y1 = self.display_to_sensor_thermal(x, y)
            x2, y2 = self.display_to_sensor_thermal(x + w, y + h)
        else:
            # 转换可见光边界框
            x1, y1 = self.display_to_sensor_visible(x, y)
            x2, y2 = self.display_to_sensor_visible(x + w, y + h)
        
        # 确保坐标顺序正确
        min_x, max_x = min(x1, x2), max(x1, x2)
        min_y, max_y = min(y1, y2), max(y1, y2)
        
        return min_x, min_y, max_x - min_x, max_y - min_y
    
    def convert_bbox_sensor_to_display(self, bbox: Tuple[int, int, int, int], 
                                     is_thermal: bool) -> Tuple[int, int, int, int]:
        """将传感器边界框转换为显示边界框"""
        x, y, w, h = bbox
        
        if is_thermal:
            # 转换热成像边界框
            x1, y1 = self.sensor_to_display_thermal(x, y)
            x2, y2 = self.sensor_to_display_thermal(x + w, y + h)
        else:
            # 转换可见光边界框
            x1, y1 = self.sensor_to_display_visible(x, y)
            x2, y2 = self.sensor_to_display_visible(x + w, y + h)
        
        # 确保坐标顺序正确
        min_x, max_x = min(x1, x2), max(x1, x2)
        min_y, max_y = min(y1, y2), max(y1, y2)
        
        return min_x, min_y, max_x - min_x, max_y - min_y
    
    def get_sensor_dimensions(self) -> Tuple[Tuple[int, int], Tuple[int, int]]:
        """获取传感器尺寸"""
        thermal_dims = (self.config.sensor_thermal_width, self.config.sensor_thermal_height)
        visible_dims = (self.config.display_visible_width, self.config.display_visible_height)
        return thermal_dims, visible_dims
    
    def get_display_dimensions(self) -> Tuple[Tuple[int, int], Tuple[int, int], int]:
        """获取显示尺寸"""
        thermal_dims = (self.config.display_thermal_width, self.config.display_thermal_height)
        visible_dims = (self.config.display_visible_width, self.config.display_visible_height)
        return thermal_dims, visible_dims, self.config.thermal_display_offset_x


# 全局坐标系统管理器实例
_coordinate_system_manager: Optional[CoordinateSystemManager] = None


def get_coordinate_system_manager() -> CoordinateSystemManager:
    """获取全局坐标系统管理器实例"""
    global _coordinate_system_manager
    if _coordinate_system_manager is None:
        _coordinate_system_manager = CoordinateSystemManager()
    return _coordinate_system_manager


def create_coordinate_system_manager(config: Optional[CoordinateSystemConfig] = None) -> CoordinateSystemManager:
    """创建新的坐标系统管理器实例"""
    return CoordinateSystemManager(config)
