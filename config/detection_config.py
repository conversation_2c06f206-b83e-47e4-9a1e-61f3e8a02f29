#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检测配置模块 - 兼容性包装器
此文件保持向后兼容性，实际功能已拆分到独立模块中
"""

# 导入拆分后的模块
from .heat_detection_config import HeatDetectionConfig, HEAT_DETECTION_CONFIG
from .human_detection_config import HumanDetectionConfig, HUMAN_DETECTION_CONFIG
from .thermal_dimension_manager import ThermalDimensionManager
from .heat_detection_renderer import HeatDetectionRenderer

# 全局配置实例（保持向后兼容）
# 这些实例从拆分后的模块中导入

# 创建渲染器实例
heat_detection_renderer = HeatDetectionRenderer(HEAT_DETECTION_CONFIG)

def get_heat_detection_renderer() -> HeatDetectionRenderer:
    """获取热源检测渲染器实例"""
    return heat_detection_renderer

def get_thermal_dimension_manager() -> ThermalDimensionManager:
    """获取热成像尺寸管理器实例"""
    return heat_detection_renderer.dimension_manager

def get_human_detection_config() -> HumanDetectionConfig:
    """获取人体检测配置实例"""
    return HUMAN_DETECTION_CONFIG