#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPU配置模块
管理系统的GPU使用设置，方便在不同环境间切换
"""

import torch
from dataclasses import dataclass
from typing import Optional

@dataclass
class GPUConfig:
    """GPU配置类"""
    
    # 基本GPU设置
    use_gpu: bool = False  # 是否使用GPU
    device_id: int = 0  # GPU设备ID（如果有多张显卡）
    auto_detect: bool = True  # 自动检测GPU可用性
    
    # 性能设置
    enable_half_precision: bool = False  # 是否启用半精度（FP16）
    enable_tensorrt: bool = False  # 是否启用TensorRT优化
    batch_size: int = 1  # 批处理大小
    
    # 内存管理
    gpu_memory_fraction: float = 0.8  # GPU内存使用比例
    allow_memory_growth: bool = True  # 允许内存动态增长
    
    def __post_init__(self):
        """初始化后处理"""
        if self.auto_detect:
            self.use_gpu = self.detect_gpu_availability()
    
    def detect_gpu_availability(self) -> bool:
        """检测GPU可用性"""
        try:
            if torch.cuda.is_available():
                gpu_count = torch.cuda.device_count()
                gpu_name = torch.cuda.get_device_name(self.device_id)
                print(f"🎮 检测到 {gpu_count} 张GPU")
                print(f"🎯 将使用GPU {self.device_id}: {gpu_name}")
                return True
            else:
                print("⚠️ 未检测到可用的GPU，将使用CPU")
                return False
        except Exception as e:
            print(f"❌ GPU检测失败: {e}")
            return False
    
    def get_device_string(self) -> str:
        """获取设备字符串"""
        if self.use_gpu and torch.cuda.is_available():
            return f"cuda:{self.device_id}"
        else:
            return "cpu"
    
    def get_device_info(self) -> dict:
        """获取设备详细信息"""
        info = {
            "device": self.get_device_string(),
            "use_gpu": self.use_gpu,
            "gpu_available": torch.cuda.is_available() if torch.cuda.is_available() else False,
        }
        
        if self.use_gpu and torch.cuda.is_available():
            info.update({
                "gpu_count": torch.cuda.device_count(),
                "gpu_name": torch.cuda.get_device_name(self.device_id),
                "gpu_memory_total": torch.cuda.get_device_properties(self.device_id).total_memory,
                "half_precision": self.enable_half_precision,
                "tensorrt": self.enable_tensorrt,
            })
        
        return info
    
    def print_device_info(self):
        """打印设备信息"""
        info = self.get_device_info()
        print("\n🎮 GPU配置信息:")
        print("=" * 40)
        print(f"设备: {info['device']}")
        print(f"使用GPU: {'✅' if info['use_gpu'] else '❌'}")
        print(f"GPU可用: {'✅' if info['gpu_available'] else '❌'}")
        
        if info['use_gpu'] and info['gpu_available']:
            print(f"GPU数量: {info['gpu_count']}")
            print(f"GPU名称: {info['gpu_name']}")
            print(f"GPU内存: {info['gpu_memory_total'] / 1024**3:.1f} GB")
            print(f"半精度: {'✅' if info['half_precision'] else '❌'}")
            print(f"TensorRT: {'✅' if info['tensorrt'] else '❌'}")


# 预定义配置
class GPUPresets:
    """GPU预设配置"""
    
    @staticmethod
    def cpu_only():
        """仅使用CPU"""
        return GPUConfig(
            use_gpu=False,
            auto_detect=False
        )
    
    @staticmethod
    def gpu_basic():
        """基础GPU配置"""
        return GPUConfig(
            use_gpu=True,
            auto_detect=True,
            enable_half_precision=False,
            enable_tensorrt=False
        )
    
    @staticmethod
    def gpu_optimized():
        """优化的GPU配置"""
        return GPUConfig(
            use_gpu=True,
            auto_detect=True,
            enable_half_precision=True,
            enable_tensorrt=False,
            gpu_memory_fraction=0.9
        )
    
    @staticmethod
    def gpu_high_performance():
        """高性能GPU配置"""
        return GPUConfig(
            use_gpu=True,
            auto_detect=True,
            enable_half_precision=True,
            enable_tensorrt=True,
            gpu_memory_fraction=0.95,
            batch_size=4
        )


# 环境配置
class EnvironmentConfig:
    """环境配置管理"""
    
    # 开发环境（通常是CPU）
    DEVELOPMENT = GPUPresets.cpu_only()
    
    # 测试环境（基础GPU）
    TESTING = GPUPresets.gpu_basic()
    
    # 生产环境（优化GPU）
    PRODUCTION = GPUPresets.gpu_optimized()
    
    # 高性能环境
    HIGH_PERFORMANCE = GPUPresets.gpu_high_performance()


# 全局GPU配置实例
# 🔧 在这里修改你的GPU配置！
# 部署到带显卡的系统时，将下面的配置改为你需要的环境
CURRENT_GPU_CONFIG = EnvironmentConfig.TESTING  # 👈 修改为基础GPU配置！

# 可选的配置：
# CURRENT_GPU_CONFIG = EnvironmentConfig.DEVELOPMENT  # 仅CPU
# CURRENT_GPU_CONFIG = EnvironmentConfig.PRODUCTION   # 优化GPU
# CURRENT_GPU_CONFIG = EnvironmentConfig.HIGH_PERFORMANCE  # 高性能GPU

def get_gpu_config() -> GPUConfig:
    """获取当前GPU配置"""
    return CURRENT_GPU_CONFIG

def set_gpu_config(config: GPUConfig):
    """设置GPU配置"""
    global CURRENT_GPU_CONFIG
    CURRENT_GPU_CONFIG = config

def apply_gpu_config_to_detection_configs():
    """将GPU配置应用到检测配置中"""
    from config.fire_detection_config import get_fire_detection_config
    from config.human_detection_config import get_human_detection_config
    
    gpu_config = get_gpu_config()
    device = gpu_config.get_device_string()
    
    # 更新火焰烟雾检测配置
    fire_config = get_fire_detection_config()
    fire_config.device = device
    fire_config.half_precision = gpu_config.enable_half_precision
    
    # 更新人体检测配置
    human_config = get_human_detection_config()
    human_config.device = device
    human_config.half_precision = gpu_config.enable_half_precision
    
    print(f"🔧 已将设备配置更新为: {device}")
    if gpu_config.enable_half_precision:
        print("⚡ 已启用半精度加速")

def print_current_config():
    """打印当前配置"""
    config = get_gpu_config()
    config.print_device_info()

if __name__ == "__main__":
    print("🎮 GPU配置测试")
    print_current_config()
    
    print("\n🔧 应用配置到检测模块...")
    apply_gpu_config_to_detection_configs()
