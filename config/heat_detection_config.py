#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
热源检测配置模块
管理热源检测相关的配置参数
"""

from dataclasses import dataclass
from .camera_config import UI_CONFIG


@dataclass
class HeatDetectionConfig:
    """热源检测配置类"""
    
    # 边界框样式
    bbox_color: tuple = (0, 0, 255)  # 红色边界框
    bbox_thickness: int = 1  # 调细边界框

    # 标签显示控制
    show_labels: bool = False  # 设置为False隐藏标签，True显示标签
    
    # 标签样式（从UI_CONFIG继承）
    @property
    def label_text(self) -> str:
        return UI_CONFIG.heat_source_label_text
    
    @property
    def font_face(self) -> int:
        return UI_CONFIG.heat_source_font_face
    
    @property
    def font_scale(self) -> float:
        return UI_CONFIG.heat_source_font_scale
    
    @property
    def font_thickness(self) -> int:
        return UI_CONFIG.heat_source_font_thickness
    
    @property
    def text_color(self) -> tuple:
        return UI_CONFIG.heat_source_text_color
    
    @property
    def bg_color(self) -> tuple:
        return UI_CONFIG.heat_source_bg_color
    
    @property
    def label_offset(self) -> int:
        return UI_CONFIG.heat_source_label_offset
    
    # 默认热成像尺寸
    default_thermal_width: int = 384
    default_thermal_height: int = 288


# 全局配置实例
HEAT_DETECTION_CONFIG = HeatDetectionConfig()


def get_heat_detection_config() -> HeatDetectionConfig:
    """获取热源检测配置实例"""
    return HEAT_DETECTION_CONFIG
