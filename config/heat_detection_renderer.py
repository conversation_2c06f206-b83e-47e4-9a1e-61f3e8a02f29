#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
热源检测渲染器模块
负责热源检测结果的渲染和显示
"""

import cv2
import numpy as np
from typing import List, Tuple
from .heat_detection_config import HeatDetectionConfig
from .thermal_dimension_manager import ThermalDimensionManager
from utils.chinese_text_renderer import draw_chinese_text


class HeatDetectionRenderer:
    """热源检测渲染器"""

    def __init__(self, config: HeatDetectionConfig):
        self.config = config
        self.dimension_manager = ThermalDimensionManager(config)
        self.heat_source_analyzer = None  # 延迟初始化

    def _get_heat_source_analyzer(self):
        """获取热源分析器（延迟初始化）"""
        if self.heat_source_analyzer is None:
            try:
                from detection.analysis.heat_source_analyzer import HeatSourceAnalyzer
                self.heat_source_analyzer = HeatSourceAnalyzer()
            except ImportError as e:
                print(f"⚠️ 无法导入热源分析器: {e}")
                self.heat_source_analyzer = None
        return self.heat_source_analyzer
    
    def apply_heat_detection(self, thermal_frame: np.ndarray, bboxes: List[Tuple[int, int, int, int]], 
                           temperature_manager=None, heat_detector=None, 
                           display_manager=None, logger=None) -> np.ndarray:
        """应用ISAPI热源检测渲染"""
        if not bboxes:
            return thermal_frame
        
        try:
            # 在RTSP视频帧上绘制检测结果
            result_frame = thermal_frame.copy()
            
            # 获取缩放比例 - 关键修复！
            original_height, original_width = self.dimension_manager.get_original_thermal_dimensions(
                temperature_manager, heat_detector
            )
            current_height, current_width = thermal_frame.shape[:2]
            
            scale_x = current_width / original_width if original_width > 0 else 1.0
            scale_y = current_height / original_height if original_height > 0 else 1.0
            
            # 获取热源详细信息用于标签显示
            heat_sources_detail = []
            analyzer = self._get_heat_source_analyzer()
            if analyzer:
                try:
                    # 获取温度矩阵
                    temp_matrix = None
                    if (temperature_manager and
                        hasattr(temperature_manager, 'get_primary_reader')):
                        reader = temperature_manager.get_primary_reader()
                        if (reader and hasattr(reader, 'current_temp_matrix') and
                            reader.current_temp_matrix is not None):
                            temp_matrix = reader.current_temp_matrix

                    # 分析最新的检测结果
                    heat_sources_detail = analyzer.analyze_latest_detection(temp_matrix)
                except:
                    pass

            for i, bbox in enumerate(bboxes):
                x, y, w, h = bbox

                # 将原始坐标缩放到当前显示尺寸
                scaled_x = int(x * scale_x)
                scaled_y = int(y * scale_y)
                scaled_w = int(w * scale_x)
                scaled_h = int(h * scale_y)

                # 边界检查
                scaled_x = max(0, min(scaled_x, current_width - 1))
                scaled_y = max(0, min(scaled_y, current_height - 1))
                scaled_w = max(1, min(scaled_w, current_width - scaled_x))
                scaled_h = max(1, min(scaled_h, current_height - scaled_y))

                # 绘制边界框
                cv2.rectangle(result_frame, (scaled_x, scaled_y),
                            (scaled_x + scaled_w, scaled_y + scaled_h),
                            self.config.bbox_color, self.config.bbox_thickness)

                # 绘制热源编号标签
                self._draw_heat_source_id_label(result_frame, scaled_x, scaled_y,
                                               heat_sources_detail, i)
            
            # 更新显示管理器的检测状态信息（如果提供）
            if display_manager and heat_detector:
                status_text = heat_detector.get_status_text() if hasattr(heat_detector, 'get_status_text') else ""
                scale_info = f"Scale: {scale_x:.2f}x{scale_y:.2f}"

                display_manager.update_detection_status(
                    enabled=True,
                    status_text=status_text,
                    sources_count=len(bboxes),
                    scale_info=scale_info
                )

                # 注释掉OpenCV系统的热源详细信息分析，避免与Qt系统重复
                # 现在由Qt系统的定时器负责热源信息的更新
                # analyzer = self._get_heat_source_analyzer()
                # if analyzer:
                #     try:
                #         # 获取温度矩阵
                #         temp_matrix = None
                #         if (temperature_manager and
                #             hasattr(temperature_manager, 'get_primary_reader')):
                #             reader = temperature_manager.get_primary_reader()
                #             if (reader and hasattr(reader, 'current_temp_matrix') and
                #                 reader.current_temp_matrix is not None):
                #                 temp_matrix = reader.current_temp_matrix
                #
                #         # 分析最新的检测结果
                #         heat_sources_detail = analyzer.analyze_latest_detection(temp_matrix)
                #
                #         # 更新侧边栏显示
                #         display_manager.update_heat_sources_detail(heat_sources_detail)
                #
                #     except Exception as e:
                #         if logger:
                #             logger.warning(f"热源详细信息分析失败: {e}")
                #         else:
                #             print(f"⚠️ 热源详细信息分析失败: {e}")
                pass  # Qt系统负责热源详情更新
            
            return result_frame
            
        except Exception as e:
            if logger:
                logger.exception(f"ISAPI热源检测渲染失败: {e}")
            else:
                print(f"❌ ISAPI热源检测渲染失败: {e}")
            return thermal_frame

    def _draw_heat_source_label(self, frame: np.ndarray, x: int, y: int):
        """绘制热源标签"""
        label = self.config.label_text
        font_scale = self.config.font_scale
        text_color = self.config.text_color
        bg_color = self.config.bg_color
        offset = self.config.label_offset

        # 计算字体大小（将font_scale转换为像素大小）
        font_size = int(font_scale * 20)

        # 标签位置
        label_y = y - offset

        try:
            # 使用中文文字渲染器绘制标签
            draw_chinese_text(
                frame, label,
                (x, label_y),
                font_size=font_size,
                color=text_color,
                background_color=bg_color,
                padding=5
            )
        except Exception as e:
            print(f"⚠️ 热源标签渲染失败，使用备用方案: {e}")
            # 备用方案：使用OpenCV绘制
            font_face = self.config.font_face
            font_thickness = self.config.font_thickness
            label_size = cv2.getTextSize(label, font_face, font_scale, font_thickness)[0]

            # 绘制标签背景
            cv2.rectangle(frame,
                         (x, y - label_size[1] - 10),
                         (x + label_size[0], y),
                         bg_color, -1)

            # 绘制标签文字
            cv2.putText(frame, label, (x, y - offset),
                       font_face, font_scale, text_color, font_thickness)

    def _draw_heat_source_id_label(self, frame: np.ndarray, x: int, y: int,
                                  heat_sources_detail: list, bbox_index: int):
        """绘制热源编号标签"""
        # 确定热源ID
        source_id = bbox_index + 1  # 默认使用索引+1

        # 如果有详细信息，使用实际的热源ID
        if heat_sources_detail and bbox_index < len(heat_sources_detail):
            source_info = heat_sources_detail[bbox_index]
            source_id = source_info.id

        # 创建标签文本（只显示编号）
        label_text = f"{source_id}"

        # 字体设置（调小字体）
        font_face = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 0.4  # 调小字体
        font_thickness = 1  # 调细字体粗细

        # 获取文字尺寸
        (text_width, text_height), _ = cv2.getTextSize(label_text, font_face, font_scale, font_thickness)

        # 计算标签位置（左上角）
        label_x = x + 2  # 稍微向右偏移
        label_y = max(y - 3, text_height + 3)  # 确保不超出边界

        # 绘制背景（使用和边界框相同的颜色）
        bg_color = self.config.bbox_color  # 使用边界框颜色作为背景
        cv2.rectangle(frame,
                     (label_x - 2, label_y - text_height - 2),
                     (label_x + text_width + 2, label_y + 2),
                     bg_color, -1)

        # 绘制文字（使用白色文字以便在彩色背景上清晰显示）
        text_color = (255, 255, 255)  # 白色文字
        cv2.putText(frame, label_text, (label_x, label_y),
                   font_face, font_scale, text_color, font_thickness)


def create_heat_detection_renderer(config: HeatDetectionConfig) -> HeatDetectionRenderer:
    """创建热源检测渲染器实例"""
    return HeatDetectionRenderer(config)
