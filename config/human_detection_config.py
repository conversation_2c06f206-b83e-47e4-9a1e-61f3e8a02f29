#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
人体检测配置模块
管理人体检测相关的配置参数
"""

from dataclasses import dataclass


@dataclass
class HumanDetectionConfig:
    """人体检测配置类"""

    # 模型配置
    model_name: str = "models/human_detection/yolov8n_human.rknn"  # 默认使用轻量级模型
    confidence_threshold: float = 0.5  # 置信度阈值
    iou_threshold: float = 0.45  # IoU阈值
    device: str = "cpu"  # 推理设备

    # 边界框样式
    bbox_color: tuple = (0, 255, 0)  # 绿色边界框
    bbox_thickness: int = 2  # 边界框粗细

    # 标签样式
    show_labels: bool = True  # 显示标签
    label_font_scale: float = 0.6
    label_font_thickness: int = 1
    label_text_color: tuple = (255, 255, 255)  # 白色文字
    label_bg_color: tuple = (0, 255, 0)  # 绿色背景

    # 检测控制
    max_detections: int = 50  # 最大检测数量
    enable_tracking: bool = False  # 是否启用跟踪

    # 性能配置
    input_size: int = 640  # 输入图像尺寸
    half_precision: bool = False  # 是否使用半精度

    # 统计信息显示 - 移到侧边栏而不是图像上
    show_stats_on_image: bool = False  # 不在图像上显示统计信息
    update_sidebar: bool = True  # 更新侧边栏信息


# 全局配置实例
HUMAN_DETECTION_CONFIG = HumanDetectionConfig()


def get_human_detection_config() -> HumanDetectionConfig:
    """获取人体检测配置实例"""
    return HUMAN_DETECTION_CONFIG


def apply_gpu_config():
    """应用GPU配置到人体检测配置"""
    try:
        from config.gpu_config import get_gpu_config
        gpu_config = get_gpu_config()

        # 更新设备配置
        HUMAN_DETECTION_CONFIG.device = gpu_config.get_device_string()
        HUMAN_DETECTION_CONFIG.half_precision = gpu_config.enable_half_precision

        print(f"👤 人体检测设备配置: {HUMAN_DETECTION_CONFIG.device}")
        if gpu_config.enable_half_precision:
            print("⚡ 人体检测已启用半精度加速")

    except ImportError:
        print("⚠️ GPU配置模块未找到，使用默认设备配置")
    except Exception as e:
        print(f"❌ 应用GPU配置失败: {e}")


# 自动应用GPU配置
apply_gpu_config()
