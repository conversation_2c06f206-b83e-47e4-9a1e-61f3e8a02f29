#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统配置模块
管理系统级别的配置参数
"""

import os
from dataclasses import dataclass
from pathlib import Path
import platform

@dataclass
class SystemConfig:
    """系统配置类"""

    # 文件路径 - 使用pathlib确保跨平台兼容性
    captures_dir: str = './captures'
    config_dir: str = './config'
    logs_dir: str = './logs'

    # 配置文件
    thermal_calibration_file: str = 'thermal_scale_calibration.json'
    temperature_calibration_file: str = 'temperature_calibration.json'
    
    # 温度更新间隔
    temperature_update_interval: int = 5  # 秒
    
    # 日志配置
    log_level: str = 'INFO'
    log_format: str = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    # 性能监控
    enable_performance_monitor: bool = True
    fps_display_interval: int = 1  # 秒
    
    def __post_init__(self):
        """初始化后创建必要的目录"""
        self.ensure_directories()
    
    def ensure_directories(self):
        """确保必要的目录存在"""
        directories = [
            self.captures_dir,
            self.config_dir,
            self.logs_dir
        ]

        for directory in directories:
            dir_path = Path(directory).resolve()  # 解析为绝对路径
            dir_path.mkdir(parents=True, exist_ok=True)
            # 显示相对路径，避免暴露绝对路径
            relative_path = Path(directory)
            print(f"✅ 目录已创建/确认: {relative_path}")
    
    @property
    def thermal_calibration_path(self) -> str:
        """热成像校准文件完整路径"""
        return str(Path(self.config_dir) / self.thermal_calibration_file)

    @property
    def temperature_calibration_path(self) -> str:
        """温度校准文件完整路径"""
        return str(Path(self.config_dir) / self.temperature_calibration_file)

    def get_platform_specific_paths(self) -> dict:
        """获取平台特定的路径配置"""
        system = platform.system()
        paths = {
            'system': system,
            'temp_dir': Path.home() / 'tmp' if system == 'Linux' else Path.cwd() / 'temp',
            'config_dir': Path(self.config_dir).resolve(),
            'logs_dir': Path(self.logs_dir).resolve(),
            'captures_dir': Path(self.captures_dir).resolve()
        }
        return paths

@dataclass
class TemperatureConfig:
    """温度相关配置"""
    
    # 默认温度范围
    default_min_temp: float = 18.0
    default_max_temp: float = 32.0
    
    # 温度计算参数
    color_adjustment_factor: float = 1.5
    red_threshold: float = 0.7
    blue_threshold: float = 0.7
    
    # 温度显示精度
    temperature_precision: int = 1
    
    # 温度阈值（用于热源检测）
    heat_source_threshold: float = 35.0
    cold_source_threshold: float = 15.0

def get_system_config() -> SystemConfig:
    """获取系统配置"""
    return SystemConfig()

def get_temperature_config() -> TemperatureConfig:
    """获取温度配置"""
    return TemperatureConfig()

# 全局配置实例
SYSTEM_CONFIG = get_system_config()
TEMPERATURE_CONFIG = get_temperature_config()
