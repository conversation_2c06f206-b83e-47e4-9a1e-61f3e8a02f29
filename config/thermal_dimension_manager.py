#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
热成像尺寸管理器模块
管理热成像的尺寸信息和缓存
"""

from typing import Optional, Tuple
from .heat_detection_config import HeatDetectionConfig


class ThermalDimensionManager:
    """热成像尺寸管理器"""
    
    def __init__(self, config: HeatDetectionConfig):
        self.config = config
        self._cached_dimensions: Optional[Tuple[int, int]] = None
    
    def get_original_thermal_dimensions(self, temperature_manager=None, heat_detector=None) -> Tuple[int, int]:
        """获取原始热成像尺寸"""
        try:
            # 尝试从ISAPI温度读取器获取原始尺寸
            if (temperature_manager and 
                hasattr(temperature_manager, 'get_primary_reader')):
                reader = temperature_manager.get_primary_reader()
                if (reader and hasattr(reader, 'current_temp_matrix') and 
                    reader.current_temp_matrix is not None):
                    height, width = reader.current_temp_matrix.shape
                    self._cached_dimensions = (height, width)
                    return height, width
            
            # 备用：从热源检测器获取
            if heat_detector and hasattr(heat_detector, 'get_original_dimensions'):
                dimensions = heat_detector.get_original_dimensions()
                self._cached_dimensions = dimensions
                return dimensions
            
            # 使用缓存的尺寸
            if self._cached_dimensions:
                return self._cached_dimensions
            
            # 默认尺寸（海康威视常见的热成像分辨率）
            default_dims = (self.config.default_thermal_height, self.config.default_thermal_width)
            self._cached_dimensions = default_dims
            return default_dims
            
        except Exception as e:
            print(f"⚠️ 获取原始热成像尺寸失败: {e}")
            # 返回默认尺寸
            default_dims = (self.config.default_thermal_height, self.config.default_thermal_width)
            return default_dims
    
    def clear_cache(self):
        """清理缓存的尺寸信息"""
        self._cached_dimensions = None


def create_thermal_dimension_manager(config: HeatDetectionConfig) -> ThermalDimensionManager:
    """创建热成像尺寸管理器实例"""
    return ThermalDimensionManager(config)
