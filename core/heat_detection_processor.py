#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
热源检测处理器模块
负责热源检测的应用逻辑和处理流程
"""

import numpy as np
from typing import Optional, List, Tuple, Any
from utils.logger import get_logger


class HeatDetectionProcessor:
    """热源检测处理器"""
    
    def __init__(self, system_instance):
        """
        初始化热源检测处理器
        
        Args:
            system_instance: 系统实例引用
        """
        self.system = system_instance
        self.logger = get_logger("HeatDetectionProcessor")
        
        # 检测状态
        self.detection_enabled = False
        
    def apply_isapi_heat_detection(self, thermal_frame: np.ndarray) -> np.ndarray:
        """
        应用ISAPI热源检测
        
        Args:
            thermal_frame: 热成像帧
            
        Returns:
            处理后的帧
        """
        if not self.detection_enabled:
            return thermal_frame
            
        try:
            # 获取热源检测器
            heat_detector = self._get_heat_detector()
            if not heat_detector:
                return thermal_frame
            
            # 执行热源检测
            bboxes, vis_images = heat_detector.detect_heat_sources()
            
            if not bboxes:
                return thermal_frame
            
            # 获取热源检测渲染器
            heat_detection_renderer = self._get_heat_detection_renderer()
            if not heat_detection_renderer:
                return thermal_frame
            
            # 应用检测结果渲染
            result_frame = heat_detection_renderer.apply_heat_detection(
                thermal_frame=thermal_frame,
                bboxes=bboxes,
                temperature_manager=self._get_temperature_manager(),
                heat_detector=heat_detector,
                display_manager=self._get_display_manager(),
                logger=self.logger
            )
            
            return result_frame
            
        except Exception as e:
            self.logger.error(f"ISAPI热源检测应用失败: {e}")
            return thermal_frame
    
    def _get_heat_detector(self):
        """获取热源检测器实例"""
        if (hasattr(self.system, 'heat_detector') and 
            self.system.heat_detector):
            return self.system.heat_detector
        return None
    
    def _get_heat_detection_renderer(self):
        """获取热源检测渲染器实例"""
        if (hasattr(self.system, 'heat_detection_renderer') and 
            self.system.heat_detection_renderer):
            return self.system.heat_detection_renderer
        return None
    
    def _get_temperature_manager(self):
        """获取温度管理器实例"""
        if (hasattr(self.system, 'temperature_manager') and 
            self.system.temperature_manager):
            return self.system.temperature_manager
        return None
    
    def _get_display_manager(self):
        """获取显示管理器实例"""
        if (hasattr(self.system, 'display_manager') and 
            self.system.display_manager):
            return self.system.display_manager
        return None
    
    def set_detection_enabled(self, enabled: bool):
        """设置检测启用状态"""
        self.detection_enabled = enabled
        self.logger.info(f"热源检测状态: {'启用' if enabled else '禁用'}")
    
    def is_detection_enabled(self) -> bool:
        """检查检测是否启用"""
        return self.detection_enabled
    
    def get_detection_statistics(self) -> dict:
        """获取检测统计信息"""
        stats = {
            'detection_enabled': self.detection_enabled,
            'heat_detector_available': self._get_heat_detector() is not None,
            'renderer_available': self._get_heat_detection_renderer() is not None,
            'temperature_manager_available': self._get_temperature_manager() is not None
        }
        
        # 获取热源检测器统计信息
        heat_detector = self._get_heat_detector()
        if heat_detector and hasattr(heat_detector, 'get_statistics'):
            try:
                stats['detector_stats'] = heat_detector.get_statistics()
            except Exception as e:
                self.logger.warning(f"获取检测器统计信息失败: {e}")
        
        return stats
