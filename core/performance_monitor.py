#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能监控器模块
负责FPS计算和性能统计
"""

import time
import psutil
from utils.logger import get_logger


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, reset_interval: int = 250):
        self.reset_interval = reset_interval
        self.frame_count = 0
        self.start_time = time.time()
        self.current_fps = 0.0

        # 系统性能监控
        self.logger = get_logger("PerformanceMonitor")
        self.excel_exporter = None
        self.last_system_check = 0.0
        self.system_check_interval = 60.0  # 60秒检查一次系统性能
    
    def update(self) -> float:
        """更新帧计数并计算FPS"""
        current_time = time.time()
        self.frame_count += 1
        
        # 计算FPS
        elapsed_time = current_time - self.start_time
        if elapsed_time > 0:
            self.current_fps = self.frame_count / elapsed_time
        
        # 重置计数器
        if self.frame_count >= self.reset_interval:
            self.start_time = current_time
            self.frame_count = 0
        
        return self.current_fps
    
    def get_fps(self) -> float:
        """获取当前FPS"""
        return self.current_fps

    def set_excel_exporter(self, excel_exporter):
        """设置Excel数据导出器"""
        self.excel_exporter = excel_exporter
        self.logger.info("📊 性能监控器已连接Excel数据导出器")

    def check_system_performance(self):
        """检查系统性能并导出到Excel"""
        current_time = time.time()

        # 检查是否需要进行系统性能检查
        if current_time - self.last_system_check < self.system_check_interval:
            return

        try:
            # 获取系统性能指标
            cpu_usage = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')

            # 获取网络IO（如果可用）
            network_io = 0.0
            try:
                net_io = psutil.net_io_counters()
                network_io = net_io.bytes_sent + net_io.bytes_recv
            except:
                pass

            # 构建系统指标数据
            system_metrics = {
                'cpu_usage': cpu_usage,
                'memory_usage': memory.percent,
                'disk_usage': disk.percent,
                'network_io': network_io,
                'detection_fps': self.current_fps,
                'camera_status': 'active',  # 简化状态
                'detection_status': 'active',
                'alert_status': 'active'
            }

            # 导出到Excel
            if self.excel_exporter:
                self.excel_exporter.add_system_metrics(system_metrics)

            self.last_system_check = current_time

        except Exception as e:
            self.logger.error(f"❌ 系统性能检查失败: {e}")
