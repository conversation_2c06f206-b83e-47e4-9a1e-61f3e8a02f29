#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频捕获模块
提供低延迟的视频流捕获功能
"""

import cv2
import threading
import time
import os
import numpy as np
from opencv_fix import create_safe_video_capture, apply_video_capture_fix

# OpenCV线程安全设置 - 防止pthread_frame.c错误
cv2.setNumThreads(1)
from queue import Queue, Empty
from typing import Optional, Tuple, List
from config.camera_config import VideoConfig

class LowLatencyVideoCapture:
    """低延迟视频捕获类"""
    
    def __init__(self, url: str, name: str = "Camera", config: Optional[VideoConfig] = None):
        self.url = url
        self.name = name
        self.config = config or VideoConfig()
        
        # 视频捕获对象
        self.cap: Optional[cv2.VideoCapture] = None
        
        # 线程控制
        self.running = False
        self.thread: Optional[threading.Thread] = None
        
        # 帧队列
        self.frame_queue = Queue(maxsize=self.config.max_queue_size)
        
        # 统计信息
        self.frame_count = 0
        self.last_fps_time = time.time()
        self.current_fps = 0.0
    
    def start(self) -> bool:
        """启动视频捕获"""
        try:
            self.cap = create_safe_video_capture(self.url)
            if not self.cap.isOpened():
                print(f"❌ {self.name} 连接失败!")
                return False

            # 设置低延迟参数
            self.cap.set(cv2.CAP_PROP_BUFFERSIZE, self.config.buffer_size)
            # 应用额外的修复设置
            apply_video_capture_fix(self.cap)
            
            # 启动捕获线程
            self.running = True
            self.thread = threading.Thread(target=self._capture_frames, daemon=True)
            self.thread.start()
            
            print(f"✅ {self.name} 低延迟模式启动成功!")
            return True
            
        except Exception as e:
            print(f"❌ {self.name} 启动异常: {e}")
            return False
    
    def _capture_frames(self):
        """后台线程持续读取帧"""
        while self.running and self.cap is not None:
            try:
                ret, frame = self.cap.read()
                if ret:
                    # 更新统计信息
                    self.frame_count += 1
                    self._update_fps()
                    
                    # 清空队列，只保留最新帧
                    while not self.frame_queue.empty():
                        try:
                            self.frame_queue.get_nowait()
                        except Empty:
                            break
                    
                    # 添加新帧
                    try:
                        self.frame_queue.put((ret, frame), timeout=0.001)
                    except:
                        pass  # 队列满了就丢弃
                else:
                    # 读取失败，短暂等待后重试
                    time.sleep(0.01)
                    
            except Exception as e:
                print(f"❌ {self.name} 捕获异常: {e}")
                time.sleep(0.1)
    
    def _update_fps(self):
        """更新FPS统计"""
        current_time = time.time()
        if current_time - self.last_fps_time >= 1.0:  # 每秒更新一次
            self.current_fps = self.frame_count / (current_time - self.last_fps_time)
            self.frame_count = 0
            self.last_fps_time = current_time
    
    def read(self) -> Tuple[bool, Optional[np.ndarray]]:
        """读取最新帧"""
        try:
            return self.frame_queue.get(timeout=0.5)  # 增加超时时间到0.5秒
        except Empty:
            return False, None
    
    def get(self, prop: int):
        """获取视频属性"""
        if self.cap:
            return self.cap.get(prop)
        return 0
    
    def isOpened(self) -> bool:
        """检查是否打开"""
        return self.cap is not None and self.cap.isOpened()
    
    def get_fps(self) -> float:
        """获取当前FPS"""
        return self.current_fps
    
    def get_resolution(self) -> Tuple[int, int]:
        """获取视频分辨率"""
        if self.cap:
            width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            return width, height
        return 0, 0
    
    def release(self):
        """释放资源"""
        self.running = False
        
        # 等待线程结束
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=1.0)
        
        # 释放视频捕获对象
        if self.cap:
            self.cap.release()
            self.cap = None
        
        print(f"✅ {self.name} 资源已释放")

class DualCameraCapture:
    """双摄像头捕获管理器"""

    def __init__(self, visible_url, thermal_url, config: Optional[VideoConfig] = None, use_video_files: bool = False, video_folder: str = None):
        self.config = config or VideoConfig()
        self.use_video_files = use_video_files

        # 创建可见光视频捕获对象
        if use_video_files:
            # 使用视频文件作为可见光输入
            if isinstance(visible_url, str):
                if video_folder:
                    # 文件夹模式（自动播放列表）
                    video_config = {
                        'mode': 'file',
                        'url': video_folder
                    }
                    print(f"🎬 使用视频文件夹作为可见光输入: {video_folder}")
                else:
                    # 单个文件模式
                    video_config = {
                        'mode': 'file',
                        'url': visible_url
                    }
                    print(f"🎬 使用单个视频文件作为可见光输入: {visible_url}")
                self.visible_capture = VideoCaptureWrapper(video_config)
            else:
                # 如果传入的是已创建的视频捕获对象
                self.visible_capture = visible_url
        else:
            # 使用RTSP流作为可见光输入
            self.visible_capture = LowLatencyVideoCapture(visible_url, "可见光摄像头", config)

        # 红外摄像头处理
        if thermal_url is not None:
            # 红外摄像头使用RTSP流
            self.thermal_capture = LowLatencyVideoCapture(thermal_url, "红外摄像头", config)
        else:
            # 视频模式下可能没有红外摄像头
            self.thermal_capture = None
            print("ℹ️ 视频检测模式：无红外摄像头")

        # 状态标志
        self.is_running = False
    
    def start(self) -> bool:
        """启动摄像头捕获"""
        if self.thermal_capture is None:
            print("🎥 启动视频检测模式（仅可见光）...")
        else:
            print("🎥 启动双摄像头捕获...")

        # 启动可见光摄像头
        if self.use_video_files:
            # 视频文件模式：检查是否已连接
            if not self.visible_capture.isOpened():
                print("❌ 可见光视频文件启动失败!")
                return False
            else:
                print("✅ 可见光视频文件已就绪!")
        else:
            # RTSP模式：启动低延迟捕获
            if not self.visible_capture.start():
                print("❌ 可见光摄像头启动失败!")
                return False

        # 启动红外摄像头（如果存在）
        if self.thermal_capture is not None:
            if not self.thermal_capture.start():
                print("❌ 红外摄像头启动失败!")
                if self.use_video_files:
                    self.visible_capture.release()
                else:
                    self.visible_capture.release()
                return False
            print("✅ 红外摄像头启动成功!")
        else:
            print("ℹ️ 视频检测模式：跳过红外摄像头")

        self.is_running = True
        if self.thermal_capture is None:
            print("✅ 视频检测模式启动成功!")
        else:
            print("✅ 双摄像头捕获启动成功!")

        # 等待一下让线程稳定
        time.sleep(1)
        return True
    
    def read_frames(self) -> Tuple[bool, bool, Optional[np.ndarray], Optional[np.ndarray]]:
        """读取摄像头帧"""
        # 读取可见光帧
        if self.use_video_files:
            # 视频文件模式：直接读取，VideoCaptureWrapper会自动处理视频切换
            ret_visible, frame_visible = self.visible_capture.read()
            if not ret_visible:
                print("⚠️ 视频文件读取失败，可能是播放列表结束")
        else:
            # RTSP模式：从低延迟捕获读取
            ret_visible, frame_visible = self.visible_capture.read()

        # 读取红外帧（如果存在）
        if self.thermal_capture is not None:
            ret_thermal, frame_thermal = self.thermal_capture.read()
        else:
            # 视频模式下没有红外摄像头
            ret_thermal, frame_thermal = False, None

        return ret_visible, ret_thermal, frame_visible, frame_thermal
    
    def get_info(self) -> dict:
        """获取摄像头信息"""
        # 获取可见光信息
        if self.use_video_files:
            # 视频文件模式：从VideoCaptureWrapper获取信息
            if self.visible_capture.isOpened():
                cap = self.visible_capture.cap
                visible_fps = cap.get(cv2.CAP_PROP_FPS) if cap else 0
                visible_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH)) if cap else 0
                visible_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT)) if cap else 0
                current_fps = visible_fps  # 视频文件没有实时FPS概念
            else:
                visible_fps = visible_width = visible_height = current_fps = 0
        else:
            # RTSP模式：从LowLatencyVideoCapture获取信息
            visible_fps = self.visible_capture.get(cv2.CAP_PROP_FPS)
            visible_width, visible_height = self.visible_capture.get_resolution()
            current_fps = self.visible_capture.get_fps()

        # 获取红外信息（如果存在）
        if self.thermal_capture is not None:
            thermal_fps = self.thermal_capture.get(cv2.CAP_PROP_FPS)
            thermal_width, thermal_height = self.thermal_capture.get_resolution()
        else:
            thermal_fps = thermal_width = thermal_height = 0

        return {
            'visible': {
                'fps': visible_fps,
                'width': visible_width,
                'height': visible_height,
                'current_fps': current_fps,
                'source_type': 'video_file' if self.use_video_files else 'rtsp'
            },
            'thermal': {
                'fps': thermal_fps,
                'width': thermal_width,
                'height': thermal_height,
                'current_fps': self.thermal_capture.get_fps(),
                'source_type': 'rtsp'
            }
        }
    
    def release(self):
        """释放所有资源"""
        self.is_running = False
        if self.visible_capture:
            self.visible_capture.release()
        if self.thermal_capture:
            self.thermal_capture.release()

        if self.thermal_capture is None:
            print("✅ 视频检测模式资源已释放")
        else:
            print("✅ 双摄像头捕获资源已释放")


class VideoCaptureWrapper:
    """支持视频列表播放的视频捕获包装器"""

    def __init__(self, source_config: dict):
        """
        初始化视频捕获包装器

        Args:
            source_config: 配置字典，包含:
                - mode: 'file' 或 'rtsp'
                - url: 文件路径/文件夹路径 或 RTSP URL
        """
        self.config = source_config
        self.mode = self.config.get('mode', 'rtsp')

        self.cap = None
        self.is_file_source = (self.mode == 'file')

        if self.is_file_source:
            # [!!!] --- 核心修改开始 --- [!!!]

            # url现在可能是一个单独的文件路径，也可能是一个文件夹路径
            source_path = self.config.get('url')

            self.video_playlist: List[str] = []  # 存储所有待播放的视频路径
            self.current_video_index = -1  # 当前播放视频的索引

            if os.path.isdir(source_path):
                # 如果提供的是一个文件夹路径，自动加载所有支持的视频文件
                print(f"📁 正在从文件夹加载播放列表: {source_path}")
                for filename in sorted(os.listdir(source_path)):
                    if filename.lower().endswith(('.mp4', '.avi', '.mov', '.mkv')):
                        self.video_playlist.append(os.path.join(source_path, filename))
            elif os.path.isfile(source_path):
                # 如果是单个文件，播放列表里就只有它一个
                self.video_playlist.append(source_path)

            if not self.video_playlist:
                print(f"❌ 错误：在路径 {source_path} 中没有找到任何视频文件！")
            else:
                print(f"✅ 成功加载 {len(self.video_playlist)} 个视频到播放列表。")
                for i, video_path in enumerate(self.video_playlist):
                    print(f"  {i+1}. {os.path.basename(video_path)}")
                self.current_video_index = 0  # 准备播放第一个视频

            # [!!!] --- 核心修改结束 --- [!!!]
        else:
            self.url = self.config.get('url')  # 实时模式保持不变

        self.connect()

    def connect(self):
        """连接到视频源"""
        if self.is_file_source:
            # 文件模式的连接逻辑现在是打开播放列表中的当前视频
            if 0 <= self.current_video_index < len(self.video_playlist):
                current_video = self.video_playlist[self.current_video_index]
                print(f"▶️ 正在播放 ({self.current_video_index + 1}/{len(self.video_playlist)}): {os.path.basename(current_video)}")
                self.cap = create_safe_video_capture(current_video)
                if not self.cap.isOpened():
                    print(f"❌ 错误：无法打开视频文件 {current_video}")
                    self.cap = None
            else:
                # 播放列表为空或索引无效
                self.cap = None
        else:
            # 实时模式保持不变
            print(f"🚀 正在以【实时模式】连接: {self.url}")
            self.cap = create_safe_video_capture(self.url)
            if not self.cap.isOpened():
                print(f"❌ 错误：无法连接到摄像头 {self.url}")
                self.cap = None

    def read(self) -> Tuple[bool, Optional[np.ndarray]]:
        """读取视频帧"""
        if not self.isOpened():
            return False, None

        ret, frame = self.cap.read()

        if not ret:
            if self.is_file_source:
                # 视频文件播放完毕
                current_video_name = os.path.basename(self.video_playlist[self.current_video_index])
                print(f"✅ 视频 '{current_video_name}' 播放完毕。")

                # [!!!] --- 切换逻辑 --- [!!!]
                self.current_video_index += 1  # 移动到下一个视频的索引

                if self.current_video_index >= len(self.video_playlist):
                    # 如果已经是最后一个视频，可以选择停止或从头循环整个列表
                    print("🔄 所有视频播放完毕，正在从头循环整个播放列表...")
                    self.current_video_index = 0

                # 释放旧的 capture 对象
                self.release()
                # 连接到新的视频
                self.connect()

                # 再次尝试读取新视频的第一帧
                if self.isOpened():
                    ret, frame = self.cap.read()
                else:
                    # 如果下一个视频也打不开，就返回失败
                    return False, None
            else:
                # 实时模式的重连逻辑
                print("❌ 错误：实时码流中断！")
                # 可以在这里添加重连逻辑

        return ret, frame

    def isOpened(self) -> bool:
        """检查视频捕获是否已打开"""
        return self.cap is not None and self.cap.isOpened()

    def release(self):
        """释放视频捕获资源"""
        if self.cap is not None:
            self.cap.release()
            self.cap = None

    def get_current_video_info(self) -> dict:
        """获取当前视频信息"""
        if not self.is_file_source or not self.video_playlist:
            return {}

        if 0 <= self.current_video_index < len(self.video_playlist):
            current_video = self.video_playlist[self.current_video_index]
            return {
                'current_index': self.current_video_index,
                'total_videos': len(self.video_playlist),
                'current_video': os.path.basename(current_video),
                'current_path': current_video,
                'playlist': [os.path.basename(v) for v in self.video_playlist]
            }
        return {}

    def skip_to_next_video(self) -> bool:
        """手动跳转到下一个视频"""
        if not self.is_file_source or not self.video_playlist:
            return False

        self.current_video_index += 1
        if self.current_video_index >= len(self.video_playlist):
            self.current_video_index = 0

        self.release()
        self.connect()
        return self.isOpened()

    def skip_to_previous_video(self) -> bool:
        """手动跳转到上一个视频"""
        if not self.is_file_source or not self.video_playlist:
            return False

        self.current_video_index -= 1
        if self.current_video_index < 0:
            self.current_video_index = len(self.video_playlist) - 1

        self.release()
        self.connect()
        return self.isOpened()
