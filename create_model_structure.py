#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建RKNN模型目录结构脚本
为RKNN模型文件创建标准化的目录结构
"""

import os
from pathlib import Path
from utils.logger import get_logger


def create_model_directories():
    """创建模型目录结构"""
    logger = get_logger("ModelStructureCreator")
    
    # 定义目录结构
    model_dirs = [
        "models",
        "models/human_detection",
        "models/fire_detection",
        "models/general",
        "models/backup",
        "models/converted"
    ]
    
    # 创建目录
    for dir_path in model_dirs:
        try:
            Path(dir_path).mkdir(parents=True, exist_ok=True)
            logger.info(f"✅ 目录已创建/确认: {dir_path}")
        except Exception as e:
            logger.error(f"❌ 创建目录失败 {dir_path}: {e}")
    
    # 创建README文件
    create_readme_files(logger)
    
    # 创建模型转换指南
    create_conversion_guide(logger)


def create_readme_files(logger):
    """创建README文件"""
    readme_contents = {
        "models/README.md": """# RKNN模型文件目录

此目录包含用于RKNN推理的模型文件。

## 目录结构

- `human_detection/` - 人体检测模型
- `fire_detection/` - 火焰烟雾检测模型  
- `general/` - 通用检测模型
- `backup/` - 模型备份
- `converted/` - 转换后的模型

## 模型格式

所有模型文件应为 `.rknn` 格式，适用于瑞芯微NPU硬件加速。

## 注意事项

1. 确保模型文件与代码中的路径引用一致
2. 定期备份重要模型文件
3. 转换新模型时请更新相应的配置文件
""",
        
        "models/human_detection/README.md": """# 人体检测模型

## 模型列表

- `yolov8n_human.rknn` - YOLOv8 Nano 人体检测模型（轻量级）
- `yolov8s_human.rknn` - YOLOv8 Small 人体检测模型
- `yolov8m_human.rknn` - YOLOv8 Medium 人体检测模型
- `yolov8l_human.rknn` - YOLOv8 Large 人体检测模型
- `yolov8x_human.rknn` - YOLOv8 Extra Large 人体检测模型

## 使用说明

这些模型专门用于人体检测，基于COCO数据集训练，只检测人体类别。

## 性能建议

- 实时应用推荐使用 `yolov8n_human.rknn`
- 高精度需求推荐使用 `yolov8l_human.rknn` 或 `yolov8x_human.rknn`
""",
        
        "models/fire_detection/README.md": """# 火焰烟雾检测模型

## 模型列表

- `fire_smoke.rknn` - 火焰和烟雾综合检测模型
- `fire.rknn` - 专用火焰检测模型
- `smoke.rknn` - 专用烟雾检测模型
- `fire_smoke_n.rknn` - 轻量级火焰烟雾检测模型
- `fire_smoke_s.rknn` - 小型火焰烟雾检测模型

## 检测模式

1. **单模型模式**: 使用 `fire_smoke.rknn` 同时检测火焰和烟雾
2. **双模型模式**: 分别使用 `fire.rknn` 和 `smoke.rknn` 进行检测

## 使用建议

- 一般场景推荐使用单模型模式
- 对特定类别有高精度要求时使用双模型模式
"""
    }
    
    for file_path, content in readme_contents.items():
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            logger.info(f"✅ README文件已创建: {file_path}")
        except Exception as e:
            logger.error(f"❌ 创建README文件失败 {file_path}: {e}")


def create_conversion_guide(logger):
    """创建模型转换指南"""
    guide_content = """# PyTorch到RKNN模型转换指南

## 转换工具

使用瑞芯微官方的 `rknn-toolkit2` 进行模型转换。

## 安装依赖

```bash
pip install rknn-toolkit2
```

## 转换步骤

### 1. 导出ONNX模型

```python
# 从PyTorch模型导出ONNX
import torch
from ultralytics import YOLO

# 加载PyTorch模型
model = YOLO('yolov8n.pt')

# 导出为ONNX格式
model.export(format='onnx', imgsz=640)
```

### 2. 转换为RKNN

```python
from rknn.api import RKNN

# 创建RKNN对象
rknn = RKNN(verbose=True)

# 配置模型
rknn.config(mean_values=[[0, 0, 0]], std_values=[[255, 255, 255]], target_platform='rk3588')

# 加载ONNX模型
ret = rknn.load_onnx(model='yolov8n.onnx')

# 构建RKNN模型
ret = rknn.build(do_quantization=True, dataset='calibration_data.txt')

# 导出RKNN模型
ret = rknn.export_rknn('./yolov8n.rknn')

# 释放资源
rknn.release()
```

### 3. 量化数据准备

创建校准数据集文件 `calibration_data.txt`：

```
path/to/image1.jpg
path/to/image2.jpg
...
```

## 模型命名规范

- 人体检测: `{model_size}_human.rknn`
- 火焰检测: `fire.rknn`
- 烟雾检测: `smoke.rknn`
- 综合检测: `fire_smoke.rknn`

## 验证转换结果

转换完成后，使用以下代码验证模型：

```python
from rknnlite.api import RKNNLite

# 创建RKNN Lite对象
rknn_lite = RKNNLite()

# 加载RKNN模型
ret = rknn_lite.load_rknn('model.rknn')

# 初始化运行时
ret = rknn_lite.init_runtime()

# 测试推理
import numpy as np
input_data = np.random.randint(0, 255, (1, 3, 640, 640), dtype=np.uint8)
outputs = rknn_lite.inference(inputs=[input_data])

print("模型转换成功，推理正常")
```

## 注意事项

1. 确保输入尺寸与原模型一致
2. 注意数据预处理的差异
3. 验证输出格式是否正确
4. 在目标硬件上测试性能
"""
    
    try:
        with open("models/CONVERSION_GUIDE.md", 'w', encoding='utf-8') as f:
            f.write(guide_content)
        logger.info("✅ 模型转换指南已创建: models/CONVERSION_GUIDE.md")
    except Exception as e:
        logger.error(f"❌ 创建转换指南失败: {e}")


def create_model_mapping_file():
    """创建模型映射文件"""
    mapping = {
        "model_mappings": {
            "pytorch_to_rknn": {
                "yolov8n.pt": "models/human_detection/yolov8n_human.rknn",
                "yolov8s.pt": "models/human_detection/yolov8s_human.rknn",
                "yolov8m.pt": "models/human_detection/yolov8m_human.rknn",
                "yolov8l.pt": "models/human_detection/yolov8l_human.rknn",
                "yolov8x.pt": "models/human_detection/yolov8x_human.rknn",
                "best.pt": "models/fire_detection/fire_smoke.rknn",
                "best-1.pt": "models/fire_detection/fire.rknn",
                "best-2.pt": "models/fire_detection/smoke.rknn",
                "fire_model.pt": "models/fire_detection/fire.rknn",
                "smoke_model.pt": "models/fire_detection/smoke.rknn"
            }
        },
        "model_info": {
            "human_detection": {
                "input_size": [640, 640],
                "classes": ["person"],
                "format": "YOLO"
            },
            "fire_detection": {
                "input_size": [640, 640],
                "classes": ["fire", "smoke"],
                "format": "YOLO"
            }
        }
    }
    
    import json
    try:
        with open("models/model_mappings.json", 'w', encoding='utf-8') as f:
            json.dump(mapping, f, indent=2, ensure_ascii=False)
        print("✅ 模型映射文件已创建: models/model_mappings.json")
    except Exception as e:
        print(f"❌ 创建模型映射文件失败: {e}")


def main():
    """主函数"""
    print("🏗️ 开始创建RKNN模型目录结构")
    
    create_model_directories()
    create_model_mapping_file()
    
    print("\n✅ RKNN模型目录结构创建完成!")
    print("\n📁 创建的目录结构:")
    print("models/")
    print("├── human_detection/")
    print("├── fire_detection/")
    print("├── general/")
    print("├── backup/")
    print("├── converted/")
    print("├── README.md")
    print("├── CONVERSION_GUIDE.md")
    print("└── model_mappings.json")


if __name__ == "__main__":
    main()
