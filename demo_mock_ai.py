#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模拟AI Worker演示脚本
展示PC端模拟功能的基本使用方法
"""

import sys
import cv2
import numpy as np
import time
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget, QLabel, QPushButton, QTextEdit
from PyQt5.QtCore import QTimer, pyqtSignal
from PyQt5.QtGui import QPixmap, QImage

from platform_detector import get_ai_worker_instance, print_platform_info
from utils.logger import get_logger


class MockAIDemo(QMainWindow):
    """模拟AI演示窗口"""
    
    def __init__(self):
        super().__init__()
        self.logger = get_logger("MockAIDemo")
        
        # 设置窗口
        self.setWindowTitle("模拟AI Worker演示")
        self.setGeometry(100, 100, 1000, 700)
        
        # AI Worker
        self.ai_worker = None
        
        # 测试图像
        self.test_frame = None
        
        # 初始化UI
        self.init_ui()
        
        # 初始化AI Worker
        self.init_ai_worker()
        
        # 创建测试图像
        self.create_test_frame()
    
    def init_ui(self):
        """初始化用户界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QHBoxLayout(central_widget)
        
        # 左侧：图像显示
        left_layout = QVBoxLayout()
        
        # 原始图像
        self.original_label = QLabel("原始图像")
        self.original_label.setMinimumSize(400, 300)
        self.original_label.setStyleSheet("border: 1px solid gray;")
        left_layout.addWidget(self.original_label)
        
        # 检测结果图像
        self.result_label = QLabel("检测结果")
        self.result_label.setMinimumSize(400, 300)
        self.result_label.setStyleSheet("border: 1px solid gray;")
        left_layout.addWidget(self.result_label)
        
        main_layout.addLayout(left_layout)
        
        # 右侧：控制面板
        right_layout = QVBoxLayout()
        
        # 控制按钮
        self.human_btn = QPushButton("人体检测")
        self.human_btn.clicked.connect(self.test_human_detection)
        right_layout.addWidget(self.human_btn)
        
        self.fire_btn = QPushButton("火焰烟雾检测")
        self.fire_btn.clicked.connect(self.test_fire_smoke_detection)
        right_layout.addWidget(self.fire_btn)
        
        self.heat_btn = QPushButton("热源检测")
        self.heat_btn.clicked.connect(self.test_heat_detection)
        right_layout.addWidget(self.heat_btn)
        
        self.clear_btn = QPushButton("清空队列")
        self.clear_btn.clicked.connect(self.clear_queue)
        right_layout.addWidget(self.clear_btn)
        
        # 状态显示
        self.status_text = QTextEdit()
        self.status_text.setMaximumHeight(200)
        self.status_text.setReadOnly(True)
        right_layout.addWidget(self.status_text)
        
        # 统计信息
        self.stats_label = QLabel("统计信息")
        right_layout.addWidget(self.stats_label)
        
        main_layout.addLayout(right_layout)
        
        # 定时器更新统计信息
        self.stats_timer = QTimer()
        self.stats_timer.timeout.connect(self.update_stats)
        self.stats_timer.start(1000)  # 每秒更新
    
    def init_ai_worker(self):
        """初始化AI Worker"""
        try:
            self.ai_worker = get_ai_worker_instance(self)
            
            # 连接信号
            self.ai_worker.human_detection_finished.connect(self.on_human_detection_finished)
            self.ai_worker.fire_smoke_detection_finished.connect(self.on_fire_smoke_detection_finished)
            self.ai_worker.heat_detection_finished.connect(self.on_heat_detection_finished)
            self.ai_worker.processing_error.connect(self.on_processing_error)
            
            # 初始化检测器
            self.ai_worker.initialize_detectors(
                human_detector="MockHumanDetector",
                fire_smoke_detector="MockFireSmokeDetector",
                heat_detector="MockHeatDetector"
            )
            
            # 启动工作线程
            self.ai_worker.start()
            
            self.add_status("✅ AI Worker初始化成功")
            
        except Exception as e:
            self.add_status(f"❌ AI Worker初始化失败: {e}")
    
    def create_test_frame(self):
        """创建测试图像"""
        # 创建一个彩色测试图像
        self.test_frame = np.zeros((480, 640, 3), dtype=np.uint8)
        
        # 添加背景渐变
        for y in range(480):
            for x in range(640):
                self.test_frame[y, x] = [
                    int(255 * x / 640),
                    int(255 * y / 480),
                    128
                ]
        
        # 添加一些几何图形
        cv2.rectangle(self.test_frame, (100, 100), (250, 250), (100, 150, 200), -1)
        cv2.circle(self.test_frame, (450, 300), 80, (200, 100, 150), -1)
        cv2.ellipse(self.test_frame, (320, 150), (60, 40), 45, 0, 360, (150, 200, 100), -1)
        
        # 添加文字
        cv2.putText(self.test_frame, "Mock AI Demo", (200, 400), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        
        # 显示原始图像
        self.display_image(self.test_frame, self.original_label)
    
    def display_image(self, image, label):
        """在标签中显示图像"""
        if image is None:
            return
        
        # 转换为Qt格式
        height, width, channel = image.shape
        bytes_per_line = 3 * width
        q_image = QImage(image.data, width, height, bytes_per_line, QImage.Format_RGB888).rgbSwapped()
        
        # 缩放到标签大小
        pixmap = QPixmap.fromImage(q_image)
        scaled_pixmap = pixmap.scaled(label.size(), aspectRatioMode=1)  # KeepAspectRatio
        
        label.setPixmap(scaled_pixmap)
    
    def add_status(self, message):
        """添加状态信息"""
        timestamp = time.strftime("%H:%M:%S")
        self.status_text.append(f"[{timestamp}] {message}")
        
        # 自动滚动到底部
        scrollbar = self.status_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())
    
    def test_human_detection(self):
        """测试人体检测"""
        if self.ai_worker and self.test_frame is not None:
            success = self.ai_worker.add_human_detection_task(
                self.test_frame,
                metadata={'demo_type': 'human_detection'}
            )
            if success:
                self.add_status("🚀 人体检测任务已提交")
            else:
                self.add_status("❌ 人体检测任务提交失败")
    
    def test_fire_smoke_detection(self):
        """测试火焰烟雾检测"""
        if self.ai_worker and self.test_frame is not None:
            success = self.ai_worker.add_fire_smoke_detection_task(
                self.test_frame,
                metadata={'demo_type': 'fire_smoke_detection'}
            )
            if success:
                self.add_status("🚀 火焰烟雾检测任务已提交")
            else:
                self.add_status("❌ 火焰烟雾检测任务提交失败")
    
    def test_heat_detection(self):
        """测试热源检测"""
        if self.ai_worker and self.test_frame is not None:
            success = self.ai_worker.add_heat_detection_task(
                self.test_frame,
                metadata={'demo_type': 'heat_detection'}
            )
            if success:
                self.add_status("🚀 热源检测任务已提交")
            else:
                self.add_status("❌ 热源检测任务提交失败")
    
    def clear_queue(self):
        """清空任务队列"""
        if self.ai_worker:
            self.ai_worker.clear_queue()
            self.add_status("🧹 任务队列已清空")
    
    def on_human_detection_finished(self, result_data):
        """人体检测完成"""
        detections = result_data.detections
        processing_time = result_data.processing_time
        
        self.add_status(f"✅ 人体检测完成: {len(detections)}个检测, 耗时{processing_time:.3f}s")
        
        # 显示结果图像
        if result_data.annotated_frame is not None:
            self.display_image(result_data.annotated_frame, self.result_label)
    
    def on_fire_smoke_detection_finished(self, result_data):
        """火焰烟雾检测完成"""
        detections = result_data.detections
        processing_time = result_data.processing_time
        
        self.add_status(f"✅ 火焰烟雾检测完成: {len(detections)}个检测, 耗时{processing_time:.3f}s")
        
        # 显示结果图像
        if result_data.annotated_frame is not None:
            self.display_image(result_data.annotated_frame, self.result_label)
    
    def on_heat_detection_finished(self, result_data):
        """热源检测完成"""
        detections = result_data.detections
        processing_time = result_data.processing_time
        
        self.add_status(f"✅ 热源检测完成: {len(detections)}个检测, 耗时{processing_time:.3f}s")
        
        # 显示结果图像
        if result_data.annotated_frame is not None:
            self.display_image(result_data.annotated_frame, self.result_label)
    
    def on_processing_error(self, task_id, error_message):
        """处理错误"""
        self.add_status(f"❌ 处理错误 [{task_id}]: {error_message}")
    
    def update_stats(self):
        """更新统计信息"""
        if self.ai_worker:
            stats = self.ai_worker.get_statistics()
            
            stats_text = f"""统计信息:
总任务数: {stats['total_tasks_processed']}
人体检测: {stats['human_detection_count']}
火焰烟雾: {stats['fire_smoke_detection_count']}
热源检测: {stats['heat_detection_count']}
队列大小: {stats['queue_size']}
错误数: {stats['error_count']}
平均耗时: {stats['average_processing_time']:.3f}s"""
            
            self.stats_label.setText(stats_text)
    
    def closeEvent(self, event):
        """关闭事件"""
        if self.ai_worker:
            self.ai_worker.stop()
            self.ai_worker.wait(3000)  # 等待3秒
        
        event.accept()


def main():
    """主函数"""
    print("🎭 模拟AI Worker演示")
    
    # 打印平台信息
    print_platform_info()
    
    # 创建Qt应用
    app = QApplication(sys.argv)
    
    # 创建演示窗口
    demo = MockAIDemo()
    demo.show()
    
    # 运行应用
    return app.exec_()


if __name__ == "__main__":
    sys.exit(main())
