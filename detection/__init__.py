#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检测模块初始化文件
"""

from .core.heat_source_detector import HeatSourceDetector
from .core.human_detector import HumanDetector
from .core.fusion_detector import FusionDetector
# from .core.fire_smoke_detector import FireSmokeDetector  # 已清理
# # from .core.flame_detector import FlameDetector  # 已删除  # 已弃用
# # from .core.smoke_detector import SmokeDetector  # 已删除  # 已弃用
# # from .core.fire_detector import FireDetector, FireDetectionResult  # 已删除  # 已弃用
from .processors.image_segmentation import ThermalPreprocessor, ThermalSegmenter
from .processors.temperature_processor import TemperatureProcessor
from .processors.contour_analyzer import ContourAnalyzer
from .analysis.heat_source_analyzer import HeatSourceAnalyzer
from .utils.yolo_utils import YOLOModelManager, YOLOConfigManager
from .utils.fire_yolo_utils import FireYOLOModelManager, FireYOLOConfigManager, FireModelConfig
from .debug.debug_tool import DetectionDebugger
from .debug.debug_base import BaseDebugger
from .debug.isapi_debugger import ISAPIThermalDebugger

# 火焰分析模块（已清理）
# try:
#     from .flame_analysis.flame_flicker_analyzer import FlameFlickerAnalyzer, FlickerAnalysisResult
#     from .flame_analysis.flame_morphology_analyzer import FlameMorphologyAnalyzer, MorphologyAnalysisResult
#     FLAME_ANALYSIS_AVAILABLE = True
# except ImportError:
FLAME_ANALYSIS_AVAILABLE = False
FlameFlickerAnalyzer = None
FlickerAnalysisResult = None
FlameMorphologyAnalyzer = None
MorphologyAnalysisResult = None

__all__ = [
    'HeatSourceDetector',
    'HumanDetector',
    'FusionDetector',
    # 'FireSmokeDetector',  # 已清理
    # # 'FlameDetector',  # 已删除  # 已弃用
    # # 'SmokeDetector',  # 已删除  # 已弃用
    # # 'FireDetector',  # 已删除  # 已弃用
    # # 'FireDetectionResult',  # 已删除  # 已弃用
    'ThermalPreprocessor',
    'ThermalSegmenter',
    'TemperatureProcessor',
    'ContourAnalyzer',
    'HeatSourceAnalyzer',
    'YOLOModelManager',
    'YOLOConfigManager',
    'FireYOLOModelManager',
    'FireYOLOConfigManager',
    'FireModelConfig',
    'DetectionDebugger',
    'BaseDebugger',
    'ISAPIThermalDebugger'
]

# 如果火焰分析可用，添加到导出列表
if FLAME_ANALYSIS_AVAILABLE:
    __all__.extend([
        'FlameFlickerAnalyzer',
        'FlickerAnalysisResult',
        'FlameMorphologyAnalyzer',
        'MorphologyAnalysisResult'
    ])