#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析和跟踪模块
包含热源分析、跟踪、历史记录、变化率计算等功能
"""

from .heat_source_analyzer import HeatSourceAnalyzer, HeatSourceInfo
from .heat_source_tracker import HeatSourceTracker
from .heat_source_history import HeatSourceHistory, HeatSourceSnapshot
from .change_rate_calculator import ChangeRateCalculator
from .heat_area_calculator import HeatAreaCalculator
from .heat_growth_analyzer import HeatGrowthAnalyzer
from .detection_count_manager import DetectionCountManager, DetectionCounts, get_detection_count_manager





__all__ = [
    'HeatSourceAnalyzer',
    'HeatSourceInfo',
    'HeatSourceTracker',
    'HeatSourceHistory',
    'HeatSourceSnapshot',
    'ChangeRateCalculator',
    'HeatAreaCalculator',
    'HeatGrowthAnalyzer',
    'DetectionCountManager',
    'DetectionCounts',
    'get_detection_count_manager'
]

# 指标计算模块（可选导入）
try:
    from .global_metrics_calculator import GlobalMetricsCalculator, GlobalMetrics, get_global_metrics_calculator
    from .object_detailed_metrics_calculator import ObjectDetailedMetricsCalculator, ObjectDetailedMetrics, get_object_metrics_calculator
    from .comprehensive_metrics_manager import ComprehensiveMetricsManager, get_comprehensive_metrics_manager
    METRICS_AVAILABLE = True
except ImportError:
    METRICS_AVAILABLE = False
    GlobalMetricsCalculator = None
    GlobalMetrics = None
    get_global_metrics_calculator = None
    ObjectDetailedMetricsCalculator = None
    ObjectDetailedMetrics = None
    get_object_metrics_calculator = None
    ComprehensiveMetricsManager = None
    get_comprehensive_metrics_manager = None



# 如果指标计算可用，添加到导出列表
if METRICS_AVAILABLE:
    __all__.extend([
        'GlobalMetricsCalculator', 'GlobalMetrics', 'get_global_metrics_calculator',
        'ObjectDetailedMetricsCalculator', 'ObjectDetailedMetrics', 'get_object_metrics_calculator',
        'ComprehensiveMetricsManager', 'get_comprehensive_metrics_manager'
    ])




