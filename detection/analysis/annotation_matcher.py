#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
热源与标注区域匹配器
判断热源是否位于已标注的区域内，并返回对应的标注区域名称
"""

from typing import List, Optional, Tuple, Dict
from dataclasses import dataclass
import logging

# 导入标注相关的类
try:
    from qt_ui.manual_annotation_widget import ManualAnnotation, ManualAnnotationManager
except ImportError:
    # 如果无法导入，定义基本的数据结构
    @dataclass
    class ManualAnnotation:
        id: str
        bbox: Tuple[int, int, int, int]  # (x, y, width, height)
        label: str
        confidence: float
        color: Tuple[int, int, int]
        visible: bool = True
        is_thermal: bool = False


class AnnotationMatcher:
    """热源与标注区域匹配器"""
    
    def __init__(self):
        """初始化匹配器"""
        self.logger = logging.getLogger("AnnotationMatcher")
        self.annotation_manager = None
        self._cached_annotations = []
        self._cache_timestamp = 0
        
    def set_annotation_manager(self, manager):
        """设置标注管理器"""
        self.annotation_manager = manager
        self.logger.info("标注管理器已设置")
    
    def get_thermal_annotations(self) -> List[ManualAnnotation]:
        """获取热成像标注列表"""
        annotations = []

        # 1. 从内存标注管理器获取标注（优先使用）
        try:
            from .memory_annotation_manager import get_memory_annotation_manager
            memory_manager = get_memory_annotation_manager()
            regions = memory_manager.get_all_regions('Thermal')

            for region in regions:
                # 转换为ManualAnnotation格式
                annotation = ManualAnnotation(
                    id=region.asset_id,
                    bbox=region.bbox,
                    label=region.asset_name,
                    confidence=1.0,
                    color=(0, 255, 0),
                    timestamp=region.creation_time.isoformat(),
                    visible=True,
                    is_thermal=True
                )
                annotations.append(annotation)

            self.logger.debug(f"从内存标注管理器获取到 {len(annotations)} 个热成像标注")

        except Exception as e:
            self.logger.error(f"从内存标注管理器获取标注失败: {e}")

        # 2. 从标注管理器获取手动标注（备用）
        if not annotations and self.annotation_manager is not None:
            try:
                all_annotations = self.annotation_manager.get_visible_annotations()
                thermal_annotations = [
                    ann for ann in all_annotations
                    if getattr(ann, 'is_thermal', False)
                ]
                annotations.extend(thermal_annotations)
            except Exception as e:
                self.logger.error(f"获取手动标注失败: {e}")

        # 3. 从监控区域配置获取手动标注区域（最后备用）
        try:
            from pathlib import Path
            import json

            zone_file = Path("config/monitoring_zones.json")
            if zone_file.exists():
                with open(zone_file, 'r', encoding='utf-8') as f:
                    zones_data = json.load(f)

                for zone in zones_data.get('zones', []):
                    if zone.get('zone_type') == 'manual':
                        # 创建标注对象
                        bbox = tuple(zone['bbox'])  # (x, y, width, height)
                        from datetime import datetime

                        # 解析创建时间
                        timestamp = datetime.now()
                        if zone.get('created_time'):
                            try:
                                timestamp = datetime.fromisoformat(zone['created_time'])
                            except:
                                pass

                        annotation = ManualAnnotation(
                            id=zone['id'],
                            bbox=bbox,
                            label=zone['name'],
                            confidence=1.0,
                            color=(0, 255, 0),
                            timestamp=timestamp,
                            visible=zone.get('enabled', True),
                            is_thermal=True  # 监控区域标注默认为热成像
                        )
                        annotations.append(annotation)

        except Exception as e:
            self.logger.error(f"获取监控区域标注失败: {e}")

        return annotations
    

    
    def calculate_overlap_ratio(self, bbox1: Tuple[int, int, int, int],
                               bbox2: Tuple[int, int, int, int]) -> float:
        """
        计算两个矩形的重叠比例

        Args:
            bbox1: 第一个矩形 (x, y, width, height)
            bbox2: 第二个矩形 (x, y, width, height)

        Returns:
            重叠比例 (0.0 - 1.0)
        """
        x1, y1, w1, h1 = bbox1
        x2, y2, w2, h2 = bbox2

        # 计算重叠区域
        left = max(x1, x2)
        top = max(y1, y2)
        right = min(x1 + w1, x2 + w2)
        bottom = min(y1 + h1, y2 + h2)

        # 检查是否有重叠
        if left >= right or top >= bottom:
            return 0.0

        overlap_area = (right - left) * (bottom - top)
        bbox1_area = w1 * h1

        return overlap_area / bbox1_area if bbox1_area > 0 else 0.0
    
    def match_heat_source_to_annotation(self, heat_source_position: Tuple[int, int],
                                       heat_source_bbox: Tuple[int, int, int, int],
                                       overlap_threshold: float = 0.1) -> Optional[str]:
        """
        使用重叠比例匹配热源到标注区域

        Args:
            heat_source_position: 热源中心位置 (x, y) - 保留参数以兼容现有调用，暂未使用
            heat_source_bbox: 热源边界框 (x, y, width, height)
            overlap_threshold: 重叠比例阈值 (0.0-1.0)，默认10%

        Returns:
            匹配到的标注区域名称，如果没有匹配则返回None
        """
        # 注意：heat_source_position 参数保留以兼容现有调用，但在重叠比例匹配中不使用
        thermal_annotations = self.get_thermal_annotations()

        if not thermal_annotations:
            return None

        best_match = None
        best_score = 0.0

        for annotation in thermal_annotations:
            # 使用重叠比例匹配（唯一方法）
            overlap_ratio = self.calculate_overlap_ratio(heat_source_bbox, annotation.bbox)
            if overlap_ratio > overlap_threshold:
                if overlap_ratio > best_score:
                    best_score = overlap_ratio
                    best_match = annotation.label

        return best_match
    
    def match_multiple_heat_sources(self, heat_sources: List[Dict],
                                   overlap_threshold: float = 0.1) -> Dict[int, str]:
        """
        批量匹配多个热源到标注区域

        Args:
            heat_sources: 热源列表，每个热源包含position和bbox信息
            overlap_threshold: 重叠比例阈值 (0.0-1.0)，默认10%

        Returns:
            热源ID到标注区域名称的映射字典
        """
        matches = {}

        for heat_source in heat_sources:
            heat_source_id = heat_source.get('id', 0)
            position = heat_source.get('position', (0, 0))
            bbox = heat_source.get('bbox', (0, 0, 0, 0))

            matched_label = self.match_heat_source_to_annotation(
                position, bbox, overlap_threshold
            )

            if matched_label:
                matches[heat_source_id] = matched_label
                self.logger.debug(f"热源{heat_source_id}匹配到标注区域: {matched_label} (重叠比例阈值: {overlap_threshold})")

        return matches
    
    def get_annotation_statistics(self) -> Dict[str, int]:
        """
        获取标注区域统计信息
        
        Returns:
            标注区域名称到热源数量的映射
        """
        thermal_annotations = self.get_thermal_annotations()
        
        stats = {}
        for annotation in thermal_annotations:
            label = annotation.label
            if label not in stats:
                stats[label] = 0
        
        return stats
    
    def validate_coordinates(self, position: Tuple[int, int], 
                           bbox: Tuple[int, int, int, int],
                           image_size: Tuple[int, int] = None) -> bool:
        """
        验证坐标是否有效
        
        Args:
            position: 位置坐标
            bbox: 边界框
            image_size: 图像尺寸 (width, height)，可选
            
        Returns:
            坐标是否有效
        """
        px, py = position
        x, y, w, h = bbox
        
        # 基本有效性检查
        if w <= 0 or h <= 0:
            return False
        
        if px < 0 or py < 0:
            return False
        
        # 如果提供了图像尺寸，检查是否在图像范围内
        if image_size:
            img_w, img_h = image_size
            if px >= img_w or py >= img_h:
                return False
            if x + w > img_w or y + h > img_h:
                return False
        
        return True


# 全局匹配器实例
_global_matcher = None

def get_annotation_matcher() -> AnnotationMatcher:
    """获取全局标注匹配器实例"""
    global _global_matcher
    if _global_matcher is None:
        _global_matcher = AnnotationMatcher()
    return _global_matcher

def set_annotation_manager(manager):
    """设置全局标注管理器"""
    matcher = get_annotation_matcher()
    matcher.set_annotation_manager(manager)
