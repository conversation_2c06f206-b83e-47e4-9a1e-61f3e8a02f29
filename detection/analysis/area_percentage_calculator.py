#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
火焰面积占比计算器
计算火焰/烟雾相对于画面的面积百分比，支持面积阈值判断
"""

import cv2
import numpy as np
from typing import List, Dict, Tuple, Optional, Any
from dataclasses import dataclass
from enum import Enum

from utils.logger import get_logger


class DetectionType(Enum):
    """检测类型枚举"""
    FLAME = "flame"
    SMOKE = "smoke"
    COMBINED = "combined"


@dataclass
class AreaPercentageResult:
    """面积占比计算结果"""
    detection_type: DetectionType
    total_detections: int           # 检测目标总数
    total_area_pixels: int          # 总面积（像素）
    frame_area_pixels: int          # 画面总面积（像素）
    area_percentage: float          # 面积占比（%）
    individual_areas: List[int]     # 各个目标的面积
    individual_percentages: List[float]  # 各个目标的占比
    largest_area_pixels: int        # 最大目标面积
    largest_percentage: float       # 最大目标占比
    bounding_boxes: List[Tuple[int, int, int, int]]  # 边界框列表
    confidence_scores: List[float]  # 置信度列表
    timestamp: float               # 时间戳


@dataclass
class AreaThresholdConfig:
    """面积阈值配置"""
    detection_type: DetectionType
    warning_percentage: float      # 警告阈值（%）
    critical_percentage: float     # 严重阈值（%）
    emergency_percentage: float    # 紧急阈值（%）
    min_detection_area: int        # 最小检测面积（像素）
    growth_rate_threshold: float   # 增长率阈值（%/秒）


class AreaPercentageCalculator:
    """火焰面积占比计算器"""
    
    def __init__(self, frame_width: int = 640, frame_height: int = 480):
        """
        初始化面积占比计算器
        
        Args:
            frame_width: 画面宽度
            frame_height: 画面高度
        """
        self.logger = get_logger("AreaPercentageCalculator")
        
        # 画面尺寸
        self.frame_width = frame_width
        self.frame_height = frame_height
        self.frame_area = frame_width * frame_height
        
        # 阈值配置
        self.thresholds: Dict[DetectionType, AreaThresholdConfig] = {}
        
        # 历史数据（用于计算增长率）
        self.history: Dict[DetectionType, List[AreaPercentageResult]] = {
            DetectionType.FLAME: [],
            DetectionType.SMOKE: [],
            DetectionType.COMBINED: []
        }
        
        # 配置参数
        self.max_history_size = 100
        self.smoothing_window = 5
        
        # 初始化默认阈值
        self._setup_default_thresholds()
    
    def _setup_default_thresholds(self):
        """设置默认阈值配置"""
        # 火焰阈值
        self.thresholds[DetectionType.FLAME] = AreaThresholdConfig(
            detection_type=DetectionType.FLAME,
            warning_percentage=2.0,     # 2%
            critical_percentage=5.0,    # 5%
            emergency_percentage=10.0,  # 10%
            min_detection_area=100,     # 100像素
            growth_rate_threshold=1.0   # 1%/秒
        )
        
        # 烟雾阈值
        self.thresholds[DetectionType.SMOKE] = AreaThresholdConfig(
            detection_type=DetectionType.SMOKE,
            warning_percentage=3.0,     # 3%
            critical_percentage=8.0,    # 8%
            emergency_percentage=15.0,  # 15%
            min_detection_area=200,     # 200像素
            growth_rate_threshold=2.0   # 2%/秒
        )
        
        # 综合阈值
        self.thresholds[DetectionType.COMBINED] = AreaThresholdConfig(
            detection_type=DetectionType.COMBINED,
            warning_percentage=2.5,     # 2.5%
            critical_percentage=6.0,    # 6%
            emergency_percentage=12.0,  # 12%
            min_detection_area=150,     # 150像素
            growth_rate_threshold=1.5   # 1.5%/秒
        )
    
    def update_frame_size(self, width: int, height: int):
        """更新画面尺寸"""
        self.frame_width = width
        self.frame_height = height
        self.frame_area = width * height
        self.logger.debug(f"更新画面尺寸: {width}x{height}")
    
    def calculate_from_detections(self, detections: List[Dict], 
                                detection_type: DetectionType) -> AreaPercentageResult:
        """
        从检测结果计算面积占比
        
        Args:
            detections: 检测结果列表，每个元素包含 {bbox, confidence, class_name}
            detection_type: 检测类型
            
        Returns:
            AreaPercentageResult: 面积占比计算结果
        """
        import time
        
        try:
            # 过滤相关检测
            filtered_detections = self._filter_detections(detections, detection_type)
            
            if not filtered_detections:
                return self._create_empty_result(detection_type)
            
            # 计算各个目标的面积
            individual_areas = []
            individual_percentages = []
            bounding_boxes = []
            confidence_scores = []
            
            for detection in filtered_detections:
                bbox = detection.get('bbox', [0, 0, 0, 0])
                confidence = detection.get('confidence', 0.0)
                
                # 计算边界框面积
                if len(bbox) >= 4:
                    x, y, w, h = bbox[:4]
                    area = w * h
                    percentage = (area / self.frame_area) * 100
                    
                    individual_areas.append(area)
                    individual_percentages.append(percentage)
                    bounding_boxes.append((x, y, w, h))
                    confidence_scores.append(confidence)
            
            # 计算总面积和占比
            total_area = sum(individual_areas)
            total_percentage = (total_area / self.frame_area) * 100
            
            # 找到最大目标
            largest_area = max(individual_areas) if individual_areas else 0
            largest_percentage = max(individual_percentages) if individual_percentages else 0.0
            
            # 创建结果
            result = AreaPercentageResult(
                detection_type=detection_type,
                total_detections=len(filtered_detections),
                total_area_pixels=total_area,
                frame_area_pixels=self.frame_area,
                area_percentage=total_percentage,
                individual_areas=individual_areas,
                individual_percentages=individual_percentages,
                largest_area_pixels=largest_area,
                largest_percentage=largest_percentage,
                bounding_boxes=bounding_boxes,
                confidence_scores=confidence_scores,
                timestamp=time.time()
            )
            
            # 添加到历史记录
            self._add_to_history(result)
            
            return result
            
        except Exception as e:
            self.logger.error(f"计算面积占比失败: {e}")
            return self._create_empty_result(detection_type)
    
    def calculate_from_mask(self, mask: np.ndarray, 
                           detection_type: DetectionType) -> AreaPercentageResult:
        """
        从二值掩码计算面积占比
        
        Args:
            mask: 二值掩码
            detection_type: 检测类型
            
        Returns:
            AreaPercentageResult: 面积占比计算结果
        """
        import time
        
        try:
            # 计算掩码中的白色像素数量
            white_pixels = cv2.countNonZero(mask)
            total_pixels = mask.shape[0] * mask.shape[1]
            
            # 计算占比
            percentage = (white_pixels / total_pixels) * 100
            
            # 查找轮廓以获取边界框
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            individual_areas = []
            individual_percentages = []
            bounding_boxes = []
            
            for contour in contours:
                area = cv2.contourArea(contour)
                if area > 0:
                    x, y, w, h = cv2.boundingRect(contour)
                    contour_percentage = (area / total_pixels) * 100
                    
                    individual_areas.append(int(area))
                    individual_percentages.append(contour_percentage)
                    bounding_boxes.append((x, y, w, h))
            
            # 创建结果
            result = AreaPercentageResult(
                detection_type=detection_type,
                total_detections=len(contours),
                total_area_pixels=white_pixels,
                frame_area_pixels=total_pixels,
                area_percentage=percentage,
                individual_areas=individual_areas,
                individual_percentages=individual_percentages,
                largest_area_pixels=max(individual_areas) if individual_areas else 0,
                largest_percentage=max(individual_percentages) if individual_percentages else 0.0,
                bounding_boxes=bounding_boxes,
                confidence_scores=[1.0] * len(contours),  # 掩码默认置信度为1.0
                timestamp=time.time()
            )
            
            # 添加到历史记录
            self._add_to_history(result)
            
            return result
            
        except Exception as e:
            self.logger.error(f"从掩码计算面积占比失败: {e}")
            return self._create_empty_result(detection_type)
    
    def _filter_detections(self, detections: List[Dict], 
                          detection_type: DetectionType) -> List[Dict]:
        """过滤检测结果"""
        if detection_type == DetectionType.COMBINED:
            # 综合类型包含火焰和烟雾
            return [d for d in detections if d.get('class_name') in ['fire', 'flame', 'smoke']]
        elif detection_type == DetectionType.FLAME:
            return [d for d in detections if d.get('class_name') in ['fire', 'flame']]
        elif detection_type == DetectionType.SMOKE:
            return [d for d in detections if d.get('class_name') == 'smoke']
        else:
            return []
    
    def _create_empty_result(self, detection_type: DetectionType) -> AreaPercentageResult:
        """创建空结果"""
        import time
        
        return AreaPercentageResult(
            detection_type=detection_type,
            total_detections=0,
            total_area_pixels=0,
            frame_area_pixels=self.frame_area,
            area_percentage=0.0,
            individual_areas=[],
            individual_percentages=[],
            largest_area_pixels=0,
            largest_percentage=0.0,
            bounding_boxes=[],
            confidence_scores=[],
            timestamp=time.time()
        )
    
    def _add_to_history(self, result: AreaPercentageResult):
        """添加结果到历史记录"""
        history = self.history[result.detection_type]
        history.append(result)
        
        # 保持历史记录大小
        if len(history) > self.max_history_size:
            history.pop(0)
    
    def check_thresholds(self, result: AreaPercentageResult) -> Dict[str, bool]:
        """检查面积阈值"""
        threshold_config = self.thresholds.get(result.detection_type)
        if not threshold_config:
            return {}
        
        return {
            'warning_exceeded': result.area_percentage >= threshold_config.warning_percentage,
            'critical_exceeded': result.area_percentage >= threshold_config.critical_percentage,
            'emergency_exceeded': result.area_percentage >= threshold_config.emergency_percentage,
            'min_area_met': result.largest_area_pixels >= threshold_config.min_detection_area
        }
    
    def calculate_growth_rate(self, detection_type: DetectionType, 
                            time_window: float = 30.0) -> Optional[float]:
        """
        计算面积增长率
        
        Args:
            detection_type: 检测类型
            time_window: 时间窗口（秒）
            
        Returns:
            Optional[float]: 增长率（%/秒），None表示数据不足
        """
        history = self.history[detection_type]
        if len(history) < 2:
            return None
        
        try:
            # 获取时间窗口内的数据
            current_time = history[-1].timestamp
            window_data = [r for r in history if current_time - r.timestamp <= time_window]
            
            if len(window_data) < 2:
                return None
            
            # 计算线性回归斜率
            times = [r.timestamp - window_data[0].timestamp for r in window_data]
            percentages = [r.area_percentage for r in window_data]
            
            if len(times) < 2:
                return None
            
            # 简单线性回归
            n = len(times)
            sum_t = sum(times)
            sum_p = sum(percentages)
            sum_tp = sum(t * p for t, p in zip(times, percentages))
            sum_t2 = sum(t * t for t in times)
            
            if n * sum_t2 - sum_t * sum_t == 0:
                return None
            
            slope = (n * sum_tp - sum_t * sum_p) / (n * sum_t2 - sum_t * sum_t)
            return slope  # %/秒
            
        except Exception as e:
            self.logger.error(f"计算增长率失败: {e}")
            return None
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = {}
        
        for detection_type in DetectionType:
            history = self.history[detection_type]
            if history:
                latest = history[-1]
                stats[detection_type.value] = {
                    'history_count': len(history),
                    'latest_percentage': latest.area_percentage,
                    'latest_detections': latest.total_detections,
                    'max_percentage': max(r.area_percentage for r in history),
                    'avg_percentage': sum(r.area_percentage for r in history) / len(history)
                }
            else:
                stats[detection_type.value] = {
                    'history_count': 0,
                    'latest_percentage': 0.0,
                    'latest_detections': 0,
                    'max_percentage': 0.0,
                    'avg_percentage': 0.0
                }
        
        return stats
    
    def set_threshold(self, detection_type: DetectionType, warning: float, 
                     critical: float, emergency: float):
        """设置面积阈值"""
        if detection_type in self.thresholds:
            config = self.thresholds[detection_type]
            config.warning_percentage = warning
            config.critical_percentage = critical
            config.emergency_percentage = emergency
            
            self.logger.info(f"更新 {detection_type.value} 面积阈值: "
                           f"警告={warning}%, 严重={critical}%, 紧急={emergency}%")
