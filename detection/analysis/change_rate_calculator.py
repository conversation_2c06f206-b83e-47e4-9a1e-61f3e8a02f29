#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
变化率计算器
计算热源的面积变化率、温度变化率等时序指标
"""

import numpy as np
from typing import List, Dict, Optional, Tuple, Any
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum

from .heat_source_history import TimeSeriesDataPoint, HeatSourceHistory


class ChangeRateType(Enum):
    """变化率类型"""
    ABSOLUTE = "absolute"  # 绝对变化率
    RELATIVE = "relative"  # 相对变化率（百分比）
    DERIVATIVE = "derivative"  # 导数（变化速度）


@dataclass
class ChangeRateResult:
    """变化率计算结果"""
    tracking_id: int
    data_type: str  # 'area', 'max_temp', 'min_temp', 'avg_temp'
    time_window_seconds: float
    change_rate: float
    change_rate_type: ChangeRateType
    confidence: float
    sample_count: int
    start_value: Optional[float] = None
    end_value: Optional[float] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    
    def get_formatted_rate(self) -> str:
        """获取格式化的变化率字符串"""
        if self.change_rate_type == ChangeRateType.RELATIVE:
            # 计算每秒的变化率
            if self.time_window_seconds and self.time_window_seconds > 0:
                rate_per_second = self.change_rate / self.time_window_seconds
                return f"{rate_per_second:.3f}%/秒"
            else:
                return f"{self.change_rate:.2f}%"
        elif self.change_rate_type == ChangeRateType.DERIVATIVE:
            unit_map = {
                'area': '像素²/秒',
                'max_temp': '°C/秒',
                'min_temp': '°C/秒',
                'avg_temp': '°C/秒'
            }
            unit = unit_map.get(self.data_type, '/秒')
            return f"{self.change_rate:.4f} {unit}"
        else:  # ABSOLUTE
            unit_map = {
                'area': '像素²',
                'max_temp': '°C',
                'min_temp': '°C',
                'avg_temp': '°C'
            }
            unit = unit_map.get(self.data_type, '')
            return f"{self.change_rate:.2f} {unit}"


class ChangeRateCalculator:
    """变化率计算器"""
    
    def __init__(self, min_sample_count: int = 2, confidence_threshold: float = 0.35):
        """
        初始化变化率计算器

        Args:
            min_sample_count: 最小样本数量（优化：从3降到2）
            confidence_threshold: 置信度阈值（设置为0.35，中等置信度水平）
        """
        self.min_sample_count = min_sample_count
        self.confidence_threshold = confidence_threshold
    
    def calculate_change_rate(self, data_points: List[TimeSeriesDataPoint],
                            change_rate_type: ChangeRateType = ChangeRateType.RELATIVE,
                            time_window_seconds: Optional[float] = None) -> Optional[ChangeRateResult]:
        """
        计算变化率
        
        Args:
            data_points: 时序数据点列表
            change_rate_type: 变化率类型
            time_window_seconds: 时间窗口（如果指定，则只使用该窗口内的数据）
        """
        if len(data_points) < self.min_sample_count:
            return None
        
        # 按时间排序
        sorted_points = sorted(data_points, key=lambda p: p.timestamp)
        
        # 如果指定了时间窗口，过滤数据
        if time_window_seconds is not None:
            end_time = sorted_points[-1].timestamp
            start_time = end_time - timedelta(seconds=time_window_seconds)
            sorted_points = [p for p in sorted_points if p.timestamp >= start_time]
            
            if len(sorted_points) < self.min_sample_count:
                return None
        
        # 提取值和时间
        values = [p.value for p in sorted_points]
        timestamps = [p.timestamp for p in sorted_points]
        
        # 计算时间差（秒）
        time_diffs = [(timestamps[i] - timestamps[0]).total_seconds() 
                     for i in range(len(timestamps))]
        
        start_value = values[0]
        end_value = values[-1]
        total_time = time_diffs[-1]
        
        if total_time == 0:
            return None
        
        # 计算变化率
        if change_rate_type == ChangeRateType.ABSOLUTE:
            change_rate = end_value - start_value
        elif change_rate_type == ChangeRateType.RELATIVE:
            if start_value == 0:
                change_rate = 0.0 if end_value == 0 else float('inf')
            else:
                change_rate = ((end_value - start_value) / start_value) * 100
        else:  # DERIVATIVE
            change_rate = (end_value - start_value) / total_time
        
        # 计算置信度（重新设计：更合理的置信度计算）
        # 数据点置信度：2个点=0.6, 3个点=0.8, 4个点=0.9, 5个点=1.0
        if len(sorted_points) >= 5:
            data_confidence = 1.0
        elif len(sorted_points) >= 4:
            data_confidence = 0.9
        elif len(sorted_points) >= 3:
            data_confidence = 0.8
        elif len(sorted_points) >= 2:
            data_confidence = 0.6
        else:
            data_confidence = 0.3

        # 时间置信度：3秒=0.6, 5秒=0.8, 10秒=1.0
        if total_time >= 10.0:
            time_confidence = 1.0
        elif total_time >= 5.0:
            time_confidence = 0.8
        elif total_time >= 3.0:
            time_confidence = 0.6
        else:
            time_confidence = 0.4

        # 使用加权平均而非乘法，避免过度惩罚
        confidence = 0.6 * data_confidence + 0.4 * time_confidence
        
        return ChangeRateResult(
            tracking_id=0,  # 将在外部设置
            data_type="unknown",  # 将在外部设置
            time_window_seconds=time_window_seconds or total_time,
            change_rate=change_rate,
            change_rate_type=change_rate_type,
            confidence=confidence,
            sample_count=len(sorted_points),
            start_value=start_value,
            end_value=end_value,
            start_time=timestamps[0],
            end_time=timestamps[-1]
        )
    
    def calculate_linear_trend(self, data_points: List[TimeSeriesDataPoint]) -> Optional[Tuple[float, float]]:
        """
        计算线性趋势（斜率和截距）
        
        Returns:
            Tuple[slope, intercept] 或 None
        """
        if len(data_points) < 2:
            return None
        
        # 按时间排序
        sorted_points = sorted(data_points, key=lambda p: p.timestamp)
        
        # 转换为数值数组
        base_time = sorted_points[0].timestamp
        x = np.array([(p.timestamp - base_time).total_seconds() for p in sorted_points])
        y = np.array([p.value for p in sorted_points])
        
        # 计算线性回归
        try:
            slope, intercept = np.polyfit(x, y, 1)
            return float(slope), float(intercept)
        except np.linalg.LinAlgError:
            return None
    
    def calculate_moving_average(self, data_points: List[TimeSeriesDataPoint],
                               window_size: int = 5) -> List[TimeSeriesDataPoint]:
        """计算移动平均"""
        if len(data_points) < window_size:
            return data_points
        
        sorted_points = sorted(data_points, key=lambda p: p.timestamp)
        smoothed_points = []
        
        for i in range(len(sorted_points)):
            start_idx = max(0, i - window_size // 2)
            end_idx = min(len(sorted_points), i + window_size // 2 + 1)
            
            window_values = [sorted_points[j].value for j in range(start_idx, end_idx)]
            avg_value = np.mean(window_values)
            
            smoothed_point = TimeSeriesDataPoint(
                timestamp=sorted_points[i].timestamp,
                value=avg_value,
                metadata={'original_value': sorted_points[i].value, 'window_size': len(window_values)}
            )
            smoothed_points.append(smoothed_point)
        
        return smoothed_points
    
    def calculate_all_change_rates(self, history: HeatSourceHistory, tracking_id: int,
                                 time_windows: List[float] = None) -> Dict[str, List[ChangeRateResult]]:
        """
        计算指定热源的所有变化率
        
        Args:
            history: 历史数据管理器
            tracking_id: 热源跟踪ID
            time_windows: 时间窗口列表（秒），默认为[10, 30, 60, 300]
        
        Returns:
            Dict[data_type, List[ChangeRateResult]]
        """
        if time_windows is None:
            time_windows = [5.0, 10.0, 30.0, 60.0]  # 优化：添加5秒窗口，移除300秒窗口
        
        data_types = ['area', 'max_temp', 'min_temp', 'avg_temp']
        results = {}
        
        for data_type in data_types:
            results[data_type] = []
            
            for window_seconds in time_windows:
                # 获取时序数据
                data_points = history.get_time_series(tracking_id, data_type, window_seconds)
                
                if len(data_points) < self.min_sample_count:
                    continue
                
                # 计算相对变化率
                relative_result = self.calculate_change_rate(
                    data_points, ChangeRateType.RELATIVE, window_seconds
                )
                
                if relative_result and relative_result.confidence >= self.confidence_threshold:
                    relative_result.tracking_id = tracking_id
                    relative_result.data_type = data_type
                    results[data_type].append(relative_result)
                
                # 计算导数（变化速度）
                derivative_result = self.calculate_change_rate(
                    data_points, ChangeRateType.DERIVATIVE, window_seconds
                )
                
                if derivative_result and derivative_result.confidence >= self.confidence_threshold:
                    derivative_result.tracking_id = tracking_id
                    derivative_result.data_type = data_type
                    results[data_type].append(derivative_result)
        
        return results
    
    def get_latest_change_rates(self, history: HeatSourceHistory, tracking_id: int,
                              window_seconds: float = 5.0) -> Dict[str, ChangeRateResult]:
        """
        获取最新的变化率（每种数据类型一个）
        
        Returns:
            Dict[data_type, ChangeRateResult]
        """
        data_types = ['area', 'max_temp', 'min_temp', 'avg_temp']
        latest_rates = {}
        
        for data_type in data_types:
            data_points = history.get_time_series(tracking_id, data_type, window_seconds)

            # 简化调试信息输出，保持后台简洁
            # if tracking_id == 1:  # 只对第一个热源输出调试信息，避免日志过多
            #     print(f"🔍 跟踪ID {tracking_id}, 数据类型 {data_type}: {len(data_points)} 个数据点")

            if len(data_points) >= self.min_sample_count:
                result = self.calculate_change_rate(
                    data_points, ChangeRateType.RELATIVE, window_seconds
                )

                if result and result.confidence >= self.confidence_threshold:
                    result.tracking_id = tracking_id
                    result.data_type = data_type
                    latest_rates[data_type] = result
                    # 简化调试信息输出，保持后台简洁
                    # if tracking_id == 1:
                    #     print(f"   ✅ {data_type} 变化率: {result.change_rate:.2f}%, 置信度: {result.confidence:.2f}")
                elif result:
                    # if tracking_id == 1:
                    #     print(f"   ❌ {data_type} 置信度不足: {result.confidence:.2f} < {self.confidence_threshold}")
                    pass
            else:
                # if tracking_id == 1:
                #     print(f"   ❌ {data_type} 数据点不足: {len(data_points)} < {self.min_sample_count}")
                pass
        
        return latest_rates
    
    def detect_anomalies(self, data_points: List[TimeSeriesDataPoint],
                        threshold_std: float = 2.0) -> List[int]:
        """
        检测异常值（基于标准差）
        
        Args:
            data_points: 时序数据点
            threshold_std: 标准差阈值
            
        Returns:
            异常值的索引列表
        """
        if len(data_points) < 3:
            return []
        
        values = [p.value for p in data_points]
        mean_val = np.mean(values)
        std_val = np.std(values)
        
        if std_val == 0:
            return []
        
        anomalies = []
        for i, value in enumerate(values):
            z_score = abs(value - mean_val) / std_val
            if z_score > threshold_std:
                anomalies.append(i)
        
        return anomalies
