#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
综合指标管理器
整合全局指标和单个对象指标的计算、存储和查询功能
"""

import threading
from typing import List, Dict, Optional
from datetime import datetime, timedelta

from .heat_source_history import HeatSourceSnapshot
from .global_metrics_calculator import GlobalMetricsCalculator, GlobalMetrics, get_global_metrics_calculator
from .object_detailed_metrics_calculator import ObjectDetailedMetricsCalculator, ObjectDetailedMetrics, get_object_metrics_calculator

from .detection_count_manager import get_detection_count_manager


class ComprehensiveMetricsManager:
    """综合指标管理器"""
    
    def __init__(self):
        """
        初始化综合指标管理器
        """

        # 初始化计算器
        self.global_calculator = get_global_metrics_calculator()
        self.object_calculator = get_object_metrics_calculator()

        # 初始化检测计数管理器
        self.detection_count_manager = get_detection_count_manager()



        self.logger = self._get_logger()
    
    def _get_logger(self):
        """获取日志记录器"""
        try:
            from utils.logger import get_logger
            return get_logger(__name__)
        except ImportError:
            import logging
            return logging.getLogger(__name__)
    
    def process_snapshots(self, snapshots: List[HeatSourceSnapshot]):
        """
        处理热源快照，计算并保存所有指标
        
        Args:
            snapshots: 当前时刻的所有热源快照
        """
        try:
            # 1. 获取当前检测计数
            human_count, fire_count, smoke_count = self.detection_count_manager.get_counts_tuple()

            # 2. 计算全局指标（包含检测计数）
            global_metrics = self.global_calculator.calculate_global_metrics(
                snapshots, human_count, fire_count, smoke_count
            )

            # 3. 计算每个对象的详细指标
            object_metrics_list = []
            for snapshot in snapshots:
                object_metrics = self.object_calculator.calculate_object_metrics(snapshot)
                object_metrics_list.append(object_metrics)


            
            self.logger.debug(f"📊 处理了 {len(snapshots)} 个快照的指标计算")
            
        except Exception as e:
            self.logger.error(f"❌ 处理快照指标失败: {e}")
    

    
    def get_latest_global_metrics(self) -> Optional[GlobalMetrics]:
        """获取最新的全局指标"""
        try:
            recent_metrics = self.global_calculator.get_recent_metrics(count=1)
            return recent_metrics[0] if recent_metrics else None
        except Exception as e:
            self.logger.error(f"❌ 获取最新全局指标失败: {e}")
            return None
    
    def get_latest_object_metrics(self, tracking_id: int) -> Optional[ObjectDetailedMetrics]:
        """获取指定对象的最新详细指标"""
        try:
            return self.object_calculator.get_latest_object_metrics(tracking_id)
        except Exception as e:
            self.logger.error(f"❌ 获取最新对象指标失败: {e}")
            return None
    
    def get_all_active_objects_metrics(self, hours: float = 1.0) -> Dict[int, ObjectDetailedMetrics]:
        """获取所有活跃对象的最新指标"""
        try:
            return self.object_calculator.get_all_active_objects_metrics(hours)
        except Exception as e:
            self.logger.error(f"❌ 获取活跃对象指标失败: {e}")
            return {}
    
    def get_global_metrics_summary(self) -> Dict:
        """获取全局指标摘要"""
        try:
            return self.global_calculator.get_metrics_summary()
        except Exception as e:
            self.logger.error(f"❌ 获取全局指标摘要失败: {e}")
            return {}
    
    def get_object_metrics_summary(self, tracking_id: int) -> Dict:
        """获取指定对象的指标摘要"""
        try:
            return self.object_calculator.get_metrics_summary(tracking_id)
        except Exception as e:
            self.logger.error(f"❌ 获取对象指标摘要失败: {e}")
            return {}
    
    def get_comprehensive_report(self) -> Dict:
        """获取综合指标报告"""
        try:
            # 获取最新全局指标
            latest_global = self.get_latest_global_metrics()
            
            # 获取所有活跃对象指标
            active_objects = self.get_all_active_objects_metrics()
            
            # 获取全局指标摘要
            global_summary = self.get_global_metrics_summary()
            
            # 构建综合报告
            report = {
                'timestamp': datetime.now().isoformat(),
                'global_metrics': {
                    'latest': {
                        'timestamp': latest_global.timestamp.isoformat() if latest_global else None,
                        'total_heat_sources': latest_global.total_heat_sources if latest_global else 0,
                        'total_heat_sources_change_rate': latest_global.total_heat_sources_change_rate if latest_global else None,
                        'total_heat_area': latest_global.total_heat_area if latest_global else 0,
                        'total_heat_area_change_rate': latest_global.total_heat_area_change_rate if latest_global else None,
                        'avg_temperature_30days': latest_global.avg_temperature_30days if latest_global else None,
                        'human_count': latest_global.human_count if latest_global else 0,
                        'fire_count': latest_global.fire_count if latest_global else 0,
                        'smoke_count': latest_global.smoke_count if latest_global else 0
                    },
                    'summary': global_summary
                },
                'object_metrics': {
                    'active_objects_count': len(active_objects),
                    'active_objects': {}
                }
            }
            
            # 添加每个活跃对象的详细信息
            for tracking_id, metrics in active_objects.items():
                report['object_metrics']['active_objects'][tracking_id] = {
                    'timestamp': metrics.timestamp.isoformat(),
                    'dimensions': {
                        'length_pixels': metrics.length_pixels,
                        'length_change_rate': metrics.length_change_rate,
                        'width_pixels': metrics.width_pixels,
                        'width_change_rate': metrics.width_change_rate
                    },
                    'area': {
                        'area_pixels': metrics.area_pixels,
                        'area_change_rate': metrics.area_change_rate
                    },
                    'temperature': {
                        'min_temperature': metrics.min_temperature,
                        'min_temp_change_rate': metrics.min_temp_change_rate,
                        'max_temperature': metrics.max_temperature,
                        'max_temp_change_rate': metrics.max_temp_change_rate,
                        'avg_temperature': metrics.avg_temperature,
                        'avg_temp_change_rate': metrics.avg_temp_change_rate,
                        'temperature_diff': metrics.temperature_diff,
                        'temp_diff_change_rate': metrics.temp_diff_change_rate
                    }
                }
            
            return report
            
        except Exception as e:
            self.logger.error(f"❌ 获取综合指标报告失败: {e}")
            return {}
    

    
    def export_metrics_to_json(self, filepath: str, 
                              start_time: Optional[datetime] = None,
                              end_time: Optional[datetime] = None) -> bool:
        """导出指标数据到JSON文件"""
        try:
            import json
            import os
            
            # 获取全局指标数据（使用内存数据）
            global_metrics_data = []

            # 获取活跃对象列表
            active_objects = self.get_all_active_objects_metrics()

            # 获取每个对象的详细指标数据
            object_metrics_data = {}
            
            # 构建导出数据
            export_data = {
                'export_info': {
                    'timestamp': datetime.now().isoformat(),
                    'start_time': start_time.isoformat() if start_time else None,
                    'end_time': end_time.isoformat() if end_time else None,
                    'data_source': 'comprehensive_metrics_manager'
                },
                'global_metrics': global_metrics_data,
                'object_metrics': object_metrics_data,
                'summary': {
                    'global_metrics_count': len(global_metrics_data),
                    'active_objects_count': len(active_objects),
                    'total_object_metrics': sum(len(metrics) for metrics in object_metrics_data.values())
                }
            }
            
            # 确保目录存在
            os.makedirs(os.path.dirname(filepath), exist_ok=True)
            
            # 写入文件
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False, default=str)
            
            self.logger.info(f"✅ 指标数据导出成功: {filepath}")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 指标数据导出失败: {e}")
            return False
    
    def cleanup_old_data(self, days: int = 7):
        """清理过期数据"""
        try:
            # 清理计算器中的过期数据
            self.global_calculator.clear_old_data(days)
            self.object_calculator.clear_inactive_objects(hours=days*24)
            

            
        except Exception as e:
            self.logger.error(f"❌ 清理过期数据失败: {e}")


# 全局实例
_comprehensive_metrics_manager: Optional[ComprehensiveMetricsManager] = None


def get_comprehensive_metrics_manager() -> ComprehensiveMetricsManager:
    """获取综合指标管理器实例（单例模式）"""
    global _comprehensive_metrics_manager
    
    if _comprehensive_metrics_manager is None:
        _comprehensive_metrics_manager = ComprehensiveMetricsManager()
    
    return _comprehensive_metrics_manager
