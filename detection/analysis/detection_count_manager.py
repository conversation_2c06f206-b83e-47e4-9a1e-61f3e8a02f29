#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检测计数管理器
管理人体、火焰、烟雾的实时检测计数
"""

import threading
import time
from typing import Dict, List, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass


@dataclass
class DetectionCounts:
    """检测计数数据类"""
    timestamp: datetime
    human_count: int = 0
    fire_count: int = 0
    smoke_count: int = 0
    fire_area: float = 0.0  # 火焰总面积（像素²）
    smoke_area: float = 0.0  # 烟雾总面积（像素²）

    def to_dict(self) -> Dict:
        """转换为字典格式"""
        return {
            'timestamp': self.timestamp.isoformat(),
            'human_count': self.human_count,
            'fire_count': self.fire_count,
            'smoke_count': self.smoke_count,
            'fire_area': self.fire_area,
            'smoke_area': self.smoke_area
        }


class DetectionCountManager:
    """检测计数管理器"""
    
    def __init__(self):
        """初始化检测计数管理器"""
        self.lock = threading.Lock()
        self.current_counts = DetectionCounts(timestamp=datetime.now())
        self.logger = self._get_logger()
        
        # 历史计数记录（用于统计分析）
        self.count_history: List[DetectionCounts] = []
        self.max_history_size = 1000
        
    def _get_logger(self):
        """获取日志记录器"""
        try:
            from utils.logger import get_logger
            return get_logger(__name__)
        except ImportError:
            import logging
            return logging.getLogger(__name__)
    
    def update_human_count(self, count: int):
        """更新人体检测计数"""
        with self.lock:
            self.current_counts.human_count = count
            self.current_counts.timestamp = datetime.now()
            self.logger.debug(f"更新人体计数: {count}")
    
    def update_fire_count(self, count: int):
        """更新火焰检测计数"""
        with self.lock:
            self.current_counts.fire_count = count
            self.current_counts.timestamp = datetime.now()
            self.logger.debug(f"更新火焰计数: {count}")
    
    def update_smoke_count(self, count: int):
        """更新烟雾检测计数"""
        with self.lock:
            self.current_counts.smoke_count = count
            self.current_counts.timestamp = datetime.now()
            self.logger.debug(f"更新烟雾计数: {count}")
    
    def update_fire_smoke_counts(self, fire_count: int, smoke_count: int):
        """同时更新火焰和烟雾检测计数"""
        with self.lock:
            self.current_counts.fire_count = fire_count
            self.current_counts.smoke_count = smoke_count
            self.current_counts.timestamp = datetime.now()
            self.logger.debug(f"更新火焰烟雾计数: 火焰={fire_count}, 烟雾={smoke_count}")

    def update_fire_smoke_areas(self, fire_area: float, smoke_area: float):
        """同时更新火焰和烟雾检测面积"""
        with self.lock:
            self.current_counts.fire_area = fire_area
            self.current_counts.smoke_area = smoke_area
            self.current_counts.timestamp = datetime.now()
            self.logger.debug(f"更新火焰烟雾面积: 火焰={fire_area:.0f}像素², 烟雾={smoke_area:.0f}像素²")
    
    def update_all_counts(self, human_count: int, fire_count: int, smoke_count: int):
        """同时更新所有检测计数"""
        with self.lock:
            self.current_counts.human_count = human_count
            self.current_counts.fire_count = fire_count
            self.current_counts.smoke_count = smoke_count
            self.current_counts.timestamp = datetime.now()
            self.logger.debug(f"更新所有计数: 人体={human_count}, 火焰={fire_count}, 烟雾={smoke_count}")
    
    def get_current_counts(self) -> DetectionCounts:
        """获取当前检测计数"""
        with self.lock:
            return DetectionCounts(
                timestamp=self.current_counts.timestamp,
                human_count=self.current_counts.human_count,
                fire_count=self.current_counts.fire_count,
                smoke_count=self.current_counts.smoke_count,
                fire_area=self.current_counts.fire_area,
                smoke_area=self.current_counts.smoke_area
            )
    
    def get_counts_tuple(self) -> tuple:
        """获取计数元组 (human_count, fire_count, smoke_count)"""
        with self.lock:
            return (
                self.current_counts.human_count,
                self.current_counts.fire_count,
                self.current_counts.smoke_count
            )
    
    def save_current_to_history(self):
        """将当前计数保存到历史记录"""
        with self.lock:
            # 创建当前计数的副本
            current_copy = DetectionCounts(
                timestamp=self.current_counts.timestamp,
                human_count=self.current_counts.human_count,
                fire_count=self.current_counts.fire_count,
                smoke_count=self.current_counts.smoke_count,
                fire_area=self.current_counts.fire_area,
                smoke_area=self.current_counts.smoke_area
            )
            
            # 添加到历史记录
            self.count_history.append(current_copy)
            
            # 限制历史记录大小
            if len(self.count_history) > self.max_history_size:
                self.count_history.pop(0)
            
            self.logger.debug(f"保存计数到历史记录: {current_copy.to_dict()}")
    
    def get_recent_history(self, count: int = 10) -> List[DetectionCounts]:
        """获取最近的历史计数记录"""
        with self.lock:
            return self.count_history[-count:] if self.count_history else []
    
    def get_change_rates(self) -> Dict[str, float]:
        """计算各类检测的变化率"""
        with self.lock:
            if len(self.count_history) < 1:
                self.logger.debug("📊 变化率计算: 历史数据不足")
                return {
                    'fire_count_change_rate': 0.0,
                    'smoke_count_change_rate': 0.0,
                    'human_count_change_rate': 0.0,
                    'fire_area_change_rate': 0.0,
                    'smoke_area_change_rate': 0.0
                }

            # 获取当前值和历史记录中的最后一个值
            current = self.current_counts
            previous = self.count_history[-1]

            # 计算时间差（秒）
            time_diff = (current.timestamp - previous.timestamp).total_seconds()
            if time_diff <= 0:
                time_diff = 1.0  # 避免除零错误

            # 添加调试信息
            self.logger.debug(f"📊 变化率计算:")
            self.logger.debug(f"   历史记录数: {len(self.count_history)}")
            self.logger.debug(f"   时间差: {time_diff:.2f}秒")
            self.logger.debug(f"   当前计数: 火焰={current.fire_count}, 烟雾={current.smoke_count}")
            self.logger.debug(f"   历史计数: 火焰={previous.fire_count}, 烟雾={previous.smoke_count}")
            self.logger.debug(f"   当前面积: 火焰={current.fire_area:.0f}, 烟雾={current.smoke_area:.0f}")
            self.logger.debug(f"   历史面积: 火焰={previous.fire_area:.0f}, 烟雾={previous.smoke_area:.0f}")

            # 计算变化率（每秒变化量）
            def calculate_absolute_rate(current_val, previous_val):
                """计算绝对变化率（单位/秒）"""
                rate = (current_val - previous_val) / time_diff
                return rate

            def calculate_relative_rate(current_val, previous_val):
                """计算相对变化率（%/秒）"""
                if previous_val == 0:
                    rate = 100.0 / time_diff if current_val > 0 else 0.0
                else:
                    rate = ((current_val - previous_val) / previous_val) * 100.0 / time_diff
                return rate

            rates = {
                'fire_count_change_rate': calculate_relative_rate(current.fire_count, previous.fire_count),
                'smoke_count_change_rate': calculate_relative_rate(current.smoke_count, previous.smoke_count),
                'human_count_change_rate': calculate_relative_rate(current.human_count, previous.human_count),
                'fire_area_change_rate': calculate_absolute_rate(current.fire_area, previous.fire_area),
                'smoke_area_change_rate': calculate_absolute_rate(current.smoke_area, previous.smoke_area)
            }

            # 输出计算结果
            self.logger.debug(f"   计算结果:")
            self.logger.debug(f"     火焰计数变化率: {rates['fire_count_change_rate']:.3f}%/秒")
            self.logger.debug(f"     烟雾计数变化率: {rates['smoke_count_change_rate']:.3f}%/秒")
            self.logger.debug(f"     火焰面积变化率: {rates['fire_area_change_rate']:.1f}像素²/秒")
            self.logger.debug(f"     烟雾面积变化率: {rates['smoke_area_change_rate']:.1f}像素²/秒")

            return rates

    def get_statistics(self) -> Dict:
        """获取检测计数统计信息"""
        with self.lock:
            current = self.current_counts
            change_rates = self.get_change_rates()

            stats = {
                'current_counts': current.to_dict(),
                'history_size': len(self.count_history),
                'change_rates': change_rates,
                'total_detections': {
                    'human': current.human_count,
                    'fire': current.fire_count,
                    'smoke': current.smoke_count,
                    'total': current.human_count + current.fire_count + current.smoke_count
                }
            }
            
            # 如果有历史数据，计算一些统计信息
            if self.count_history:
                recent_history = self.count_history[-10:]  # 最近10条记录
                
                avg_human = sum(h.human_count for h in recent_history) / len(recent_history)
                avg_fire = sum(h.fire_count for h in recent_history) / len(recent_history)
                avg_smoke = sum(h.smoke_count for h in recent_history) / len(recent_history)
                
                stats['recent_averages'] = {
                    'human_avg': round(avg_human, 2),
                    'fire_avg': round(avg_fire, 2),
                    'smoke_avg': round(avg_smoke, 2)
                }
            
            return stats
    
    def reset_counts(self):
        """重置所有计数"""
        with self.lock:
            self.current_counts = DetectionCounts(timestamp=datetime.now())
            self.logger.info("重置所有检测计数")
    
    def clear_history(self):
        """清空历史记录"""
        with self.lock:
            self.count_history.clear()
            self.logger.info("清空检测计数历史记录")
    
    def cleanup_old_history(self, hours: int = 24):
        """清理过期的历史记录"""
        with self.lock:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            
            # 过滤掉过期的记录
            self.count_history = [
                record for record in self.count_history
                if record.timestamp >= cutoff_time
            ]
            
            self.logger.info(f"清理了{hours}小时前的检测计数历史记录")


# 全局实例
_detection_count_manager: Optional[DetectionCountManager] = None
_manager_lock = threading.Lock()


def get_detection_count_manager() -> DetectionCountManager:
    """获取检测计数管理器实例（单例模式）"""
    global _detection_count_manager
    
    if _detection_count_manager is None:
        with _manager_lock:
            if _detection_count_manager is None:
                _detection_count_manager = DetectionCountManager()
    
    return _detection_count_manager


if __name__ == "__main__":
    # 测试代码
    print("🔧 测试检测计数管理器...")
    
    manager = get_detection_count_manager()
    
    # 测试更新计数
    manager.update_all_counts(5, 2, 1)
    print(f"当前计数: {manager.get_current_counts().to_dict()}")
    
    # 测试保存到历史
    manager.save_current_to_history()
    
    # 测试统计信息
    stats = manager.get_statistics()
    print(f"统计信息: {stats}")
    
    print("✅ 检测计数管理器测试通过")
