#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
持续时间监控器
监控温度变化、火焰/烟雾检测的持续时间，支持时间阈值判断
"""

import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum

from utils.logger import get_logger


class MonitorType(Enum):
    """监控类型枚举"""
    TEMPERATURE_RISE = "temperature_rise"      # 温度上升
    TEMPERATURE_DROP = "temperature_drop"      # 温度下降
    FLAME_DETECTION = "flame_detection"        # 火焰检测
    SMOKE_DETECTION = "smoke_detection"        # 烟雾检测
    COMBINED_DETECTION = "combined_detection"  # 综合检测


@dataclass
class DurationEvent:
    """持续时间事件"""
    event_id: str                    # 事件ID
    monitor_type: MonitorType        # 监控类型
    start_time: datetime             # 开始时间
    last_update: datetime            # 最后更新时间
    current_value: float             # 当前值
    initial_value: float             # 初始值
    threshold_value: float           # 阈值
    is_active: bool = True           # 是否活跃
    metadata: Dict[str, Any] = None  # 额外元数据
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}
    
    @property
    def duration(self) -> float:
        """获取持续时间（秒）"""
        return (self.last_update - self.start_time).total_seconds()
    
    @property
    def value_change(self) -> float:
        """获取值变化量"""
        return self.current_value - self.initial_value


@dataclass
class DurationThreshold:
    """持续时间阈值配置"""
    monitor_type: MonitorType
    min_duration: float              # 最小持续时间（秒）
    max_duration: Optional[float]    # 最大持续时间（秒），None表示无限制
    value_threshold: float           # 值阈值
    tolerance: float = 0.1           # 容忍度


class DurationMonitor:
    """持续时间监控器"""
    
    def __init__(self, cleanup_interval: float = 300.0):
        """
        初始化持续时间监控器
        
        Args:
            cleanup_interval: 清理间隔（秒）
        """
        self.logger = get_logger("DurationMonitor")
        
        # 活跃事件
        self.active_events: Dict[str, DurationEvent] = {}
        
        # 已完成事件历史
        self.completed_events: List[DurationEvent] = []
        
        # 阈值配置
        self.thresholds: Dict[MonitorType, DurationThreshold] = {}
        
        # 清理配置
        self.cleanup_interval = cleanup_interval
        self.last_cleanup = time.time()
        self.max_history_size = 1000
        
        # 统计信息
        self.total_events = 0
        self.threshold_exceeded_count = 0
        
        # 初始化默认阈值
        self._setup_default_thresholds()
    
    def _setup_default_thresholds(self):
        """设置默认阈值配置"""
        # 温度上升阈值
        self.thresholds[MonitorType.TEMPERATURE_RISE] = DurationThreshold(
            monitor_type=MonitorType.TEMPERATURE_RISE,
            min_duration=30.0,      # 30秒
            max_duration=None,
            value_threshold=3.0,    # 3°C
            tolerance=0.5
        )
        
        # 火焰检测阈值
        self.thresholds[MonitorType.FLAME_DETECTION] = DurationThreshold(
            monitor_type=MonitorType.FLAME_DETECTION,
            min_duration=10.0,      # 10秒
            max_duration=None,
            value_threshold=0.5,    # 置信度0.5
            tolerance=0.1
        )
        
        # 烟雾检测阈值
        self.thresholds[MonitorType.SMOKE_DETECTION] = DurationThreshold(
            monitor_type=MonitorType.SMOKE_DETECTION,
            min_duration=20.0,      # 20秒
            max_duration=None,
            value_threshold=0.4,    # 置信度0.4
            tolerance=0.1
        )
        
        # 综合检测阈值
        self.thresholds[MonitorType.COMBINED_DETECTION] = DurationThreshold(
            monitor_type=MonitorType.COMBINED_DETECTION,
            min_duration=15.0,      # 15秒
            max_duration=None,
            value_threshold=0.6,    # 综合置信度0.6
            tolerance=0.1
        )
    
    def start_monitoring(self, event_id: str, monitor_type: MonitorType, 
                        initial_value: float, metadata: Dict[str, Any] = None) -> bool:
        """
        开始监控事件
        
        Args:
            event_id: 事件唯一标识
            monitor_type: 监控类型
            initial_value: 初始值
            metadata: 额外元数据
            
        Returns:
            bool: 是否成功开始监控
        """
        try:
            # 检查是否已存在
            if event_id in self.active_events:
                self.logger.warning(f"事件 {event_id} 已在监控中")
                return False
            
            # 获取阈值配置
            threshold_config = self.thresholds.get(monitor_type)
            if not threshold_config:
                self.logger.error(f"未找到监控类型 {monitor_type} 的阈值配置")
                return False
            
            # 创建事件
            current_time = datetime.now()
            event = DurationEvent(
                event_id=event_id,
                monitor_type=monitor_type,
                start_time=current_time,
                last_update=current_time,
                current_value=initial_value,
                initial_value=initial_value,
                threshold_value=threshold_config.value_threshold,
                metadata=metadata or {}
            )
            
            # 添加到活跃事件
            self.active_events[event_id] = event
            self.total_events += 1
            
            self.logger.debug(f"开始监控事件: {event_id} ({monitor_type.value})")
            return True
            
        except Exception as e:
            self.logger.error(f"开始监控事件失败: {e}")
            return False
    
    def update_event(self, event_id: str, current_value: float, 
                    metadata: Dict[str, Any] = None) -> bool:
        """
        更新事件状态
        
        Args:
            event_id: 事件ID
            current_value: 当前值
            metadata: 额外元数据
            
        Returns:
            bool: 是否成功更新
        """
        try:
            event = self.active_events.get(event_id)
            if not event:
                return False
            
            # 更新事件
            event.current_value = current_value
            event.last_update = datetime.now()
            
            # 更新元数据
            if metadata:
                event.metadata.update(metadata)
            
            return True
            
        except Exception as e:
            self.logger.error(f"更新事件失败: {e}")
            return False
    
    def stop_monitoring(self, event_id: str, reason: str = "manual") -> bool:
        """
        停止监控事件
        
        Args:
            event_id: 事件ID
            reason: 停止原因
            
        Returns:
            bool: 是否成功停止
        """
        try:
            event = self.active_events.get(event_id)
            if not event:
                return False
            
            # 标记为非活跃
            event.is_active = False
            event.metadata['stop_reason'] = reason
            event.metadata['stop_time'] = datetime.now()
            
            # 移动到已完成事件
            self.completed_events.append(event)
            del self.active_events[event_id]
            
            self.logger.debug(f"停止监控事件: {event_id} (原因: {reason})")
            return True
            
        except Exception as e:
            self.logger.error(f"停止监控事件失败: {e}")
            return False
    
    def check_thresholds(self) -> List[Tuple[str, DurationEvent, bool]]:
        """
        检查所有活跃事件的阈值
        
        Returns:
            List[Tuple[event_id, event, threshold_exceeded]]: 检查结果
        """
        results = []
        
        try:
            for event_id, event in self.active_events.items():
                threshold_config = self.thresholds.get(event.monitor_type)
                if not threshold_config:
                    continue
                
                # 检查持续时间阈值
                duration = event.duration
                duration_exceeded = duration >= threshold_config.min_duration
                
                # 检查值阈值
                value_exceeded = abs(event.value_change) >= threshold_config.value_threshold
                
                # 综合判断
                threshold_exceeded = duration_exceeded and value_exceeded
                
                if threshold_exceeded:
                    self.threshold_exceeded_count += 1
                
                results.append((event_id, event, threshold_exceeded))
            
        except Exception as e:
            self.logger.error(f"检查阈值失败: {e}")
        
        return results
    
    def get_event_status(self, event_id: str) -> Optional[Dict[str, Any]]:
        """获取事件状态信息"""
        event = self.active_events.get(event_id)
        if not event:
            return None
        
        threshold_config = self.thresholds.get(event.monitor_type)
        
        return {
            'event_id': event.event_id,
            'monitor_type': event.monitor_type.value,
            'duration': event.duration,
            'value_change': event.value_change,
            'current_value': event.current_value,
            'initial_value': event.initial_value,
            'threshold_value': event.threshold_value,
            'min_duration_threshold': threshold_config.min_duration if threshold_config else None,
            'is_duration_exceeded': event.duration >= (threshold_config.min_duration if threshold_config else 0),
            'is_value_exceeded': abs(event.value_change) >= event.threshold_value,
            'metadata': event.metadata
        }
    
    def cleanup_old_events(self):
        """清理过期的已完成事件"""
        try:
            current_time = time.time()
            
            # 检查是否需要清理
            if current_time - self.last_cleanup < self.cleanup_interval:
                return
            
            # 保持历史记录大小
            if len(self.completed_events) > self.max_history_size:
                # 保留最新的事件
                self.completed_events = self.completed_events[-self.max_history_size:]
                self.logger.debug(f"清理历史事件，保留最新 {self.max_history_size} 个")
            
            self.last_cleanup = current_time
            
        except Exception as e:
            self.logger.error(f"清理事件失败: {e}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            'total_events': self.total_events,
            'active_events_count': len(self.active_events),
            'completed_events_count': len(self.completed_events),
            'threshold_exceeded_count': self.threshold_exceeded_count,
            'monitor_types': list(self.thresholds.keys())
        }
    
    def set_threshold(self, monitor_type: MonitorType, min_duration: float, 
                     value_threshold: float, max_duration: Optional[float] = None):
        """设置监控阈值"""
        self.thresholds[monitor_type] = DurationThreshold(
            monitor_type=monitor_type,
            min_duration=min_duration,
            max_duration=max_duration,
            value_threshold=value_threshold
        )
        
        self.logger.info(f"更新阈值配置: {monitor_type.value}")
