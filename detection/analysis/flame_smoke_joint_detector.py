#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
火焰烟雾联合检测模块
实现火焰和烟雾的联合检测机制，用于四级预警的火灾确认
"""

import time
import numpy as np
from typing import List, Dict, Tuple, Optional, Any
from dataclasses import dataclass, field
from enum import Enum
from collections import deque

from utils.logger import get_logger


class DetectionType(Enum):
    """检测类型枚举"""
    FLAME = "flame"
    SMOKE = "smoke"
    COMBINED = "combined"


@dataclass
class DetectionFrame:
    """检测帧数据"""
    timestamp: float                          # 时间戳
    flame_detections: List[Dict]              # 火焰检测结果
    smoke_detections: List[Dict]              # 烟雾检测结果
    flame_confidence: float                   # 最大火焰置信度
    smoke_confidence: float                   # 最大烟雾置信度
    flame_area_pixels: int                    # 火焰面积（像素）
    smoke_area_pixels: int                    # 烟雾面积（像素）
    combined_area_pixels: int                 # 联合面积（像素）


@dataclass
class JointDetectionConfig:
    """联合检测配置"""
    # 火焰检测配置
    flame_confidence_threshold: float = 0.45  # 火焰置信度阈值
    flame_min_area_pixels: int = 300          # 火焰最小面积（像素）
    flame_consecutive_frames: int = 3         # 火焰连续检测帧数
    
    # 烟雾检测配置
    smoke_confidence_threshold: float = 0.4   # 烟雾置信度阈值
    smoke_min_area_pixels: int = 200          # 烟雾最小面积（像素）
    smoke_consecutive_frames: int = 2         # 烟雾连续检测帧数
    
    # 联合检测配置
    joint_confidence_flame: float = 0.6       # 联合检测火焰置信度
    joint_confidence_smoke: float = 0.5       # 联合检测烟雾置信度
    joint_area_percentage: float = 5.0        # 联合面积占比阈值（%）
    joint_duration_seconds: float = 5.0       # 联合检测持续时间（秒）
    
    # 时间窗口配置
    frame_history_size: int = 30              # 帧历史大小
    detection_timeout: float = 10.0           # 检测超时时间（秒）


@dataclass
class JointDetectionResult:
    """联合检测结果"""
    is_flame_detected: bool                   # 是否检测到火焰
    is_smoke_detected: bool                   # 是否检测到烟雾
    is_joint_detected: bool                   # 是否联合检测成功
    
    flame_confidence: float                   # 火焰置信度
    smoke_confidence: float                   # 烟雾置信度
    joint_confidence: float                   # 联合置信度
    
    flame_area_pixels: int                    # 火焰面积
    smoke_area_pixels: int                    # 烟雾面积
    combined_area_pixels: int                 # 联合面积
    combined_area_percentage: float           # 联合面积占比
    
    flame_duration: float                     # 火焰持续时间
    smoke_duration: float                     # 烟雾持续时间
    joint_duration: float                     # 联合持续时间
    
    consecutive_flame_frames: int             # 连续火焰帧数
    consecutive_smoke_frames: int             # 连续烟雾帧数
    consecutive_joint_frames: int             # 连续联合帧数
    
    details: Dict[str, Any] = field(default_factory=dict)  # 详细信息


class FlameSmokejointDetector:
    """火焰烟雾联合检测器"""
    
    def __init__(self, config: Optional[JointDetectionConfig] = None):
        """
        初始化联合检测器
        
        Args:
            config: 联合检测配置
        """
        self.logger = get_logger("FlameSmokejointDetector")
        self.config = config or JointDetectionConfig()
        
        # 帧历史
        self.frame_history: deque = deque(maxlen=self.config.frame_history_size)
        
        # 检测状态
        self.flame_start_time = None
        self.smoke_start_time = None
        self.joint_start_time = None
        
        # 连续帧计数
        self.consecutive_flame_frames = 0
        self.consecutive_smoke_frames = 0
        self.consecutive_joint_frames = 0
        
        # 统计信息
        self.total_frames = 0
        self.flame_detections = 0
        self.smoke_detections = 0
        self.joint_detections = 0
        
        # 画面尺寸
        self.frame_width = 640
        self.frame_height = 480
        self.frame_area = self.frame_width * self.frame_height
    
    def update_frame_size(self, width: int, height: int):
        """更新画面尺寸"""
        self.frame_width = width
        self.frame_height = height
        self.frame_area = width * height
    
    def detect(self, 
              flame_detections: List[Dict],
              smoke_detections: List[Dict],
              current_time: Optional[float] = None) -> JointDetectionResult:
        """
        执行联合检测
        
        Args:
            flame_detections: 火焰检测结果
            smoke_detections: 烟雾检测结果
            current_time: 当前时间（可选）
            
        Returns:
            联合检测结果
        """
        if current_time is None:
            current_time = time.time()
        
        self.total_frames += 1
        
        # 创建检测帧
        detection_frame = self._create_detection_frame(
            flame_detections, smoke_detections, current_time
        )
        
        # 添加到历史
        self.frame_history.append(detection_frame)
        
        # 执行检测分析
        result = self._analyze_detections(current_time)
        
        return result
    
    def _create_detection_frame(self, 
                              flame_detections: List[Dict],
                              smoke_detections: List[Dict],
                              timestamp: float) -> DetectionFrame:
        """创建检测帧"""
        # 计算火焰信息
        flame_confidence = 0.0
        flame_area = 0
        if flame_detections:
            flame_confidence = max(det.get('confidence', 0.0) for det in flame_detections)
            flame_area = sum(self._calculate_detection_area(det) for det in flame_detections)
        
        # 计算烟雾信息
        smoke_confidence = 0.0
        smoke_area = 0
        if smoke_detections:
            smoke_confidence = max(det.get('confidence', 0.0) for det in smoke_detections)
            smoke_area = sum(self._calculate_detection_area(det) for det in smoke_detections)
        
        # 计算联合面积（去重）
        combined_area = self._calculate_combined_area(flame_detections, smoke_detections)
        
        return DetectionFrame(
            timestamp=timestamp,
            flame_detections=flame_detections,
            smoke_detections=smoke_detections,
            flame_confidence=flame_confidence,
            smoke_confidence=smoke_confidence,
            flame_area_pixels=flame_area,
            smoke_area_pixels=smoke_area,
            combined_area_pixels=combined_area
        )
    
    def _calculate_detection_area(self, detection: Dict) -> int:
        """计算单个检测的面积"""
        if 'bbox' in detection:
            x, y, w, h = detection['bbox']
            return w * h
        elif 'area' in detection:
            return detection['area']
        else:
            return 0
    
    def _calculate_combined_area(self, 
                                flame_detections: List[Dict],
                                smoke_detections: List[Dict]) -> int:
        """计算联合面积（去重）"""
        if not flame_detections and not smoke_detections:
            return 0
        
        # 创建掩码来计算联合面积
        mask = np.zeros((self.frame_height, self.frame_width), dtype=np.uint8)
        
        # 添加火焰区域
        for detection in flame_detections:
            if 'bbox' in detection:
                x, y, w, h = detection['bbox']
                x, y, w, h = max(0, x), max(0, y), min(w, self.frame_width-x), min(h, self.frame_height-y)
                if w > 0 and h > 0:
                    mask[y:y+h, x:x+w] = 1
        
        # 添加烟雾区域
        for detection in smoke_detections:
            if 'bbox' in detection:
                x, y, w, h = detection['bbox']
                x, y, w, h = max(0, x), max(0, y), min(w, self.frame_width-x), min(h, self.frame_height-y)
                if w > 0 and h > 0:
                    mask[y:y+h, x:x+w] = 1
        
        return int(np.sum(mask))
    
    def _analyze_detections(self, current_time: float) -> JointDetectionResult:
        """分析检测结果"""
        if not self.frame_history:
            return self._create_empty_result()
        
        latest_frame = self.frame_history[-1]
        
        # 1. 单独检测分析
        flame_detected = self._is_flame_detected(latest_frame)
        smoke_detected = self._is_smoke_detected(latest_frame)
        
        # 2. 更新连续帧计数
        self._update_consecutive_frames(flame_detected, smoke_detected)
        
        # 3. 更新持续时间
        flame_duration = self._update_flame_duration(flame_detected, current_time)
        smoke_duration = self._update_smoke_duration(smoke_detected, current_time)
        
        # 4. 联合检测分析
        joint_detected = self._is_joint_detected(latest_frame, flame_detected, smoke_detected)
        joint_duration = self._update_joint_duration(joint_detected, current_time)
        
        # 5. 计算联合置信度
        joint_confidence = self._calculate_joint_confidence(latest_frame, joint_detected)
        
        # 6. 计算面积占比
        area_percentage = (latest_frame.combined_area_pixels / self.frame_area * 100) if self.frame_area > 0 else 0.0
        
        # 7. 更新统计
        if flame_detected:
            self.flame_detections += 1
        if smoke_detected:
            self.smoke_detections += 1
        if joint_detected:
            self.joint_detections += 1
        
        return JointDetectionResult(
            is_flame_detected=flame_detected,
            is_smoke_detected=smoke_detected,
            is_joint_detected=joint_detected,
            flame_confidence=latest_frame.flame_confidence,
            smoke_confidence=latest_frame.smoke_confidence,
            joint_confidence=joint_confidence,
            flame_area_pixels=latest_frame.flame_area_pixels,
            smoke_area_pixels=latest_frame.smoke_area_pixels,
            combined_area_pixels=latest_frame.combined_area_pixels,
            combined_area_percentage=area_percentage,
            flame_duration=flame_duration,
            smoke_duration=smoke_duration,
            joint_duration=joint_duration,
            consecutive_flame_frames=self.consecutive_flame_frames,
            consecutive_smoke_frames=self.consecutive_smoke_frames,
            consecutive_joint_frames=self.consecutive_joint_frames,
            details={
                'frame_count': len(self.frame_history),
                'flame_detections_count': len(latest_frame.flame_detections),
                'smoke_detections_count': len(latest_frame.smoke_detections)
            }
        )
    
    def _is_flame_detected(self, frame: DetectionFrame) -> bool:
        """判断是否检测到火焰"""
        return (frame.flame_confidence >= self.config.flame_confidence_threshold and
                frame.flame_area_pixels >= self.config.flame_min_area_pixels)
    
    def _is_smoke_detected(self, frame: DetectionFrame) -> bool:
        """判断是否检测到烟雾"""
        return (frame.smoke_confidence >= self.config.smoke_confidence_threshold and
                frame.smoke_area_pixels >= self.config.smoke_min_area_pixels)
    
    def _is_joint_detected(self, frame: DetectionFrame, flame_detected: bool, smoke_detected: bool) -> bool:
        """判断是否联合检测成功"""
        if not (flame_detected and smoke_detected):
            return False
        
        # 检查置信度
        flame_conf_ok = frame.flame_confidence >= self.config.joint_confidence_flame
        smoke_conf_ok = frame.smoke_confidence >= self.config.joint_confidence_smoke
        
        # 检查面积占比
        area_percentage = (frame.combined_area_pixels / self.frame_area * 100) if self.frame_area > 0 else 0.0
        area_ok = area_percentage >= self.config.joint_area_percentage
        
        # 检查连续帧数
        flame_frames_ok = self.consecutive_flame_frames >= self.config.flame_consecutive_frames
        smoke_frames_ok = self.consecutive_smoke_frames >= self.config.smoke_consecutive_frames
        
        return flame_conf_ok and smoke_conf_ok and area_ok and flame_frames_ok and smoke_frames_ok
    
    def _update_consecutive_frames(self, flame_detected: bool, smoke_detected: bool):
        """更新连续帧计数"""
        # 更新火焰连续帧
        if flame_detected:
            self.consecutive_flame_frames += 1
        else:
            self.consecutive_flame_frames = 0
        
        # 更新烟雾连续帧
        if smoke_detected:
            self.consecutive_smoke_frames += 1
        else:
            self.consecutive_smoke_frames = 0
        
        # 更新联合连续帧
        if flame_detected and smoke_detected:
            self.consecutive_joint_frames += 1
        else:
            self.consecutive_joint_frames = 0
    
    def _update_flame_duration(self, flame_detected: bool, current_time: float) -> float:
        """更新火焰持续时间"""
        if flame_detected:
            if self.flame_start_time is None:
                self.flame_start_time = current_time
            return current_time - self.flame_start_time
        else:
            self.flame_start_time = None
            return 0.0
    
    def _update_smoke_duration(self, smoke_detected: bool, current_time: float) -> float:
        """更新烟雾持续时间"""
        if smoke_detected:
            if self.smoke_start_time is None:
                self.smoke_start_time = current_time
            return current_time - self.smoke_start_time
        else:
            self.smoke_start_time = None
            return 0.0
    
    def _update_joint_duration(self, joint_detected: bool, current_time: float) -> float:
        """更新联合持续时间"""
        if joint_detected:
            if self.joint_start_time is None:
                self.joint_start_time = current_time
            return current_time - self.joint_start_time
        else:
            self.joint_start_time = None
            return 0.0
    
    def _calculate_joint_confidence(self, frame: DetectionFrame, joint_detected: bool) -> float:
        """计算联合置信度"""
        if not joint_detected:
            return 0.0
        
        # 基于火焰和烟雾置信度的加权平均
        flame_weight = 0.6
        smoke_weight = 0.4
        
        base_confidence = (frame.flame_confidence * flame_weight + 
                          frame.smoke_confidence * smoke_weight)
        
        # 基于连续帧数的加成
        frame_bonus = min(self.consecutive_joint_frames / 10.0, 0.2)
        
        # 基于面积的加成
        area_percentage = (frame.combined_area_pixels / self.frame_area * 100) if self.frame_area > 0 else 0.0
        area_bonus = min(area_percentage / 20.0, 0.2)
        
        return min(base_confidence + frame_bonus + area_bonus, 1.0)
    
    def _create_empty_result(self) -> JointDetectionResult:
        """创建空结果"""
        return JointDetectionResult(
            is_flame_detected=False,
            is_smoke_detected=False,
            is_joint_detected=False,
            flame_confidence=0.0,
            smoke_confidence=0.0,
            joint_confidence=0.0,
            flame_area_pixels=0,
            smoke_area_pixels=0,
            combined_area_pixels=0,
            combined_area_percentage=0.0,
            flame_duration=0.0,
            smoke_duration=0.0,
            joint_duration=0.0,
            consecutive_flame_frames=0,
            consecutive_smoke_frames=0,
            consecutive_joint_frames=0
        )
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            'total_frames': self.total_frames,
            'flame_detections': self.flame_detections,
            'smoke_detections': self.smoke_detections,
            'joint_detections': self.joint_detections,
            'flame_detection_rate': self.flame_detections / self.total_frames if self.total_frames > 0 else 0.0,
            'smoke_detection_rate': self.smoke_detections / self.total_frames if self.total_frames > 0 else 0.0,
            'joint_detection_rate': self.joint_detections / self.total_frames if self.total_frames > 0 else 0.0,
            'frame_history_size': len(self.frame_history)
        }
    
    def reset(self):
        """重置检测器"""
        self.frame_history.clear()
        self.flame_start_time = None
        self.smoke_start_time = None
        self.joint_start_time = None
        self.consecutive_flame_frames = 0
        self.consecutive_smoke_frames = 0
        self.consecutive_joint_frames = 0
        self.total_frames = 0
        self.flame_detections = 0
        self.smoke_detections = 0
        self.joint_detections = 0
