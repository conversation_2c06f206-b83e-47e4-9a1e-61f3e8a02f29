#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全局指标计算模块
计算和管理热源监控系统的全局统计指标
"""

import numpy as np
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
from collections import deque

from .heat_source_history import HeatSourceSnapshot


@dataclass
class GlobalMetrics:
    """全局指标数据类"""
    timestamp: datetime
    total_heat_sources: int                    # 总热源个数
    total_heat_sources_change_rate: Optional[float]  # 总热源个数变化率（个/秒）
    total_heat_area: int                       # 整体画面热源面积（像素）
    total_heat_area_change_rate: Optional[float]     # 整体画面热源变化率（像素/秒）
    avg_temperature_30days: Optional[float]    # 整个画面平均温度在30天内平均值（℃）
    # 新增检测计数字段
    human_count: int = 0                       # 人体个数
    fire_count: int = 0                        # 火焰个数
    smoke_count: int = 0                       # 烟雾个数

    def to_dict(self) -> Dict:
        """转换为字典格式，便于UI显示"""
        return {
            'timestamp': self.timestamp.strftime('%H:%M:%S'),
            'total_heat_sources': self.total_heat_sources,
            'total_heat_sources_change_rate': self.total_heat_sources_change_rate,
            'total_heat_area': self.total_heat_area,
            'total_heat_area_change_rate': self.total_heat_area_change_rate,
            'avg_temperature_30days': self.avg_temperature_30days,
            'human_count': self.human_count,
            'fire_count': self.fire_count,
            'smoke_count': self.smoke_count
        }


class GlobalMetricsCalculator:
    """全局指标计算器"""
    
    def __init__(self, max_history_points: int = 1000):
        """
        初始化全局指标计算器
        
        Args:
            max_history_points: 最大历史数据点数量
        """
        self.max_history_points = max_history_points
        
        # 历史数据存储
        self.metrics_history: deque = deque(maxlen=max_history_points)
        self.temperature_history_30days: deque = deque(maxlen=30*24*60)  # 30天，每分钟一个点
        
        self.logger = self._get_logger()
    
    def _get_logger(self):
        """获取日志记录器"""
        try:
            from utils.logger import get_logger
            return get_logger(__name__)
        except ImportError:
            import logging
            return logging.getLogger(__name__)
    
    def calculate_global_metrics(self, current_snapshots: List[HeatSourceSnapshot],
                                human_count: int = 0, fire_count: int = 0, smoke_count: int = 0) -> GlobalMetrics:
        """
        计算当前时刻的全局指标

        Args:
            current_snapshots: 当前时刻的所有热源快照
            human_count: 当前检测到的人体个数
            fire_count: 当前检测到的火焰个数
            smoke_count: 当前检测到的烟雾个数

        Returns:
            GlobalMetrics: 全局指标对象
        """
        timestamp = datetime.now()
        
        # 1. 计算总热源个数
        total_heat_sources = len(current_snapshots)
        
        # 2. 计算总热源个数变化率
        total_heat_sources_change_rate = self._calculate_heat_sources_change_rate(total_heat_sources)
        
        # 3. 计算整体画面热源面积
        total_heat_area = sum(snapshot.area for snapshot in current_snapshots)
        
        # 4. 计算整体画面热源变化率
        total_heat_area_change_rate = self._calculate_heat_area_change_rate(total_heat_area)
        
        # 5. 计算整个画面平均温度
        current_avg_temp = self._calculate_current_average_temperature(current_snapshots)
        
        # 更新30天温度历史
        if current_avg_temp is not None:
            self.temperature_history_30days.append({
                'timestamp': timestamp,
                'avg_temperature': current_avg_temp
            })
        
        # 6. 计算30天内平均温度
        avg_temperature_30days = self._calculate_30days_average_temperature()
        
        # 创建全局指标对象
        metrics = GlobalMetrics(
            timestamp=timestamp,
            total_heat_sources=total_heat_sources,
            total_heat_sources_change_rate=total_heat_sources_change_rate,
            total_heat_area=total_heat_area,
            total_heat_area_change_rate=total_heat_area_change_rate,
            avg_temperature_30days=avg_temperature_30days,
            human_count=human_count,
            fire_count=fire_count,
            smoke_count=smoke_count
        )
        
        # 保存到历史记录
        self.metrics_history.append(metrics)
        
        return metrics
    
    def _calculate_heat_sources_change_rate(self, current_count: int) -> Optional[float]:
        """计算热源个数变化率（优化：降低数据要求）"""
        if len(self.metrics_history) < 1:  # 优化：从2降到1
            return 0.0  # 优化：返回0而不是None
        
        try:
            # 获取最近的历史数据（优化：减少要求的数据点）
            recent_metrics = list(self.metrics_history)[-5:]  # 优化：从10个减少到5个数据点

            if len(recent_metrics) < 1:  # 优化：从2降到1
                return 0.0
            
            # 计算时间差和数量差
            time_diffs = []
            count_diffs = []
            
            for i in range(1, len(recent_metrics)):
                prev_metric = recent_metrics[i-1]
                curr_metric = recent_metrics[i]
                
                time_diff = (curr_metric.timestamp - prev_metric.timestamp).total_seconds()
                count_diff = curr_metric.total_heat_sources - prev_metric.total_heat_sources
                
                if time_diff > 0:
                    time_diffs.append(time_diff)
                    count_diffs.append(count_diff)
            
            if not time_diffs:
                return None
            
            # 计算平均变化率（个/秒）
            avg_time_diff = np.mean(time_diffs)
            avg_count_diff = np.mean(count_diffs)
            
            return avg_count_diff / avg_time_diff if avg_time_diff > 0 else 0.0
            
        except Exception as e:
            self.logger.error(f"❌ 计算热源个数变化率失败: {e}")
            return None
    
    def _calculate_heat_area_change_rate(self, current_area: int) -> Optional[float]:
        """计算热源面积变化率（优化：降低数据要求）"""
        if len(self.metrics_history) < 1:  # 优化：从2降到1
            return 0.0  # 优化：返回0而不是None

        try:
            # 获取最近的历史数据（优化：减少要求的数据点）
            recent_metrics = list(self.metrics_history)[-5:]  # 优化：从10个减少到5个数据点

            if len(recent_metrics) < 1:  # 优化：从2降到1
                return 0.0
            
            # 计算时间差和面积差
            time_diffs = []
            area_diffs = []
            
            for i in range(1, len(recent_metrics)):
                prev_metric = recent_metrics[i-1]
                curr_metric = recent_metrics[i]
                
                time_diff = (curr_metric.timestamp - prev_metric.timestamp).total_seconds()
                area_diff = curr_metric.total_heat_area - prev_metric.total_heat_area
                
                if time_diff > 0:
                    time_diffs.append(time_diff)
                    area_diffs.append(area_diff)
            
            if not time_diffs:
                return None
            
            # 计算平均变化率（像素/秒）
            avg_time_diff = np.mean(time_diffs)
            avg_area_diff = np.mean(area_diffs)
            
            return avg_area_diff / avg_time_diff if avg_time_diff > 0 else 0.0
            
        except Exception as e:
            self.logger.error(f"❌ 计算热源面积变化率失败: {e}")
            return None
    
    def _calculate_current_average_temperature(self, snapshots: List[HeatSourceSnapshot]) -> Optional[float]:
        """计算当前画面平均温度"""
        if not snapshots:
            return None
        
        try:
            # 使用面积加权平均温度
            total_weighted_temp = 0.0
            total_area = 0
            
            for snapshot in snapshots:
                area = snapshot.area
                avg_temp = snapshot.avg_temperature
                
                total_weighted_temp += avg_temp * area
                total_area += area
            
            return total_weighted_temp / total_area if total_area > 0 else None
            
        except Exception as e:
            self.logger.error(f"❌ 计算当前平均温度失败: {e}")
            return None
    
    def _calculate_30days_average_temperature(self) -> Optional[float]:
        """计算30天内平均温度"""
        if not self.temperature_history_30days:
            return None
        
        try:
            # 过滤30天内的数据
            cutoff_time = datetime.now() - timedelta(days=30)
            valid_temps = []
            
            for temp_record in self.temperature_history_30days:
                if temp_record['timestamp'] >= cutoff_time:
                    valid_temps.append(temp_record['avg_temperature'])
            
            return np.mean(valid_temps) if valid_temps else None
            
        except Exception as e:
            self.logger.error(f"❌ 计算30天平均温度失败: {e}")
            return None
    
    def get_recent_metrics(self, count: int = 10) -> List[GlobalMetrics]:
        """获取最近的全局指标"""
        return list(self.metrics_history)[-count:] if self.metrics_history else []
    
    def get_metrics_summary(self) -> Dict:
        """获取指标摘要"""
        if not self.metrics_history:
            return {}
        
        try:
            recent_metrics = list(self.metrics_history)
            latest = recent_metrics[-1]
            
            return {
                'latest_metrics': {
                    'timestamp': latest.timestamp.isoformat(),
                    'total_heat_sources': latest.total_heat_sources,
                    'total_heat_sources_change_rate': latest.total_heat_sources_change_rate,
                    'total_heat_area': latest.total_heat_area,
                    'total_heat_area_change_rate': latest.total_heat_area_change_rate,
                    'avg_temperature_30days': latest.avg_temperature_30days,
                    'human_count': latest.human_count,
                    'fire_count': latest.fire_count,
                    'smoke_count': latest.smoke_count
                },
                'history_count': len(recent_metrics),
                'temperature_history_count': len(self.temperature_history_30days)
            }
            
        except Exception as e:
            self.logger.error(f"❌ 获取指标摘要失败: {e}")
            return {}
    
    def clear_old_data(self, days: int = 30):
        """清理过期数据"""
        try:
            cutoff_time = datetime.now() - timedelta(days=days)
            
            # 清理过期的温度历史数据
            while (self.temperature_history_30days and 
                   self.temperature_history_30days[0]['timestamp'] < cutoff_time):
                self.temperature_history_30days.popleft()
            
            self.logger.info(f"🧹 清理了过期的全局指标数据")
            
        except Exception as e:
            self.logger.error(f"❌ 清理过期数据失败: {e}")


# 全局实例
_global_metrics_calculator: Optional[GlobalMetricsCalculator] = None


def get_global_metrics_calculator() -> GlobalMetricsCalculator:
    """获取全局指标计算器实例（单例模式）"""
    global _global_metrics_calculator
    
    if _global_metrics_calculator is None:
        _global_metrics_calculator = GlobalMetricsCalculator()
    
    return _global_metrics_calculator
