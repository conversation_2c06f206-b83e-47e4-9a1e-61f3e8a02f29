#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
热源面积计算器
实现热点面积计算指标，包括像素面积到物理面积的转换和面积阈值警报
"""

import cv2
import numpy as np
import json
import os
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from datetime import datetime


@dataclass
class AreaThreshold:
    """面积阈值配置"""
    warning_pixels: int = 100      # 警告阈值（像素）
    critical_pixels: int = 500     # 严重阈值（像素）
    warning_m2: float = 0.1        # 警告阈值（平方米）
    critical_m2: float = 0.5       # 严重阈值（平方米）


@dataclass
class CameraCalibration:
    """相机标定参数"""
    pixel_to_m2_factor: float = 0.001  # 像素到平方米转换因子
    distance_meters: float = 5.0       # 目标距离（米）
    focal_length_mm: float = 8.0       # 焦距（毫米）
    sensor_width_mm: float = 6.4       # 传感器宽度（毫米）
    sensor_height_mm: float = 4.8      # 传感器高度（毫米）
    image_width_pixels: int = 320       # 图像宽度（像素）
    image_height_pixels: int = 240      # 图像高度（像素）
    
    def calculate_pixel_to_m2_factor(self) -> float:
        """根据相机参数计算像素到平方米的转换因子"""
        # 计算每像素对应的物理尺寸（米）
        pixel_size_x = (self.sensor_width_mm / 1000) / self.image_width_pixels
        pixel_size_y = (self.sensor_height_mm / 1000) / self.image_height_pixels
        
        # 在目标距离处的物理尺寸
        physical_size_x = pixel_size_x * self.distance_meters / (self.focal_length_mm / 1000)
        physical_size_y = pixel_size_y * self.distance_meters / (self.focal_length_mm / 1000)
        
        # 每像素对应的物理面积（平方米）
        return physical_size_x * physical_size_y


@dataclass
class AreaAnalysisResult:
    """面积分析结果"""
    contour_id: int
    area_pixels: int
    area_m2: float
    perimeter_pixels: float
    aspect_ratio: float
    extent: float  # 填充度
    is_warning: bool = False
    is_critical: bool = False
    alert_level: str = "normal"  # "normal", "warning", "critical"
    bbox: Tuple[int, int, int, int] = (0, 0, 0, 0)


class HeatAreaCalculator:
    """热源面积计算器"""
    
    def __init__(self, config_file: str = "config/heat_area_config.json"):
        self.config_file = config_file
        self.calibration = CameraCalibration()
        self.thresholds = AreaThreshold()
        
        # 加载配置
        self.load_config()
        
        # 更新转换因子
        self.update_conversion_factor()
        
        # 统计信息
        self.analysis_history: List[AreaAnalysisResult] = []
        
    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                
                # 加载相机标定参数
                if 'calibration' in config_data:
                    cal_data = config_data['calibration']
                    for key, value in cal_data.items():
                        if hasattr(self.calibration, key):
                            setattr(self.calibration, key, value)
                
                # 加载阈值配置
                if 'thresholds' in config_data:
                    thresh_data = config_data['thresholds']
                    for key, value in thresh_data.items():
                        if hasattr(self.thresholds, key):
                            setattr(self.thresholds, key, value)
                            
                print(f"✅ 热源面积配置已加载: {self.config_file}")
            else:
                print(f"⚠️ 配置文件不存在，使用默认配置: {self.config_file}")
                self.save_config()
                
        except Exception as e:
            print(f"❌ 加载配置失败: {e}")
    
    def save_config(self):
        """保存配置文件"""
        try:
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            
            config_data = {
                'calibration': {
                    'pixel_to_m2_factor': self.calibration.pixel_to_m2_factor,
                    'distance_meters': self.calibration.distance_meters,
                    'focal_length_mm': self.calibration.focal_length_mm,
                    'sensor_width_mm': self.calibration.sensor_width_mm,
                    'sensor_height_mm': self.calibration.sensor_height_mm,
                    'image_width_pixels': self.calibration.image_width_pixels,
                    'image_height_pixels': self.calibration.image_height_pixels
                },
                'thresholds': {
                    'warning_pixels': self.thresholds.warning_pixels,
                    'critical_pixels': self.thresholds.critical_pixels,
                    'warning_m2': self.thresholds.warning_m2,
                    'critical_m2': self.thresholds.critical_m2
                },
                'last_updated': datetime.now().isoformat(),
                'description': "热源面积计算配置文件"
            }
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
                
            print(f"✅ 配置已保存: {self.config_file}")
            
        except Exception as e:
            print(f"❌ 保存配置失败: {e}")
    
    def update_conversion_factor(self):
        """更新像素到平方米的转换因子"""
        self.calibration.pixel_to_m2_factor = self.calibration.calculate_pixel_to_m2_factor()
        print(f"🔧 像素转换因子已更新: {self.calibration.pixel_to_m2_factor:.6f} m²/pixel")
    
    def analyze_contour_area(self, contour, contour_id: int = 0) -> AreaAnalysisResult:
        """分析单个轮廓的面积特征"""
        try:
            # 计算基本面积和周长
            area_pixels = cv2.contourArea(contour)
            perimeter_pixels = cv2.arcLength(contour, True)
            
            # 计算边界框
            x, y, w, h = cv2.boundingRect(contour)
            bbox = (x, y, w, h)
            
            # 计算形状特征
            aspect_ratio = float(w) / h if h > 0 else 0
            extent = float(area_pixels) / (w * h) if w > 0 and h > 0 else 0
            
            # 转换为物理面积
            area_m2 = area_pixels * self.calibration.pixel_to_m2_factor
            
            # 判断警报级别
            alert_level = "normal"
            is_warning = False
            is_critical = False
            
            # 基于像素面积判断
            if area_pixels >= self.thresholds.critical_pixels:
                alert_level = "critical"
                is_critical = True
            elif area_pixels >= self.thresholds.warning_pixels:
                alert_level = "warning"
                is_warning = True
            
            # 基于物理面积判断（如果更严格）
            if area_m2 >= self.thresholds.critical_m2:
                alert_level = "critical"
                is_critical = True
            elif area_m2 >= self.thresholds.warning_m2 and alert_level == "normal":
                alert_level = "warning"
                is_warning = True
            
            result = AreaAnalysisResult(
                contour_id=contour_id,
                area_pixels=int(area_pixels),
                area_m2=area_m2,
                perimeter_pixels=perimeter_pixels,
                aspect_ratio=aspect_ratio,
                extent=extent,
                is_warning=is_warning,
                is_critical=is_critical,
                alert_level=alert_level,
                bbox=bbox
            )
            
            return result
            
        except Exception as e:
            print(f"❌ 轮廓面积分析失败: {e}")
            return AreaAnalysisResult(contour_id=contour_id, area_pixels=0, area_m2=0.0, 
                                    perimeter_pixels=0.0, aspect_ratio=0.0, extent=0.0)
    
    def analyze_contours_batch(self, contours: List) -> List[AreaAnalysisResult]:
        """批量分析多个轮廓的面积"""
        results = []
        
        for i, contour in enumerate(contours):
            result = self.analyze_contour_area(contour, contour_id=i)
            results.append(result)
        
        # 保存到历史记录
        self.analysis_history.extend(results)
        
        # 限制历史记录长度
        if len(self.analysis_history) > 1000:
            self.analysis_history = self.analysis_history[-1000:]
        
        return results
    
    def get_area_summary(self, results: List[AreaAnalysisResult]) -> Dict:
        """获取面积分析摘要"""
        if not results:
            return {
                'total_count': 0,
                'total_area_pixels': 0,
                'total_area_m2': 0.0,
                'warning_count': 0,
                'critical_count': 0,
                'max_area_pixels': 0,
                'max_area_m2': 0.0
            }
        
        total_area_pixels = sum(r.area_pixels for r in results)
        total_area_m2 = sum(r.area_m2 for r in results)
        warning_count = sum(1 for r in results if r.is_warning)
        critical_count = sum(1 for r in results if r.is_critical)
        max_area_pixels = max(r.area_pixels for r in results)
        max_area_m2 = max(r.area_m2 for r in results)
        
        return {
            'total_count': len(results),
            'total_area_pixels': total_area_pixels,
            'total_area_m2': total_area_m2,
            'warning_count': warning_count,
            'critical_count': critical_count,
            'max_area_pixels': max_area_pixels,
            'max_area_m2': max_area_m2,
            'avg_area_pixels': total_area_pixels / len(results),
            'avg_area_m2': total_area_m2 / len(results)
        }
    
    def set_distance(self, distance_meters: float):
        """设置目标距离并更新转换因子"""
        self.calibration.distance_meters = distance_meters
        self.update_conversion_factor()
        print(f"🎯 目标距离已设置为: {distance_meters:.1f} 米")
    
    def set_thresholds(self, warning_pixels: int = None, critical_pixels: int = None,
                      warning_m2: float = None, critical_m2: float = None):
        """设置面积阈值"""
        if warning_pixels is not None:
            self.thresholds.warning_pixels = warning_pixels
        if critical_pixels is not None:
            self.thresholds.critical_pixels = critical_pixels
        if warning_m2 is not None:
            self.thresholds.warning_m2 = warning_m2
        if critical_m2 is not None:
            self.thresholds.critical_m2 = critical_m2
        
        print(f"🚨 面积阈值已更新:")
        print(f"   警告: {self.thresholds.warning_pixels} 像素 / {self.thresholds.warning_m2:.3f} m²")
        print(f"   严重: {self.thresholds.critical_pixels} 像素 / {self.thresholds.critical_m2:.3f} m²")
    
    def get_calibration_info(self) -> Dict:
        """获取标定信息"""
        return {
            'pixel_to_m2_factor': self.calibration.pixel_to_m2_factor,
            'distance_meters': self.calibration.distance_meters,
            'focal_length_mm': self.calibration.focal_length_mm,
            'image_size': f"{self.calibration.image_width_pixels}x{self.calibration.image_height_pixels}",
            'sensor_size_mm': f"{self.calibration.sensor_width_mm}x{self.calibration.sensor_height_mm}"
        }
