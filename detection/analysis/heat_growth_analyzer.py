#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
热点增长速率分析器
实现指标5：热点增长速率的完整分析功能
包括质心匹配、增长率计算、Mann<PERSON>Kendall趋势检验等
"""

import numpy as np
import cv2
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import scipy.stats as stats


class GrowthAlertLevel(Enum):
    """增长警报级别"""
    NORMAL = "normal"
    WARNING = "warning"
    CRITICAL = "critical"
    EMERGENCY = "emergency"


class GrowthPattern(Enum):
    """增长模式"""
    LINEAR = "linear"
    CIRCULAR = "circular"
    DIRECTIONAL = "directional"
    IRREGULAR = "irregular"


@dataclass
class HotSpotData:
    """热点数据结构"""
    id: str
    center: Tuple[float, float]  # 质心坐标
    area: float
    timestamp: datetime
    bbox: Tuple[int, int, int, int]  # (x, y, w, h)
    contour: np.ndarray = None
    max_temperature: float = 0.0
    confidence: float = 1.0


@dataclass
class GrowthRateResult:
    """增长率分析结果"""
    hot_spot_id: str
    current_area: float
    previous_area: float
    growth_rate_pixels_per_sec: float
    growth_rate_percentage: float
    time_delta_seconds: float
    alert_level: GrowthAlertLevel
    growth_pattern: GrowthPattern
    trend_significance: float = 0.0  # Mann-Kendall趋势显著性
    direction_vector: Tuple[float, float] = (0.0, 0.0)  # 增长方向向量


@dataclass
class TrendAnalysisResult:
    """趋势分析结果"""
    tracking_id: str
    trend_statistic: float  # Mann-Kendall统计量
    p_value: float  # p值
    trend_direction: str  # "increasing", "decreasing", "no_trend"
    significance_level: float  # 显著性水平
    is_intensifying: bool  # 是否为增强热点


class HeatGrowthAnalyzer:
    """热点增长速率分析器"""
    
    def __init__(self, 
                 growth_rate_threshold_pixels_per_sec: float = 10.0,
                 growth_rate_threshold_percentage: float = 20.0,
                 max_matching_distance: float = 30.0,
                 trend_significance_threshold: float = 0.05):
        """
        初始化增长分析器
        
        Args:
            growth_rate_threshold_pixels_per_sec: 像素增长率阈值（像素/秒）
            growth_rate_threshold_percentage: 百分比增长率阈值（%）
            max_matching_distance: 最大匹配距离（像素）
            trend_significance_threshold: 趋势显著性阈值
        """
        self.growth_rate_threshold_pixels = growth_rate_threshold_pixels_per_sec
        self.growth_rate_threshold_percentage = growth_rate_threshold_percentage
        self.max_matching_distance = max_matching_distance
        self.trend_significance_threshold = trend_significance_threshold
        
        # 热点跟踪数据
        self.hot_spot_tracking_data: Dict[str, List[HotSpotData]] = {}
        self.next_hot_spot_id = 1
        
        # 增长历史记录
        self.growth_history: Dict[str, List[GrowthRateResult]] = {}
        
        # 统计信息
        self.total_detections = 0
        self.total_alarms = 0
    
    def calculate_centroid(self, contour: np.ndarray) -> Tuple[float, float]:
        """计算轮廓质心"""
        try:
            M = cv2.moments(contour)
            if M["m00"] != 0:
                cx = M["m10"] / M["m00"]
                cy = M["m01"] / M["m00"]
                return (float(cx), float(cy))
            else:
                # 如果无法计算质心，使用边界框中心
                x, y, w, h = cv2.boundingRect(contour)
                return (float(x + w/2), float(y + h/2))
        except Exception:
            return (0.0, 0.0)
    
    def find_matching_previous_hot_spot(self, current_hs: HotSpotData, 
                                      previous_hot_spots_data: Dict[str, List[HotSpotData]]) -> Optional[Tuple[str, HotSpotData]]:
        """
        基于质心距离查找匹配的先前热点
        
        Args:
            current_hs: 当前热点数据
            previous_hot_spots_data: 先前热点跟踪数据
            
        Returns:
            Tuple[hot_spot_id, previous_data] 或 None
        """
        best_match = None
        min_distance = float('inf')
        
        for hs_id, hs_history in previous_hot_spots_data.items():
            if not hs_history:
                continue
                
            # 获取最新的热点数据
            latest_hs = hs_history[-1]
            
            # 计算质心距离
            dist = np.sqrt((current_hs.center[0] - latest_hs.center[0])**2 + 
                          (current_hs.center[1] - latest_hs.center[1])**2)
            
            if dist < self.max_matching_distance and dist < min_distance:
                min_distance = dist
                best_match = (hs_id, latest_hs)
        
        return best_match
    
    def update_hot_spot_tracking(self, current_hot_spot: HotSpotData) -> str:
        """
        更新热点跟踪数据
        
        Args:
            current_hot_spot: 当前热点数据
            
        Returns:
            热点ID
        """
        # 尝试匹配已存在的热点
        matched = self.find_matching_previous_hot_spot(current_hot_spot, self.hot_spot_tracking_data)
        
        if matched:
            hs_id, _ = matched
            # 更新已存在的热点
            self.hot_spot_tracking_data[hs_id].append(current_hot_spot)
            current_hot_spot.id = hs_id
            return hs_id
        else:
            # 创建新的热点ID
            new_id = f"hs_{self.next_hot_spot_id}"
            self.next_hot_spot_id += 1
            
            # 初始化新热点的历史记录
            self.hot_spot_tracking_data[new_id] = [current_hot_spot]
            current_hot_spot.id = new_id
            return new_id
    
    def calculate_growth_pattern(self, hs_history: List[HotSpotData]) -> GrowthPattern:
        """
        分析增长模式
        
        Args:
            hs_history: 热点历史数据
            
        Returns:
            增长模式
        """
        if len(hs_history) < 3:
            return GrowthPattern.IRREGULAR
        
        # 提取面积和位置数据
        areas = [hs.area for hs in hs_history]
        centers = [hs.center for hs in hs_history]
        
        # 分析面积增长趋势
        area_diffs = np.diff(areas)
        area_trend_consistency = np.std(area_diffs) / (np.mean(np.abs(area_diffs)) + 1e-6)
        
        # 分析位置变化
        center_diffs = np.diff(centers, axis=0)
        position_variance = np.var(center_diffs, axis=0)
        
        # 判断增长模式
        if area_trend_consistency < 0.3:  # 面积增长一致
            if np.sum(position_variance) < 100:  # 位置变化小
                return GrowthPattern.CIRCULAR
            else:
                return GrowthPattern.DIRECTIONAL
        elif area_trend_consistency < 0.8:
            return GrowthPattern.LINEAR
        else:
            return GrowthPattern.IRREGULAR
    
    def calculate_direction_vector(self, hs_history: List[HotSpotData]) -> Tuple[float, float]:
        """
        计算增长方向向量
        
        Args:
            hs_history: 热点历史数据
            
        Returns:
            方向向量 (dx, dy)
        """
        if len(hs_history) < 2:
            return (0.0, 0.0)
        
        # 计算质心移动的平均方向
        centers = np.array([hs.center for hs in hs_history])
        center_diffs = np.diff(centers, axis=0)
        
        if len(center_diffs) == 0:
            return (0.0, 0.0)
        
        # 计算平均方向向量
        avg_direction = np.mean(center_diffs, axis=0)
        
        # 归一化
        magnitude = np.linalg.norm(avg_direction)
        if magnitude > 0:
            return (float(avg_direction[0] / magnitude), float(avg_direction[1] / magnitude))
        else:
            return (0.0, 0.0)
    
    def mann_kendall_trend_test(self, data: List[float]) -> TrendAnalysisResult:
        """
        Mann-Kendall趋势检验
        
        Args:
            data: 时序数据
            
        Returns:
            趋势分析结果
        """
        if len(data) < 3:
            return TrendAnalysisResult(
                tracking_id="",
                trend_statistic=0.0,
                p_value=1.0,
                trend_direction="no_trend",
                significance_level=self.trend_significance_threshold,
                is_intensifying=False
            )
        
        try:
            # 使用scipy进行Mann-Kendall检验
            statistic, p_value = stats.kendalltau(range(len(data)), data)
            
            # 判断趋势方向
            if p_value < self.trend_significance_threshold:
                if statistic > 0:
                    trend_direction = "increasing"
                    is_intensifying = True
                else:
                    trend_direction = "decreasing"
                    is_intensifying = False
            else:
                trend_direction = "no_trend"
                is_intensifying = False
            
            return TrendAnalysisResult(
                tracking_id="",
                trend_statistic=float(statistic),
                p_value=float(p_value),
                trend_direction=trend_direction,
                significance_level=self.trend_significance_threshold,
                is_intensifying=is_intensifying
            )
            
        except Exception as e:
            print(f"⚠️ Mann-Kendall趋势检验失败: {e}")
            return TrendAnalysisResult(
                tracking_id="",
                trend_statistic=0.0,
                p_value=1.0,
                trend_direction="no_trend",
                significance_level=self.trend_significance_threshold,
                is_intensifying=False
            )
    
    def hot_spot_growth_rate(self, current_detected_hot_spots: List[HotSpotData]) -> Tuple[bool, List[GrowthRateResult]]:
        """
        计算热点增长速率并生成警报
        
        Args:
            current_detected_hot_spots: 当前检测到的热点列表
            
        Returns:
            Tuple[has_alarms, growth_results]
        """
        alarms = []
        growth_results = []
        
        self.total_detections += len(current_detected_hot_spots)
        
        for current_hs in current_detected_hot_spots:
            # 更新跟踪数据
            hs_id = self.update_hot_spot_tracking(current_hs)
            
            # 获取历史数据
            hs_history = self.hot_spot_tracking_data[hs_id]
            
            if len(hs_history) >= 2:
                # 计算与前一帧的增长率
                previous_hs = hs_history[-2]
                
                time_delta = (current_hs.timestamp - previous_hs.timestamp).total_seconds()
                
                if time_delta > 0:
                    # 计算面积增长率
                    area_delta = current_hs.area - previous_hs.area
                    growth_rate_pixels = area_delta / time_delta
                    
                    # 计算百分比增长率
                    if previous_hs.area > 0:
                        growth_rate_percentage = (area_delta / previous_hs.area) * 100
                    else:
                        growth_rate_percentage = 0.0
                    
                    # 分析增长模式
                    growth_pattern = self.calculate_growth_pattern(hs_history)
                    
                    # 计算方向向量
                    direction_vector = self.calculate_direction_vector(hs_history)
                    
                    # 进行Mann-Kendall趋势检验
                    areas = [hs.area for hs in hs_history]
                    trend_result = self.mann_kendall_trend_test(areas)
                    
                    # 确定警报级别
                    alert_level = GrowthAlertLevel.NORMAL
                    
                    if (growth_rate_pixels > self.growth_rate_threshold_pixels or 
                        growth_rate_percentage > self.growth_rate_threshold_percentage):
                        
                        if trend_result.is_intensifying and trend_result.p_value < 0.01:
                            alert_level = GrowthAlertLevel.EMERGENCY
                        elif growth_rate_pixels > self.growth_rate_threshold_pixels * 2:
                            alert_level = GrowthAlertLevel.CRITICAL
                        else:
                            alert_level = GrowthAlertLevel.WARNING
                    
                    # 创建增长率结果
                    growth_result = GrowthRateResult(
                        hot_spot_id=hs_id,
                        current_area=current_hs.area,
                        previous_area=previous_hs.area,
                        growth_rate_pixels_per_sec=growth_rate_pixels,
                        growth_rate_percentage=growth_rate_percentage,
                        time_delta_seconds=time_delta,
                        alert_level=alert_level,
                        growth_pattern=growth_pattern,
                        trend_significance=trend_result.p_value,
                        direction_vector=direction_vector
                    )
                    
                    growth_results.append(growth_result)
                    
                    # 如果需要警报，添加到警报列表
                    if alert_level != GrowthAlertLevel.NORMAL:
                        alarms.append(growth_result)
                        self.total_alarms += 1
                    
                    # 保存到增长历史
                    if hs_id not in self.growth_history:
                        self.growth_history[hs_id] = []
                    self.growth_history[hs_id].append(growth_result)
        
        # 清理过期的跟踪数据
        self._cleanup_old_tracking_data()
        
        return len(alarms) > 0, growth_results
    
    def _cleanup_old_tracking_data(self, max_age_minutes: float = 30.0):
        """清理过期的跟踪数据"""
        current_time = datetime.now()
        cutoff_time = current_time - timedelta(minutes=max_age_minutes)
        
        # 清理跟踪数据
        ids_to_remove = []
        for hs_id, hs_history in self.hot_spot_tracking_data.items():
            if hs_history and hs_history[-1].timestamp < cutoff_time:
                ids_to_remove.append(hs_id)
        
        for hs_id in ids_to_remove:
            del self.hot_spot_tracking_data[hs_id]
            if hs_id in self.growth_history:
                del self.growth_history[hs_id]
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        active_hot_spots = len(self.hot_spot_tracking_data)
        total_growth_records = sum(len(history) for history in self.growth_history.values())
        
        return {
            'total_detections': self.total_detections,
            'total_alarms': self.total_alarms,
            'active_hot_spots': active_hot_spots,
            'total_growth_records': total_growth_records,
            'alarm_rate': self.total_alarms / max(self.total_detections, 1) * 100,
            'next_hot_spot_id': self.next_hot_spot_id
        }
