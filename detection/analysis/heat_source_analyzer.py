#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
热源信息分析器
解析analysis.txt文件并计算每个热源的详细信息，包括最高温度
"""

import os
import re
import numpy as np
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime


@dataclass
class HeatSourceInfo:
    """热源信息数据类"""
    id: int
    position: Tuple[int, int]  # (x, y)
    size: Tuple[int, int]      # (width, height)
    area: int
    max_temperature: float
    min_temperature: float
    bbox: Tuple[int, int, int, int]  # (x, y, w, h)
    # 新增时序相关字段
    tracking_id: Optional[int] = None  # 跟踪ID，用于时序分析
    timestamp: Optional[datetime] = None  # 检测时间戳
    confidence: float = 1.0  # 检测置信度
    # 新增标注区域匹配字段
    annotation_label: Optional[str] = None  # 匹配到的标注区域名称


class HeatSourceAnalyzer:
    """热源信息分析器"""

    def __init__(self, debug_dir: str = "detection/debug_test", enable_tracking: bool = True, enable_area_analysis: bool = True, enable_annotation_matching: bool = True):
        self.debug_dir = debug_dir
        self.latest_analysis_file: Optional[str] = None
        self.latest_heat_sources: List[HeatSourceInfo] = []

        # 时序跟踪功能
        self.enable_tracking = enable_tracking
        self.tracker = None
        self.history = None
        self.change_calculator = None

        # 面积分析功能
        self.enable_area_analysis = enable_area_analysis
        self.area_calculator = None

        # 标注区域匹配功能
        self.enable_annotation_matching = enable_annotation_matching
        self.annotation_matcher = None

        # 自动指标计算功能
        self.enable_auto_metrics = False
        self.comprehensive_metrics_manager = None

        if self.enable_tracking:
            from .heat_source_tracker import HeatSourceTracker
            from .heat_source_history import HeatSourceHistory
            from .change_rate_calculator import ChangeRateCalculator

            self.tracker = HeatSourceTracker()
            self.history = HeatSourceHistory()
            self.change_calculator = ChangeRateCalculator()

        if self.enable_annotation_matching:
            from .annotation_matcher import AnnotationMatcher
            self.annotation_matcher = AnnotationMatcher()

        # Excel数据导出器
        self.excel_exporter = None

        # 场景分析器 - 已禁用
        self.scene_analyzer = None
        self.enable_scene_analysis = False  # 禁用场景分析功能

        if self.enable_area_analysis:
            from .heat_area_calculator import HeatAreaCalculator
            self.area_calculator = HeatAreaCalculator()

        # 场景分析器已禁用
        # if self.enable_scene_analysis:
        #     from .scene_analyzer import get_scene_analyzer
        #     self.scene_analyzer = get_scene_analyzer()
        #     print("🎬 场景分析器已启用")
        print("ℹ️ 场景分析器已禁用，将只使用手动标注区域")

    def set_annotation_manager(self, annotation_manager):
        """设置标注管理器"""
        # 保存标注管理器实例用于同步标注框分析
        self.annotation_manager_instance = annotation_manager

        if self.annotation_matcher:
            self.annotation_matcher.set_annotation_manager(annotation_manager)
            print("🏷️ 热源分析器已连接标注管理器")

        print(f"📝 热源分析器已保存标注管理器实例，当前标注数: {len(annotation_manager.annotations) if annotation_manager else 0}")

    def set_excel_exporter(self, excel_exporter):
        """设置Excel数据导出器"""
        self.excel_exporter = excel_exporter
        print("📊 热源分析器已连接Excel数据导出器")

        # 增长速率分析功能
        self.enable_growth_analysis = enable_tracking  # 增长分析依赖于跟踪功能
        self.growth_analyzer = None

        if self.enable_growth_analysis:
            from .heat_growth_analyzer import HeatGrowthAnalyzer
            self.growth_analyzer = HeatGrowthAnalyzer()

        # 综合指标管理器
        self.enable_auto_metrics = True
        self.comprehensive_metrics_manager = None
        self.last_metrics_trigger_time = None  # 防重复触发
        self.metrics_trigger_interval = 5.0   # 最小触发间隔（秒）
        try:
            from .comprehensive_metrics_manager import get_comprehensive_metrics_manager
            self.comprehensive_metrics_manager = get_comprehensive_metrics_manager()
            print("✅ 热源分析器：综合指标管理器已启用")
        except Exception as e:
            print(f"⚠️ 热源分析器：综合指标管理器初始化失败: {e}")
            self.enable_auto_metrics = False
        
    def get_latest_analysis_file(self) -> Optional[str]:
        """获取最新的analysis.txt文件"""
        try:
            if not os.path.exists(self.debug_dir):
                return None
                
            # 查找所有analysis.txt文件
            analysis_files = []
            for filename in os.listdir(self.debug_dir):
                if filename.startswith('analysis_') and filename.endswith('.txt'):
                    filepath = os.path.join(self.debug_dir, filename)
                    analysis_files.append((filepath, os.path.getmtime(filepath)))
            
            if not analysis_files:
                return None
                
            # 返回最新的文件
            latest_file = max(analysis_files, key=lambda x: x[1])[0]
            return latest_file
            
        except Exception as e:
            print(f"❌ 获取最新分析文件失败: {e}")
            return None
    
    def parse_analysis_file(self, filepath: str) -> Dict:
        """解析analysis.txt文件"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 解析温度数据
            temp_data = self._parse_temperature_data(content)
            
            # 解析检测参数
            detection_params = self._parse_detection_params(content)
            
            # 解析热源结果
            heat_sources = self._parse_heat_sources(content)
            
            return {
                'temperature_data': temp_data,
                'detection_params': detection_params,
                'heat_sources': heat_sources,
                'file_path': filepath,
                'parse_time': datetime.now()
            }
            
        except Exception as e:
            print(f"❌ 解析分析文件失败: {e}")
            return {}
    
    def _parse_temperature_data(self, content: str) -> Dict:
        """解析温度数据部分"""
        temp_data = {}
        
        # 解析最低温度
        min_temp_match = re.search(r'最低温度:\s*([\d.]+)°C', content)
        if min_temp_match:
            temp_data['min_temp'] = float(min_temp_match.group(1))
        
        # 解析最高温度
        max_temp_match = re.search(r'最高温度:\s*([\d.]+)°C', content)
        if max_temp_match:
            temp_data['max_temp'] = float(max_temp_match.group(1))
        
        # 解析平均温度
        avg_temp_match = re.search(r'平均温度:\s*([\d.]+)°C', content)
        if avg_temp_match:
            temp_data['avg_temp'] = float(avg_temp_match.group(1))
        
        # 解析图像尺寸
        size_match = re.search(r'图像尺寸:\s*(\d+)\s*x\s*(\d+)', content)
        if size_match:
            temp_data['width'] = int(size_match.group(1))
            temp_data['height'] = int(size_match.group(2))
        
        return temp_data
    
    def _parse_detection_params(self, content: str) -> Dict:
        """解析检测参数部分"""
        params = {}
        
        # 解析阈值模式
        mode_match = re.search(r'阈值模式:\s*(\S+)', content)
        if mode_match:
            params['threshold_mode'] = mode_match.group(1)
        
        # 解析计算阈值
        threshold_match = re.search(r'计算阈值:\s*([\d.]+)°C', content)
        if threshold_match:
            params['threshold_temp'] = float(threshold_match.group(1))
        
        # 解析最小面积
        area_match = re.search(r'最小面积:\s*(\d+)', content)
        if area_match:
            params['min_area'] = int(area_match.group(1))
        
        return params
    
    def _parse_heat_sources(self, content: str) -> List[Dict]:
        """解析热源结果部分"""
        heat_sources = []
        
        # 解析热源数量
        count_match = re.search(r'热源数量:\s*(\d+)', content)
        if not count_match:
            return heat_sources
        
        # 解析每个热源
        source_pattern = r'热源(\d+):\s*位置\((\d+),(\d+)\)\s*尺寸\((\d+)x(\d+)\)\s*面积\((\d+)\)'
        matches = re.findall(source_pattern, content)
        
        for match in matches:
            source_id, x, y, w, h, area = match
            heat_sources.append({
                'id': int(source_id),
                'x': int(x),
                'y': int(y),
                'width': int(w),
                'height': int(h),
                'area': int(area)
            })
        
        return heat_sources

    def calculate_heat_source_temperatures(self, heat_sources: List[Dict],
                                         temp_matrix: np.ndarray) -> List[HeatSourceInfo]:
        """计算每个热源的温度信息"""
        heat_source_infos = []

        if temp_matrix is None:
            print("⚠️ 温度矩阵为空，无法计算热源温度")
            return heat_source_infos

        try:
            for source in heat_sources:
                # 获取热源边界框
                x, y, w, h = source['x'], source['y'], source['width'], source['height']

                # 边界检查
                matrix_height, matrix_width = temp_matrix.shape
                x = max(0, min(x, matrix_width - 1))
                y = max(0, min(y, matrix_height - 1))
                w = max(1, min(w, matrix_width - x))
                h = max(1, min(h, matrix_height - y))

                # 提取热源区域的温度数据
                region = temp_matrix[y:y+h, x:x+w]

                if region.size > 0:
                    max_temp = float(np.max(region))
                    min_temp = float(np.min(region))
                else:
                    max_temp = 0.0
                    min_temp = 0.0

                # 计算中心点位置
                center_x = x + w // 2
                center_y = y + h // 2

                # 创建热源信息对象
                heat_info = HeatSourceInfo(
                    id=source['id'],
                    position=(center_x, center_y),  # 使用中心点位置
                    size=(w, h),
                    area=source['area'],
                    max_temperature=max_temp,
                    min_temperature=min_temp,
                    bbox=(x, y, w, h)  # 保留原始边界框信息
                )

                heat_source_infos.append(heat_info)

        except Exception as e:
            print(f"❌ 计算热源温度失败: {e}")

        return heat_source_infos

    def analyze_latest_detection(self, temp_matrix: Optional[np.ndarray] = None) -> List[HeatSourceInfo]:
        """分析最新的检测结果"""
        try:
            # 获取最新的分析文件
            latest_file = self.get_latest_analysis_file()
            if not latest_file:
                return []

            # 如果是同一个文件且已经分析过，直接返回缓存结果
            if latest_file == self.latest_analysis_file and self.latest_heat_sources:
                return self.latest_heat_sources

            # 解析分析文件
            analysis_data = self.parse_analysis_file(latest_file)
            if not analysis_data or not analysis_data.get('heat_sources'):
                return []

            # 计算温度信息
            if temp_matrix is not None:
                heat_source_infos = self.calculate_heat_source_temperatures(
                    analysis_data['heat_sources'], temp_matrix
                )
            else:
                # 如果没有温度矩阵，创建基本信息
                heat_source_infos = []
                for source in analysis_data['heat_sources']:
                    # 计算中心点位置
                    center_x = source['x'] + source['width'] // 2
                    center_y = source['y'] + source['height'] // 2

                    heat_info = HeatSourceInfo(
                        id=source['id'],
                        position=(center_x, center_y),  # 使用中心点位置
                        size=(source['width'], source['height']),
                        area=source['area'],
                        max_temperature=0.0,
                        min_temperature=0.0,
                        bbox=(source['x'], source['y'], source['width'], source['height'])  # 保留原始边界框
                    )
                    heat_source_infos.append(heat_info)

            # 应用时序跟踪（如果启用）
            if self.enable_tracking and self.tracker and heat_source_infos:
                heat_source_infos = self._apply_tracking(heat_source_infos)

            # 应用标注区域匹配（如果启用）
            if self.enable_annotation_matching and self.annotation_matcher and heat_source_infos:
                # print(f"🏷️ 开始标注区域匹配，热源数量: {len(heat_source_infos)}")
                heat_source_infos = self._apply_annotation_matching(heat_source_infos)
            # elif not self.enable_annotation_matching:
            #     print("⚠️ 标注区域匹配功能已禁用")
            # elif not self.annotation_matcher:
            #     print("⚠️ 标注匹配器未初始化")
            # elif not heat_source_infos:
            #     print("⚠️ 没有热源需要匹配")

            # 添加同步标注框内的热成像信息分析
            if temp_matrix is not None:
                heat_source_infos = self._add_sync_annotation_analysis(heat_source_infos, temp_matrix)

                # 添加独立的标注区域温度分析（不依赖热源检测）
                annotation_thermal_infos = self._analyze_annotation_regions_independently(temp_matrix)
                if annotation_thermal_infos:
                    # 将标注区域分析结果添加到热源列表中
                    heat_source_infos.extend(annotation_thermal_infos)

            # 缓存结果
            self.latest_analysis_file = latest_file
            self.latest_heat_sources = heat_source_infos

            # 自动触发综合指标计算（如果启用）
            if self.enable_auto_metrics and self.comprehensive_metrics_manager and heat_source_infos:
                self._trigger_metrics_calculation(heat_source_infos)

            # 注意：Excel导出已移至主处理循环，确保使用统一的数据流
            # 热源分析器只负责：检测 → 跟踪 → 匹配，不负责导出

            return heat_source_infos

        except Exception as e:
            print(f"❌ 分析最新检测结果失败: {e}")
            return []

    def get_heat_source_summary(self) -> Dict:
        """获取热源检测摘要信息"""
        if not self.latest_heat_sources:
            return {
                'total_count': 0,
                'total_area': 0,
                'max_temperature': 0.0,
                'min_temperature': 0.0,
                'sources': []
            }

        total_area = sum(source.area for source in self.latest_heat_sources)
        max_temperature = max(source.max_temperature for source in self.latest_heat_sources)
        min_temperature = min(source.min_temperature for source in self.latest_heat_sources)

        return {
            'total_count': len(self.latest_heat_sources),
            'total_area': total_area,
            'max_temperature': max_temperature,
            'min_temperature': min_temperature,
            'sources': self.latest_heat_sources
        }

    def _apply_tracking(self, heat_source_infos: List[HeatSourceInfo]) -> List[HeatSourceInfo]:
        """应用热源跟踪"""
        try:
            # 使用跟踪器更新热源信息
            tracked_sources = self.tracker.update(heat_source_infos)

            # 将跟踪结果添加到历史记录
            if self.history:
                from .heat_source_history import HeatSourceSnapshot

                for source in tracked_sources:
                    if source.tracking_id is not None:
                        snapshot = HeatSourceSnapshot.from_heat_source_info(source, source.timestamp)
                        self.history.add_snapshot(snapshot)

            return tracked_sources

        except Exception as e:
            print(f"⚠️ 热源跟踪失败: {e}")
            return heat_source_infos

    def _apply_annotation_matching(self, heat_source_infos: List[HeatSourceInfo]) -> List[HeatSourceInfo]:
        """应用标注区域匹配"""
        try:
            if not self.annotation_matcher:
                print("⚠️ 标注匹配器未初始化")
                return heat_source_infos

            # 获取可用的标注区域
            thermal_annotations = self.annotation_matcher.get_thermal_annotations()

            # 为每个热源匹配标注区域
            matched_count = 0
            for heat_source in heat_source_infos:
                # print(f"🔍 检查热源{heat_source.id}: 位置{heat_source.position}, 边界框{heat_source.bbox}")

                matched_label = self.annotation_matcher.match_heat_source_to_annotation(
                    heat_source.position,
                    heat_source.bbox,
                    overlap_threshold=0.1  # 使用10%重叠比例阈值
                )

                if matched_label:
                    heat_source.annotation_label = matched_label

                    # 同时获取对应的资产ID（统一数据源）
                    asset_id = self._find_asset_id_for_annotation(matched_label)
                    heat_source.asset_id = asset_id

                    matched_count += 1
                    # print(f"🏷️ 热源{heat_source.id}匹配到标注区域: {matched_label} (资产ID: {asset_id})")
                else:
                    heat_source.annotation_label = None
                    heat_source.asset_id = None
                    # print(f"❌ 热源{heat_source.id}未匹配到任何标注区域")

            # print(f"📊 标注匹配结果: {matched_count}/{len(heat_source_infos)} 个热源匹配成功")
            return heat_source_infos

        except Exception as e:
            print(f"⚠️ 标注区域匹配失败: {e}")
            import traceback
            traceback.print_exc()
            return heat_source_infos

    def _find_asset_id_for_annotation(self, annotation_label: str) -> Optional[str]:
        """根据标注区域名称查找对应的资产ID"""
        try:
            # 方法1: 从内存标注管理器查找
            from detection.analysis.memory_annotation_manager import get_memory_annotation_manager
            manager = get_memory_annotation_manager()
            regions = manager.get_all_regions('Thermal')

            for region in regions:
                if region.asset_name == annotation_label:
                    return region.asset_id

            # 方法2: 从区域管理器查找
            try:
                from detection.analysis.zone_manager import get_zone_manager
                zone_manager = get_zone_manager()
                zones = zone_manager.get_zones_for_camera("camera_01")
                for zone in zones:
                    if zone.name == annotation_label:
                        return zone.id
            except:
                pass

            # 方法3: 从Excel导出器的资产表查找
            if self.excel_exporter:
                for asset in self.excel_exporter.monitored_assets:
                    if asset.get('Asset_Name') == annotation_label:
                        return asset.get('Asset_ID')

            return None

        except Exception as e:
            print(f"⚠️ 查找资产ID失败: {e}")
            return None

    def _apply_scene_analysis(self, heat_source_infos: List[HeatSourceInfo]) -> List[HeatSourceInfo]:
        """应用场景分析"""
        try:
            if not self.scene_analyzer:
                return heat_source_infos

            # 进行场景分析，获取状态事件
            events = self.scene_analyzer.analyze_frame(heat_sources=heat_source_infos)

            # 根据事件结果更新热源的区域标签
            for event in events:
                # 找到对应的热源
                target_id = event.target.id
                for heat_source in heat_source_infos:
                    if f"heat_{heat_source.id}" == target_id:
                        # 如果还没有标注标签，使用场景分析的结果
                        if not hasattr(heat_source, 'annotation_label') or not heat_source.annotation_label:
                            heat_source.annotation_label = event.zone.name
                            print(f"🎬 热源{heat_source.id}通过场景分析匹配到区域: {event.zone.name}")
                        break

            if events:
                print(f"🎬 场景分析生成了 {len(events)} 个事件")

            return heat_source_infos

        except Exception as e:
            print(f"⚠️ 场景分析失败: {e}")
            return heat_source_infos

    def get_change_rates(self, tracking_id: int, time_window_seconds: float = 60.0) -> Dict:
        """获取指定热源的变化率信息"""
        if not self.enable_tracking or not self.change_calculator or not self.history:
            return {}

        try:
            return self.change_calculator.get_latest_change_rates(
                self.history, tracking_id, time_window_seconds
            )
        except Exception as e:
            print(f"⚠️ 获取变化率失败: {e}")
            return {}

    def get_tracking_statistics(self) -> Dict:
        """获取跟踪统计信息"""
        if not self.enable_tracking or not self.tracker:
            return {}

        return self.tracker.get_tracking_statistics()

    def cleanup_tracking_data(self):
        """清理跟踪数据"""
        if self.enable_tracking:
            if self.tracker:
                self.tracker.cleanup_old_sources()
            if self.history:
                self.history.cleanup_old_data()

    def get_heat_source_with_trends(self, tracking_id: int) -> Optional[Dict]:
        """获取带有趋势信息的热源详情"""
        if not self.enable_tracking or not self.history:
            return None

        try:
            # 获取最新快照
            snapshots = self.history.get_snapshots(tracking_id, count=1)
            if not snapshots:
                return None

            latest_snapshot = snapshots[0]

            # 获取变化率
            change_rates = self.get_change_rates(tracking_id)

            return {
                'tracking_id': tracking_id,
                'latest_info': {
                    'position': latest_snapshot.position,
                    'size': latest_snapshot.size,
                    'area': latest_snapshot.area,
                    'max_temperature': latest_snapshot.max_temperature,
                    'min_temperature': latest_snapshot.min_temperature,
                    'avg_temperature': latest_snapshot.avg_temperature,
                    'timestamp': latest_snapshot.timestamp
                },
                'change_rates': change_rates,
                'history_count': len(self.history.get_snapshots(tracking_id, count=100))
            }

        except Exception as e:
            print(f"⚠️ 获取热源趋势信息失败: {e}")
            return None

    def analyze_heat_source_areas(self, contours: List = None) -> Dict:
        """分析热源面积并生成警报"""
        if not self.enable_area_analysis or not self.area_calculator:
            return {}

        try:
            if contours is None:
                # 如果没有提供轮廓，从最新热源信息中构造
                contours = self._create_contours_from_heat_sources()

            if not contours:
                return {}

            # 批量分析轮廓面积
            area_results = self.area_calculator.analyze_contours_batch(contours)

            # 生成摘要
            area_summary = self.area_calculator.get_area_summary(area_results)

            # 检查警报
            alerts = self._generate_area_alerts(area_results)

            return {
                'area_results': area_results,
                'area_summary': area_summary,
                'alerts': alerts,
                'calibration_info': self.area_calculator.get_calibration_info()
            }

        except Exception as e:
            print(f"❌ 热源面积分析失败: {e}")
            return {}

    def _create_contours_from_heat_sources(self) -> List:
        """从热源信息创建轮廓（用于面积分析）"""
        import cv2
        contours = []

        try:
            for source in self.latest_heat_sources:
                x, y, w, h = source.bbox
                # 创建矩形轮廓
                rect_contour = np.array([
                    [x, y],
                    [x + w, y],
                    [x + w, y + h],
                    [x, y + h]
                ], dtype=np.int32)
                contours.append(rect_contour)

        except Exception as e:
            print(f"⚠️ 从热源信息创建轮廓失败: {e}")

        return contours

    def _generate_area_alerts(self, area_results) -> List[Dict]:
        """生成面积警报"""
        alerts = []

        try:
            for result in area_results:
                if result.is_critical:
                    alerts.append({
                        'level': 'critical',
                        'message': f"严重警报：热源{result.contour_id + 1}面积过大",
                        'area_pixels': result.area_pixels,
                        'area_m2': result.area_m2,
                        'bbox': result.bbox,
                        'timestamp': datetime.now()
                    })
                elif result.is_warning:
                    alerts.append({
                        'level': 'warning',
                        'message': f"警告：热源{result.contour_id + 1}面积较大",
                        'area_pixels': result.area_pixels,
                        'area_m2': result.area_m2,
                        'bbox': result.bbox,
                        'timestamp': datetime.now()
                    })

        except Exception as e:
            print(f"⚠️ 生成面积警报失败: {e}")

        return alerts

    def _trigger_metrics_calculation(self, heat_source_infos: List[HeatSourceInfo]):
        """触发综合指标计算（带防重复机制）"""
        try:
            current_time = datetime.now()

            # 检查是否需要触发（防止过于频繁）
            if (self.last_metrics_trigger_time is not None and
                (current_time - self.last_metrics_trigger_time).total_seconds() < self.metrics_trigger_interval):
                return  # 跳过本次触发

            # 将HeatSourceInfo转换为HeatSourceSnapshot
            snapshots = []

            for source in heat_source_infos:
                # 计算平均温度（如果没有则使用最高温度）
                avg_temp = (source.max_temperature + source.min_temperature) / 2 if source.min_temperature > 0 else source.max_temperature

                # 创建快照
                from .heat_source_history import HeatSourceSnapshot
                snapshot = HeatSourceSnapshot(
                    timestamp=current_time,
                    tracking_id=source.tracking_id if source.tracking_id is not None else source.id,
                    position=source.position,
                    size=source.size,
                    area=source.area,
                    max_temperature=source.max_temperature,
                    min_temperature=source.min_temperature,
                    avg_temperature=avg_temp,
                    bbox=source.bbox,
                    confidence=source.confidence
                )
                snapshots.append(snapshot)

            # 批量处理快照，计算所有指标
            self.comprehensive_metrics_manager.process_snapshots(snapshots)

            # 更新最后触发时间
            self.last_metrics_trigger_time = current_time

            # 简化调试信息输出，保持后台简洁
            # print(f"📊 已触发 {len(snapshots)} 个热源的综合指标计算")

        except Exception as e:
            print(f"⚠️ 触发指标计算失败: {e}")

    def get_comprehensive_metrics_report(self) -> Dict:
        """获取综合指标报告"""
        if not self.comprehensive_metrics_manager:
            return {}

        try:
            return self.comprehensive_metrics_manager.get_comprehensive_report()
        except Exception as e:
            print(f"⚠️ 获取综合指标报告失败: {e}")
            return {}

    def export_comprehensive_metrics(self, filepath: str) -> bool:
        """导出综合指标数据"""
        if not self.comprehensive_metrics_manager:
            return False

        try:
            return self.comprehensive_metrics_manager.export_metrics_to_json(filepath)
        except Exception as e:
            print(f"⚠️ 导出综合指标失败: {e}")
            return False

    def set_area_thresholds(self, warning_pixels: int = None, critical_pixels: int = None,
                           warning_m2: float = None, critical_m2: float = None):
        """设置面积阈值"""
        if self.area_calculator:
            self.area_calculator.set_thresholds(warning_pixels, critical_pixels, warning_m2, critical_m2)

    def set_target_distance(self, distance_meters: float):
        """设置目标距离（用于像素到物理面积转换）"""
        if self.area_calculator:
            self.area_calculator.set_distance(distance_meters)

    def get_area_calibration_info(self) -> Dict:
        """获取面积标定信息"""
        if self.area_calculator:
            return self.area_calculator.get_calibration_info()
        return {}

    def analyze_growth_rates(self, contours: List = None, temp_matrix: Optional[np.ndarray] = None) -> Dict:
        """
        分析热源增长速率

        Args:
            contours: 热源轮廓列表
            temp_matrix: 温度矩阵

        Returns:
            增长速率分析结果
        """
        if not self.enable_growth_analysis or not self.growth_analyzer:
            return {}

        try:
            # 如果没有提供轮廓，从最新热源信息中构造
            if contours is None:
                contours = self._create_contours_from_heat_sources()

            if not contours:
                return {}

            # 将轮廓转换为HotSpotData格式
            hot_spots = self._convert_contours_to_hot_spots(contours, temp_matrix)

            # 执行增长速率分析
            has_alarms, growth_results = self.growth_analyzer.hot_spot_growth_rate(hot_spots)

            # 生成增长警报
            growth_alerts = self._generate_growth_alerts(growth_results)

            # 获取统计信息
            growth_stats = self.growth_analyzer.get_statistics()

            return {
                'has_alarms': has_alarms,
                'growth_results': growth_results,
                'growth_alerts': growth_alerts,
                'growth_statistics': growth_stats,
                'total_hot_spots': len(hot_spots)
            }

        except Exception as e:
            print(f"❌ 增长速率分析失败: {e}")
            return {}

    def _add_sync_annotation_analysis(self, heat_source_infos: List, temp_matrix: np.ndarray) -> List:
        """
        为热源信息添加同步标注框内的热成像分析数据

        Args:
            heat_source_infos: 热源信息列表
            temp_matrix: 温度矩阵

        Returns:
            增强后的热源信息列表
        """
        try:
            # 获取同步标注管理器
            sync_annotation_data = self._get_sync_annotation_data()
            if not sync_annotation_data:
                return heat_source_infos

            # 为每个热源添加同步标注框分析
            enhanced_heat_sources = []
            for heat_source in heat_source_infos:
                enhanced_source = heat_source

                # 查找与热源重叠的同步标注框
                overlapping_annotations = self._find_overlapping_sync_annotations(
                    heat_source, sync_annotation_data
                )

                if overlapping_annotations:
                    # 分析同步标注框内的热成像数据
                    annotation_analysis = self._analyze_annotation_thermal_data(
                        overlapping_annotations, temp_matrix
                    )

                    # 将分析结果添加到热源信息中
                    if hasattr(enhanced_source, '__dict__'):
                        enhanced_source.sync_annotation_analysis = annotation_analysis
                    else:
                        # 如果是字典格式，直接添加
                        if isinstance(enhanced_source, dict):
                            enhanced_source['sync_annotation_analysis'] = annotation_analysis

                enhanced_heat_sources.append(enhanced_source)

            return enhanced_heat_sources

        except Exception as e:
            print(f"❌ 同步标注框分析失败: {e}")
            return heat_source_infos

    def _get_sync_annotation_data(self) -> List[Dict]:
        """
        获取同步标注框数据

        Returns:
            同步标注框信息列表
        """
        try:
            # 尝试从保存的标注管理器实例获取
            sync_annotations = []

            # 方法1: 优先使用保存的标注管理器实例
            if hasattr(self, 'annotation_manager_instance') and self.annotation_manager_instance:
                annotation_manager = self.annotation_manager_instance

                if annotation_manager.annotations:
                    print(f"🔍 从保存的标注管理器实例获取到 {len(annotation_manager.annotations)} 个标注")

                    for ann_id, annotation in annotation_manager.annotations.items():
                        if getattr(annotation, 'is_thermal', False) and annotation.visible:
                            sync_annotations.append({
                                'id': ann_id,
                                'label': annotation.label,
                                'bbox': annotation.bbox,  # (x, y, width, height)
                                'color': annotation.color
                            })
                            print(f"📍 找到热成像标注: {ann_id} - {annotation.label}")

                    return sync_annotations
                else:
                    print(f"⚠️ 保存的标注管理器实例无标注数据")
            else:
                print(f"⚠️ 未找到保存的标注管理器实例")

            # 方法2: 尝试从全局标注管理器获取（备选方案）
            try:
                from qt_ui.manual_annotation_widget import get_global_annotation_manager
                annotation_manager = get_global_annotation_manager()

                if annotation_manager and annotation_manager.annotations:
                    print(f"🔍 从全局标注管理器获取到 {len(annotation_manager.annotations)} 个标注")

                    for ann_id, annotation in annotation_manager.annotations.items():
                        if getattr(annotation, 'is_thermal', False) and annotation.visible:
                            sync_annotations.append({
                                'id': ann_id,
                                'label': annotation.label,
                                'bbox': annotation.bbox,  # (x, y, width, height)
                                'color': annotation.color
                            })
                            print(f"📍 找到热成像标注: {ann_id} - {annotation.label}")

                    return sync_annotations
                else:
                    print(f"⚠️ 全局标注管理器为空或无标注数据")

            except Exception as e:
                print(f"⚠️ 从全局标注管理器获取失败: {e}")

            # 方法3: 尝试从内存标注管理器获取
            try:
                from detection.analysis.memory_annotation_manager import get_memory_annotation_manager
                memory_manager = get_memory_annotation_manager()

                if memory_manager:
                    thermal_regions = memory_manager.get_all_regions('Thermal')
                    print(f"🔍 从内存标注管理器获取到 {len(thermal_regions)} 个热成像区域")

                    for region in thermal_regions:
                        sync_annotations.append({
                            'id': region.asset_id,
                            'label': region.asset_name,
                            'bbox': region.bbox,
                            'color': (0, 255, 0)  # 默认颜色
                        })
                        print(f"📍 找到热成像区域: {region.asset_id} - {region.asset_name}")

                    return sync_annotations
                else:
                    print(f"⚠️ 内存标注管理器为空")

            except Exception as e:
                print(f"⚠️ 从内存标注管理器获取失败: {e}")

            # 方法4: 创建新实例（最后的备选方案）
            try:
                from qt_ui.annotation_sync_manager import AnnotationSyncManager
                from qt_ui.manual_annotation_widget import ManualAnnotationManager

                annotation_manager = ManualAnnotationManager()

                if annotation_manager.annotations:
                    print(f"🔍 从新建标注管理器获取到 {len(annotation_manager.annotations)} 个标注")

                    for ann_id, annotation in annotation_manager.annotations.items():
                        if getattr(annotation, 'is_thermal', False) and annotation.visible:
                            sync_annotations.append({
                                'id': ann_id,
                                'label': annotation.label,
                                'bbox': annotation.bbox,
                                'color': annotation.color
                            })

                    return sync_annotations
                else:
                    print(f"⚠️ 新建标注管理器无标注数据")

            except Exception as e:
                print(f"⚠️ 创建新标注管理器失败: {e}")

            print(f"❌ 所有方法都无法获取同步标注数据")
            return []

        except Exception as e:
            print(f"❌ 获取同步标注数据失败: {e}")
            return []

    def _find_overlapping_sync_annotations(self, heat_source, sync_annotations: List[Dict]) -> List[Dict]:
        """
        查找与热源重叠的同步标注框

        Args:
            heat_source: 热源信息
            sync_annotations: 同步标注框列表

        Returns:
            重叠的标注框列表
        """
        try:
            overlapping = []

            # 获取热源边界框
            if hasattr(heat_source, 'bbox'):
                heat_bbox = heat_source.bbox
            elif hasattr(heat_source, 'position') and hasattr(heat_source, 'size'):
                # 从位置和尺寸构造边界框
                x = heat_source.position[0] - heat_source.size[0] // 2
                y = heat_source.position[1] - heat_source.size[1] // 2
                heat_bbox = (x, y, heat_source.size[0], heat_source.size[1])
            else:
                return overlapping

            # 检查每个同步标注框
            for annotation in sync_annotations:
                ann_bbox = annotation['bbox']

                # 计算重叠面积
                overlap_ratio = self._calculate_bbox_overlap(heat_bbox, ann_bbox)

                # 如果重叠比例超过阈值，认为是重叠的
                if overlap_ratio > 0.1:  # 10%重叠阈值
                    annotation['overlap_ratio'] = overlap_ratio
                    overlapping.append(annotation)

            return overlapping

        except Exception as e:
            print(f"⚠️ 查找重叠标注失败: {e}")
            return []

    def _calculate_bbox_overlap(self, bbox1: tuple, bbox2: tuple) -> float:
        """
        计算两个边界框的重叠比例

        Args:
            bbox1: 边界框1 (x, y, width, height)
            bbox2: 边界框2 (x, y, width, height)

        Returns:
            重叠比例 (0.0 - 1.0)
        """
        try:
            x1, y1, w1, h1 = bbox1
            x2, y2, w2, h2 = bbox2

            # 计算重叠区域
            overlap_x = max(0, min(x1 + w1, x2 + w2) - max(x1, x2))
            overlap_y = max(0, min(y1 + h1, y2 + h2) - max(y1, y2))
            overlap_area = overlap_x * overlap_y

            # 计算总面积
            area1 = w1 * h1
            area2 = w2 * h2
            union_area = area1 + area2 - overlap_area

            # 返回重叠比例
            return overlap_area / union_area if union_area > 0 else 0.0

        except Exception as e:
            print(f"⚠️ 计算重叠比例失败: {e}")
            return 0.0

    def _analyze_annotation_thermal_data(self, annotations: List[Dict], temp_matrix: np.ndarray) -> Dict:
        """
        分析标注框内的热成像数据

        Args:
            annotations: 重叠的标注框列表
            temp_matrix: 温度矩阵

        Returns:
            标注框内热成像分析结果
        """
        try:
            analysis_results = {}

            for annotation in annotations:
                ann_id = annotation['id']
                ann_label = annotation['label']
                ann_bbox = annotation['bbox']

                # 提取标注框内的温度数据
                thermal_data = self._extract_thermal_data_from_bbox(ann_bbox, temp_matrix)

                if thermal_data:
                    analysis_results[ann_id] = {
                        'label': ann_label,
                        'bbox': ann_bbox,
                        'overlap_ratio': annotation.get('overlap_ratio', 0.0),
                        'thermal_analysis': thermal_data
                    }

            return analysis_results

        except Exception as e:
            print(f"❌ 分析标注框热成像数据失败: {e}")
            return {}

    def _extract_thermal_data_from_bbox(self, bbox: tuple, temp_matrix: np.ndarray) -> Dict:
        """
        从边界框内提取热成像数据

        Args:
            bbox: 边界框 (x, y, width, height)
            temp_matrix: 温度矩阵

        Returns:
            热成像数据分析结果
        """
        try:
            x, y, w, h = bbox

            # 边界检查
            matrix_height, matrix_width = temp_matrix.shape
            x = max(0, min(x, matrix_width - 1))
            y = max(0, min(y, matrix_height - 1))
            w = max(1, min(w, matrix_width - x))
            h = max(1, min(h, matrix_height - y))

            # 提取区域温度数据
            region = temp_matrix[y:y+h, x:x+w]

            if region.size == 0:
                return {}

            # 计算统计信息
            max_temp = float(np.max(region))
            min_temp = float(np.min(region))
            avg_temp = float(np.mean(region))
            std_temp = float(np.std(region))

            # 计算温度分布
            temp_range = max_temp - min_temp
            hot_pixels = np.sum(region > (avg_temp + std_temp))  # 高温像素数
            cold_pixels = np.sum(region < (avg_temp - std_temp))  # 低温像素数
            total_pixels = region.size

            # 查找最热点位置
            max_pos = np.unravel_index(np.argmax(region), region.shape)
            hottest_point = (x + max_pos[1], y + max_pos[0])  # 转换为全局坐标

            return {
                'max_temperature': max_temp,
                'min_temperature': min_temp,
                'avg_temperature': avg_temp,
                'std_temperature': std_temp,
                'temperature_range': temp_range,
                'hottest_point': hottest_point,
                'hot_pixel_ratio': hot_pixels / total_pixels,
                'cold_pixel_ratio': cold_pixels / total_pixels,
                'total_pixels': total_pixels,
                'region_size': (w, h)
            }

        except Exception as e:
            print(f"❌ 提取热成像数据失败: {e}")
            return {}

    def _analyze_annotation_regions_independently(self, temp_matrix: np.ndarray) -> List:
        """
        独立分析标注区域的温度信息（不依赖热源检测）

        Args:
            temp_matrix: 温度矩阵

        Returns:
            标注区域温度分析结果列表
        """
        try:
            # 获取同步标注数据
            sync_annotation_data = self._get_sync_annotation_data()
            if not sync_annotation_data:
                return []

            annotation_thermal_infos = []

            for annotation in sync_annotation_data:
                ann_id = annotation['id']
                ann_label = annotation['label']
                ann_bbox = annotation['bbox']

                # 提取标注区域的温度数据
                thermal_data = self._extract_thermal_data_from_bbox(ann_bbox, temp_matrix)

                if thermal_data:
                    # 创建标注区域热成像信息对象
                    annotation_thermal_info = self._create_annotation_thermal_info(
                        ann_id, ann_label, ann_bbox, thermal_data
                    )

                    annotation_thermal_infos.append(annotation_thermal_info)

                    print(f"📍 独立分析标注区域: {ann_label}")
                    print(f"   温度范围: {thermal_data['min_temperature']:.1f}°C ~ {thermal_data['max_temperature']:.1f}°C")
                    print(f"   平均温度: {thermal_data['avg_temperature']:.1f}°C")

            return annotation_thermal_infos

        except Exception as e:
            print(f"❌ 独立分析标注区域失败: {e}")
            return []

    def _create_annotation_thermal_info(self, ann_id: str, ann_label: str,
                                       ann_bbox: tuple, thermal_data: Dict) -> object:
        """
        创建标注区域热成像信息对象

        Args:
            ann_id: 标注ID
            ann_label: 标注标签
            ann_bbox: 标注边界框
            thermal_data: 热成像分析数据

        Returns:
            标注区域热成像信息对象
        """
        try:
            x, y, w, h = ann_bbox
            center_x = x + w // 2
            center_y = y + h // 2

            # 创建类似HeatSourceInfo的对象
            class AnnotationThermalInfo:
                def __init__(self):
                    self.id = f"annotation_{ann_id}"
                    self.position = (center_x, center_y)
                    self.size = (w, h)
                    self.bbox = ann_bbox
                    self.area = w * h
                    self.max_temperature = thermal_data['max_temperature']
                    self.min_temperature = thermal_data['min_temperature']
                    self.avg_temperature = thermal_data.get('avg_temperature', 0)
                    self.tracking_id = None  # 标注区域不需要跟踪
                    self.annotation_label = ann_label
                    self.confidence = 1.0

                    # 标记这是标注区域分析，不是热源检测
                    self.is_annotation_analysis = True
                    self.annotation_id = ann_id

                    # 添加详细的热成像分析数据
                    self.thermal_analysis = thermal_data

                    # 添加标注区域特有的信息
                    self.annotation_thermal_analysis = {
                        ann_id: {
                            'label': ann_label,
                            'bbox': ann_bbox,
                            'overlap_ratio': 1.0,  # 标注区域与自身100%重叠
                            'thermal_analysis': thermal_data
                        }
                    }

            return AnnotationThermalInfo()

        except Exception as e:
            print(f"❌ 创建标注区域热成像信息对象失败: {e}")
            return None

    def _convert_contours_to_hot_spots(self, contours: List, temp_matrix: Optional[np.ndarray] = None) -> List:
        """将轮廓转换为HotSpotData格式"""
        from .heat_growth_analyzer import HotSpotData
        import cv2

        hot_spots = []
        current_time = datetime.now()

        try:
            for i, contour in enumerate(contours):
                # 计算质心
                M = cv2.moments(contour)
                if M["m00"] != 0:
                    cx = M["m10"] / M["m00"]
                    cy = M["m01"] / M["m00"]
                else:
                    x, y, w, h = cv2.boundingRect(contour)
                    cx, cy = x + w/2, y + h/2

                # 计算面积
                area = cv2.contourArea(contour)

                # 计算边界框
                bbox = cv2.boundingRect(contour)

                # 计算最高温度（如果有温度矩阵）
                max_temp = 0.0
                if temp_matrix is not None:
                    x, y, w, h = bbox
                    # 边界检查
                    matrix_height, matrix_width = temp_matrix.shape
                    x = max(0, min(x, matrix_width - 1))
                    y = max(0, min(y, matrix_height - 1))
                    w = max(1, min(w, matrix_width - x))
                    h = max(1, min(h, matrix_height - y))

                    region = temp_matrix[y:y+h, x:x+w]
                    if region.size > 0:
                        max_temp = float(np.max(region))

                # 创建HotSpotData对象
                hot_spot = HotSpotData(
                    id=f"temp_{i}",  # 临时ID，会在跟踪时更新
                    center=(float(cx), float(cy)),
                    area=float(area),
                    timestamp=current_time,
                    bbox=bbox,
                    contour=contour,
                    max_temperature=max_temp
                )

                hot_spots.append(hot_spot)

        except Exception as e:
            print(f"⚠️ 轮廓转换失败: {e}")

        return hot_spots

    def _generate_growth_alerts(self, growth_results) -> List[Dict]:
        """生成增长速率警报"""
        from .heat_growth_analyzer import GrowthAlertLevel

        alerts = []

        try:
            for result in growth_results:
                if result.alert_level != GrowthAlertLevel.NORMAL:
                    alert_message = self._format_growth_alert_message(result)

                    alerts.append({
                        'level': result.alert_level.value,
                        'message': alert_message,
                        'hot_spot_id': result.hot_spot_id,
                        'growth_rate_pixels': result.growth_rate_pixels_per_sec,
                        'growth_rate_percentage': result.growth_rate_percentage,
                        'current_area': result.current_area,
                        'growth_pattern': result.growth_pattern.value,
                        'trend_significance': result.trend_significance,
                        'direction_vector': result.direction_vector,
                        'timestamp': datetime.now()
                    })

        except Exception as e:
            print(f"⚠️ 生成增长警报失败: {e}")

        return alerts

    def _format_growth_alert_message(self, result) -> str:
        """格式化增长警报消息"""
        from .heat_growth_analyzer import GrowthAlertLevel, GrowthPattern

        level_messages = {
            GrowthAlertLevel.WARNING: "警告：热源快速增长",
            GrowthAlertLevel.CRITICAL: "严重：热源急剧扩大",
            GrowthAlertLevel.EMERGENCY: "紧急：火势蔓延加速"
        }

        pattern_descriptions = {
            GrowthPattern.LINEAR: "线性增长",
            GrowthPattern.CIRCULAR: "圆形扩散",
            GrowthPattern.DIRECTIONAL: "定向蔓延",
            GrowthPattern.IRREGULAR: "不规则增长"
        }

        base_message = level_messages.get(result.alert_level, "热源增长异常")
        pattern_desc = pattern_descriptions.get(result.growth_pattern, "未知模式")

        return f"{base_message} - {pattern_desc} ({result.growth_rate_pixels_per_sec:.1f}像素/秒, {result.growth_rate_percentage:.1f}%)"

    def set_growth_thresholds(self, pixels_per_sec: float = None, percentage: float = None):
        """设置增长速率阈值"""
        if self.growth_analyzer:
            if pixels_per_sec is not None:
                self.growth_analyzer.growth_rate_threshold_pixels = pixels_per_sec
            if percentage is not None:
                self.growth_analyzer.growth_rate_threshold_percentage = percentage

    def get_growth_statistics(self) -> Dict:
        """获取增长分析统计信息"""
        if self.growth_analyzer:
            return self.growth_analyzer.get_statistics()
        return {}
