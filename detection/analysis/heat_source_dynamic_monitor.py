#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
热源动态监控模块
监控热源面积扩展、数量变化、温度上升率等动态特征
"""

import time
import numpy as np
from typing import List, Dict, Tuple, Optional, Any
from dataclasses import dataclass, field
from enum import Enum
from collections import deque

from utils.logger import get_logger


class MonitoringType(Enum):
    """监控类型枚举"""
    AREA_EXPANSION = "area_expansion"         # 面积扩展监控
    COUNT_CHANGE = "count_change"             # 数量变化监控
    TEMPERATURE_RISE = "temperature_rise"     # 温度上升监控
    COMBINED = "combined"                     # 综合监控


@dataclass
class HeatSourceSnapshot:
    """热源快照"""
    timestamp: float                          # 时间戳
    total_area_pixels: int                    # 总面积（像素）
    heat_source_count: int                    # 热源数量
    average_temperature: float                # 平均温度
    max_temperature: float                    # 最高温度
    area_percentage: float                    # 面积占比（%）
    individual_areas: List[int] = field(default_factory=list)  # 各热源面积
    individual_temps: List[float] = field(default_factory=list)  # 各热源温度


@dataclass
class DynamicMonitorConfig:
    """动态监控配置"""
    # 面积扩展监控配置
    area_growth_threshold: float = 0.4        # 面积增长率阈值（%/秒）
    area_monitoring_window: float = 10.0      # 面积监控时间窗口（秒）
    
    # 数量变化监控配置
    count_change_threshold: int = 1           # 数量变化阈值
    count_monitoring_window: float = 5.0      # 数量监控时间窗口（秒）
    
    # 温度上升监控配置
    temp_rise_threshold: float = 0.04         # 温度上升率阈值（℃/秒）
    temp_monitoring_window: float = 15.0      # 温度监控时间窗口（秒）
    
    # 综合监控配置
    snapshot_interval: float = 1.0            # 快照间隔（秒）
    max_snapshots: int = 100                  # 最大快照数量
    
    # 触发条件
    min_trigger_duration: float = 3.0         # 最小触发持续时间（秒）


@dataclass
class MonitoringResult:
    """监控结果"""
    monitoring_type: MonitoringType           # 监控类型
    is_triggered: bool                        # 是否触发
    trigger_value: float                      # 触发值
    threshold_value: float                    # 阈值
    duration_seconds: float                   # 持续时间
    confidence: float                         # 置信度
    details: Dict[str, Any] = field(default_factory=dict)  # 详细信息


class HeatSourceDynamicMonitor:
    """热源动态监控器"""
    
    def __init__(self, config: Optional[DynamicMonitorConfig] = None):
        """
        初始化热源动态监控器
        
        Args:
            config: 监控配置
        """
        self.logger = get_logger("HeatSourceDynamicMonitor")
        self.config = config or DynamicMonitorConfig()
        
        # 快照历史
        self.snapshots: deque = deque(maxlen=self.config.max_snapshots)
        
        # 监控状态
        self.last_snapshot_time = 0.0
        self.monitoring_start_time = time.time()
        
        # 触发状态
        self.area_trigger_start = None
        self.count_trigger_start = None
        self.temp_trigger_start = None
        
        # 统计信息
        self.total_snapshots = 0
        self.area_triggers = 0
        self.count_triggers = 0
        self.temp_triggers = 0
    
    def update_heat_sources(self, 
                          heat_sources: List[Dict],
                          frame_area: int,
                          current_time: Optional[float] = None) -> List[MonitoringResult]:
        """
        更新热源数据并执行监控
        
        Args:
            heat_sources: 热源列表
            frame_area: 画面总面积
            current_time: 当前时间（可选）
            
        Returns:
            监控结果列表
        """
        if current_time is None:
            current_time = time.time()
        
        # 检查快照间隔
        if current_time - self.last_snapshot_time < self.config.snapshot_interval:
            return []
        
        # 创建快照
        snapshot = self._create_snapshot(heat_sources, frame_area, current_time)
        self.snapshots.append(snapshot)
        self.last_snapshot_time = current_time
        self.total_snapshots += 1
        
        # 执行监控分析
        monitoring_results = []
        
        # 1. 面积扩展监控
        area_result = self._monitor_area_expansion(current_time)
        if area_result:
            monitoring_results.append(area_result)
        
        # 2. 数量变化监控
        count_result = self._monitor_count_change(current_time)
        if count_result:
            monitoring_results.append(count_result)
        
        # 3. 温度上升监控
        temp_result = self._monitor_temperature_rise(current_time)
        if temp_result:
            monitoring_results.append(temp_result)
        
        return monitoring_results
    
    def _create_snapshot(self, 
                        heat_sources: List[Dict],
                        frame_area: int,
                        timestamp: float) -> HeatSourceSnapshot:
        """创建热源快照"""
        if not heat_sources:
            return HeatSourceSnapshot(
                timestamp=timestamp,
                total_area_pixels=0,
                heat_source_count=0,
                average_temperature=0.0,
                max_temperature=0.0,
                area_percentage=0.0
            )
        
        # 计算统计信息
        individual_areas = []
        individual_temps = []
        total_area = 0
        total_temp = 0.0
        max_temp = 0.0
        
        for heat_source in heat_sources:
            # 获取面积
            if 'area_pixels' in heat_source:
                area = heat_source['area_pixels']
            elif 'bbox' in heat_source:
                x, y, w, h = heat_source['bbox']
                area = w * h
            else:
                area = 0
            
            individual_areas.append(area)
            total_area += area
            
            # 获取温度
            temp = heat_source.get('temperature', 0.0)
            individual_temps.append(temp)
            total_temp += temp
            max_temp = max(max_temp, temp)
        
        count = len(heat_sources)
        avg_temp = total_temp / count if count > 0 else 0.0
        area_percentage = (total_area / frame_area * 100) if frame_area > 0 else 0.0
        
        return HeatSourceSnapshot(
            timestamp=timestamp,
            total_area_pixels=total_area,
            heat_source_count=count,
            average_temperature=avg_temp,
            max_temperature=max_temp,
            area_percentage=area_percentage,
            individual_areas=individual_areas,
            individual_temps=individual_temps
        )
    
    def _monitor_area_expansion(self, current_time: float) -> Optional[MonitoringResult]:
        """监控面积扩展"""
        if len(self.snapshots) < 2:
            return None
        
        # 获取监控窗口内的快照
        window_snapshots = self._get_snapshots_in_window(
            current_time, self.config.area_monitoring_window
        )
        
        if len(window_snapshots) < 2:
            return None
        
        # 计算面积增长率
        oldest_snapshot = window_snapshots[0]
        latest_snapshot = window_snapshots[-1]
        
        time_diff = latest_snapshot.timestamp - oldest_snapshot.timestamp
        if time_diff <= 0:
            return None
        
        area_diff = latest_snapshot.total_area_pixels - oldest_snapshot.total_area_pixels
        growth_rate = (area_diff / oldest_snapshot.total_area_pixels * 100 / time_diff) if oldest_snapshot.total_area_pixels > 0 else 0.0
        
        # 检查是否触发
        is_triggered = growth_rate >= self.config.area_growth_threshold
        
        # 更新触发状态
        if is_triggered:
            if self.area_trigger_start is None:
                self.area_trigger_start = current_time
            trigger_duration = current_time - self.area_trigger_start
        else:
            self.area_trigger_start = None
            trigger_duration = 0.0
        
        # 检查是否满足最小触发持续时间
        if is_triggered and trigger_duration >= self.config.min_trigger_duration:
            self.area_triggers += 1
            confidence = min(growth_rate / self.config.area_growth_threshold, 2.0) * 0.5
            
            return MonitoringResult(
                monitoring_type=MonitoringType.AREA_EXPANSION,
                is_triggered=True,
                trigger_value=growth_rate,
                threshold_value=self.config.area_growth_threshold,
                duration_seconds=trigger_duration,
                confidence=confidence,
                details={
                    'area_change_pixels': area_diff,
                    'initial_area': oldest_snapshot.total_area_pixels,
                    'current_area': latest_snapshot.total_area_pixels,
                    'window_duration': time_diff
                }
            )
        
        return None
    
    def _monitor_count_change(self, current_time: float) -> Optional[MonitoringResult]:
        """监控数量变化"""
        if len(self.snapshots) < 2:
            return None
        
        # 获取监控窗口内的快照
        window_snapshots = self._get_snapshots_in_window(
            current_time, self.config.count_monitoring_window
        )
        
        if len(window_snapshots) < 2:
            return None
        
        # 检查数量变化
        oldest_snapshot = window_snapshots[0]
        latest_snapshot = window_snapshots[-1]
        
        count_change = latest_snapshot.heat_source_count - oldest_snapshot.heat_source_count
        
        # 检查是否触发（数量增加）
        is_triggered = count_change >= self.config.count_change_threshold
        
        # 更新触发状态
        if is_triggered:
            if self.count_trigger_start is None:
                self.count_trigger_start = current_time
            trigger_duration = current_time - self.count_trigger_start
        else:
            self.count_trigger_start = None
            trigger_duration = 0.0
        
        # 检查是否满足最小触发持续时间
        if is_triggered and trigger_duration >= self.config.min_trigger_duration:
            self.count_triggers += 1
            confidence = min(count_change / self.config.count_change_threshold, 3.0) * 0.33
            
            return MonitoringResult(
                monitoring_type=MonitoringType.COUNT_CHANGE,
                is_triggered=True,
                trigger_value=float(count_change),
                threshold_value=float(self.config.count_change_threshold),
                duration_seconds=trigger_duration,
                confidence=confidence,
                details={
                    'initial_count': oldest_snapshot.heat_source_count,
                    'current_count': latest_snapshot.heat_source_count,
                    'count_increase': count_change
                }
            )
        
        return None
    
    def _monitor_temperature_rise(self, current_time: float) -> Optional[MonitoringResult]:
        """监控温度上升"""
        if len(self.snapshots) < 2:
            return None
        
        # 获取监控窗口内的快照
        window_snapshots = self._get_snapshots_in_window(
            current_time, self.config.temp_monitoring_window
        )
        
        if len(window_snapshots) < 2:
            return None
        
        # 计算温度上升率
        oldest_snapshot = window_snapshots[0]
        latest_snapshot = window_snapshots[-1]
        
        time_diff = latest_snapshot.timestamp - oldest_snapshot.timestamp
        if time_diff <= 0:
            return None
        
        temp_diff = latest_snapshot.max_temperature - oldest_snapshot.max_temperature
        temp_rise_rate = temp_diff / time_diff
        
        # 检查是否触发
        is_triggered = temp_rise_rate >= self.config.temp_rise_threshold
        
        # 更新触发状态
        if is_triggered:
            if self.temp_trigger_start is None:
                self.temp_trigger_start = current_time
            trigger_duration = current_time - self.temp_trigger_start
        else:
            self.temp_trigger_start = None
            trigger_duration = 0.0
        
        # 检查是否满足最小触发持续时间
        if is_triggered and trigger_duration >= self.config.min_trigger_duration:
            self.temp_triggers += 1
            confidence = min(temp_rise_rate / self.config.temp_rise_threshold, 2.0) * 0.5
            
            return MonitoringResult(
                monitoring_type=MonitoringType.TEMPERATURE_RISE,
                is_triggered=True,
                trigger_value=temp_rise_rate,
                threshold_value=self.config.temp_rise_threshold,
                duration_seconds=trigger_duration,
                confidence=confidence,
                details={
                    'temperature_change': temp_diff,
                    'initial_temp': oldest_snapshot.max_temperature,
                    'current_temp': latest_snapshot.max_temperature,
                    'window_duration': time_diff
                }
            )
        
        return None
    
    def _get_snapshots_in_window(self, current_time: float, window_duration: float) -> List[HeatSourceSnapshot]:
        """获取时间窗口内的快照"""
        cutoff_time = current_time - window_duration
        return [snapshot for snapshot in self.snapshots if snapshot.timestamp >= cutoff_time]
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            'total_snapshots': self.total_snapshots,
            'area_triggers': self.area_triggers,
            'count_triggers': self.count_triggers,
            'temp_triggers': self.temp_triggers,
            'monitoring_duration': time.time() - self.monitoring_start_time,
            'current_snapshot_count': len(self.snapshots)
        }
    
    def reset(self):
        """重置监控器"""
        self.snapshots.clear()
        self.last_snapshot_time = 0.0
        self.monitoring_start_time = time.time()
        self.area_trigger_start = None
        self.count_trigger_start = None
        self.temp_trigger_start = None
        self.total_snapshots = 0
        self.area_triggers = 0
        self.count_triggers = 0
        self.temp_triggers = 0
