#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
热源历史数据管理
提供热源时序数据的存储、查询和管理功能
"""

import numpy as np
from typing import List, Dict, Optional, Tuple, Any
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from collections import deque
import json
import os

from .heat_source_analyzer import HeatSourceInfo


@dataclass
class TimeSeriesDataPoint:
    """时序数据点"""
    timestamp: datetime
    value: float
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class HeatSourceSnapshot:
    """热源快照数据"""
    timestamp: datetime
    tracking_id: int
    position: Tuple[int, int]
    size: Tuple[int, int]
    area: int
    max_temperature: float
    min_temperature: float
    avg_temperature: float
    bbox: Tuple[int, int, int, int]
    confidence: float = 1.0
    
    @classmethod
    def from_heat_source_info(cls, info: HeatSourceInfo, timestamp: datetime = None):
        """从HeatSourceInfo创建快照"""
        if timestamp is None:
            timestamp = datetime.now()
        
        # 计算平均温度（简单估算）
        avg_temp = (info.max_temperature + info.min_temperature) / 2.0
        
        return cls(
            timestamp=timestamp,
            tracking_id=info.tracking_id or 0,
            position=info.position,
            size=info.size,
            area=info.area,
            max_temperature=info.max_temperature,
            min_temperature=info.min_temperature,
            avg_temperature=avg_temp,
            bbox=info.bbox,
            confidence=getattr(info, 'confidence', 1.0)
        )


class SlidingWindow:
    """滑动窗口数据结构"""
    
    def __init__(self, max_size: int = 200, max_age_seconds: float = 600):
        """
        初始化滑动窗口（优化：增加存储容量和时间范围）

        Args:
            max_size: 最大数据点数量（优化：从100增加到200）
            max_age_seconds: 最大数据年龄（秒）（优化：从300增加到600秒）
        """
        self.max_size = max_size
        self.max_age_seconds = max_age_seconds
        self.data: deque = deque(maxlen=max_size)
    
    def add(self, data_point: TimeSeriesDataPoint):
        """添加数据点"""
        self.data.append(data_point)
        self._cleanup_old_data()
    
    def _cleanup_old_data(self):
        """清理过期数据"""
        if not self.data:
            return
        
        cutoff_time = datetime.now() - timedelta(seconds=self.max_age_seconds)
        
        # 从左侧移除过期数据
        while self.data and self.data[0].timestamp < cutoff_time:
            self.data.popleft()
    
    def get_data_in_range(self, start_time: datetime, end_time: datetime) -> List[TimeSeriesDataPoint]:
        """获取指定时间范围内的数据"""
        return [point for point in self.data 
                if start_time <= point.timestamp <= end_time]
    
    def get_latest_data(self, count: int = 10) -> List[TimeSeriesDataPoint]:
        """获取最新的N个数据点"""
        return list(self.data)[-count:] if len(self.data) >= count else list(self.data)
    
    def get_data_by_time_window(self, window_seconds: float) -> List[TimeSeriesDataPoint]:
        """获取指定时间窗口内的数据"""
        if not self.data:
            return []
        
        end_time = self.data[-1].timestamp
        start_time = end_time - timedelta(seconds=window_seconds)
        return self.get_data_in_range(start_time, end_time)
    
    def size(self) -> int:
        """获取当前数据点数量"""
        return len(self.data)
    
    def is_empty(self) -> bool:
        """检查是否为空"""
        return len(self.data) == 0


class HeatSourceHistory:
    """热源历史数据管理器"""

    def __init__(self, max_snapshots: int = 1000, max_age_hours: float = 24.0):
        """
        初始化历史数据管理器

        Args:
            max_snapshots: 每个热源最大快照数量
            max_age_hours: 最大数据年龄（小时）
        """
        self.max_snapshots = max_snapshots
        self.max_age_hours = max_age_hours

        # 存储结构：tracking_id -> 各种时序数据
        self.snapshots: Dict[int, List[HeatSourceSnapshot]] = {}
        self.area_series: Dict[int, SlidingWindow] = {}
        self.max_temp_series: Dict[int, SlidingWindow] = {}
        self.min_temp_series: Dict[int, SlidingWindow] = {}
        self.avg_temp_series: Dict[int, SlidingWindow] = {}
        self.position_series: Dict[int, SlidingWindow] = {}

        # 统计信息
        self.total_snapshots = 0
        self.active_tracking_ids: set = set()



        # 综合指标管理器
        self.comprehensive_metrics_manager = None
        try:
            from .comprehensive_metrics_manager import get_comprehensive_metrics_manager
            self.comprehensive_metrics_manager = get_comprehensive_metrics_manager()
            print("✅ 综合指标管理器已启用")
        except Exception as e:
            print(f"⚠️ 综合指标管理器初始化失败: {e}")
    
    def add_snapshot(self, snapshot: HeatSourceSnapshot):
        """添加热源快照"""
        tracking_id = snapshot.tracking_id
        
        # 初始化数据结构（如果需要）
        if tracking_id not in self.snapshots:
            self.snapshots[tracking_id] = []
            self.area_series[tracking_id] = SlidingWindow(
                max_size=self.max_snapshots,
                max_age_seconds=self.max_age_hours * 3600
            )
            self.max_temp_series[tracking_id] = SlidingWindow(
                max_size=self.max_snapshots,
                max_age_seconds=self.max_age_hours * 3600
            )
            self.min_temp_series[tracking_id] = SlidingWindow(
                max_size=self.max_snapshots,
                max_age_seconds=self.max_age_hours * 3600
            )
            self.avg_temp_series[tracking_id] = SlidingWindow(
                max_size=self.max_snapshots,
                max_age_seconds=self.max_age_hours * 3600
            )
            self.position_series[tracking_id] = SlidingWindow(
                max_size=self.max_snapshots,
                max_age_seconds=self.max_age_hours * 3600
            )
        
        # 添加快照
        self.snapshots[tracking_id].append(snapshot)
        
        # 限制快照数量
        if len(self.snapshots[tracking_id]) > self.max_snapshots:
            self.snapshots[tracking_id] = self.snapshots[tracking_id][-self.max_snapshots:]
        
        # 添加时序数据
        timestamp = snapshot.timestamp
        
        self.area_series[tracking_id].add(
            TimeSeriesDataPoint(timestamp, float(snapshot.area))
        )
        
        self.max_temp_series[tracking_id].add(
            TimeSeriesDataPoint(timestamp, snapshot.max_temperature)
        )
        
        self.min_temp_series[tracking_id].add(
            TimeSeriesDataPoint(timestamp, snapshot.min_temperature)
        )
        
        self.avg_temp_series[tracking_id].add(
            TimeSeriesDataPoint(timestamp, snapshot.avg_temperature)
        )
        
        # 位置数据（存储为距离原点的距离）
        position_distance = np.sqrt(snapshot.position[0]**2 + snapshot.position[1]**2)
        self.position_series[tracking_id].add(
            TimeSeriesDataPoint(timestamp, position_distance, 
                              metadata={'x': snapshot.position[0], 'y': snapshot.position[1]})
        )
        
        self.active_tracking_ids.add(tracking_id)
        self.total_snapshots += 1


    
    def get_snapshots(self, tracking_id: int, count: int = 10) -> List[HeatSourceSnapshot]:
        """获取指定热源的最新快照"""
        if tracking_id not in self.snapshots:
            return []
        
        snapshots = self.snapshots[tracking_id]
        return snapshots[-count:] if len(snapshots) >= count else snapshots
    
    def get_time_series(self, tracking_id: int, data_type: str, 
                       window_seconds: float = 60.0) -> List[TimeSeriesDataPoint]:
        """
        获取指定热源的时序数据
        
        Args:
            tracking_id: 热源跟踪ID
            data_type: 数据类型 ('area', 'max_temp', 'min_temp', 'avg_temp', 'position')
            window_seconds: 时间窗口（秒）
        """
        series_map = {
            'area': self.area_series,
            'max_temp': self.max_temp_series,
            'min_temp': self.min_temp_series,
            'avg_temp': self.avg_temp_series,
            'position': self.position_series
        }
        
        if data_type not in series_map or tracking_id not in series_map[data_type]:
            return []
        
        return series_map[data_type][tracking_id].get_data_by_time_window(window_seconds)
    
    def get_all_active_tracking_ids(self) -> List[int]:
        """获取所有活跃的跟踪ID"""
        return list(self.active_tracking_ids)
    
    def cleanup_old_data(self):
        """清理过期数据"""
        cutoff_time = datetime.now() - timedelta(hours=self.max_age_hours)
        
        # 清理快照数据
        for tracking_id in list(self.snapshots.keys()):
            snapshots = self.snapshots[tracking_id]
            valid_snapshots = [s for s in snapshots if s.timestamp >= cutoff_time]
            
            if valid_snapshots:
                self.snapshots[tracking_id] = valid_snapshots
            else:
                # 如果没有有效快照，移除该跟踪ID
                del self.snapshots[tracking_id]
                self.active_tracking_ids.discard(tracking_id)
                
                # 清理相关的时序数据
                for series_dict in [self.area_series, self.max_temp_series, 
                                  self.min_temp_series, self.avg_temp_series, 
                                  self.position_series]:
                    if tracking_id in series_dict:
                        del series_dict[tracking_id]
    
    def get_statistics(self) -> Dict:
        """获取历史数据统计信息"""
        return {
            'total_snapshots': self.total_snapshots,
            'active_tracking_ids': len(self.active_tracking_ids),
            'total_tracking_ids': len(self.snapshots),
            'average_snapshots_per_source': (
                self.total_snapshots / len(self.snapshots) if self.snapshots else 0
            )
        }
    
    def export_data(self, tracking_id: int, filepath: str):
        """导出指定热源的历史数据到JSON文件"""
        if tracking_id not in self.snapshots:
            raise ValueError(f"Tracking ID {tracking_id} not found")
        
        export_data = {
            'tracking_id': tracking_id,
            'snapshots': [],
            'export_time': datetime.now().isoformat()
        }
        
        for snapshot in self.snapshots[tracking_id]:
            export_data['snapshots'].append({
                'timestamp': snapshot.timestamp.isoformat(),
                'position': snapshot.position,
                'size': snapshot.size,
                'area': snapshot.area,
                'max_temperature': snapshot.max_temperature,
                'min_temperature': snapshot.min_temperature,
                'avg_temperature': snapshot.avg_temperature,
                'bbox': snapshot.bbox,
                'confidence': snapshot.confidence
            })
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)



    def process_snapshots_batch(self, snapshots: List[HeatSourceSnapshot]):
        """
        批量处理快照，计算综合指标

        Args:
            snapshots: 当前时刻的所有热源快照列表
        """
        try:
            # 添加快照到历史记录
            for snapshot in snapshots:
                self.add_snapshot(snapshot)

            # 使用综合指标管理器处理快照
            if self.comprehensive_metrics_manager:
                self.comprehensive_metrics_manager.process_snapshots(snapshots)

        except Exception as e:
            print(f"⚠️ 批量处理快照失败: {e}")

    def get_comprehensive_metrics_report(self) -> Dict:
        """获取综合指标报告"""
        if not self.comprehensive_metrics_manager:
            return {}

        try:
            return self.comprehensive_metrics_manager.get_comprehensive_report()
        except Exception as e:
            print(f"⚠️ 获取综合指标报告失败: {e}")
            return {}

    def export_comprehensive_metrics(self, filepath: str,
                                   start_time: Optional[datetime] = None,
                                   end_time: Optional[datetime] = None) -> bool:
        """导出综合指标数据"""
        if not self.comprehensive_metrics_manager:
            return False

        try:
            return self.comprehensive_metrics_manager.export_metrics_to_json(
                filepath, start_time, end_time
            )
        except Exception as e:
            print(f"⚠️ 导出综合指标失败: {e}")
            return False


