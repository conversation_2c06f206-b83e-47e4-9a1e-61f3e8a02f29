#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
热源跟踪器
实现热源的时序跟踪，包括ID分配、匹配算法、生命周期管理等功能
"""

import numpy as np
from typing import List, Dict, Optional, Tuple, Set
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from collections import defaultdict
import uuid

from .heat_source_analyzer import HeatSourceInfo


@dataclass
class TrackedHeatSource:
    """被跟踪的热源对象"""
    tracking_id: int
    current_info: HeatSourceInfo
    first_seen: datetime
    last_seen: datetime
    total_detections: int = 1
    missed_frames: int = 0
    is_active: bool = True
    
    # 历史数据（用于计算变化率）
    position_history: List[Tuple[int, int]] = field(default_factory=list)
    area_history: List[int] = field(default_factory=list)
    max_temp_history: List[float] = field(default_factory=list)
    min_temp_history: List[float] = field(default_factory=list)
    timestamp_history: List[datetime] = field(default_factory=list)
    
    def __post_init__(self):
        """初始化历史数据"""
        if not self.position_history:
            self.position_history.append(self.current_info.position)
            self.area_history.append(self.current_info.area)
            self.max_temp_history.append(self.current_info.max_temperature)
            self.min_temp_history.append(self.current_info.min_temperature)
            self.timestamp_history.append(self.first_seen)
    
    def update(self, new_info: HeatSourceInfo, timestamp: datetime):
        """更新热源信息"""
        self.current_info = new_info
        self.last_seen = timestamp
        self.total_detections += 1
        self.missed_frames = 0
        
        # 更新历史数据
        self.position_history.append(new_info.position)
        self.area_history.append(new_info.area)
        self.max_temp_history.append(new_info.max_temperature)
        self.min_temp_history.append(new_info.min_temperature)
        self.timestamp_history.append(timestamp)
        
        # 限制历史数据长度（保留最近100个记录）
        max_history = 100
        if len(self.timestamp_history) > max_history:
            self.position_history = self.position_history[-max_history:]
            self.area_history = self.area_history[-max_history:]
            self.max_temp_history = self.max_temp_history[-max_history:]
            self.min_temp_history = self.min_temp_history[-max_history:]
            self.timestamp_history = self.timestamp_history[-max_history:]
    
    def mark_missed(self):
        """标记为未检测到"""
        self.missed_frames += 1
        if self.missed_frames > 10:  # 连续10帧未检测到则标记为非活跃
            self.is_active = False


class HeatSourceTracker:
    """热源跟踪器"""
    
    def __init__(self, iou_threshold: float = 0.3, max_missed_frames: int = 50,
                 max_tracking_distance: float = 50.0):
        """
        初始化热源跟踪器
        
        Args:
            iou_threshold: IoU阈值，用于判断热源匹配
            max_missed_frames: 最大丢失帧数，超过则认为热源消失
            max_tracking_distance: 最大跟踪距离（像素）
        """
        self.iou_threshold = iou_threshold
        self.max_missed_frames = max_missed_frames
        self.max_tracking_distance = max_tracking_distance
        
        # 跟踪状态
        self.tracked_sources: Dict[int, TrackedHeatSource] = {}
        self.next_tracking_id = 1
        self.frame_count = 0
        
        # 统计信息
        self.total_sources_created = 0
        self.total_sources_lost = 0
    
    def calculate_iou(self, bbox1: Tuple[int, int, int, int], 
                     bbox2: Tuple[int, int, int, int]) -> float:
        """计算两个边界框的IoU"""
        x1, y1, w1, h1 = bbox1
        x2, y2, w2, h2 = bbox2
        
        # 计算交集
        x_left = max(x1, x2)
        y_top = max(y1, y2)
        x_right = min(x1 + w1, x2 + w2)
        y_bottom = min(y1 + h1, y2 + h2)
        
        if x_right < x_left or y_bottom < y_top:
            return 0.0
        
        intersection = (x_right - x_left) * (y_bottom - y_top)
        
        # 计算并集
        area1 = w1 * h1
        area2 = w2 * h2
        union = area1 + area2 - intersection
        
        if union == 0:
            return 0.0
        
        return intersection / union
    
    def calculate_distance(self, pos1: Tuple[int, int], pos2: Tuple[int, int]) -> float:
        """计算两个位置之间的欧几里得距离"""
        return np.sqrt((pos1[0] - pos2[0])**2 + (pos1[1] - pos2[1])**2)
    
    def find_best_matches(self, current_sources: List[HeatSourceInfo], 
                         timestamp: datetime) -> Dict[int, int]:
        """
        为当前检测到的热源找到最佳匹配的跟踪ID
        
        Returns:
            Dict[tracking_id, source_index]: 跟踪ID到当前热源索引的映射
        """
        matches = {}
        used_sources = set()
        
        # 计算所有可能的匹配分数
        match_scores = []
        
        for tracking_id, tracked_source in self.tracked_sources.items():
            if not tracked_source.is_active:
                continue
                
            for i, current_source in enumerate(current_sources):
                if i in used_sources:
                    continue
                
                # 计算IoU分数
                iou = self.calculate_iou(tracked_source.current_info.bbox, current_source.bbox)
                
                # 计算距离分数
                distance = self.calculate_distance(
                    tracked_source.current_info.position, 
                    current_source.position
                )
                
                # 综合分数（IoU权重更高）
                if iou >= self.iou_threshold and distance <= self.max_tracking_distance:
                    score = iou * 0.7 + (1.0 - min(distance / self.max_tracking_distance, 1.0)) * 0.3
                    match_scores.append((score, tracking_id, i))
        
        # 按分数排序，选择最佳匹配
        match_scores.sort(reverse=True)
        
        for score, tracking_id, source_index in match_scores:
            if tracking_id not in matches and source_index not in used_sources:
                matches[tracking_id] = source_index
                used_sources.add(source_index)
        
        return matches
    
    def update(self, current_sources: List[HeatSourceInfo], 
               timestamp: Optional[datetime] = None) -> List[HeatSourceInfo]:
        """
        更新跟踪器状态
        
        Args:
            current_sources: 当前帧检测到的热源列表
            timestamp: 当前时间戳
            
        Returns:
            带有跟踪ID的热源列表
        """
        if timestamp is None:
            timestamp = datetime.now()
        
        self.frame_count += 1
        
        # 为当前热源分配跟踪ID
        tracked_sources = []
        
        # 找到最佳匹配
        matches = self.find_best_matches(current_sources, timestamp)
        
        # 更新已匹配的热源
        matched_tracking_ids = set()
        for tracking_id, source_index in matches.items():
            current_source = current_sources[source_index]
            current_source.tracking_id = tracking_id
            current_source.timestamp = timestamp
            
            self.tracked_sources[tracking_id].update(current_source, timestamp)
            tracked_sources.append(current_source)
            matched_tracking_ids.add(tracking_id)
        
        # 为未匹配的热源创建新的跟踪ID
        for i, current_source in enumerate(current_sources):
            if current_source.tracking_id is None:  # 未匹配的热源
                tracking_id = self.next_tracking_id
                self.next_tracking_id += 1
                
                current_source.tracking_id = tracking_id
                current_source.timestamp = timestamp
                
                # 创建新的跟踪对象
                tracked_source = TrackedHeatSource(
                    tracking_id=tracking_id,
                    current_info=current_source,
                    first_seen=timestamp,
                    last_seen=timestamp
                )
                
                self.tracked_sources[tracking_id] = tracked_source
                tracked_sources.append(current_source)
                self.total_sources_created += 1
        
        # 标记未匹配的已跟踪热源为丢失
        for tracking_id, tracked_source in self.tracked_sources.items():
            if tracking_id not in matched_tracking_ids and tracked_source.is_active:
                tracked_source.mark_missed()
                if not tracked_source.is_active:
                    self.total_sources_lost += 1
        
        return tracked_sources
    
    def get_active_tracked_sources(self) -> List[TrackedHeatSource]:
        """获取所有活跃的跟踪热源"""
        return [source for source in self.tracked_sources.values() if source.is_active]
    
    def get_tracking_statistics(self) -> Dict:
        """获取跟踪统计信息"""
        active_count = len(self.get_active_tracked_sources())
        total_count = len(self.tracked_sources)
        
        return {
            'frame_count': self.frame_count,
            'active_sources': active_count,
            'total_sources_ever': total_count,
            'sources_created': self.total_sources_created,
            'sources_lost': self.total_sources_lost,
            'next_tracking_id': self.next_tracking_id
        }
    
    def cleanup_old_sources(self, max_age_hours: float = 1.0):
        """清理过期的非活跃热源"""
        current_time = datetime.now()
        cutoff_time = current_time - timedelta(hours=max_age_hours)
        
        to_remove = []
        for tracking_id, tracked_source in self.tracked_sources.items():
            if not tracked_source.is_active and tracked_source.last_seen < cutoff_time:
                to_remove.append(tracking_id)
        
        for tracking_id in to_remove:
            del self.tracked_sources[tracking_id]
        
        return len(to_remove)
