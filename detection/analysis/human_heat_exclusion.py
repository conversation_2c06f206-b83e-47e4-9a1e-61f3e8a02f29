#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
人体热源排除算法模块
用于识别和排除人体热源，确保预警系统只对非人体异常热源进行预警
"""

import cv2
import numpy as np
import time
from typing import List, Dict, Tuple, Optional, Any
from dataclasses import dataclass
from enum import Enum

from utils.logger import get_logger


class HumanExclusionMethod(Enum):
    """人体排除方法枚举"""
    IOU_OVERLAP = "iou_overlap"           # IoU重叠度方法
    SHAPE_ANALYSIS = "shape_analysis"     # 形状分析方法
    TEMPERATURE_RANGE = "temperature_range"  # 温度范围方法
    COMBINED = "combined"                 # 综合方法


@dataclass
class HumanExclusionConfig:
    """人体排除配置"""
    # IoU重叠度配置
    iou_threshold: float = 0.3            # IoU阈值
    
    # 人体温度范围配置
    human_temp_min: float = 32.0          # 人体最低温度（℃）
    human_temp_max: float = 42.0          # 人体最高温度（℃）
    
    # 形状分析配置
    aspect_ratio_min: float = 0.3         # 最小长宽比
    aspect_ratio_max: float = 3.0         # 最大长宽比
    min_area_pixels: int = 50             # 最小面积（像素）
    max_area_pixels: int = 5000           # 最大面积（像素）
    
    # 综合判断配置
    confidence_threshold: float = 0.6     # 综合置信度阈值
    
    # 排除方法
    exclusion_method: HumanExclusionMethod = HumanExclusionMethod.COMBINED


@dataclass
class HeatSourceInfo:
    """热源信息"""
    bbox: Tuple[int, int, int, int]       # 边界框 (x, y, w, h)
    area_pixels: int                      # 面积（像素）
    temperature: float                    # 平均温度
    max_temperature: float                # 最高温度
    aspect_ratio: float                   # 长宽比
    position: Tuple[int, int]             # 中心位置
    contour: Optional[np.ndarray] = None  # 轮廓数据


@dataclass
class HumanDetectionInfo:
    """人体检测信息"""
    bbox: Tuple[int, int, int, int]       # 边界框 (x, y, w, h)
    confidence: float                     # 检测置信度
    position: Tuple[int, int]             # 中心位置


@dataclass
class ExclusionResult:
    """排除结果"""
    is_human_heat: bool                   # 是否为人体热源
    confidence: float                     # 排除置信度
    exclusion_reasons: List[str]          # 排除原因
    method_scores: Dict[str, float]       # 各方法得分


class HumanHeatExclusionEngine:
    """人体热源排除引擎"""
    
    def __init__(self, config: Optional[HumanExclusionConfig] = None):
        """
        初始化人体热源排除引擎
        
        Args:
            config: 排除配置
        """
        self.logger = get_logger("HumanHeatExclusionEngine")
        self.config = config or HumanExclusionConfig()
        
        # 统计信息
        self.total_exclusions = 0
        self.human_heat_excluded = 0
        self.non_human_heat_detected = 0
        
        # 历史数据（用于学习和优化）
        self.exclusion_history: List[Dict] = []
        self.max_history_size = 1000
    
    def exclude_human_heat_sources(self, 
                                 heat_sources: List[HeatSourceInfo],
                                 human_detections: List[HumanDetectionInfo],
                                 temp_matrix: Optional[np.ndarray] = None) -> Tuple[List[HeatSourceInfo], List[ExclusionResult]]:
        """
        排除人体热源
        
        Args:
            heat_sources: 热源信息列表
            human_detections: 人体检测信息列表
            temp_matrix: 温度矩阵（可选）
            
        Returns:
            filtered_heat_sources: 过滤后的热源列表
            exclusion_results: 排除结果列表
        """
        if not heat_sources:
            return [], []
        
        start_time = time.time()
        filtered_heat_sources = []
        exclusion_results = []
        
        try:
            for heat_source in heat_sources:
                # 执行排除判断
                exclusion_result = self._evaluate_heat_source(
                    heat_source, human_detections, temp_matrix
                )
                
                exclusion_results.append(exclusion_result)
                
                # 如果不是人体热源，保留
                if not exclusion_result.is_human_heat:
                    filtered_heat_sources.append(heat_source)
                    self.non_human_heat_detected += 1
                else:
                    self.human_heat_excluded += 1
                
                self.total_exclusions += 1
            
            # 记录历史
            self._record_exclusion_history(heat_sources, exclusion_results, time.time() - start_time)
            
            self.logger.debug(f"热源排除完成: 输入{len(heat_sources)}个，排除{self.human_heat_excluded}个人体热源，保留{len(filtered_heat_sources)}个")
            
            return filtered_heat_sources, exclusion_results
            
        except Exception as e:
            self.logger.error(f"人体热源排除失败: {e}")
            return heat_sources, []  # 出错时返回原始热源列表
    
    def _evaluate_heat_source(self, 
                            heat_source: HeatSourceInfo,
                            human_detections: List[HumanDetectionInfo],
                            temp_matrix: Optional[np.ndarray] = None) -> ExclusionResult:
        """评估单个热源是否为人体热源"""
        method_scores = {}
        exclusion_reasons = []
        
        # 1. IoU重叠度方法
        iou_score = self._calculate_iou_score(heat_source, human_detections)
        method_scores['iou_overlap'] = iou_score
        
        if iou_score > self.config.iou_threshold:
            exclusion_reasons.append(f"与人体检测框重叠度高 (IoU: {iou_score:.2f})")
        
        # 2. 温度范围方法
        temp_score = self._calculate_temperature_score(heat_source, temp_matrix)
        method_scores['temperature_range'] = temp_score
        
        if temp_score > 0.7:
            exclusion_reasons.append(f"温度在人体范围内 ({heat_source.temperature:.1f}°C)")
        
        # 3. 形状分析方法
        shape_score = self._calculate_shape_score(heat_source)
        method_scores['shape_analysis'] = shape_score
        
        if shape_score > 0.6:
            exclusion_reasons.append(f"形状特征符合人体 (长宽比: {heat_source.aspect_ratio:.2f})")
        
        # 4. 综合判断
        if self.config.exclusion_method == HumanExclusionMethod.IOU_OVERLAP:
            final_confidence = iou_score
        elif self.config.exclusion_method == HumanExclusionMethod.TEMPERATURE_RANGE:
            final_confidence = temp_score
        elif self.config.exclusion_method == HumanExclusionMethod.SHAPE_ANALYSIS:
            final_confidence = shape_score
        else:  # COMBINED
            # 加权综合评分
            final_confidence = (
                iou_score * 0.5 +      # IoU权重最高
                temp_score * 0.3 +     # 温度权重中等
                shape_score * 0.2      # 形状权重最低
            )
        
        is_human_heat = final_confidence >= self.config.confidence_threshold
        
        return ExclusionResult(
            is_human_heat=is_human_heat,
            confidence=final_confidence,
            exclusion_reasons=exclusion_reasons,
            method_scores=method_scores
        )
    
    def _calculate_iou_score(self, 
                           heat_source: HeatSourceInfo,
                           human_detections: List[HumanDetectionInfo]) -> float:
        """计算IoU重叠度得分"""
        if not human_detections:
            return 0.0
        
        max_iou = 0.0
        heat_bbox = heat_source.bbox
        
        for human_detection in human_detections:
            human_bbox = human_detection.bbox
            iou = self._calculate_iou(heat_bbox, human_bbox)
            max_iou = max(max_iou, iou)
        
        return max_iou
    
    def _calculate_iou(self, bbox1: Tuple[int, int, int, int], 
                      bbox2: Tuple[int, int, int, int]) -> float:
        """计算两个边界框的IoU"""
        x1, y1, w1, h1 = bbox1
        x2, y2, w2, h2 = bbox2
        
        # 计算交集
        x_left = max(x1, x2)
        y_top = max(y1, y2)
        x_right = min(x1 + w1, x2 + w2)
        y_bottom = min(y1 + h1, y2 + h2)
        
        if x_right < x_left or y_bottom < y_top:
            return 0.0
        
        intersection = (x_right - x_left) * (y_bottom - y_top)
        
        # 计算并集
        area1 = w1 * h1
        area2 = w2 * h2
        union = area1 + area2 - intersection
        
        if union == 0:
            return 0.0
        
        return intersection / union
    
    def _calculate_temperature_score(self, 
                                   heat_source: HeatSourceInfo,
                                   temp_matrix: Optional[np.ndarray] = None) -> float:
        """计算温度范围得分"""
        # 使用热源的平均温度或最高温度
        temp = heat_source.temperature
        
        # 如果有温度矩阵，可以进行更精确的分析
        if temp_matrix is not None:
            # 这里可以添加更复杂的温度分析逻辑
            pass
        
        # 判断是否在人体温度范围内
        if self.config.human_temp_min <= temp <= self.config.human_temp_max:
            # 计算在人体温度范围内的置信度
            temp_range = self.config.human_temp_max - self.config.human_temp_min
            if temp <= (self.config.human_temp_min + self.config.human_temp_max) / 2:
                # 偏低温度
                score = (temp - self.config.human_temp_min) / (temp_range / 2)
            else:
                # 偏高温度
                score = (self.config.human_temp_max - temp) / (temp_range / 2)
            
            return min(score, 1.0)
        else:
            return 0.0
    
    def _calculate_shape_score(self, heat_source: HeatSourceInfo) -> float:
        """计算形状特征得分"""
        score = 0.0
        
        # 1. 长宽比检查
        aspect_ratio = heat_source.aspect_ratio
        if self.config.aspect_ratio_min <= aspect_ratio <= self.config.aspect_ratio_max:
            # 人体典型长宽比为 1.5-2.5
            if 1.5 <= aspect_ratio <= 2.5:
                score += 0.4
            else:
                score += 0.2
        
        # 2. 面积检查
        area = heat_source.area_pixels
        if self.config.min_area_pixels <= area <= self.config.max_area_pixels:
            # 人体典型面积范围
            if 200 <= area <= 2000:
                score += 0.3
            else:
                score += 0.1
        
        # 3. 形状规则性（如果有轮廓数据）
        if heat_source.contour is not None:
            regularity_score = self._calculate_shape_regularity(heat_source.contour)
            score += regularity_score * 0.3
        
        return min(score, 1.0)
    
    def _calculate_shape_regularity(self, contour: np.ndarray) -> float:
        """计算形状规则性"""
        try:
            # 计算轮廓的凸包
            hull = cv2.convexHull(contour)
            hull_area = cv2.contourArea(hull)
            contour_area = cv2.contourArea(contour)
            
            if hull_area == 0:
                return 0.0
            
            # 凸性比率（越接近1越规则）
            convexity_ratio = contour_area / hull_area
            
            # 人体轮廓通常比较规则，凸性比率较高
            if convexity_ratio > 0.8:
                return 1.0
            elif convexity_ratio > 0.6:
                return 0.7
            else:
                return 0.3
                
        except Exception:
            return 0.0
    
    def _record_exclusion_history(self, 
                                heat_sources: List[HeatSourceInfo],
                                exclusion_results: List[ExclusionResult],
                                processing_time: float):
        """记录排除历史"""
        record = {
            'timestamp': time.time(),
            'heat_source_count': len(heat_sources),
            'excluded_count': sum(1 for r in exclusion_results if r.is_human_heat),
            'processing_time': processing_time,
            'average_confidence': np.mean([r.confidence for r in exclusion_results]) if exclusion_results else 0.0
        }
        
        self.exclusion_history.append(record)
        
        # 限制历史记录大小
        if len(self.exclusion_history) > self.max_history_size:
            self.exclusion_history = self.exclusion_history[-self.max_history_size:]
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        if self.total_exclusions == 0:
            return {
                'total_exclusions': 0,
                'human_heat_excluded': 0,
                'non_human_heat_detected': 0,
                'exclusion_rate': 0.0,
                'average_processing_time': 0.0
            }
        
        recent_history = self.exclusion_history[-100:] if self.exclusion_history else []
        avg_processing_time = np.mean([r['processing_time'] for r in recent_history]) if recent_history else 0.0
        
        return {
            'total_exclusions': self.total_exclusions,
            'human_heat_excluded': self.human_heat_excluded,
            'non_human_heat_detected': self.non_human_heat_detected,
            'exclusion_rate': self.human_heat_excluded / self.total_exclusions,
            'average_processing_time': avg_processing_time
        }


# 工具函数
def convert_heat_bboxes_to_heat_sources(heat_bboxes: List[Tuple[int, int, int, int]],
                                      temp_matrix: Optional[np.ndarray] = None,
                                      default_temperature: float = 35.0) -> List[HeatSourceInfo]:
    """
    将热源边界框转换为HeatSourceInfo对象

    Args:
        heat_bboxes: 热源边界框列表 [(x, y, w, h), ...]
        temp_matrix: 温度矩阵（可选）
        default_temperature: 默认温度

    Returns:
        HeatSourceInfo对象列表
    """
    heat_sources = []

    for bbox in heat_bboxes:
        x, y, w, h = bbox
        area_pixels = w * h
        aspect_ratio = w / h if h > 0 else 1.0
        position = (x + w // 2, y + h // 2)

        # 计算温度（如果有温度矩阵）
        if temp_matrix is not None:
            try:
                # 提取热源区域的温度
                roi = temp_matrix[y:y+h, x:x+w]
                if roi.size > 0:
                    temperature = float(np.mean(roi))
                    max_temperature = float(np.max(roi))
                else:
                    temperature = default_temperature
                    max_temperature = default_temperature
            except Exception:
                temperature = default_temperature
                max_temperature = default_temperature
        else:
            temperature = default_temperature
            max_temperature = default_temperature

        heat_source = HeatSourceInfo(
            bbox=bbox,
            area_pixels=area_pixels,
            temperature=temperature,
            max_temperature=max_temperature,
            aspect_ratio=aspect_ratio,
            position=position
        )

        heat_sources.append(heat_source)

    return heat_sources


def convert_human_detections_to_human_info(human_detections: List[Dict]) -> List[HumanDetectionInfo]:
    """
    将人体检测结果转换为HumanDetectionInfo对象

    Args:
        human_detections: 人体检测结果列表 [{'bbox': (x,y,w,h), 'confidence': float}, ...]

    Returns:
        HumanDetectionInfo对象列表
    """
    human_info_list = []

    for detection in human_detections:
        bbox = detection.get('bbox', (0, 0, 0, 0))
        confidence = detection.get('confidence', 0.0)

        x, y, w, h = bbox
        position = (x + w // 2, y + h // 2)

        human_info = HumanDetectionInfo(
            bbox=bbox,
            confidence=confidence,
            position=position
        )

        human_info_list.append(human_info)

    return human_info_list


def create_human_heat_exclusion_engine(config: Optional[Dict] = None) -> HumanHeatExclusionEngine:
    """
    创建人体热源排除引擎

    Args:
        config: 配置字典（可选）

    Returns:
        HumanHeatExclusionEngine实例
    """
    if config:
        exclusion_config = HumanExclusionConfig(**config)
    else:
        exclusion_config = HumanExclusionConfig()

    return HumanHeatExclusionEngine(exclusion_config)
