#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
内存标注管理器
高性能的标注区域管理，避免每帧查询Excel表格
"""

import json
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Optional, NamedTuple
from datetime import datetime


class AnnotationRegion(NamedTuple):
    """标注区域数据结构"""
    asset_id: str
    asset_name: str
    bbox: Tuple[int, int, int, int]  # (x, y, width, height)
    spectrum_type: str  # 'Thermal' or 'Visible'
    is_active: bool
    priority: int
    creation_time: datetime


class MemoryAnnotationManager:
    """
    内存标注管理器

    功能：
    1. 在程序启动时加载所有活跃标注区域到内存
    2. 提供高性能的热源区域匹配功能
    3. 监听标注变化并自动更新内存缓存
    """

    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)

        # 内存中的标注区域缓存
        self.thermal_regions: Dict[str, AnnotationRegion] = {}  # 热成像标注
        self.visible_regions: Dict[str, AnnotationRegion] = {}  # 可见光标注

        # 配置文件路径
        self.manual_annotations_file = Path("manual_annotations.json")
        self.monitoring_zones_file = Path("config/monitoring_zones.json")

        # 文件修改时间缓存，用于检测变化
        self.last_manual_mtime = 0
        self.last_zones_mtime = 0

        # 统计信息
        self.total_regions = 0
        self.thermal_count = 0
        self.visible_count = 0

    def initialize(self) -> bool:
        """
        初始化标注管理器，加载所有活跃标注区域

        Returns:
            bool: 是否初始化成功
        """
        try:
            self.logger.info("🚀 初始化内存标注管理器...")

            # 清空现有缓存
            self.thermal_regions.clear()
            self.visible_regions.clear()

            # 加载手动标注
            self._load_manual_annotations()

            # 加载监控区域配置
            self._load_monitoring_zones()

            # 更新统计信息
            self._update_statistics()

            self.logger.info(f"✅ 标注管理器初始化完成")
            self.logger.info(f"📊 加载区域统计: 总计={self.total_regions}, 热成像={self.thermal_count}, 可见光={self.visible_count}")

            return True

        except Exception as e:
            self.logger.error(f"❌ 标注管理器初始化失败: {e}")
            return False

    def find_matching_region(self, heat_source_bbox: Tuple[int, int, int, int],
                           spectrum_type: str = 'Thermal') -> Optional[AnnotationRegion]:
        """
        查找热源所在的标注区域

        Args:
            heat_source_bbox: 热源边界框 (x, y, width, height)
            spectrum_type: 光谱类型 'Thermal' 或 'Visible'

        Returns:
            匹配的标注区域，如果没有匹配则返回None
        """
        # 计算热源中心点
        center_x = heat_source_bbox[0] + heat_source_bbox[2] // 2
        center_y = heat_source_bbox[1] + heat_source_bbox[3] // 2

        # 选择对应的区域字典
        regions = self.thermal_regions if spectrum_type == 'Thermal' else self.visible_regions

        # 查找包含中心点的区域（按优先级排序）
        matching_regions = []

        for region in regions.values():
            if not region.is_active:
                continue

            # 检查中心点是否在区域内
            if self._point_in_bbox(center_x, center_y, region.bbox):
                matching_regions.append(region)

        # 如果有多个匹配，返回优先级最高的
        if matching_regions:
            return min(matching_regions, key=lambda r: r.priority)

        return None

    def _point_in_bbox(self, x: int, y: int, bbox: Tuple[int, int, int, int]) -> bool:
        """
        检查点是否在边界框内

        Args:
            x, y: 点坐标
            bbox: 边界框 (x, y, width, height)

        Returns:
            bool: 点是否在边界框内
        """
        bbox_x, bbox_y, bbox_w, bbox_h = bbox
        return (bbox_x <= x <= bbox_x + bbox_w and
                bbox_y <= y <= bbox_y + bbox_h)

    def _load_manual_annotations(self):
        """加载手动标注文件（确保使用传感器坐标系统）"""
        if not self.manual_annotations_file.exists():
            self.logger.warning("⚠️ 手动标注文件不存在")
            return

        try:
            # 更新文件修改时间
            self.last_manual_mtime = self.manual_annotations_file.stat().st_mtime

            with open(self.manual_annotations_file, 'r', encoding='utf-8') as f:
                annotations_data = json.load(f)

            for ann_id, ann_data in annotations_data.items():
                if not ann_data.get('visible', True):  # 跳过不可见的标注
                    continue

                # 解析创建时间
                creation_time = datetime.now()
                if ann_data.get('timestamp'):
                    try:
                        creation_time = datetime.fromisoformat(ann_data['timestamp'])
                    except:
                        pass

                # 获取边界框（应该已经是传感器坐标）
                bbox = tuple(ann_data['bbox'])

                # 验证坐标系统：手动标注现在应该使用传感器坐标
                # 如果bbox数值很大（>1000），可能是旧的显示坐标，需要转换
                is_thermal = ann_data.get('is_thermal', False)
                if is_thermal and (bbox[0] > 500 or bbox[1] > 400):
                    self.logger.warning(f"⚠️ 检测到可能的旧显示坐标: {bbox}，建议重新标注")

                # 创建标注区域对象
                region = AnnotationRegion(
                    asset_id=ann_id,
                    asset_name=ann_data['label'],
                    bbox=bbox,  # 现在应该是传感器坐标
                    spectrum_type='Thermal' if is_thermal else 'Visible',
                    is_active=True,
                    priority=1,  # 手动标注默认高优先级
                    creation_time=creation_time
                )

                # 根据光谱类型分类存储
                if region.spectrum_type == 'Thermal':
                    self.thermal_regions[ann_id] = region
                else:
                    self.visible_regions[ann_id] = region

                self.logger.debug(f"📍 加载{region.spectrum_type}标注: {region.asset_name} ({ann_id}) 坐标: {bbox}")

            self.logger.info(f"✅ 加载手动标注完成: {len(annotations_data)} 个")

        except Exception as e:
            self.logger.error(f"❌ 加载手动标注失败: {e}")

    def _load_monitoring_zones(self):
        """加载监控区域配置"""
        if not self.monitoring_zones_file.exists():
            self.logger.warning("⚠️ 监控区域配置文件不存在")
            return

        try:
            # 更新文件修改时间
            self.last_zones_mtime = self.monitoring_zones_file.stat().st_mtime

            with open(self.monitoring_zones_file, 'r', encoding='utf-8') as f:
                zones_data = json.load(f)

            for zone in zones_data.get('zones', []):
                if zone.get('zone_type') != 'manual' or not zone.get('enabled', True):
                    continue  # 只处理启用的手动标注区域

                # 解析创建时间
                creation_time = datetime.now()
                if zone.get('created_time'):
                    try:
                        creation_time = datetime.fromisoformat(zone['created_time'])
                    except:
                        pass

                # 获取边界框（应该已经是传感器坐标）
                bbox = tuple(zone['bbox'])

                # 验证坐标系统：监控区域现在应该使用传感器坐标
                if bbox[0] > 500 or bbox[1] > 400:
                    self.logger.warning(f"⚠️ 监控区域{zone['name']}可能使用旧显示坐标: {bbox}")

                # 创建标注区域对象
                region = AnnotationRegion(
                    asset_id=zone['id'],
                    asset_name=zone['name'],
                    bbox=bbox,  # 现在应该是传感器坐标
                    spectrum_type='Thermal',  # 监控区域默认为热成像
                    is_active=zone.get('enabled', True),
                    priority=zone.get('priority', 2),
                    creation_time=creation_time
                )

                # 避免重复添加（手动标注文件优先）
                if region.asset_id not in self.thermal_regions:
                    self.thermal_regions[region.asset_id] = region
                    self.logger.debug(f"📍 加载监控区域: {region.asset_name} ({region.asset_id}) 坐标: {bbox}")

            self.logger.info(f"✅ 加载监控区域完成: {len(zones_data.get('zones', []))} 个")

        except Exception as e:
            self.logger.error(f"❌ 加载监控区域失败: {e}")

    def _update_statistics(self):
        """更新统计信息"""
        self.thermal_count = len(self.thermal_regions)
        self.visible_count = len(self.visible_regions)
        self.total_regions = self.thermal_count + self.visible_count

    def check_for_updates(self) -> bool:
        """
        检查标注文件是否有更新

        Returns:
            bool: 是否有文件更新
        """
        try:
            manual_updated = False
            zones_updated = False

            # 检查手动标注文件
            if self.manual_annotations_file.exists():
                current_mtime = self.manual_annotations_file.stat().st_mtime
                if current_mtime > self.last_manual_mtime:
                    manual_updated = True

            # 检查监控区域文件
            if self.monitoring_zones_file.exists():
                current_mtime = self.monitoring_zones_file.stat().st_mtime
                if current_mtime > self.last_zones_mtime:
                    zones_updated = True

            return manual_updated or zones_updated

        except Exception as e:
            self.logger.error(f"❌ 检查文件更新失败: {e}")
            return False

    def reload_if_needed(self) -> bool:
        """
        如果文件有更新则重新加载

        Returns:
            bool: 是否进行了重新加载
        """
        if self.check_for_updates():
            self.logger.info("🔄 检测到标注文件更新，重新加载...")
            return self.initialize()
        return False

    def get_all_regions(self, spectrum_type: Optional[str] = None) -> List[AnnotationRegion]:
        """
        获取所有标注区域

        Args:
            spectrum_type: 光谱类型过滤，None表示获取所有类型

        Returns:
            标注区域列表
        """
        if spectrum_type == 'Thermal':
            return list(self.thermal_regions.values())
        elif spectrum_type == 'Visible':
            return list(self.visible_regions.values())
        else:
            return list(self.thermal_regions.values()) + list(self.visible_regions.values())

    def get_region_by_id(self, asset_id: str) -> Optional[AnnotationRegion]:
        """
        根据资产ID获取标注区域

        Args:
            asset_id: 资产ID

        Returns:
            标注区域或None
        """
        return (self.thermal_regions.get(asset_id) or
                self.visible_regions.get(asset_id))

    def get_statistics(self) -> Dict[str, int]:
        """
        获取统计信息

        Returns:
            统计信息字典
        """
        return {
            'total_regions': self.total_regions,
            'thermal_count': self.thermal_count,
            'visible_count': self.visible_count
        }


# 全局实例
_global_annotation_manager = None


def get_memory_annotation_manager() -> MemoryAnnotationManager:
    """
    获取全局标注管理器实例

    Returns:
        MemoryAnnotationManager: 标注管理器实例
    """
    global _global_annotation_manager
    if _global_annotation_manager is None:
        _global_annotation_manager = MemoryAnnotationManager()
    return _global_annotation_manager


def initialize_global_annotation_manager() -> bool:
    """
    初始化全局标注管理器

    Returns:
        bool: 是否初始化成功
    """
    manager = get_memory_annotation_manager()
    return manager.initialize()


def find_heat_source_asset_id(heat_source_bbox: Tuple[int, int, int, int],
                             spectrum_type: str = 'Thermal') -> Optional[str]:
    """
    便捷函数：查找热源所在的资产ID

    Args:
        heat_source_bbox: 热源边界框 (x, y, width, height)
        spectrum_type: 光谱类型 'Thermal' 或 'Visible'

    Returns:
        匹配的资产ID，如果没有匹配则返回None
    """
    manager = get_memory_annotation_manager()
    region = manager.find_matching_region(heat_source_bbox, spectrum_type)
    return region.asset_id if region else None


def find_heat_source_asset_name(heat_source_bbox: Tuple[int, int, int, int],
                               spectrum_type: str = 'Thermal') -> Optional[str]:
    """
    便捷函数：查找热源所在的资产名称

    Args:
        heat_source_bbox: 热源边界框 (x, y, width, height)
        spectrum_type: 光谱类型 'Thermal' 或 'Visible'

    Returns:
        匹配的资产名称，如果没有匹配则返回None
    """
    manager = get_memory_annotation_manager()
    region = manager.find_matching_region(heat_source_bbox, spectrum_type)
    return region.asset_name if region else None


# 示例用法
if __name__ == "__main__":
    # 初始化管理器
    if initialize_global_annotation_manager():
        print("✅ 标注管理器初始化成功")

        # 获取统计信息
        manager = get_memory_annotation_manager()
        stats = manager.get_statistics()
        print(f"📊 统计信息: {stats}")

        # 测试热源匹配
        test_bbox = (100, 100, 50, 50)  # 测试热源位置
        asset_id = find_heat_source_asset_id(test_bbox, 'Thermal')
        asset_name = find_heat_source_asset_name(test_bbox, 'Thermal')

        if asset_id:
            print(f"🎯 热源匹配到资产: {asset_name} (ID: {asset_id})")
        else:
            print("❌ 热源未匹配到任何标注区域")
    else:
        print("❌ 标注管理器初始化失败")