#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
单个对象详细指标计算模块
计算单个热源的详细指标，包括尺寸、面积、温度及其变化率
"""

import numpy as np
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
from collections import defaultdict, deque

from .heat_source_history import HeatSourceSnapshot


@dataclass
class ObjectDetailedMetrics:
    """单个对象详细指标数据类"""
    tracking_id: int
    timestamp: datetime
    
    # 尺寸指标
    length_pixels: int                         # 长（像素）
    length_change_rate: Optional[float]        # 长度变化率（像素/秒）
    width_pixels: int                          # 宽（像素）
    width_change_rate: Optional[float]         # 宽度变化率（像素/秒）
    
    # 面积指标
    area_pixels: int                           # 像素级面积（像素）
    area_change_rate: Optional[float]          # 面积变化率（像素/秒）
    
    # 温度指标
    min_temperature: float                     # 最低温（℃）
    min_temp_change_rate: Optional[float]      # 最低温度变化率（℃/秒）
    max_temperature: float                     # 最高温（℃）
    max_temp_change_rate: Optional[float]      # 最高温度变化率（℃/秒）
    avg_temperature: float                     # 平均温度（℃）
    avg_temp_change_rate: Optional[float]      # 平均温度变化率（℃/秒）
    temperature_diff: float                    # 温差（℃）
    temp_diff_change_rate: Optional[float]     # 温差变化率（℃/秒）


class ObjectDetailedMetricsCalculator:
    """单个对象详细指标计算器"""
    
    def __init__(self, max_history_points: int = 100):
        """
        初始化单个对象详细指标计算器
        
        Args:
            max_history_points: 每个对象最大历史数据点数量
        """
        self.max_history_points = max_history_points
        
        # 每个tracking_id的历史数据存储
        self.metrics_history: Dict[int, deque] = defaultdict(
            lambda: deque(maxlen=max_history_points)
        )
        
        self.logger = self._get_logger()
    
    def _get_logger(self):
        """获取日志记录器"""
        try:
            from utils.logger import get_logger
            return get_logger(__name__)
        except ImportError:
            import logging
            return logging.getLogger(__name__)
    
    def calculate_object_metrics(self, snapshot: HeatSourceSnapshot) -> ObjectDetailedMetrics:
        """
        计算单个对象的详细指标
        
        Args:
            snapshot: 热源快照
            
        Returns:
            ObjectDetailedMetrics: 单个对象详细指标
        """
        tracking_id = snapshot.tracking_id
        timestamp = snapshot.timestamp
        
        # 1. 计算尺寸指标
        length_pixels = snapshot.size[0]  # 宽度作为长度
        width_pixels = snapshot.size[1]   # 高度作为宽度
        
        # 2. 计算面积指标
        area_pixels = snapshot.area
        
        # 3. 计算温度指标
        min_temperature = snapshot.min_temperature
        max_temperature = snapshot.max_temperature
        avg_temperature = snapshot.avg_temperature
        temperature_diff = max_temperature - min_temperature
        
        # 4. 计算变化率
        length_change_rate = self._calculate_change_rate(tracking_id, 'length', length_pixels, timestamp)
        width_change_rate = self._calculate_change_rate(tracking_id, 'width', width_pixels, timestamp)
        area_change_rate = self._calculate_change_rate(tracking_id, 'area', area_pixels, timestamp)
        min_temp_change_rate = self._calculate_change_rate(tracking_id, 'min_temp', min_temperature, timestamp)
        max_temp_change_rate = self._calculate_change_rate(tracking_id, 'max_temp', max_temperature, timestamp)
        avg_temp_change_rate = self._calculate_change_rate(tracking_id, 'avg_temp', avg_temperature, timestamp)
        temp_diff_change_rate = self._calculate_change_rate(tracking_id, 'temp_diff', temperature_diff, timestamp)
        
        # 创建详细指标对象
        metrics = ObjectDetailedMetrics(
            tracking_id=tracking_id,
            timestamp=timestamp,
            length_pixels=length_pixels,
            length_change_rate=length_change_rate,
            width_pixels=width_pixels,
            width_change_rate=width_change_rate,
            area_pixels=area_pixels,
            area_change_rate=area_change_rate,
            min_temperature=min_temperature,
            min_temp_change_rate=min_temp_change_rate,
            max_temperature=max_temperature,
            max_temp_change_rate=max_temp_change_rate,
            avg_temperature=avg_temperature,
            avg_temp_change_rate=avg_temp_change_rate,
            temperature_diff=temperature_diff,
            temp_diff_change_rate=temp_diff_change_rate
        )
        
        # 保存到历史记录
        self.metrics_history[tracking_id].append(metrics)
        
        return metrics
    
    def _calculate_change_rate(self, tracking_id: int, metric_type: str, 
                              current_value: float, current_time: datetime) -> Optional[float]:
        """
        计算指定指标的变化率
        
        Args:
            tracking_id: 跟踪ID
            metric_type: 指标类型
            current_value: 当前值
            current_time: 当前时间
            
        Returns:
            变化率（单位/秒）
        """
        history = self.metrics_history[tracking_id]
        
        if len(history) < 2:
            return None
        
        try:
            # 获取最近的历史数据点
            recent_metrics = list(history)[-10:]  # 最近10个数据点
            
            if len(recent_metrics) < 2:
                return None
            
            # 提取指定指标的值和时间
            values = []
            times = []
            
            for metric in recent_metrics:
                if metric_type == 'length':
                    value = metric.length_pixels
                elif metric_type == 'width':
                    value = metric.width_pixels
                elif metric_type == 'area':
                    value = metric.area_pixels
                elif metric_type == 'min_temp':
                    value = metric.min_temperature
                elif metric_type == 'max_temp':
                    value = metric.max_temperature
                elif metric_type == 'avg_temp':
                    value = metric.avg_temperature
                elif metric_type == 'temp_diff':
                    value = metric.temperature_diff
                else:
                    continue
                
                values.append(value)
                times.append(metric.timestamp)
            
            # 添加当前值
            values.append(current_value)
            times.append(current_time)
            
            if len(values) < 2:
                return None
            
            # 计算时间差和值差
            time_diffs = []
            value_diffs = []
            
            for i in range(1, len(values)):
                time_diff = (times[i] - times[i-1]).total_seconds()
                value_diff = values[i] - values[i-1]
                
                if time_diff > 0:
                    time_diffs.append(time_diff)
                    value_diffs.append(value_diff)
            
            if not time_diffs:
                return None
            
            # 计算平均变化率
            avg_time_diff = np.mean(time_diffs)
            avg_value_diff = np.mean(value_diffs)
            
            return avg_value_diff / avg_time_diff if avg_time_diff > 0 else 0.0
            
        except Exception as e:
            self.logger.error(f"❌ 计算变化率失败 (tracking_id={tracking_id}, metric={metric_type}): {e}")
            return None
    
    def get_object_metrics_history(self, tracking_id: int, count: int = 10) -> List[ObjectDetailedMetrics]:
        """获取指定对象的指标历史"""
        history = self.metrics_history.get(tracking_id, deque())
        return list(history)[-count:] if history else []
    
    def get_latest_object_metrics(self, tracking_id: int) -> Optional[ObjectDetailedMetrics]:
        """获取指定对象的最新指标"""
        history = self.metrics_history.get(tracking_id, deque())
        return history[-1] if history else None
    
    def get_all_active_objects_metrics(self, hours: float = 1.0) -> Dict[int, ObjectDetailedMetrics]:
        """获取所有活跃对象的最新指标"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        active_metrics = {}
        
        for tracking_id, history in self.metrics_history.items():
            if history and history[-1].timestamp >= cutoff_time:
                active_metrics[tracking_id] = history[-1]
        
        return active_metrics
    
    def get_metrics_summary(self, tracking_id: int) -> Dict:
        """获取指定对象的指标摘要"""
        history = self.metrics_history.get(tracking_id, deque())
        
        if not history:
            return {}
        
        try:
            latest = history[-1]
            metrics_list = list(history)
            
            # 计算统计信息
            lengths = [m.length_pixels for m in metrics_list]
            widths = [m.width_pixels for m in metrics_list]
            areas = [m.area_pixels for m in metrics_list]
            min_temps = [m.min_temperature for m in metrics_list]
            max_temps = [m.max_temperature for m in metrics_list]
            avg_temps = [m.avg_temperature for m in metrics_list]
            temp_diffs = [m.temperature_diff for m in metrics_list]
            
            return {
                'tracking_id': tracking_id,
                'latest_metrics': {
                    'timestamp': latest.timestamp.isoformat(),
                    'length_pixels': latest.length_pixels,
                    'width_pixels': latest.width_pixels,
                    'area_pixels': latest.area_pixels,
                    'min_temperature': latest.min_temperature,
                    'max_temperature': latest.max_temperature,
                    'avg_temperature': latest.avg_temperature,
                    'temperature_diff': latest.temperature_diff,
                    'length_change_rate': latest.length_change_rate,
                    'width_change_rate': latest.width_change_rate,
                    'area_change_rate': latest.area_change_rate,
                    'min_temp_change_rate': latest.min_temp_change_rate,
                    'max_temp_change_rate': latest.max_temp_change_rate,
                    'avg_temp_change_rate': latest.avg_temp_change_rate,
                    'temp_diff_change_rate': latest.temp_diff_change_rate
                },
                'statistics': {
                    'length_range': [min(lengths), max(lengths)] if lengths else [0, 0],
                    'width_range': [min(widths), max(widths)] if widths else [0, 0],
                    'area_range': [min(areas), max(areas)] if areas else [0, 0],
                    'min_temp_range': [min(min_temps), max(min_temps)] if min_temps else [0, 0],
                    'max_temp_range': [min(max_temps), max(max_temps)] if max_temps else [0, 0],
                    'avg_temp_range': [min(avg_temps), max(avg_temps)] if avg_temps else [0, 0],
                    'temp_diff_range': [min(temp_diffs), max(temp_diffs)] if temp_diffs else [0, 0]
                },
                'history_count': len(metrics_list)
            }
            
        except Exception as e:
            self.logger.error(f"❌ 获取指标摘要失败 (tracking_id={tracking_id}): {e}")
            return {}
    
    def clear_old_data(self, tracking_id: int, days: int = 7):
        """清理指定对象的过期数据"""
        try:
            cutoff_time = datetime.now() - timedelta(days=days)
            history = self.metrics_history.get(tracking_id, deque())
            
            # 移除过期数据
            while history and history[0].timestamp < cutoff_time:
                history.popleft()
            
            self.logger.info(f"🧹 清理了对象 {tracking_id} 的过期指标数据")
            
        except Exception as e:
            self.logger.error(f"❌ 清理过期数据失败 (tracking_id={tracking_id}): {e}")
    
    def clear_inactive_objects(self, hours: float = 24.0):
        """清理非活跃对象的数据"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            inactive_ids = []
            
            for tracking_id, history in self.metrics_history.items():
                if not history or history[-1].timestamp < cutoff_time:
                    inactive_ids.append(tracking_id)
            
            for tracking_id in inactive_ids:
                del self.metrics_history[tracking_id]
            
            if inactive_ids:
                self.logger.info(f"🧹 清理了 {len(inactive_ids)} 个非活跃对象的数据")
            
        except Exception as e:
            self.logger.error(f"❌ 清理非活跃对象数据失败: {e}")


# 全局实例
_object_metrics_calculator: Optional[ObjectDetailedMetricsCalculator] = None


def get_object_metrics_calculator() -> ObjectDetailedMetricsCalculator:
    """获取单个对象详细指标计算器实例（单例模式）"""
    global _object_metrics_calculator
    
    if _object_metrics_calculator is None:
        _object_metrics_calculator = ObjectDetailedMetricsCalculator()
    
    return _object_metrics_calculator
