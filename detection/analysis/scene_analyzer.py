#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
场景分析器
进行异常目标与重点区域的空间关联分析
"""

from typing import List, Dict, Optional, Tuple, Any
from dataclasses import dataclass
from datetime import datetime
from enum import Enum

from .zone_manager import ZoneManager, MonitoringZone, get_zone_manager


class EventType(Enum):
    """事件类型枚举"""
    HEAT_SOURCE = "heat_source"
    FLAME = "flame"
    SMOKE = "smoke"
    HUMAN = "human"


@dataclass
class AnomalyTarget:
    """异常目标数据类"""
    id: str
    type: EventType
    bbox: Tuple[int, int, int, int]  # (x, y, width, height)
    confidence: float
    center_point: Tuple[int, int]
    additional_data: Dict = None
    
    def __post_init__(self):
        if self.additional_data is None:
            self.additional_data = {}
        
        # 自动计算中心点
        if not hasattr(self, 'center_point') or self.center_point is None:
            x, y, w, h = self.bbox
            self.center_point = (x + w // 2, y + h // 2)


@dataclass
class StatusEvent:
    """状态事件数据类"""
    id: str
    timestamp: datetime
    camera_id: str
    event_type: EventType
    zone: MonitoringZone
    target: AnomalyTarget
    iou_score: float
    description: str
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            'id': self.id,
            'timestamp': self.timestamp.isoformat(),
            'camera_id': self.camera_id,
            'event_type': self.event_type.value,
            'zone_id': self.zone.id,
            'zone_name': self.zone.name,
            'target_id': self.target.id,
            'iou_score': self.iou_score,
            'description': self.description
        }


class SceneAnalyzer:
    """场景分析器"""
    
    def __init__(self, camera_id: str = "camera_01", iou_threshold: float = 0.1):
        """
        初始化场景分析器
        
        Args:
            camera_id: 当前摄像头ID
            iou_threshold: IoU阈值，超过此值认为目标在区域内
        """
        self.camera_id = camera_id
        self.iou_threshold = iou_threshold
        self.zone_manager = get_zone_manager()
        
        # 事件历史
        self.recent_events: List[StatusEvent] = []
        self.event_counter = 0
        
        print(f"✅ 场景分析器初始化完成，摄像头: {camera_id}, IoU阈值: {iou_threshold}")
    
    def analyze_frame(self, heat_sources: List = None, flames: List = None, 
                     smoke: List = None, humans: List = None) -> List[StatusEvent]:
        """
        分析当前帧的异常目标与重点区域关联
        
        Args:
            heat_sources: 热源检测结果列表
            flames: 火焰检测结果列表  
            smoke: 烟雾检测结果列表
            humans: 人体检测结果列表
            
        Returns:
            生成的状态事件列表
        """
        current_events = []
        
        # 获取当前摄像头的重点区域
        zones = self.zone_manager.get_zones_for_camera(self.camera_id)
        
        if not zones:
            return current_events
        
        # 转换检测结果为统一的异常目标格式
        anomaly_targets = self._convert_detections_to_targets(
            heat_sources, flames, smoke, humans
        )
        
        # 进行空间关联分析
        for target in anomaly_targets:
            for zone in zones:
                # 计算IoU
                iou = self._calculate_iou(target.bbox, zone.bbox)
                
                # 检查中心点是否在区域内（备选方案）
                center_in_zone = self._point_in_zone(target.center_point, zone.bbox)
                
                # 如果IoU超过阈值或中心点在区域内，生成事件
                if iou > self.iou_threshold or center_in_zone:
                    event = self._create_status_event(target, zone, iou)
                    current_events.append(event)
                    
                    # 更新目标的区域标签（用于热源分析器）
                    if hasattr(target, 'additional_data'):
                        target.additional_data['zone_name'] = zone.name
                        target.additional_data['zone_id'] = zone.id
                    
                    # 只匹配第一个区域（按优先级排序）
                    break
        
        # 更新事件历史
        self.recent_events.extend(current_events)
        
        # 保持最近100个事件
        if len(self.recent_events) > 100:
            self.recent_events = self.recent_events[-100:]
        
        return current_events
    
    def _convert_detections_to_targets(self, heat_sources, flames, smoke, humans) -> List[AnomalyTarget]:
        """将检测结果转换为异常目标格式"""
        targets = []
        
        # 处理热源
        if heat_sources:
            for i, heat_source in enumerate(heat_sources):
                target = AnomalyTarget(
                    id=f"heat_{getattr(heat_source, 'id', i)}",
                    type=EventType.HEAT_SOURCE,
                    bbox=getattr(heat_source, 'bbox', (0, 0, 0, 0)),
                    confidence=getattr(heat_source, 'confidence', 1.0),
                    center_point=getattr(heat_source, 'position', (0, 0)),
                    additional_data={
                        'temperature': getattr(heat_source, 'max_temperature', 0),
                        'area': getattr(heat_source, 'area', 0),
                        'tracking_id': getattr(heat_source, 'tracking_id', None),
                        'heat_source_ref': heat_source  # 保存原始热源引用
                    }
                )
                targets.append(target)
        
        # 处理火焰、烟雾、人体检测结果...
        # (类似的处理逻辑)
        
        return targets
    
    def _calculate_iou(self, bbox1: Tuple[int, int, int, int], 
                      bbox2: Tuple[int, int, int, int]) -> float:
        """计算两个边界框的IoU"""
        try:
            # bbox格式: (x, y, width, height)
            x1, y1, w1, h1 = bbox1
            x2, y2, w2, h2 = bbox2
            
            # 转换为 (x1, y1, x2, y2) 格式
            box1 = (x1, y1, x1 + w1, y1 + h1)
            box2 = (x2, y2, x2 + w2, y2 + h2)
            
            # 计算交集
            x_left = max(box1[0], box2[0])
            y_top = max(box1[1], box2[1])
            x_right = min(box1[2], box2[2])
            y_bottom = min(box1[3], box2[3])
            
            if x_right < x_left or y_bottom < y_top:
                return 0.0
            
            # 交集面积
            intersection = (x_right - x_left) * (y_bottom - y_top)
            
            # 并集面积
            area1 = w1 * h1
            area2 = w2 * h2
            union = area1 + area2 - intersection
            
            if union == 0:
                return 0.0
            
            return intersection / union
            
        except Exception as e:
            print(f"❌ IoU计算失败: {e}")
            return 0.0
    
    def _point_in_zone(self, point: Tuple[int, int], zone_bbox: Tuple[int, int, int, int]) -> bool:
        """检查点是否在区域内"""
        px, py = point
        x, y, w, h = zone_bbox
        return x <= px <= x + w and y <= py <= y + h
    
    def _create_status_event(self, target: AnomalyTarget, zone: MonitoringZone, 
                           iou: float) -> StatusEvent:
        """创建状态事件"""
        self.event_counter += 1
        
        # 生成描述
        type_names = {
            EventType.HEAT_SOURCE: "热源",
            EventType.FLAME: "火焰",
            EventType.SMOKE: "烟雾",
            EventType.HUMAN: "人员"
        }
        
        type_name = type_names.get(target.type, "异常目标")
        description = f"在{zone.name}检测到{type_name}"
        
        # 添加额外信息
        if target.type == EventType.HEAT_SOURCE:
            temp = target.additional_data.get('temperature', 0)
            if temp > 0:
                description += f"，温度{temp:.1f}°C"
        
        event = StatusEvent(
            id=f"event_{self.event_counter:06d}",
            timestamp=datetime.now(),
            camera_id=self.camera_id,
            event_type=target.type,
            zone=zone,
            target=target,
            iou_score=iou,
            description=description
        )
        
        return event


# 全局场景分析器实例
_scene_analyzer = None

def get_scene_analyzer(camera_id: str = "camera_01") -> SceneAnalyzer:
    """获取全局场景分析器实例"""
    global _scene_analyzer
    if _scene_analyzer is None:
        _scene_analyzer = SceneAnalyzer(camera_id)
    return _scene_analyzer
