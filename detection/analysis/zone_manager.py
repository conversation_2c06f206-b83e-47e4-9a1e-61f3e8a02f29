#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重点区域管理器
管理摄像头的重点监控区域定义
"""

from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from pathlib import Path
import json
from datetime import datetime


@dataclass
class MonitoringZone:
    """监控区域数据类（使用传感器原始坐标系统）"""
    id: str
    name: str
    camera_id: str
    bbox: Tuple[int, int, int, int]  # (x, y, width, height) - 传感器坐标系
    zone_type: str  # 'equipment', 'safety', 'critical', 'normal', 'manual'
    priority: int  # 1-5, 1为最高优先级
    description: str = ""
    enabled: bool = True
    created_time: datetime = None
    
    def __post_init__(self):
        if self.created_time is None:
            self.created_time = datetime.now()
    
    def contains_point(self, point: Tuple[int, int]) -> bool:
        """检查点是否在区域内"""
        px, py = point
        x, y, w, h = self.bbox
        return x <= px <= x + w and y <= py <= y + h
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            'id': self.id,
            'name': self.name,
            'camera_id': self.camera_id,
            'bbox': self.bbox,
            'zone_type': self.zone_type,
            'priority': self.priority,
            'description': self.description,
            'enabled': self.enabled,
            'created_time': self.created_time.isoformat() if self.created_time else None
        }
    
    @classmethod
    def from_dict(cls, data: dict) -> 'MonitoringZone':
        """从字典创建实例"""
        created_time = None
        if data.get('created_time'):
            created_time = datetime.fromisoformat(data['created_time'])
        
        return cls(
            id=data['id'],
            name=data['name'],
            camera_id=data['camera_id'],
            bbox=tuple(data['bbox']),
            zone_type=data['zone_type'],
            priority=data['priority'],
            description=data.get('description', ''),
            enabled=data.get('enabled', True),
            created_time=created_time
        )


class ZoneManager:
    """重点区域管理器"""
    
    def __init__(self, config_file: str = "config/monitoring_zones.json"):
        self.config_file = Path(config_file)
        self.zones: Dict[str, MonitoringZone] = {}
        self.camera_zones: Dict[str, List[str]] = {}  # camera_id -> zone_ids
        
        # 确保配置目录存在
        self.config_file.parent.mkdir(parents=True, exist_ok=True)
        
        # 加载配置
        self.load_zones()
        
        # 如果没有配置，创建默认区域
        if not self.zones:
            self._create_default_zones()
    
    def add_zone(self, zone: MonitoringZone) -> bool:
        """添加监控区域"""
        try:
            self.zones[zone.id] = zone
            
            # 更新摄像头区域映射
            if zone.camera_id not in self.camera_zones:
                self.camera_zones[zone.camera_id] = []
            
            if zone.id not in self.camera_zones[zone.camera_id]:
                self.camera_zones[zone.camera_id].append(zone.id)
            
            # 保存配置
            self.save_zones()
            return True
            
        except Exception as e:
            print(f"❌ 添加监控区域失败: {e}")
            return False
    
    def get_zones_for_camera(self, camera_id: str) -> List[MonitoringZone]:
        """获取指定摄像头的所有区域"""
        zone_ids = self.camera_zones.get(camera_id, [])
        zones = []
        
        for zone_id in zone_ids:
            zone = self.zones.get(zone_id)
            if zone and zone.enabled:
                zones.append(zone)
        
        # 按优先级排序
        zones.sort(key=lambda z: z.priority)
        return zones
    
    def load_zones(self):
        """加载区域配置"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 加载区域
                for zone_data in data.get('zones', []):
                    zone = MonitoringZone.from_dict(zone_data)
                    self.zones[zone.id] = zone
                
                # 重建摄像头映射
                self._rebuild_camera_mapping()
                
                print(f"✅ 加载了 {len(self.zones)} 个监控区域")
            
        except Exception as e:
            print(f"❌ 加载区域配置失败: {e}")
    
    def save_zones(self):
        """保存区域配置"""
        try:
            data = {
                'zones': [zone.to_dict() for zone in self.zones.values()],
                'last_updated': datetime.now().isoformat()
            }
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
        except Exception as e:
            print(f"❌ 保存区域配置失败: {e}")
    
    def _rebuild_camera_mapping(self):
        """重建摄像头区域映射"""
        self.camera_zones.clear()
        
        for zone in self.zones.values():
            if zone.camera_id not in self.camera_zones:
                self.camera_zones[zone.camera_id] = []
            self.camera_zones[zone.camera_id].append(zone.id)
    
    def _create_default_zones(self):
        """创建默认监控区域 - 已禁用自动创建"""
        # 不再自动创建默认区域，只保留用户手动标注的区域
        print("ℹ️ 默认监控区域创建已禁用，只保留用户手动标注的区域")


# 全局区域管理器实例
_zone_manager = None

def get_zone_manager() -> ZoneManager:
    """获取全局区域管理器实例"""
    global _zone_manager
    if _zone_manager is None:
        _zone_manager = ZoneManager()
    return _zone_manager
