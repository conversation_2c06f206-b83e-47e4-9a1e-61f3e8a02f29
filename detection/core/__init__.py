#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
核心检测模块
包含各种检测器的核心实现
"""

# 核心检测器
from .heat_source_detector import HeatSourceDetector
from .human_detector import HumanDetector
from .fusion_detector import FusionDetector
# from .fire_smoke_detector import FireSmokeDetector  # 已清理
# from .adaptive_fire_smoke_detector import AdaptiveFireSmokeDetector  # 依赖已清理的模块

__all__ = [
    'HeatSourceDetector',
    'HumanDetector',
    'FusionDetector',
    # 'FireSmokeDetector',  # 已清理
    # 'AdaptiveFireSmokeDetector'  # 依赖已清理的模块
]