#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
融合检测器 - 结合人体检测和热源检测
在热成像画面上区分人体热源（绿色）和其他热源（红色）
"""

import cv2
import numpy as np
from typing import List, Tuple, Dict, Optional
import time

from .heat_source_detector import HeatSourceDetector
from .human_detector import HumanDetector
from utils.logger import get_logger


class FusionDetector:
    """融合检测器 - 结合人体检测和热源检测"""
    
    def __init__(self, 
                 heat_detector: Optional[HeatSourceDetector] = None,
                 human_detector: Optional[HumanDetector] = None,
                 overlap_threshold: float = 0.3):
        """
        初始化融合检测器
        
        Args:
            heat_detector: 热源检测器实例
            human_detector: 人体检测器实例  
            overlap_threshold: 重叠阈值，用于判断热源是否为人体
        """
        self.logger = get_logger("FusionDetector")
        
        self.heat_detector = heat_detector
        self.human_detector = human_detector
        self.overlap_threshold = overlap_threshold
        
        # 颜色配置
        self.human_heat_color = (0, 255, 0)  # 绿色 - 人体热源
        self.other_heat_color = (0, 0, 255)  # 红色 - 其他热源
        self.bbox_thickness = 2
        
        # 统计信息
        self.detection_stats = {
            'total_detections': 0,
            'human_heat_sources': 0,
            'other_heat_sources': 0,
            'processing_time': 0.0
        }
        
    def detect_and_classify(self, thermal_frame: np.ndarray,
                           temp_matrix: Optional[np.ndarray] = None,
                           threshold_temp: float = 40.0,
                           threshold_info: Optional[str] = None) -> Tuple[np.ndarray, Dict]:
        """
        检测并分类热源

        Args:
            thermal_frame: 热成像画面（BGR格式）
            temp_matrix: 温度矩阵（可选）
            threshold_temp: 温度阈值
            threshold_info: 阈值信息（用于显示）

        Returns:
            annotated_frame: 标注后的画面
            detection_info: 检测信息字典
        """
        start_time = time.time()

        if thermal_frame is None:
            return thermal_frame, {}

        try:
            # 1. 热源检测
            heat_bboxes = self._detect_heat_sources(thermal_frame, temp_matrix, threshold_temp)

            # 2. 人体检测（在热成像画面上）
            human_bboxes = self._detect_humans_on_thermal(thermal_frame)

            # 3. 分类热源
            human_heat_bboxes, other_heat_bboxes = self._classify_heat_sources(
                heat_bboxes, human_bboxes
            )

            # 4. 绘制检测结果
            annotated_frame = self._draw_classified_detections(
                thermal_frame, human_heat_bboxes, other_heat_bboxes, human_bboxes
            )

            # 5. 更新统计信息
            processing_time = time.time() - start_time
            self._update_stats(len(human_heat_bboxes), len(other_heat_bboxes), processing_time)

            # 6. 准备返回信息
            detection_info = {
                'human_heat_sources': human_heat_bboxes,
                'other_heat_sources': other_heat_bboxes,
                'human_detections': human_bboxes,
                'total_heat_sources': len(heat_bboxes),
                'processing_time': processing_time,
                'threshold_temp': threshold_temp,
                'threshold_info': threshold_info or f"温度阈值: {threshold_temp:.1f}°C",
                'stats': self.detection_stats.copy()
            }

            return annotated_frame, detection_info

        except Exception as e:
            self.logger.error(f"融合检测失败: {e}")
            return thermal_frame, {}
    
    def _detect_heat_sources(self, thermal_frame: np.ndarray, 
                           temp_matrix: Optional[np.ndarray],
                           threshold_temp: float) -> List[Tuple[int, int, int, int]]:
        """检测热源"""
        if self.heat_detector is None:
            return []
            
        try:
            if temp_matrix is not None:
                # 使用温度矩阵检测
                bboxes, _ = self.heat_detector.detect_from_temperature_matrix(
                    temp_matrix, threshold_temp
                )
            else:
                # 使用图像检测
                bboxes, _ = self.heat_detector.detect(thermal_frame)
            
            return bboxes
            
        except Exception as e:
            self.logger.error(f"热源检测失败: {e}")
            return []
    
    def _detect_humans_on_thermal(self, thermal_frame: np.ndarray) -> List[Dict]:
        """在热成像画面上检测人体"""
        if self.human_detector is None:
            return []
            
        try:
            detections, _ = self.human_detector.detect_humans(thermal_frame)
            return detections
            
        except Exception as e:
            self.logger.error(f"人体检测失败: {e}")
            return []
    
    def _classify_heat_sources(self, heat_bboxes: List[Tuple[int, int, int, int]], 
                             human_detections: List[Dict]) -> Tuple[List[Tuple[int, int, int, int]], 
                                                                  List[Tuple[int, int, int, int]]]:
        """分类热源为人体热源和其他热源"""
        if not heat_bboxes:
            return [], []
            
        if not human_detections:
            # 没有检测到人体，所有热源都是其他热源
            return [], heat_bboxes
        
        human_heat_bboxes = []
        other_heat_bboxes = []
        
        # 提取人体检测框
        human_bboxes = [detection['bbox'] for detection in human_detections]
        
        for heat_bbox in heat_bboxes:
            is_human_heat = False
            
            # 检查与每个人体检测框的重叠
            for human_bbox in human_bboxes:
                overlap_ratio = self._calculate_overlap_ratio(heat_bbox, human_bbox)
                
                if overlap_ratio >= self.overlap_threshold:
                    is_human_heat = True
                    break
            
            if is_human_heat:
                human_heat_bboxes.append(heat_bbox)
            else:
                other_heat_bboxes.append(heat_bbox)
        
        return human_heat_bboxes, other_heat_bboxes
    
    def _calculate_overlap_ratio(self, bbox1: Tuple[int, int, int, int], 
                               bbox2: Tuple[int, int, int, int]) -> float:
        """计算两个边界框的重叠比例"""
        x1, y1, w1, h1 = bbox1
        x2, y2, w2, h2 = bbox2
        
        # 计算交集
        x_left = max(x1, x2)
        y_top = max(y1, y2)
        x_right = min(x1 + w1, x2 + w2)
        y_bottom = min(y1 + h1, y2 + h2)
        
        if x_right <= x_left or y_bottom <= y_top:
            return 0.0
        
        intersection_area = (x_right - x_left) * (y_bottom - y_top)
        
        # 计算热源框面积
        heat_area = w1 * h1
        
        if heat_area == 0:
            return 0.0
        
        # 返回交集与热源框的比例
        return intersection_area / heat_area
    
    def _draw_classified_detections(self, frame: np.ndarray,
                                  human_heat_bboxes: List[Tuple[int, int, int, int]],
                                  other_heat_bboxes: List[Tuple[int, int, int, int]],
                                  human_detections: List[Dict]) -> np.ndarray:
        """绘制分类后的检测结果"""
        result_frame = frame.copy()
        
        # 绘制人体热源（绿色）
        for bbox in human_heat_bboxes:
            x, y, w, h = bbox
            cv2.rectangle(result_frame, (x, y), (x + w, y + h), 
                         self.human_heat_color, self.bbox_thickness)
            
            # 添加标签
            label = "人体热源"
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 1)[0]
            cv2.rectangle(result_frame, (x, y - label_size[1] - 5), 
                         (x + label_size[0], y), self.human_heat_color, -1)
            cv2.putText(result_frame, label, (x, y - 5), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        # 绘制其他热源（红色）
        for bbox in other_heat_bboxes:
            x, y, w, h = bbox
            cv2.rectangle(result_frame, (x, y), (x + w, y + h), 
                         self.other_heat_color, self.bbox_thickness)
            
            # 添加标签
            label = "热源"
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 1)[0]
            cv2.rectangle(result_frame, (x, y - label_size[1] - 5), 
                         (x + label_size[0], y), self.other_heat_color, -1)
            cv2.putText(result_frame, label, (x, y - 5), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        # 绘制人体检测框（半透明蓝色）
        for detection in human_detections:
            x, y, w, h = detection['bbox']
            confidence = detection['confidence']

            # 半透明覆盖
            overlay = result_frame.copy()
            cv2.rectangle(overlay, (x, y), (x + w, y + h), (255, 0, 0), -1)
            cv2.addWeighted(overlay, 0.2, result_frame, 0.8, 0, result_frame)

            # 边框
            cv2.rectangle(result_frame, (x, y), (x + w, y + h), (255, 0, 0), 1)

            # 置信度标签
            conf_label = f"人体 {confidence:.2f}"
            cv2.putText(result_frame, conf_label, (x, y + h + 15),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 0, 0), 1)
        
        return result_frame
    
    def _update_stats(self, human_heat_count: int, other_heat_count: int, processing_time: float):
        """更新统计信息"""
        self.detection_stats['total_detections'] += 1
        self.detection_stats['human_heat_sources'] = human_heat_count
        self.detection_stats['other_heat_sources'] = other_heat_count
        self.detection_stats['processing_time'] = processing_time
    
    def get_stats(self) -> Dict:
        """获取检测统计信息"""
        return self.detection_stats.copy()
    
    def set_overlap_threshold(self, threshold: float):
        """设置重叠阈值"""
        self.overlap_threshold = max(0.0, min(1.0, threshold))
        self.logger.info(f"重叠阈值设置为: {self.overlap_threshold}")
    
    def set_colors(self, human_heat_color: Tuple[int, int, int] = None,
                   other_heat_color: Tuple[int, int, int] = None):
        """设置检测框颜色"""
        if human_heat_color:
            self.human_heat_color = human_heat_color
        if other_heat_color:
            self.other_heat_color = other_heat_color
