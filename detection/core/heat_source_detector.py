#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
热源检测器模块
"""

import cv2
import numpy as np
from ..processors.image_segmentation import ThermalPreprocessor, ThermalSegmenter
from ..processors.temperature_processor import TemperatureProcessor
from ..processors.contour_analyzer import ContourAnalyzer

# 火焰分析模块（可选导入）
try:
    from ..flame_analysis.flame_flicker_analyzer import FlameFlickerAnalyzer
    from ..flame_analysis.flame_morphology_analyzer import FlameMorphologyAnalyzer
    FLAME_ANALYSIS_AVAILABLE = True
except ImportError:
    FLAME_ANALYSIS_AVAILABLE = False
    FlameFlickerAnalyzer = None
    FlameMorphologyAnalyzer = None


class HeatSourceDetector:
    def __init__(self, threshold_value=200, min_contour_area=15,
                 enable_morphology=True, morph_kernel_size=5, display_steps=False,
                 detection_strategy="separate_all", enable_flame_analysis=False,
                 sample_rate_hz=25.0):
        self.preprocessor = ThermalPreprocessor()
        self.segmenter = ThermalSegmenter(threshold_value, enable_morphology, morph_kernel_size)
        self.min_contour_area = min_contour_area
        self.display_steps = display_steps # Controls visualization within this class
        self.detection_strategy = detection_strategy  # "merge_all", "largest_only", "separate_all"

        # 初始化温度处理器和轮廓分析器
        self.temperature_processor = TemperatureProcessor()
        self.contour_analyzer = ContourAnalyzer(min_contour_area)

        # 火焰分析功能
        self.enable_flame_analysis = enable_flame_analysis and FLAME_ANALYSIS_AVAILABLE
        self.flame_flicker_analyzer = None
        self.flame_morphology_analyzer = None

        if self.enable_flame_analysis:
            try:
                self.flame_flicker_analyzer = FlameFlickerAnalyzer(sample_rate_hz=sample_rate_hz)
                self.flame_morphology_analyzer = FlameMorphologyAnalyzer()
                print("✅ 火焰分析功能已启用")
            except Exception as e:
                print(f"⚠️ 火焰分析功能初始化失败: {e}")
                self.enable_flame_analysis = False
        elif not FLAME_ANALYSIS_AVAILABLE:
            print("⚠️ 火焰分析模块不可用，请检查依赖")

        # 火焰分析结果存储
        self.flame_analysis_results = {}

    def _get_dominant_bbox(self, contours, frame_shape):
        """
        Helper to get a single bounding box.
        You might want a more sophisticated logic here, e.g., merging close bboxes
        or choosing the largest one if multiple distinct hot sources are valid.
        For now, if multiple valid contours, take the one with the largest area,
        or combine all valid contours into one bounding box.
        """
        if not contours:
            return None

        # Option 2: Select the largest contour's bbox
        largest_contour = max(contours, key=cv2.contourArea)
        x, y, w, h = cv2.boundingRect(largest_contour)
        return (x, y, w, h)

    def detect(self, thermal_input):
        """
        Detects heat sources in thermal data.
        Args:
            thermal_input: Can be either:
                - BGR thermal frame (from RTSP)
                - Temperature matrix (numpy array from ISAPI)
        Returns:
            final_bboxes (list): A list of (x,y,w,h) tuples for detected heat sources.
            visualization_images (dict): Dictionary of images from intermediate steps.
        """
        all_vis_images = {} # To collect images from all steps

        if thermal_input is None:
            print("Error: Input is None in HeatSourceDetector.detect.")
            return [], all_vis_images

        # 检测输入类型并转换为适合处理的格式
        if self.temperature_processor.is_temperature_matrix(thermal_input):
            # 输入是温度矩阵，转换为可视化图像
            thermal_frame_bgr = self.temperature_processor.temperature_matrix_to_image(thermal_input)
        else:
            # 输入是BGR图像
            thermal_frame_bgr = thermal_input

        # --- Step 1: Preprocessing ---
        gray_frame = self.preprocessor.to_grayscale(thermal_frame_bgr)
        if gray_frame is None:
            return [], all_vis_images

        if self.display_steps:
            all_vis_images["00 - Original BGR"] = thermal_frame_bgr.copy()
            all_vis_images["01 - Grayscale"] = gray_frame.copy()

        # --- Step 2: Segmentation (Thresholding & Morphology) ---
        # 传递温度矩阵给分割器（如果输入是温度矩阵）
        temp_matrix_for_segmenter = thermal_input if self.temperature_processor.is_temperature_matrix(thermal_input) else None

        # The segmenter itself will populate its own vis_images if its display_steps is True
        binary_mask, seg_vis_images = self.segmenter.segment_hot_regions(
            gray_frame,
            display_steps=self.display_steps,
            temp_matrix=temp_matrix_for_segmenter
        )
        if self.display_steps:
            all_vis_images.update(seg_vis_images) # Add segmenter's images

        if binary_mask is None:
            print("Error: Segmentation returned no mask.")
            return [], all_vis_images

        # --- Step 3: Contour Finding ---
        contours, _ = cv2.findContours(binary_mask,
                                       cv2.RETR_EXTERNAL, # Crucial: only external contours
                                       cv2.CHAIN_APPROX_SIMPLE)

        if self.display_steps:
            # Create an image to draw all found contours (before filtering)
            contour_img_all = thermal_frame_bgr.copy() # Draw on original color or grayscale
            cv2.drawContours(contour_img_all, contours, -1, (0, 0, 255), 1) # Draw all in red
            all_vis_images["05 - All Contours (Pre-filter)"] = contour_img_all

        # --- Step 4: Contour Filtering & BBox Generation ---
        # 使用轮廓分析器进行过滤和分析
        valid_contours, filtered_contours = self.contour_analyzer.filter_contours(contours)

        # 如果启用调试，进行详细的轮廓分析
        if self.display_steps and contours:
            self.contour_analyzer.analyze_contours(contours, thermal_frame_bgr.shape)

        if self.display_steps:
            # 使用增强的轮廓可视化
            enhanced_vis = self.contour_analyzer.create_enhanced_contour_visualization(
                thermal_frame_bgr, contours, valid_contours
            )

            if enhanced_vis:
                # 添加所有增强可视化图像
                for key, img in enhanced_vis.items():
                    all_vis_images[f"05 - {key}"] = img

            # 保持原有的简单可视化作为备份
            contour_img_simple = thermal_frame_bgr.copy()
            if valid_contours:
                cv2.drawContours(contour_img_simple, valid_contours, -1, (255, 0, 0), 2)
            all_vis_images["06 - Simple Valid Contours"] = contour_img_simple

        # 使用轮廓分析器生成边界框
        final_bboxes = self.contour_analyzer.generate_bboxes_by_strategy(valid_contours, self.detection_strategy)

        # --- Step 5: 火焰分析（可选） ---
        if self.enable_flame_analysis and valid_contours:
            flame_analysis_results = self._perform_flame_analysis(
                valid_contours, final_bboxes, thermal_frame_bgr,
                temp_matrix_for_segmenter
            )

            # 将火焰分析结果添加到可视化
            if self.display_steps and flame_analysis_results:
                flame_vis = self._create_flame_analysis_visualization(
                    thermal_frame_bgr, flame_analysis_results
                )
                if flame_vis is not None:
                    all_vis_images["07 - Flame Analysis"] = flame_vis

        # `final_bboxes` now contains your result based on the logic above.
        # The main loop will draw these.

        return final_bboxes, all_vis_images

    def detect_from_temperature_matrix(self, temp_matrix, threshold_temp=40.0):
        """
        直接从温度矩阵检测热源
        Args:
            temp_matrix: 温度矩阵 (numpy array)
            threshold_temp: 温度阈值（摄氏度）
        Returns:
            final_bboxes, visualization_images
        """
        return self.temperature_processor.detect_from_temperature_matrix(
            temp_matrix, threshold_temp, self.min_contour_area, self.display_steps
        )

    def set_params(self, threshold_value=None, min_contour_area=None, morph_kernel_size=None):
        if threshold_value is not None:
            self.segmenter.threshold_value = threshold_value
            print(f"Updated threshold to: {self.segmenter.threshold_value}")
        if min_contour_area is not None:
            self.min_contour_area = min_contour_area
            print(f"Updated min_contour_area to: {self.min_contour_area}")
        if morph_kernel_size is not None:
            self.segmenter.morph_kernel_size = morph_kernel_size
            self.segmenter.kernel = np.ones((morph_kernel_size, morph_kernel_size), np.uint8)
            print(f"Updated morph_kernel_size to: {self.segmenter.morph_kernel_size}")

    def set_threshold_method(self, method="fixed", **kwargs):
        """设置阈值方法
        Args:
            method: "fixed", "local_adaptive"
            **kwargs: 方法特定的参数
        """
        # 重置所有方法
        self.segmenter.use_local_adaptive = False

        if method == "local_adaptive":
            block_size = kwargs.get('block_size', 11)
            c = kwargs.get('c', 2)
            self.segmenter.set_local_adaptive_params(True, block_size, c)
        else:
            # 默认使用固定阈值
            print(f"🔧 使用固定阈值方法")

        print(f"🔄 阈值方法设置为: {self.segmenter.get_threshold_method_info()}")

    def get_threshold_method_info(self):
        """获取当前阈值方法信息"""
        return self.segmenter.get_threshold_method_info()

    def set_morphology_strategy(self, strategy="conservative", prevent_splitting=True, min_component_area=30):
        """设置形态学策略"""
        self.segmenter.set_morphology_strategy(strategy, prevent_splitting, min_component_area)

    def get_morphology_info(self):
        """获取形态学配置信息"""
        return self.segmenter.get_morphology_info()

    def set_detection_strategy(self, strategy="separate_all"):
        """设置检测策略
        Args:
            strategy: "separate_all", "merge_all", "largest_only"
        """
        valid_strategies = ["merge_all", "largest_only", "separate_all"]
        if strategy in valid_strategies:
            self.detection_strategy = strategy
            print(f"🔄 检测策略设置为: {strategy}")
        else:
            print(f"⚠️ 无效的策略: {strategy}，有效选项: {valid_strategies}")

    def get_detection_strategy_info(self):
        """获取当前检测策略信息"""
        strategy_descriptions = {
            "separate_all": "独立处理轮廓 - 推荐策略，为每个热源绘制独立边界框",
            "merge_all": "合并所有轮廓 - 适合人体热源检测，避免分裂",
            "largest_only": "选择最大轮廓 - 适合只关心主要热源"
        }
        return f"当前策略: {self.detection_strategy} ({strategy_descriptions.get(self.detection_strategy, '未知策略')})"

    def disable_erosion(self):
        """完全禁用腐蚀操作"""
        print("🚫 禁用所有腐蚀操作...")

        # 设置为最小形态学策略（无腐蚀）
        self.segmenter.set_morphology_strategy("minimal")

        print("✅ 腐蚀操作已禁用:")
        print("   - 只保留闭运算填充小孔")
        print("   - 完全不做开运算（无腐蚀）")
        print("   - 热源轮廓将保持原始大小")

    def disable_all_morphology(self):
        """完全禁用所有形态学操作"""
        print("🚫 禁用所有形态学操作...")
        self.segmenter.enable_morphology = False
        print("✅ 所有形态学操作已禁用 - 使用纯阈值结果")

    def allow_small_heat_sources(self, min_area=10):
        """允许检测小热源"""
        old_area = self.min_contour_area
        self.min_contour_area = min_area
        self.contour_analyzer.set_min_contour_area(min_area)
        print(f"🔍 小热源检测已启用:")
        print(f"   - 最小面积: {old_area} → {min_area} 像素")
        print(f"   - 现在可以检测更小的热源")

    def reset_to_default_area(self):
        """重置为默认面积阈值"""
        old_area = self.min_contour_area
        self.min_contour_area = 15  # 固定默认值为15
        self.contour_analyzer.set_min_contour_area(15)
        print(f"🔄 面积阈值已重置:")
        print(f"   - 最小面积: {old_area} → 15 像素 (默认值)")
        print(f"   - 这是经过优化的平衡设置")

    def disable_small_heat_sources(self, min_area=100):
        """禁用小热源检测，只保留大热源"""
        old_area = self.min_contour_area
        self.min_contour_area = min_area
        self.contour_analyzer.set_min_contour_area(min_area)
        print(f"🎯 大热源模式已启用:")
        print(f"   - 最小面积: {old_area} → {min_area} 像素")
        print(f"   - 只检测较大的热源")

    def get_area_filter_info(self):
        """获取面积过滤信息"""
        return f"最小轮廓面积: {self.min_contour_area} 像素"

    def show_current_suggestions(self):
        """显示当前的优化建议"""
        print("\n" + "="*50)
        print("🔍 热源检测优化建议")
        print("="*50)
        print(f"当前设置:")
        print(f"  - 最小轮廓面积: {self.min_contour_area} 像素")
        print(f"  - 检测策略: {self.detection_strategy}")
        print(f"  - 形态学策略: {self.segmenter.morphology_strategy}")
        print(f"  - 形态学启用: {'是' if self.segmenter.enable_morphology else '否'}")

        print(f"\n💡 优化建议:")
        print(f"  1. 如果需要更小热源: detector.allow_small_heat_sources(5)")
        print(f"  2. 如果热源被腐蚀: detector.disable_erosion()")
        print(f"  3. 如果需要更多细节: detector.disable_all_morphology()")
        print(f"  4. 重置为默认设置: detector.reset_to_default_area()")
        print(f"  5. 查看详细分析: 在检测时观察控制台输出")
        print(f"\n📊 当前面积阈值 15 像素是经过优化的平衡设置")
        print("="*50)

    def reduce_erosion(self):
        """减少腐蚀操作 - 快速设置为最小形态学策略"""
        print("🔧 减少腐蚀操作...")

        # 设置为最小形态学策略
        self.segmenter.set_morphology_strategy("minimal", prevent_splitting=True, min_component_area=20)

        # 减小核大小
        if hasattr(self.segmenter, 'morph_kernel_size'):
            self.segmenter.morph_kernel_size = 3
            self.segmenter.kernel = np.ones((3, 3), np.uint8)

        # 降低面积阈值，因为轮廓可能变小
        self.min_contour_area = max(20, self.min_contour_area // 2)

        print(f"✅ 腐蚀减少完成:")
        print(f"   - 形态学策略: minimal (只填充小孔，不腐蚀)")
        print(f"   - 核大小: 3x3")
        print(f"   - 最小轮廓面积: {self.min_contour_area}")

        return self.get_morphology_info()

    def disable_morphology(self):
        """完全禁用形态学操作"""
        print("🚫 完全禁用形态学操作...")
        self.segmenter.enable_morphology = False
        print("✅ 形态学操作已禁用 - 将保留原始阈值结果")

    def enable_morphology(self, strategy="minimal"):
        """重新启用形态学操作"""
        print(f"🔄 重新启用形态学操作 (策略: {strategy})...")
        self.segmenter.enable_morphology = True
        self.segmenter.set_morphology_strategy(strategy)
        print("✅ 形态学操作已启用")

    def _perform_flame_analysis(self, contours, bboxes, frame, temp_matrix=None):
        """
        执行火焰分析

        Args:
            contours: 有效轮廓列表
            bboxes: 边界框列表
            frame: 图像帧
            temp_matrix: 温度矩阵（可选）

        Returns:
            火焰分析结果字典
        """
        if not self.enable_flame_analysis:
            return {}

        flame_results = {}

        try:
            for i, (contour, bbox) in enumerate(zip(contours, bboxes)):
                region_id = i

                # 火焰闪烁频率分析
                flicker_result = None
                if self.flame_flicker_analyzer:
                    # 提取区域强度
                    intensity = self.flame_flicker_analyzer.extract_intensity_from_region(
                        frame, bbox, method='mean'
                    )

                    # 跟踪强度时间序列
                    self.flame_flicker_analyzer.track_intensity_series(region_id, intensity)

                    # 分析闪烁频率
                    flicker_result = self.flame_flicker_analyzer.analyze_flicker_frequency(region_id)

                # 火焰形状分析
                morphology_result = None
                if self.flame_morphology_analyzer:
                    morphology_result = self.flame_morphology_analyzer.analyze_flame_shape(
                        contour, region_id
                    )

                # 综合分析结果
                flame_results[region_id] = {
                    'bbox': bbox,
                    'contour_area': cv2.contourArea(contour),
                    'flicker_analysis': flicker_result,
                    'morphology_analysis': morphology_result,
                    'is_potential_flame': self._evaluate_flame_potential(flicker_result, morphology_result)
                }

        except Exception as e:
            print(f"⚠️ 火焰分析失败: {e}")

        # 更新全局分析结果
        self.flame_analysis_results = flame_results



        return flame_results

    def _evaluate_flame_potential(self, flicker_result, morphology_result):
        """
        评估火焰可能性

        Args:
            flicker_result: 闪烁分析结果
            morphology_result: 形状分析结果

        Returns:
            火焰可能性评估结果
        """
        if not flicker_result and not morphology_result:
            return {
                'is_flame': False,
                'confidence': 0.0,
                'reasons': ['无分析数据']
            }

        flame_indicators = []
        confidence_scores = []
        reasons = []

        # 闪烁分析评估
        if flicker_result:
            if flicker_result.is_flame_flicker:
                flame_indicators.append(True)
                confidence_scores.append(flicker_result.confidence)
                reasons.append(f'检测到火焰闪烁 ({flicker_result.dominant_frequency:.1f}Hz)')
            else:
                flame_indicators.append(False)
                reasons.append('未检测到火焰闪烁特征')

        # 形状分析评估
        if morphology_result:
            if morphology_result.is_flame_shape:
                flame_indicators.append(True)
                confidence_scores.append(morphology_result.confidence)
                reasons.append('形状特征符合火焰')
            else:
                flame_indicators.append(False)
                reasons.append('形状特征不符合火焰')

        # 综合判断
        if not flame_indicators:
            is_flame = False
            confidence = 0.0
        else:
            # 如果任一指标为True，则认为可能是火焰
            is_flame = any(flame_indicators)
            confidence = max(confidence_scores) if confidence_scores else 0.0

        return {
            'is_flame': is_flame,
            'confidence': confidence,
            'reasons': reasons,
            'flicker_detected': flicker_result.is_flame_flicker if flicker_result else False,
            'shape_detected': morphology_result.is_flame_shape if morphology_result else False
        }

    def _create_flame_analysis_visualization(self, frame, flame_results):
        """
        创建火焰分析可视化

        Args:
            frame: 原始图像帧
            flame_results: 火焰分析结果

        Returns:
            可视化图像
        """
        if not flame_results:
            return None

        vis_frame = frame.copy()

        try:
            for region_id, result in flame_results.items():
                bbox = result['bbox']
                x, y, w, h = bbox
                flame_potential = result['is_potential_flame']

                # 根据火焰可能性选择颜色
                if flame_potential['is_flame']:
                    color = (0, 0, 255)  # 红色 - 可能是火焰
                    thickness = 3
                else:
                    color = (0, 255, 255)  # 黄色 - 普通热源
                    thickness = 2

                # 绘制边界框
                cv2.rectangle(vis_frame, (x, y), (x + w, y + h), color, thickness)

                # 准备标签文本
                labels = []
                if flame_potential['is_flame']:
                    labels.append(f"火焰 {flame_potential['confidence']:.2f}")
                else:
                    labels.append("热源")

                # 添加分析详情
                flicker_result = result.get('flicker_analysis')
                morphology_result = result.get('morphology_analysis')

                if flicker_result and flicker_result.is_flame_flicker:
                    labels.append(f"闪烁 {flicker_result.dominant_frequency:.1f}Hz")

                if morphology_result and morphology_result.is_flame_shape:
                    labels.append("形状匹配")

                # 绘制标签
                label_text = " | ".join(labels)
                font = cv2.FONT_HERSHEY_SIMPLEX
                font_scale = 0.5
                font_thickness = 1

                # 计算文本尺寸
                (text_width, text_height), baseline = cv2.getTextSize(
                    label_text, font, font_scale, font_thickness
                )

                # 绘制标签背景
                label_y = y - 10 if y > 30 else y + h + 20
                cv2.rectangle(vis_frame,
                            (x, label_y - text_height - 5),
                            (x + text_width, label_y + baseline),
                            color, -1)

                # 绘制标签文本
                cv2.putText(vis_frame, label_text, (x, label_y - 2),
                          font, font_scale, (255, 255, 255), font_thickness)

        except Exception as e:
            print(f"⚠️ 火焰分析可视化失败: {e}")
            return frame.copy()

        return vis_frame

    def enable_flame_analysis(self, sample_rate_hz=25.0):
        """启用火焰分析功能"""
        if not FLAME_ANALYSIS_AVAILABLE:
            print("⚠️ 火焰分析模块不可用，请检查scipy依赖")
            return False

        try:
            self.enable_flame_analysis = True
            if self.flame_flicker_analyzer is None:
                self.flame_flicker_analyzer = FlameFlickerAnalyzer(sample_rate_hz=sample_rate_hz)
            if self.flame_morphology_analyzer is None:
                self.flame_morphology_analyzer = FlameMorphologyAnalyzer()

            print("✅ 火焰分析功能已启用")
            return True
        except Exception as e:
            print(f"❌ 火焰分析功能启用失败: {e}")
            self.enable_flame_analysis = False
            return False

    def disable_flame_analysis(self):
        """禁用火焰分析功能"""
        self.enable_flame_analysis = False
        print("🚫 火焰分析功能已禁用")

    def get_flame_analysis_results(self):
        """获取最新的火焰分析结果"""
        return self.flame_analysis_results.copy() if self.flame_analysis_results else {}

    def get_flame_analysis_statistics(self):
        """获取火焰分析统计信息"""
        stats = {
            'flame_analysis_enabled': self.enable_flame_analysis,
            'flame_analysis_available': FLAME_ANALYSIS_AVAILABLE
        }

        if self.enable_flame_analysis:
            if self.flame_flicker_analyzer:
                stats['flicker_analyzer'] = self.flame_flicker_analyzer.get_statistics()
            if self.flame_morphology_analyzer:
                stats['morphology_analyzer'] = self.flame_morphology_analyzer.get_statistics()

        return stats

    def clear_flame_analysis_history(self):
        """清除火焰分析历史数据"""
        if self.flame_flicker_analyzer:
            self.flame_flicker_analyzer.clear_history()
        if self.flame_morphology_analyzer:
            self.flame_morphology_analyzer.clear_history()
        self.flame_analysis_results.clear()
        print("🧹 火焰分析历史数据已清除")



