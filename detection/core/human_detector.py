#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RKNN人体检测器模块
使用RKNN推理引擎进行实时人体检测，替换原PyTorch/Ultralytics实现
"""

import cv2
import numpy as np
from typing import List, Tuple, Optional, Dict, Any
import time
import os
from utils.logger import get_logger
from utils.chinese_text_renderer import draw_chinese_text

# 导入RKNN推理引擎
try:
    from .rknn_inference_engine import RKNNInferenceEngine
    RKNN_AVAILABLE = True
except ImportError:
    RKNN_AVAILABLE = False
    print("⚠️ RKNN推理引擎不可用，人体检测功能将不可用")

# 保持向后兼容性 - 如果需要PyTorch版本，可以取消注释
# try:
#     from ultralytics import YOLO
#     ULTRALYTICS_AVAILABLE = True
# except ImportError:
#     ULTRALYTICS_AVAILABLE = False
#     print("⚠️ ultralytics未安装，PyTorch人体检测功能将不可用")


class HumanDetector:
    """RKNN人体检测器 - 替换原PyTorch版本"""

    def __init__(self,
                 model_path: str = "models/human_detection/yolov8n_human.rknn",
                 confidence_threshold: float = 0.45,
                 iou_threshold: float = 0.45,
                 device: str = "npu"):
        """
        初始化RKNN人体检测器

        Args:
            model_path: RKNN模型路径，默认使用yolov8n_human.rknn
            confidence_threshold: 置信度阈值
            iou_threshold: IoU阈值
            device: 推理设备 ("npu" 为NPU，保持兼容性)
        """
        self.logger = get_logger("HumanDetector")

        # 处理模型路径兼容性
        if model_path.endswith('.pt') or model_path.endswith('.pth'):
            # 如果传入PyTorch模型路径，自动转换为RKNN路径
            base_name = os.path.splitext(os.path.basename(model_path))[0]
            self.model_path = f"models/human_detection/{base_name}.rknn"
            self.logger.info(f"自动转换模型路径: {model_path} -> {self.model_path}")
        else:
            self.model_path = model_path

        self.confidence_threshold = confidence_threshold
        self.iou_threshold = iou_threshold
        self.device = device

        # RKNN推理引擎
        self.inference_engine: Optional[RKNNInferenceEngine] = None
        self.is_initialized = False

        # 统计信息
        self.detection_count = 0
        self.total_inference_time = 0.0
        self.last_fps_time = time.time()
        self.current_fps = 0.0

        # COCO数据集中人的类别ID
        self.person_class_id = 0

        # 初始化模型
        self._initialize_model()
    
    def _initialize_model(self) -> bool:
        """初始化RKNN推理引擎"""
        if not RKNN_AVAILABLE:
            self.logger.error("RKNN推理引擎不可用，无法初始化人体检测器")
            return False

        try:
            self.logger.info(f"正在加载RKNN模型: {self.model_path}")

            # 创建RKNN推理引擎
            self.inference_engine = RKNNInferenceEngine(
                model_path=self.model_path,
                input_size=(640, 640),
                confidence_threshold=self.confidence_threshold,
                iou_threshold=self.iou_threshold,
                class_names=['person']  # 只检测人体
            )

            if self.inference_engine.is_initialized:
                self.is_initialized = True
                self.logger.info(f"✅ RKNN人体检测模型加载成功，使用NPU加速")
                return True
            else:
                self.logger.error("❌ RKNN推理引擎初始化失败")
                self.is_initialized = False
                return False

        except Exception as e:
            self.logger.error(f"❌ RKNN模型加载失败: {e}")
            self.is_initialized = False
            return False

    def _check_rknn_available(self) -> bool:
        """检查RKNN是否可用"""
        try:
            from rknnlite.api import RKNNLite
            return True
        except ImportError:
            return False
    
    def detect_humans(self, frame: np.ndarray) -> Tuple[List[Dict], np.ndarray]:
        """
        检测画面中的人体 - 使用RKNN推理引擎

        Args:
            frame: 输入图像帧 (BGR格式)

        Returns:
            detections: 检测结果列表，每个元素包含 {bbox, confidence, class_name}
            annotated_frame: 标注后的图像帧
        """
        if not self.is_initialized or self.inference_engine is None:
            return [], frame.copy()

        start_time = time.time()

        try:
            # 使用RKNN推理引擎进行检测
            detections, annotated_frame = self.inference_engine.detect(frame)

            # 过滤只保留人体检测结果并转换为兼容格式
            human_detections = []
            result_frame = frame.copy()

            for det in detections:
                if det['class_id'] == self.person_class_id or det['class_name'] == 'person':
                    # 转换为兼容的格式
                    x1, y1, x2, y2 = det['bbox']

                    human_detection = {
                        'bbox': (int(x1), int(y1), int(x2 - x1), int(y2 - y1)),  # (x, y, w, h)
                        'confidence': det['confidence'],
                        'class_name': '人体',
                        'center': (int((x1 + x2) / 2), int((y1 + y2) / 2))
                    }
                    human_detections.append(human_detection)

            # 重新绘制检测结果（使用自定义样式）
            for detection in human_detections:
                result_frame = self._draw_detection(result_frame, detection)

            # 更新统计信息
            inference_time = time.time() - start_time
            self._update_statistics(inference_time)

            # 绘制统计信息
            result_frame = self._draw_statistics(result_frame, len(human_detections))

            return human_detections, result_frame

        except Exception as e:
            self.logger.error(f"RKNN人体检测失败: {e}")
            return [], frame.copy()
    
    def _draw_detection(self, frame: np.ndarray, detection: Dict) -> np.ndarray:
        """绘制单个检测结果"""
        x, y, w, h = detection['bbox']
        confidence = detection['confidence']

        # 绘制边界框
        color = (0, 255, 0)  # 绿色
        thickness = 2
        cv2.rectangle(frame, (x, y), (x + w, y + h), color, thickness)

        # 绘制标签
        label = f"人体 {confidence:.2f}"
        label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 1)[0]

        # 绘制标签背景
        cv2.rectangle(frame, (x, y - label_size[1] - 10),
                     (x + label_size[0], y), color, -1)

        # 使用中文渲染器绘制文字
        frame = draw_chinese_text(frame, label, (x + 2, y - label_size[1] - 5),
                                font_size=16, color=(255, 255, 255),
                                background_color=color, padding=2)

        # 绘制中心点
        center_x, center_y = detection['center']
        cv2.circle(frame, (center_x, center_y), 3, color, -1)

        return frame
    
    def _update_statistics(self, inference_time: float) -> None:
        """更新统计信息"""
        self.detection_count += 1
        self.total_inference_time += inference_time
        
        # 计算FPS
        current_time = time.time()
        if current_time - self.last_fps_time >= 1.0:
            self.current_fps = 1.0 / inference_time if inference_time > 0 else 0
            self.last_fps_time = current_time
    
    def _draw_statistics(self, frame: np.ndarray, detection_count: int) -> np.ndarray:
        """绘制统计信息 - 已禁用显示"""
        # 不再在画面上绘制统计信息
        return frame
    
    def set_confidence_threshold(self, threshold: float) -> None:
        """设置置信度阈值"""
        self.confidence_threshold = max(0.0, min(1.0, threshold))
        self.logger.info(f"置信度阈值设置为: {self.confidence_threshold}")
    
    def set_iou_threshold(self, threshold: float) -> None:
        """设置IoU阈值"""
        self.iou_threshold = max(0.0, min(1.0, threshold))
        self.logger.info(f"IoU阈值设置为: {self.iou_threshold}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        avg_inference_time = (self.total_inference_time / self.detection_count
                            if self.detection_count > 0 else 0)

        stats = {
            'detection_count': self.detection_count,
            'current_fps': self.current_fps,
            'avg_inference_time': avg_inference_time,
            'confidence_threshold': self.confidence_threshold,
            'iou_threshold': self.iou_threshold,
            'device': self.device,
            'is_initialized': self.is_initialized,
            'engine_type': 'RKNN',  # 标识使用RKNN引擎
            'model_path': self.model_path
        }

        # 添加RKNN引擎统计信息
        if self.inference_engine:
            engine_stats = self.inference_engine.get_statistics()
            stats['engine_stats'] = engine_stats

        return stats
    
    def reset_statistics(self) -> None:
        """重置统计信息"""
        self.detection_count = 0
        self.total_inference_time = 0.0
        self.last_fps_time = time.time()
        self.current_fps = 0.0
        self.logger.info("统计信息已重置")

    def cleanup(self) -> None:
        """清理RKNN资源"""
        if hasattr(self, 'inference_engine') and self.inference_engine:
            self.inference_engine.cleanup()
            self.inference_engine = None
            self.is_initialized = False
            if hasattr(self, 'logger'):
                self.logger.info("RKNN人体检测器资源已清理")

    def __del__(self):
        """析构函数 - 确保资源被正确释放"""
        try:
            self.cleanup()
        except Exception:
            # 忽略析构函数中的异常
            pass


class VideoHumanDetector:
    """视频文件人体检测器"""
    
    def __init__(self, detector: HumanDetector):
        """
        初始化视频人体检测器
        
        Args:
            detector: HumanDetector实例
        """
        self.detector = detector
        self.logger = get_logger("VideoHumanDetector")
    
    def process_video_file(self, video_path: str, output_path: Optional[str] = None) -> bool:
        """
        处理视频文件进行人体检测
        
        Args:
            video_path: 输入视频文件路径
            output_path: 输出视频文件路径（可选）
            
        Returns:
            处理是否成功
        """
        if not os.path.exists(video_path):
            self.logger.error(f"视频文件不存在: {video_path}")
            return False
        
        try:
            # 打开视频文件
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                self.logger.error(f"无法打开视频文件: {video_path}")
                return False
            
            # 获取视频信息
            fps = int(cap.get(cv2.CAP_PROP_FPS))
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            
            self.logger.info(f"视频信息: {width}x{height}, {fps}FPS, {total_frames}帧")
            
            # 设置输出视频编写器
            writer = None
            if output_path:
                fourcc = cv2.VideoWriter_fourcc(*'mp4v')
                writer = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
            
            frame_count = 0
            detection_results = []
            
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                # 执行人体检测
                detections, annotated_frame = self.detector.detect_humans(frame)
                
                # 记录检测结果
                frame_result = {
                    'frame_number': frame_count,
                    'detections': detections,
                    'human_count': len(detections)
                }
                detection_results.append(frame_result)
                
                # 保存标注后的帧
                if writer:
                    writer.write(annotated_frame)
                
                # 显示处理进度
                if frame_count % 30 == 0:
                    progress = (frame_count / total_frames) * 100
                    self.logger.info(f"处理进度: {progress:.1f}% ({frame_count}/{total_frames})")
                
                frame_count += 1
            
            # 清理资源
            cap.release()
            if writer:
                writer.release()
            
            self.logger.info(f"视频处理完成，共处理{frame_count}帧")
            return True
            
        except Exception as e:
            self.logger.error(f"视频处理失败: {e}")
            return False


# 向后兼容性适配器
class PyTorchHumanDetectorAdapter:
    """
    PyTorch人体检测器适配器
    为需要PyTorch版本的代码提供兼容性支持
    """

    def __init__(self, *args, **kwargs):
        """初始化适配器，自动使用RKNN版本"""
        self.logger = get_logger("PyTorchHumanDetectorAdapter")
        self.logger.warning("正在使用PyTorch适配器，实际使用RKNN引擎")

        # 创建RKNN版本的检测器
        self.detector = HumanDetector(*args, **kwargs)

        # 转发所有属性
        self.is_initialized = self.detector.is_initialized
        self.confidence_threshold = self.detector.confidence_threshold
        self.iou_threshold = self.detector.iou_threshold
        self.device = self.detector.device

    def detect_humans(self, frame: np.ndarray) -> Tuple[List[Dict], np.ndarray]:
        """转发到RKNN检测器"""
        return self.detector.detect_humans(frame)

    def get_statistics(self) -> Dict[str, Any]:
        """转发到RKNN检测器"""
        return self.detector.get_statistics()

    def set_confidence_threshold(self, threshold: float) -> None:
        """转发到RKNN检测器"""
        self.detector.set_confidence_threshold(threshold)
        self.confidence_threshold = threshold

    def set_iou_threshold(self, threshold: float) -> None:
        """转发到RKNN检测器"""
        self.detector.set_iou_threshold(threshold)
        self.iou_threshold = threshold

    def reset_statistics(self) -> None:
        """转发到RKNN检测器"""
        self.detector.reset_statistics()

    def cleanup(self) -> None:
        """转发到RKNN检测器"""
        self.detector.cleanup()

    def __del__(self):
        """析构函数"""
        self.cleanup()


# 为了完全向后兼容，可以创建一个别名
# 如果需要强制使用PyTorch版本，可以取消注释以下代码
# try:
#     from ultralytics import YOLO
#
#     class OriginalHumanDetector:
#         """原始PyTorch版本的人体检测器（备用）"""
#         # 这里可以保留原始的PyTorch实现
#         pass
#
# except ImportError:
#     OriginalHumanDetector = None
