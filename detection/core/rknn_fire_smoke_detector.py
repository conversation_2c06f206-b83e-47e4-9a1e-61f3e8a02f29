#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RKNN火焰烟雾检测器
替换基于PyTorch的FireSmokeDetectionEngine，使用RKNN推理引擎
"""

import cv2
import numpy as np
import time
from typing import List, Dict, Tuple, Optional, Any
from pathlib import Path

from .rknn_inference_engine import RKNNInferenceEngine
from utils.logger import get_logger


class RKNNFireSmokeDetector:
    """RKNN火焰烟雾检测器"""
    
    def __init__(self, config_dict: Dict):
        """
        初始化RKNN火焰烟雾检测器
        
        Args:
            config_dict: 配置字典，包含模型路径、阈值等参数
        """
        self.logger = get_logger("RKNNFireSmokeDetector")
        
        # 配置参数
        self.config = config_dict
        self.confidence_threshold = config_dict.get('confidence_threshold', 0.5)
        self.iou_threshold = config_dict.get('iou_threshold', 0.45)
        self.input_size = config_dict.get('input_size', (640, 640))
        
        # 检测模式配置
        self.detection_mode = config_dict.get('detection_mode', 'single')  # 'single' 或 'dual'
        
        # 类别配置
        self.class_names = config_dict.get('class_names', {0: 'fire', 1: 'smoke'})
        self.colors = config_dict.get('colors', {'fire': [0, 0, 255], 'smoke': [128, 128, 128]})
        
        # 推理引擎
        self.single_engine = None
        self.fire_engine = None
        self.smoke_engine = None
        
        # 统计信息
        self.detection_count = 0
        self.total_inference_time = 0.0
        self.last_error = None
        self.total_fire_detections = 0
        self.total_smoke_detections = 0
        
        # 初始化检测引擎
        self._initialize_engines()
    
    def _initialize_engines(self):
        """初始化检测引擎"""
        try:
            if self.detection_mode == 'single':
                self._initialize_single_engine()
            elif self.detection_mode == 'dual':
                self._initialize_dual_engines()
            else:
                self.logger.error(f"不支持的检测模式: {self.detection_mode}")
                
        except Exception as e:
            self.logger.error(f"初始化检测引擎失败: {e}")
            self.last_error = str(e)
    
    def _initialize_single_engine(self):
        """初始化单模型引擎"""
        model_path = self.config.get('single_model_path', 'models/fire_detection/fire_smoke.rknn')
        
        if not Path(model_path).exists():
            self.logger.error(f"单模型文件不存在: {model_path}")
            return
        
        self.single_engine = RKNNInferenceEngine(
            model_path=model_path,
            input_size=self.input_size,
            confidence_threshold=self.confidence_threshold,
            iou_threshold=self.iou_threshold,
            class_names=list(self.class_names.values())
        )
        
        if self.single_engine.is_initialized:
            self.logger.info(f"✅ 单模型RKNN引擎初始化成功: {model_path}")
        else:
            self.logger.error(f"❌ 单模型RKNN引擎初始化失败: {model_path}")
    
    def _initialize_dual_engines(self):
        """初始化双模型引擎"""
        fire_model_path = self.config.get('fire_model_path', 'models/fire_detection/fire.rknn')
        smoke_model_path = self.config.get('smoke_model_path', 'models/fire_detection/smoke.rknn')
        
        # 初始化火焰检测引擎
        if Path(fire_model_path).exists():
            self.fire_engine = RKNNInferenceEngine(
                model_path=fire_model_path,
                input_size=self.input_size,
                confidence_threshold=self.confidence_threshold,
                iou_threshold=self.iou_threshold,
                class_names=['fire']
            )
            
            if self.fire_engine.is_initialized:
                self.logger.info(f"✅ 火焰检测RKNN引擎初始化成功: {fire_model_path}")
            else:
                self.logger.error(f"❌ 火焰检测RKNN引擎初始化失败: {fire_model_path}")
        else:
            self.logger.error(f"火焰模型文件不存在: {fire_model_path}")
        
        # 初始化烟雾检测引擎
        if Path(smoke_model_path).exists():
            self.smoke_engine = RKNNInferenceEngine(
                model_path=smoke_model_path,
                input_size=self.input_size,
                confidence_threshold=self.confidence_threshold,
                iou_threshold=self.iou_threshold,
                class_names=['smoke']
            )
            
            if self.smoke_engine.is_initialized:
                self.logger.info(f"✅ 烟雾检测RKNN引擎初始化成功: {smoke_model_path}")
            else:
                self.logger.error(f"❌ 烟雾检测RKNN引擎初始化失败: {smoke_model_path}")
        else:
            self.logger.error(f"烟雾模型文件不存在: {smoke_model_path}")
    
    def detect_single_model(self, image: np.ndarray) -> List[Dict]:
        """
        使用单模型进行检测
        
        Args:
            image: 输入图像 (BGR格式)
            
        Returns:
            检测结果列表
        """
        if self.single_engine is None or not self.single_engine.is_initialized:
            self.last_error = "单模型引擎未初始化"
            return []
        
        start_time = time.time()
        
        try:
            # 执行检测
            detections, _ = self.single_engine.detect(image)
            
            # 转换为兼容格式
            formatted_detections = []
            for det in detections:
                class_id = det['class_id']
                class_name = det['class_name']
                
                formatted_det = {
                    'class_id': class_id,
                    'class_name': class_name,
                    'confidence': det['confidence'],
                    'bbox': [int(x) for x in det['bbox']],  # [x1, y1, x2, y2]
                    'source_model': 'single'
                }
                formatted_detections.append(formatted_det)
                
                # 更新统计
                if class_name == 'fire':
                    self.total_fire_detections += 1
                elif class_name == 'smoke':
                    self.total_smoke_detections += 1
            
            # 更新统计信息
            self.detection_count += 1
            self.total_inference_time += time.time() - start_time
            
            return formatted_detections
            
        except Exception as e:
            self.last_error = f"单模型检测失败: {e}"
            self.logger.error(self.last_error)
            return []
    
    def detect_dual_model(self, image: np.ndarray) -> List[Dict]:
        """
        使用双模型进行检测
        
        Args:
            image: 输入图像 (BGR格式)
            
        Returns:
            检测结果列表
        """
        if ((self.fire_engine is None or not self.fire_engine.is_initialized) and 
            (self.smoke_engine is None or not self.smoke_engine.is_initialized)):
            self.last_error = "双模型引擎未完全初始化"
            return []
        
        start_time = time.time()
        all_detections = []
        
        try:
            # 火焰检测
            if self.fire_engine and self.fire_engine.is_initialized:
                fire_detections, _ = self.fire_engine.detect(image)
                for det in fire_detections:
                    formatted_det = {
                        'class_id': 0,  # 火焰类别ID
                        'class_name': 'fire',
                        'confidence': det['confidence'],
                        'bbox': [int(x) for x in det['bbox']],
                        'source_model': 'fire'
                    }
                    all_detections.append(formatted_det)
                    self.total_fire_detections += 1
            
            # 烟雾检测
            if self.smoke_engine and self.smoke_engine.is_initialized:
                smoke_detections, _ = self.smoke_engine.detect(image)
                for det in smoke_detections:
                    formatted_det = {
                        'class_id': 1,  # 烟雾类别ID
                        'class_name': 'smoke',
                        'confidence': det['confidence'],
                        'bbox': [int(x) for x in det['bbox']],
                        'source_model': 'smoke'
                    }
                    all_detections.append(formatted_det)
                    self.total_smoke_detections += 1
            
            # 更新统计信息
            self.detection_count += 1
            self.total_inference_time += time.time() - start_time
            
            return all_detections
            
        except Exception as e:
            self.last_error = f"双模型检测失败: {e}"
            self.logger.error(self.last_error)
            return []
    
    def visualize_detections(self, image: np.ndarray, detections: List[Dict]) -> np.ndarray:
        """
        可视化检测结果
        
        Args:
            image: 输入图像
            detections: 检测结果列表
            
        Returns:
            标注后的图像
        """
        result_image = image.copy()
        
        for det in detections:
            x1, y1, x2, y2 = det['bbox']
            class_name = det['class_name']
            confidence = det['confidence']
            
            # 获取颜色
            color = self.colors.get(class_name, [255, 255, 255])
            color_bgr = (int(color[2]), int(color[1]), int(color[0]))  # RGB to BGR
            
            # 绘制边界框
            cv2.rectangle(result_image, (x1, y1), (x2, y2), color_bgr, 2)
            
            # 绘制标签
            label = f"{class_name}: {confidence:.2f}"
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
            
            # 标签背景
            cv2.rectangle(result_image, (x1, y1 - label_size[1] - 10), 
                         (x1 + label_size[0], y1), color_bgr, -1)
            
            # 标签文字
            cv2.putText(result_image, label, (x1, y1 - 5), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        
        return result_image
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取检测统计信息"""
        avg_inference_time = (self.total_inference_time / self.detection_count 
                             if self.detection_count > 0 else 0)
        
        stats = {
            'detection_count': self.detection_count,
            'total_inference_time': self.total_inference_time,
            'average_inference_time': avg_inference_time,
            'total_fire_detections': self.total_fire_detections,
            'total_smoke_detections': self.total_smoke_detections,
            'detection_mode': self.detection_mode,
            'last_error': self.last_error
        }
        
        # 添加引擎统计信息
        if self.single_engine:
            stats['single_engine_stats'] = self.single_engine.get_statistics()
        
        if self.fire_engine:
            stats['fire_engine_stats'] = self.fire_engine.get_statistics()
        
        if self.smoke_engine:
            stats['smoke_engine_stats'] = self.smoke_engine.get_statistics()
        
        return stats
    
    def is_initialized(self) -> bool:
        """检查是否已初始化"""
        if self.detection_mode == 'single':
            return self.single_engine is not None and self.single_engine.is_initialized
        elif self.detection_mode == 'dual':
            return ((self.fire_engine is not None and self.fire_engine.is_initialized) or
                    (self.smoke_engine is not None and self.smoke_engine.is_initialized))
        return False
    
    def cleanup(self):
        """清理资源"""
        if self.single_engine:
            self.single_engine.cleanup()
            self.single_engine = None
        
        if self.fire_engine:
            self.fire_engine.cleanup()
            self.fire_engine = None
        
        if self.smoke_engine:
            self.smoke_engine.cleanup()
            self.smoke_engine = None
        
        self.logger.info("RKNN火焰烟雾检测器资源已清理")
    
    def __del__(self):
        """析构函数"""
        self.cleanup()


class RKNNFireSmokeDetectorAdapter:
    """
    RKNN火焰烟雾检测器适配器
    提供与原FireSmokeDetectionEngine相同的接口
    """
    
    def __init__(self, config_dict: Dict):
        """
        初始化适配器
        
        Args:
            config_dict: 配置字典
        """
        self.logger = get_logger("RKNNFireSmokeDetectorAdapter")
        
        # 初始化RKNN检测器
        self.detector = RKNNFireSmokeDetector(config_dict)
        
        # 兼容性属性
        self.config = config_dict
        self.detection_mode = config_dict.get('detection_mode', 'single')
        
        if self.detector.is_initialized():
            self.logger.info("✅ RKNN火焰烟雾检测器适配器初始化成功")
        else:
            self.logger.error("❌ RKNN火焰烟雾检测器适配器初始化失败")
    
    def detect_single_model(self, image: np.ndarray) -> List[Dict]:
        """单模型检测（兼容接口）"""
        return self.detector.detect_single_model(image)
    
    def detect_dual_model(self, image: np.ndarray) -> List[Dict]:
        """双模型检测（兼容接口）"""
        return self.detector.detect_dual_model(image)
    
    def get_statistics(self) -> Dict:
        """获取统计信息（兼容接口）"""
        return self.detector.get_statistics()
    
    def cleanup(self):
        """清理资源（兼容接口）"""
        self.detector.cleanup()
    
    def __del__(self):
        """析构函数"""
        self.cleanup()
