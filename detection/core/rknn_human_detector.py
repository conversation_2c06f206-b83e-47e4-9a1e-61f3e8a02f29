#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RKNN人体检测器
替换基于PyTorch的HumanDetector，使用RKNN推理引擎
"""

import cv2
import numpy as np
import time
from typing import List, Dict, Tuple, Optional

from .rknn_inference_engine import RKNNInferenceEngine
from utils.logger import get_logger


class RKNNHumanDetector:
    """RKNN人体检测器"""
    
    def __init__(self, 
                 model_path: str = "models/human_detection/yolov8n_human.rknn",
                 confidence_threshold: float = 0.45,
                 iou_threshold: float = 0.45,
                 input_size: Tuple[int, int] = (640, 640)):
        """
        初始化RKNN人体检测器
        
        Args:
            model_path: RKNN模型路径
            confidence_threshold: 置信度阈值
            iou_threshold: IoU阈值
            input_size: 输入图像尺寸
        """
        self.logger = get_logger("RKNNHumanDetector")
        
        # 人体检测类别配置
        self.class_names = ['person']  # COCO数据集中人的类别
        self.person_class_id = 0
        
        # 初始化RKNN推理引擎
        self.inference_engine = RKNNInferenceEngine(
            model_path=model_path,
            input_size=input_size,
            confidence_threshold=confidence_threshold,
            iou_threshold=iou_threshold,
            class_names=self.class_names
        )
        
        # 检测器状态
        self.is_initialized = self.inference_engine.is_initialized
        
        # 统计信息
        self.detection_count = 0
        self.total_inference_time = 0.0
        self.last_fps_time = time.time()
        self.current_fps = 0.0
        
        if self.is_initialized:
            self.logger.info("✅ RKNN人体检测器初始化成功")
        else:
            self.logger.error("❌ RKNN人体检测器初始化失败")
    
    def detect_humans(self, frame: np.ndarray) -> Tuple[List[Dict], np.ndarray]:
        """
        检测画面中的人体
        
        Args:
            frame: 输入图像帧 (BGR格式)
            
        Returns:
            detections: 检测结果列表，每个元素包含 {bbox, confidence, class_name}
            annotated_frame: 标注后的图像帧
        """
        if not self.is_initialized:
            return [], frame.copy()
        
        start_time = time.time()
        
        try:
            # 使用RKNN推理引擎进行检测
            detections, annotated_frame = self.inference_engine.detect(frame)
            
            # 过滤只保留人体检测结果
            human_detections = []
            for det in detections:
                if det['class_id'] == self.person_class_id:
                    # 转换为兼容格式
                    x1, y1, x2, y2 = det['bbox']
                    human_detection = {
                        'bbox': (int(x1), int(y1), int(x2 - x1), int(y2 - y1)),  # (x, y, w, h)
                        'confidence': det['confidence'],
                        'class_name': '人体',
                        'center': (int((x1 + x2) / 2), int((y1 + y2) / 2))
                    }
                    human_detections.append(human_detection)
            
            # 重新绘制人体检测结果（使用自定义样式）
            result_frame = self._draw_human_detections(frame.copy(), human_detections)
            
            # 更新统计信息
            inference_time = time.time() - start_time
            self._update_statistics(inference_time)
            
            return human_detections, result_frame
            
        except Exception as e:
            self.logger.error(f"人体检测失败: {e}")
            return [], frame.copy()
    
    def _draw_human_detections(self, image: np.ndarray, detections: List[Dict]) -> np.ndarray:
        """绘制人体检测结果"""
        for det in detections:
            x, y, w, h = det['bbox']
            confidence = det['confidence']
            
            # 计算边界框坐标
            x1, y1 = x, y
            x2, y2 = x + w, y + h
            
            # 绘制边界框（绿色）
            cv2.rectangle(image, (x1, y1), (x2, y2), (0, 255, 0), 2)
            
            # 绘制中心点
            center_x, center_y = det['center']
            cv2.circle(image, (center_x, center_y), 3, (0, 255, 0), -1)
            
            # 绘制标签
            label = f"人体: {confidence:.2f}"
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
            
            # 标签背景
            cv2.rectangle(image, (x1, y1 - label_size[1] - 10), 
                         (x1 + label_size[0], y1), (0, 255, 0), -1)
            
            # 标签文字
            cv2.putText(image, label, (x1, y1 - 5), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)
        
        return image
    
    def _update_statistics(self, inference_time: float):
        """更新统计信息"""
        self.detection_count += 1
        self.total_inference_time += inference_time
        
        # 计算FPS
        current_time = time.time()
        if current_time - self.last_fps_time >= 1.0:  # 每秒更新一次FPS
            self.current_fps = 1.0 / inference_time if inference_time > 0 else 0
            self.last_fps_time = current_time
    
    def get_statistics(self) -> Dict:
        """获取检测统计信息"""
        engine_stats = self.inference_engine.get_statistics()
        
        return {
            'detection_count': self.detection_count,
            'total_inference_time': self.total_inference_time,
            'average_inference_time': (self.total_inference_time / self.detection_count 
                                     if self.detection_count > 0 else 0),
            'current_fps': self.current_fps,
            'engine_stats': engine_stats,
            'is_initialized': self.is_initialized
        }
    
    def set_confidence_threshold(self, threshold: float):
        """设置置信度阈值"""
        self.inference_engine.confidence_threshold = threshold
        self.logger.info(f"人体检测置信度阈值设置为: {threshold}")
    
    def set_iou_threshold(self, threshold: float):
        """设置IoU阈值"""
        self.inference_engine.iou_threshold = threshold
        self.logger.info(f"人体检测IoU阈值设置为: {threshold}")
    
    def get_model_info(self) -> Dict:
        """获取模型信息"""
        return {
            'model_path': self.inference_engine.model_path,
            'input_size': self.inference_engine.input_size,
            'confidence_threshold': self.inference_engine.confidence_threshold,
            'iou_threshold': self.inference_engine.iou_threshold,
            'class_names': self.class_names,
            'is_initialized': self.is_initialized
        }
    
    def cleanup(self):
        """清理资源"""
        if self.inference_engine:
            self.inference_engine.cleanup()
            self.logger.info("RKNN人体检测器资源已清理")
    
    def __del__(self):
        """析构函数"""
        self.cleanup()


class RKNNHumanDetectorAdapter:
    """
    RKNN人体检测器适配器
    提供与原PyTorch版本HumanDetector相同的接口
    """
    
    def __init__(self, 
                 model_path: str = "models/human_detection/yolov8n_human.rknn",
                 confidence_threshold: float = 0.45,
                 iou_threshold: float = 0.45,
                 device: str = "npu"):  # NPU设备
        """
        初始化适配器
        
        Args:
            model_path: RKNN模型路径
            confidence_threshold: 置信度阈值
            iou_threshold: IoU阈值
            device: 设备类型（保持接口兼容性，实际使用NPU）
        """
        self.logger = get_logger("RKNNHumanDetectorAdapter")
        
        # 保存配置参数
        self.model_path = model_path
        self.confidence_threshold = confidence_threshold
        self.iou_threshold = iou_threshold
        self.device = device
        
        # 初始化RKNN检测器
        self.detector = RKNNHumanDetector(
            model_path=model_path,
            confidence_threshold=confidence_threshold,
            iou_threshold=iou_threshold
        )
        
        # 兼容性属性
        self.is_initialized = self.detector.is_initialized
        self.person_class_id = 0
        
        if self.is_initialized:
            self.logger.info("✅ RKNN人体检测器适配器初始化成功")
        else:
            self.logger.error("❌ RKNN人体检测器适配器初始化失败")
    
    def detect_humans(self, frame: np.ndarray) -> Tuple[List[Dict], np.ndarray]:
        """
        检测人体（兼容接口）
        
        Args:
            frame: 输入图像帧
            
        Returns:
            检测结果和标注图像
        """
        return self.detector.detect_humans(frame)
    
    def get_statistics(self) -> Dict:
        """获取统计信息（兼容接口）"""
        return self.detector.get_statistics()
    
    def cleanup(self):
        """清理资源（兼容接口）"""
        self.detector.cleanup()
    
    def __del__(self):
        """析构函数"""
        self.cleanup()
