#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RKNN推理引擎
替换PyTorch/Ultralytics推理引擎，适配瑞芯微NPU硬件加速
"""

import os
import cv2
import numpy as np
import time
from typing import List, Dict, Tuple, Optional, Any
from pathlib import Path

from utils.logger import get_logger

try:
    from rknnlite.api import RKNNLite
    RKNN_AVAILABLE = True
except ImportError:
    RKNN_AVAILABLE = False
    print("⚠️ rknnlite库未安装，RKNN推理引擎不可用")
    print("💡 请安装rknnlite: pip install rknnlite")


class RKNNInferenceEngine:
    """RKNN推理引擎基础类"""
    
    def __init__(self, 
                 model_path: str,
                 input_size: Tuple[int, int] = (640, 640),
                 confidence_threshold: float = 0.5,
                 iou_threshold: float = 0.45,
                 class_names: Optional[List[str]] = None):
        """
        初始化RKNN推理引擎
        
        Args:
            model_path: RKNN模型文件路径 (.rknn)
            input_size: 输入图像尺寸 (width, height)
            confidence_threshold: 置信度阈值
            iou_threshold: IoU阈值
            class_names: 类别名称列表
        """
        self.logger = get_logger("RKNNInferenceEngine")
        
        # 模型配置
        self.model_path = model_path
        self.input_size = input_size
        self.confidence_threshold = confidence_threshold
        self.iou_threshold = iou_threshold
        self.class_names = class_names or []
        
        # RKNN运行时
        self.rknn_lite = None
        self.is_initialized = False
        
        # 统计信息
        self.inference_count = 0
        self.total_inference_time = 0.0
        self.last_fps = 0.0
        
        # 初始化引擎
        self._initialize_engine()
    
    def _initialize_engine(self) -> bool:
        """初始化RKNN推理引擎"""
        if not RKNN_AVAILABLE:
            self.logger.error("rknnlite库不可用，无法初始化RKNN引擎")
            return False
        
        if not os.path.exists(self.model_path):
            self.logger.error(f"RKNN模型文件不存在: {self.model_path}")
            return False
        
        try:
            self.logger.info(f"正在加载RKNN模型: {self.model_path}")
            
            # 创建RKNN Lite对象
            self.rknn_lite = RKNNLite()
            
            # 加载RKNN模型
            ret = self.rknn_lite.load_rknn(self.model_path)
            if ret != 0:
                self.logger.error(f"加载RKNN模型失败，错误码: {ret}")
                return False
            
            # 初始化运行时环境
            ret = self.rknn_lite.init_runtime()
            if ret != 0:
                self.logger.error(f"初始化RKNN运行时失败，错误码: {ret}")
                return False
            
            self.is_initialized = True
            self.logger.info("✅ RKNN推理引擎初始化成功")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ RKNN引擎初始化失败: {e}")
            return False
    
    def preprocess_image(self, image: np.ndarray) -> np.ndarray:
        """
        图像预处理
        
        Args:
            image: 输入图像 (BGR格式)
            
        Returns:
            预处理后的图像数据
        """
        # Letterbox缩放
        processed_image = self._letterbox_resize(image, self.input_size)
        
        # 归一化到[0,1]
        processed_image = processed_image.astype(np.float32) / 255.0
        
        # 转换为RGB格式
        processed_image = cv2.cvtColor(processed_image, cv2.COLOR_BGR2RGB)
        
        # 调整维度顺序 (H,W,C) -> (C,H,W)
        processed_image = np.transpose(processed_image, (2, 0, 1))
        
        # 添加batch维度 (C,H,W) -> (1,C,H,W)
        processed_image = np.expand_dims(processed_image, axis=0)
        
        return processed_image
    
    def _letterbox_resize(self, image: np.ndarray, target_size: Tuple[int, int]) -> np.ndarray:
        """
        Letterbox缩放，保持宽高比
        
        Args:
            image: 输入图像
            target_size: 目标尺寸 (width, height)
            
        Returns:
            缩放后的图像
        """
        h, w = image.shape[:2]
        target_w, target_h = target_size
        
        # 计算缩放比例
        scale = min(target_w / w, target_h / h)
        new_w = int(w * scale)
        new_h = int(h * scale)
        
        # 缩放图像
        resized = cv2.resize(image, (new_w, new_h), interpolation=cv2.INTER_LINEAR)
        
        # 创建目标尺寸的画布
        canvas = np.full((target_h, target_w, 3), 114, dtype=np.uint8)
        
        # 计算粘贴位置（居中）
        start_x = (target_w - new_w) // 2
        start_y = (target_h - new_h) // 2
        
        # 粘贴缩放后的图像
        canvas[start_y:start_y + new_h, start_x:start_x + new_w] = resized
        
        return canvas
    
    def inference(self, preprocessed_data: np.ndarray) -> List[np.ndarray]:
        """
        执行RKNN推理
        
        Args:
            preprocessed_data: 预处理后的输入数据
            
        Returns:
            推理结果列表
        """
        if not self.is_initialized or self.rknn_lite is None:
            self.logger.error("RKNN引擎未初始化")
            return []
        
        try:
            start_time = time.time()
            
            # 执行推理
            outputs = self.rknn_lite.inference(inputs=[preprocessed_data])
            
            # 更新统计信息
            inference_time = time.time() - start_time
            self.inference_count += 1
            self.total_inference_time += inference_time
            
            # 计算FPS
            if self.inference_count > 0:
                self.last_fps = 1.0 / inference_time
            
            return outputs
            
        except Exception as e:
            self.logger.error(f"RKNN推理失败: {e}")
            return []
    
    def postprocess_detections(self, 
                             outputs: List[np.ndarray], 
                             original_shape: Tuple[int, int]) -> List[Dict]:
        """
        后处理检测结果
        
        Args:
            outputs: RKNN推理输出
            original_shape: 原始图像尺寸 (height, width)
            
        Returns:
            检测结果列表
        """
        if not outputs:
            return []
        
        try:
            # 解析模型输出
            detections = self._parse_yolo_outputs(outputs)
            
            # 应用NMS
            filtered_detections = self._apply_nms(detections)
            
            # 坐标转换回原始图像尺寸
            scaled_detections = self._scale_coordinates(
                filtered_detections, original_shape
            )
            
            return scaled_detections
            
        except Exception as e:
            self.logger.error(f"后处理失败: {e}")
            return []
    
    def _parse_yolo_outputs(self, outputs: List[np.ndarray]) -> List[Dict]:
        """解析YOLO模型输出"""
        # 这里需要根据具体的YOLO模型输出格式进行解析
        # 通常YOLO输出格式为: [batch, num_detections, 5+num_classes]
        # 其中5包括: x_center, y_center, width, height, objectness
        detections = []
        
        if len(outputs) == 0:
            return detections
        
        output = outputs[0]  # 假设只有一个输出
        
        # 遍历所有检测结果
        for detection in output[0]:  # 移除batch维度
            # 解析检测数据
            x_center, y_center, width, height, objectness = detection[:5]
            class_scores = detection[5:]
            
            # 检查objectness阈值
            if objectness < self.confidence_threshold:
                continue
            
            # 找到最高置信度的类别
            class_id = np.argmax(class_scores)
            class_confidence = class_scores[class_id]
            
            # 计算最终置信度
            final_confidence = objectness * class_confidence
            
            if final_confidence < self.confidence_threshold:
                continue
            
            # 转换为边界框格式
            x1 = x_center - width / 2
            y1 = y_center - height / 2
            x2 = x_center + width / 2
            y2 = y_center + height / 2
            
            detections.append({
                'bbox': [x1, y1, x2, y2],
                'confidence': float(final_confidence),
                'class_id': int(class_id),
                'class_name': self.class_names[class_id] if class_id < len(self.class_names) else f'class_{class_id}'
            })
        
        return detections
    
    def _apply_nms(self, detections: List[Dict]) -> List[Dict]:
        """应用非极大值抑制"""
        if not detections:
            return []
        
        # 提取边界框和置信度
        boxes = np.array([det['bbox'] for det in detections])
        scores = np.array([det['confidence'] for det in detections])
        
        # 使用OpenCV的NMS
        indices = cv2.dnn.NMSBoxes(
            boxes.tolist(), 
            scores.tolist(), 
            self.confidence_threshold, 
            self.iou_threshold
        )
        
        if len(indices) == 0:
            return []
        
        # 返回保留的检测结果
        return [detections[i] for i in indices.flatten()]
    
    def _scale_coordinates(self, 
                          detections: List[Dict], 
                          original_shape: Tuple[int, int]) -> List[Dict]:
        """将坐标缩放回原始图像尺寸"""
        if not detections:
            return []
        
        orig_h, orig_w = original_shape
        input_w, input_h = self.input_size
        
        # 计算缩放比例
        scale = min(input_w / orig_w, input_h / orig_h)
        
        # 计算padding
        pad_x = (input_w - orig_w * scale) / 2
        pad_y = (input_h - orig_h * scale) / 2
        
        scaled_detections = []
        for det in detections:
            x1, y1, x2, y2 = det['bbox']
            
            # 移除padding并缩放回原始尺寸
            x1 = (x1 - pad_x) / scale
            y1 = (y1 - pad_y) / scale
            x2 = (x2 - pad_x) / scale
            y2 = (y2 - pad_y) / scale
            
            # 确保坐标在有效范围内
            x1 = max(0, min(x1, orig_w))
            y1 = max(0, min(y1, orig_h))
            x2 = max(0, min(x2, orig_w))
            y2 = max(0, min(y2, orig_h))
            
            scaled_det = det.copy()
            scaled_det['bbox'] = [x1, y1, x2, y2]
            scaled_detections.append(scaled_det)
        
        return scaled_detections
    
    def detect(self, image: np.ndarray) -> Tuple[List[Dict], np.ndarray]:
        """
        完整的检测流程
        
        Args:
            image: 输入图像 (BGR格式)
            
        Returns:
            检测结果列表和标注后的图像
        """
        if not self.is_initialized:
            return [], image.copy()
        
        # 预处理
        preprocessed = self.preprocess_image(image)
        
        # 推理
        outputs = self.inference(preprocessed)
        
        # 后处理
        detections = self.postprocess_detections(outputs, image.shape[:2])
        
        # 绘制检测结果
        annotated_image = self._draw_detections(image.copy(), detections)
        
        return detections, annotated_image
    
    def _draw_detections(self, image: np.ndarray, detections: List[Dict]) -> np.ndarray:
        """在图像上绘制检测结果"""
        for det in detections:
            x1, y1, x2, y2 = map(int, det['bbox'])
            confidence = det['confidence']
            class_name = det['class_name']
            
            # 绘制边界框
            cv2.rectangle(image, (x1, y1), (x2, y2), (0, 255, 0), 2)
            
            # 绘制标签
            label = f"{class_name}: {confidence:.2f}"
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 2)[0]
            cv2.rectangle(image, (x1, y1 - label_size[1] - 10), 
                         (x1 + label_size[0], y1), (0, 255, 0), -1)
            cv2.putText(image, label, (x1, y1 - 5), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 2)
        
        return image
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取推理统计信息"""
        avg_inference_time = (self.total_inference_time / self.inference_count 
                             if self.inference_count > 0 else 0)
        
        return {
            'inference_count': self.inference_count,
            'total_inference_time': self.total_inference_time,
            'average_inference_time': avg_inference_time,
            'last_fps': self.last_fps,
            'model_path': self.model_path,
            'input_size': self.input_size,
            'is_initialized': self.is_initialized
        }
    
    def cleanup(self):
        """清理资源"""
        if self.rknn_lite is not None:
            try:
                self.rknn_lite.release()
                self.logger.info("RKNN资源已释放")
            except Exception as e:
                self.logger.error(f"释放RKNN资源失败: {e}")
            finally:
                self.rknn_lite = None
                self.is_initialized = False
    
    def __del__(self):
        """析构函数"""
        self.cleanup()
