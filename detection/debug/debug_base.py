#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基础调试工具模块
提供通用的调试功能
"""

import cv2
import numpy as np
import os
import time
from datetime import datetime


class BaseDebugger:
    """基础调试器 - 提供通用调试功能"""
    
    def __init__(self, debug_dir="detection/debug_test"):
        self.debug_dir = debug_dir
        self.current_threshold = 200
        self.min_area = 15
        self.morph_kernel_size = 5
        self.save_debug_images = False
        self.filter_text = True
        
        # 确保调试目录存在
        os.makedirs(self.debug_dir, exist_ok=True)
    
    def analyze_thermal_range(self, thermal_frame):
        """分析热成像图像的温度范围"""
        if thermal_frame is None:
            return None
        
        # 转换为灰度图
        if len(thermal_frame.shape) == 3:
            gray = cv2.cvtColor(thermal_frame, cv2.COLOR_BGR2GRAY)
        else:
            gray = thermal_frame
        
        # 统计信息
        min_val = np.min(gray)
        max_val = np.max(gray)
        mean_val = np.mean(gray)
        std_val = np.std(gray)
        
        # 计算建议阈值
        suggested_threshold = int(mean_val + 2 * std_val)
        
        analysis = {
            'min': min_val,
            'max': max_val,
            'mean': mean_val,
            'std': std_val,
            'suggested_threshold': suggested_threshold,
            'current_threshold': self.current_threshold
        }
        
        return analysis
    
    def print_analysis(self, analysis):
        """打印分析结果"""
        if analysis is None:
            return
        
        print("\n🌡️ 热成像图像分析:")
        print(f"   最小值: {analysis['min']:.1f}")
        print(f"   最大值: {analysis['max']:.1f}")
        print(f"   平均值: {analysis['mean']:.1f}")
        print(f"   标准差: {analysis['std']:.1f}")
        print(f"   建议阈值: {analysis['suggested_threshold']}")
        print(f"   当前阈值: {analysis['current_threshold']}")
        
        if analysis['current_threshold'] < analysis['mean']:
            print("⚠️ 当前阈值可能过低，会检测到大量背景")
        elif analysis['current_threshold'] > analysis['max']:
            print("⚠️ 当前阈值过高，可能检测不到任何热源")
    
    def save_debug_images(self, original_frame, vis_images, bboxes):
        """保存调试图像"""
        timestamp = datetime.now().strftime("%H%M%S")

        # 保存原始图像
        cv2.imwrite(
            os.path.join(self.debug_dir, f"debug_{timestamp}_01_original.jpg"),
            original_frame
        )

        # 保存处理步骤图像
        step_counter = 2
        for step_name, image in vis_images.items():
            if image is not None:
                filename = f"debug_{timestamp}_{step_counter:02d}_{step_name.replace(' ', '_').replace('-', '')}.jpg"
                cv2.imwrite(os.path.join(self.debug_dir, filename), image)
                step_counter += 1

        # 保存最终结果
        result_frame = original_frame.copy()
        for bbox in bboxes:
            x, y, w, h = bbox
            cv2.rectangle(result_frame, (x, y), (x + w, y + h), (0, 0, 255), 2)
            cv2.putText(result_frame, f"Heat({w}x{h})", (x, y-5),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)

        cv2.imwrite(
            os.path.join(self.debug_dir, f"debug_{timestamp}_{step_counter:02d}_final_result.jpg"),
            result_frame
        )

        print(f"🔍 调试图像已保存到: {self.debug_dir}")
        print(f"   时间戳: {timestamp}")
        print(f"   检测到 {len(bboxes)} 个热源")
        print(f"   当前阈值: {self.current_threshold}")

        # 打印详细的图像处理信息
        self.print_processing_details(original_frame, vis_images, bboxes)
    
    def print_processing_details(self, original_frame, vis_images, bboxes):
        """打印详细的处理信息"""
        print("\n📊 图像处理详情:")
        print(f"   原始图像尺寸: {original_frame.shape}")
        print(f"   处理步骤数: {len(vis_images)}")
        print(f"   检测到的热源数: {len(bboxes)}")
        
        if bboxes:
            print("   热源详情:")
            for i, (x, y, w, h) in enumerate(bboxes):
                area = w * h
                print(f"     热源 {i+1}: 位置({x},{y}) 尺寸({w}x{h}) 面积({area})")
        
        print(f"   当前参数:")
        print(f"     - 阈值: {self.current_threshold}")
        print(f"     - 最小面积: {self.min_area}")
        print(f"     - 形态学核大小: {self.morph_kernel_size}")
    
    def adjust_threshold(self, delta):
        """调整阈值"""
        old_threshold = self.current_threshold
        self.current_threshold = max(0, min(255, self.current_threshold + delta))
        
        if self.current_threshold != old_threshold:
            print(f"🔧 阈值调整: {old_threshold} → {self.current_threshold}")
        else:
            print(f"⚠️ 阈值已达到边界: {self.current_threshold}")
    
    def adjust_min_area(self, delta):
        """调整最小面积"""
        old_area = self.min_area
        self.min_area = max(1, self.min_area + delta)
        
        if self.min_area != old_area:
            print(f"🔧 最小面积调整: {old_area} → {self.min_area}")
    
    def request_debug_save(self):
        """请求保存调试图像"""
        self.save_debug_images = True
        print("📸 下一帧将保存调试图像")
    
    def get_status_text(self):
        """获取状态文本"""
        return f"Threshold:{self.current_threshold} MinArea:{self.min_area}"
    
    def create_analysis_report(self, temp_data, detection_params, heat_sources):
        """创建分析报告"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = os.path.join(self.debug_dir, f"analysis_{timestamp}.txt")
        
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write("=" * 60 + "\n")
                f.write("热源检测分析报告\n")
                f.write("=" * 60 + "\n")
                f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                
                # 温度数据部分
                if temp_data:
                    f.write("📊 温度数据分析:\n")
                    f.write(f"  最低温度: {temp_data.get('min_temp', 'N/A')}°C\n")
                    f.write(f"  最高温度: {temp_data.get('max_temp', 'N/A')}°C\n")
                    f.write(f"  平均温度: {temp_data.get('avg_temp', 'N/A')}°C\n")
                    f.write(f"  图像尺寸: {temp_data.get('width', 'N/A')} x {temp_data.get('height', 'N/A')}\n\n")
                
                # 检测参数部分
                if detection_params:
                    f.write("🔧 检测参数:\n")
                    f.write(f"  阈值模式: {detection_params.get('threshold_mode', 'N/A')}\n")
                    f.write(f"  计算阈值: {detection_params.get('threshold_temp', 'N/A')}°C\n")
                    f.write(f"  最小面积: {detection_params.get('min_area', 'N/A')}\n\n")
                
                # 热源结果部分
                if heat_sources:
                    f.write("🔥 热源检测结果:\n")
                    f.write(f"  热源数量: {len(heat_sources)}\n")
                    for i, source in enumerate(heat_sources):
                        f.write(f"  热源{i+1}: 位置({source['x']},{source['y']}) ")
                        f.write(f"尺寸({source['width']}x{source['height']}) ")
                        f.write(f"面积({source['area']})\n")
                else:
                    f.write("🔥 热源检测结果:\n")
                    f.write("  热源数量: 0\n")
                
                f.write("\n" + "=" * 60 + "\n")
            
            # print(f"📝 分析报告已保存: {report_file}")
            return report_file
            
        except Exception as e:
            print(f"❌ 保存分析报告失败: {e}")
            return None
    
    def get_debug_statistics(self):
        """获取调试统计信息"""
        return {
            'debug_dir': self.debug_dir,
            'current_threshold': self.current_threshold,
            'min_area': self.min_area,
            'morph_kernel_size': self.morph_kernel_size,
            'save_debug_images': self.save_debug_images
        }
