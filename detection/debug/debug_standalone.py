#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
独立的热源检测调试工具
可以单独运行，用于测试和调试检测算法
"""

import cv2
import sys
import os
import time

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from detection.debug.isapi_debugger import ISAPIThermalDebugger


class StandaloneDebugger:
    """独立调试器 - 使用ISAPI原始温度数据"""

    def __init__(self, camera_ip="************", username="admin", password="dxwx12345"):
        self.debugger = ISAPIThermalDebugger(camera_ip, username, password)
        self.running = False
        self.show_steps = False
        
    def initialize(self):
        """初始化ISAPI连接"""
        print("🔗 初始化ISAPI温度数据连接...")
        # ISAPIThermalDebugger在构造时已经初始化了
        print("✅ ISAPI连接成功")
        return True
    
    def run_debug_session(self):
        """运行调试会话"""
        self.running = True
        print("\n🔍 ISAPI热源检测调试会话开始")
        print("=" * 60)
        print("🌡️ 使用ISAPI原始温度数据，无界面元素干扰")
        print("=" * 60)
        print("按键说明:")
        print("  'q' - 退出")
        print("  '+' - 增加温度阈值 (+2°C)")
        print("  '-' - 减少温度阈值 (-2°C)")
        print("  '[' - 减少最小面积 (-10)")
        print("  ']' - 增加最小面积 (+10)")
        print("  'r' - 保存调试图像和分析")
        print("  's' - 显示温度数据分析")
        print("  'p' - 显示处理步骤窗口")
        print("  'c' - 关闭处理步骤窗口")
        print("  't' - 测试单次检测")
        print("=" * 60)

        last_detection_time = 0
        detection_interval = 1.0  # 每秒检测一次

        while self.running:
            current_time = time.time()

            # 定期执行检测
            if current_time - last_detection_time > detection_interval:
                try:
                    # 获取温度数据
                    temp_data = self.debugger.get_temperature_data()
                    if temp_data:
                        print(f"\n📊 温度数据: {temp_data['min_temp']:.1f}°C - {temp_data['max_temp']:.1f}°C (平均: {temp_data['avg_temp']:.1f}°C)")

                        # 执行检测
                        bboxes, vis_images = self.debugger.detect_heat_sources()

                        # 显示检测结果
                        if bboxes:
                            print(f"🔥 检测到 {len(bboxes)} 个热源:")
                            for i, bbox in enumerate(bboxes):
                                x, y, w, h = bbox
                                print(f"   热源{i+1}: 位置({x},{y}) 尺寸({w}x{h})")
                        else:
                            print("❄️ 未检测到热源")

                        # 如果启用了步骤显示
                        if self.show_steps:
                            self._show_processing_steps(vis_images)

                        # 显示主要结果
                        if "06 - Final Result" in vis_images:
                            result_image = vis_images["06 - Final Result"]
                            # 添加状态信息
                            status_text = self.debugger.get_status_text()
                            cv2.putText(result_image, status_text, (10, 30),
                                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
                            cv2.putText(result_image, f"Detections: {len(bboxes)}", (10, 60),
                                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)

                            cv2.imshow("ISAPI Heat Detection", result_image)

                    last_detection_time = current_time

                except Exception as e:
                    print(f"❌ 检测异常: {e}")

            # 处理按键
            key = cv2.waitKey(100) & 0xFF
            if key == ord('q'):
                break
            elif key == ord('+') or key == ord('='):
                self.debugger.adjust_temperature_threshold(+2.0)
            elif key == ord('-'):
                self.debugger.adjust_temperature_threshold(-2.0)
            elif key == ord('['):
                self.debugger.adjust_min_area(-10)
            elif key == ord(']'):
                self.debugger.adjust_min_area(+10)
            elif key == ord('r'):
                self.debugger.request_debug_save()
                print("📸 下次检测将保存详细调试数据")
            elif key == ord('s'):
                self._show_temperature_analysis()
            elif key == ord('p'):
                self.show_steps = True
                print("📺 显示处理步骤窗口")
            elif key == ord('c'):
                self.show_steps = False
                cv2.destroyAllWindows()
                print("❌ 关闭处理步骤窗口")
            elif key == ord('t'):
                self._test_single_detection()

        self.cleanup()
    
    def _show_temperature_analysis(self):
        """显示温度数据分析"""
        temp_data = self.debugger.get_temperature_data()
        if temp_data:
            print(f"\n📊 详细温度分析:")
            print(f"   图像尺寸: {temp_data['width']} x {temp_data['height']}")
            print(f"   最低温度: {temp_data['min_temp']:.1f}°C")
            print(f"   最高温度: {temp_data['max_temp']:.1f}°C")
            print(f"   平均温度: {temp_data['avg_temp']:.1f}°C")
            print(f"   温度范围: {temp_data['max_temp'] - temp_data['min_temp']:.1f}°C")
            print(f"   当前阈值: {self.debugger.temperature_threshold:.1f}°C")

            # 分析阈值合理性
            if self.debugger.temperature_threshold <= temp_data['avg_temp']:
                print("   ⚠️ 阈值可能过低，会检测到大量背景区域")
            elif self.debugger.temperature_threshold > temp_data['max_temp']:
                print("   ⚠️ 阈值过高，无法检测到任何热源")
            else:
                print("   ✅ 阈值设置合理")
        else:
            print("❌ 无法获取温度数据")

    def _test_single_detection(self):
        """测试单次检测"""
        print("\n🧪 执行单次检测测试...")
        try:
            bboxes, vis_images = self.debugger.detect_heat_sources()

            if bboxes:
                print(f"✅ 检测成功: 找到 {len(bboxes)} 个热源")
                for i, bbox in enumerate(bboxes):
                    x, y, w, h = bbox
                    area = w * h
                    print(f"   热源{i+1}: 位置({x},{y}) 尺寸({w}x{h}) 面积({area})")
            else:
                print("❄️ 未检测到热源")

            # 显示处理步骤
            print(f"📊 处理步骤: {len(vis_images)} 个")
            for step_name in vis_images.keys():
                print(f"   - {step_name}")

        except Exception as e:
            print(f"❌ 测试失败: {e}")

    def _show_processing_steps(self, vis_images):
        """显示处理步骤 - Qt版本不显示OpenCV窗口"""
        # Qt版本不使用OpenCV窗口显示调试步骤
        # 调试信息保存到文件中供查看
        print(f"📊 处理步骤数量: {len(vis_images)}")
        for step_name in vis_images.keys():
            print(f"   - {step_name}")

    def cleanup(self):
        """清理资源"""
        # Qt版本不需要销毁OpenCV窗口
        print("🔚 ISAPI调试会话结束")


def main():
    """主函数"""
    print("🔥 ISAPI热源检测独立调试工具")
    print("=" * 40)
    print("🌡️ 直接使用原始温度数据，避免界面元素干扰")

    # 默认摄像头参数
    camera_ip = "************"
    username = "admin"
    password = "dxwx12345"

    # 可以通过命令行参数指定摄像头参数
    if len(sys.argv) > 1:
        camera_ip = sys.argv[1]
    if len(sys.argv) > 2:
        username = sys.argv[2]
    if len(sys.argv) > 3:
        password = sys.argv[3]

    print(f"📡 连接摄像头: {camera_ip}")

    debugger = StandaloneDebugger(camera_ip, username, password)

    if debugger.initialize():
        debugger.run_debug_session()
    else:
        print("❌ 调试会话启动失败")


if __name__ == "__main__":
    main()
