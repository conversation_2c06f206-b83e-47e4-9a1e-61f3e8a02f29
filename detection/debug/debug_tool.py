#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
热源检测调试工具
用于分析和调试热源检测算法
"""

import cv2
import numpy as np
import os
import time
from datetime import datetime
from .debug_base import BaseDebugger
from ..core.heat_source_detector import HeatSourceDetector


class DetectionDebugger(BaseDebugger):
    """热源检测调试器"""

    def __init__(self, debug_dir="detection/debug_test"):
        super().__init__(debug_dir)
        self.detector = None

        # 初始化检测器
        self._init_detector()

    def _init_detector(self):
        """初始化检测器"""
        self.detector = HeatSourceDetector(
            threshold_value=self.current_threshold,
            min_contour_area=self.min_area,
            enable_morphology=True,
            morph_kernel_size=self.morph_kernel_size,
            display_steps=True  # 开启调试模式
        )
    
    def analyze_frame(self, thermal_frame):
        """分析单帧图像"""
        if thermal_frame is None:
            return [], {}

        # 执行检测
        bboxes, vis_images = self.detector.detect(thermal_frame)

        # 如果需要保存调试图像
        if self.save_debug_images:
            self.save_debug_images(thermal_frame, vis_images, bboxes)
            self.save_debug_images = False  # 重置标志

        return bboxes, vis_images

    def adjust_threshold(self, delta):
        """调整阈值"""
        old_threshold = self.current_threshold
        self.current_threshold = max(0, min(255, self.current_threshold + delta))

        if self.current_threshold != old_threshold:
            self.detector.set_params(threshold_value=self.current_threshold)
            print(f"🔧 阈值调整: {old_threshold} → {self.current_threshold}")
        else:
            print(f"⚠️ 阈值已达到边界: {self.current_threshold}")

    def adjust_min_area(self, delta):
        """调整最小面积"""
        old_area = self.min_area
        self.min_area = max(1, self.min_area + delta)

        if self.min_area != old_area:
            self.detector.set_params(min_contour_area=self.min_area)
            print(f"🔧 最小面积调整: {old_area} → {self.min_area}")
    
    def analyze_thermal_range(self, thermal_frame):
        """分析热成像图像的温度范围"""
        if thermal_frame is None:
            return None
        
        # 转换为灰度图
        if len(thermal_frame.shape) == 3:
            gray = cv2.cvtColor(thermal_frame, cv2.COLOR_BGR2GRAY)
        else:
            gray = thermal_frame
        
        # 统计信息
        min_val = np.min(gray)
        max_val = np.max(gray)
        mean_val = np.mean(gray)
        std_val = np.std(gray)
        
        # 计算建议阈值
        suggested_threshold = int(mean_val + 2 * std_val)
        
        analysis = {
            'min': min_val,
            'max': max_val,
            'mean': mean_val,
            'std': std_val,
            'suggested_threshold': suggested_threshold,
            'current_threshold': self.current_threshold
        }
        
        return analysis
    
    def print_analysis(self, analysis):
        """打印分析结果"""
        if analysis is None:
            return
        
        print("\n🌡️ 热成像图像分析:")
        print(f"   最小值: {analysis['min']:.1f}")
        print(f"   最大值: {analysis['max']:.1f}")
        print(f"   平均值: {analysis['mean']:.1f}")
        print(f"   标准差: {analysis['std']:.1f}")
        print(f"   建议阈值: {analysis['suggested_threshold']}")
        print(f"   当前阈值: {analysis['current_threshold']}")
        
        if analysis['current_threshold'] < analysis['mean']:
            print("⚠️ 当前阈值可能过低，会检测到大量背景")
        elif analysis['current_threshold'] > analysis['max']:
            print("⚠️ 当前阈值过高，可能检测不到任何热源")
    
class QuickDebugger:
    """快速调试工具 - 用于实时调试"""
    
    def __init__(self):
        self.debugger = DetectionDebugger()
        self.last_analysis_time = 0
        self.analysis_interval = 2.0  # 每2秒分析一次
    
    def process_frame(self, thermal_frame):
        """处理帧并返回调试信息"""
        # 执行检测
        bboxes, vis_images = self.debugger.analyze_frame(thermal_frame)
        
        # 定期进行图像分析
        current_time = time.time()
        if current_time - self.last_analysis_time > self.analysis_interval:
            analysis = self.debugger.analyze_thermal_range(thermal_frame)
            if analysis:
                self.debugger.print_analysis(analysis)
            self.last_analysis_time = current_time
        
        return bboxes, vis_images
    
    def handle_key(self, key):
        """处理按键"""
        if key == ord('+') or key == ord('='):
            self.debugger.adjust_threshold(+10)
            return True
        elif key == ord('-'):
            self.debugger.adjust_threshold(-10)
            return True
        elif key == ord('r'):
            self.debugger.request_debug_save()
            return True
        elif key == ord('['):
            self.debugger.adjust_min_area(-10)
            return True
        elif key == ord(']'):
            self.debugger.adjust_min_area(+10)
            return True
        elif key == ord('a'):
            # 自动调整阈值
            analysis = self.debugger.analyze_thermal_range(None)  # 需要传入当前帧
            if analysis:
                self.debugger.detector.set_params(threshold_value=analysis['suggested_threshold'])
                self.debugger.current_threshold = analysis['suggested_threshold']
                print(f"🤖 自动调整阈值到: {analysis['suggested_threshold']}")
            return True
        
        return False
    
    def get_status_text(self):
        """获取状态文本"""
        return self.debugger.get_status_text()


# ISAPIThermalDebugger 类已移动到 isapi_debugger.py 文件中
# 请使用: from .isapi_debugger import ISAPIThermalDebugger


    # 以下方法已移动到 isapi_debugger.py 文件中
    # 请使用: from .isapi_debugger import ISAPIThermalDebugger

    # 以下方法已移动到 isapi_debugger.py 文件中
    # 请使用: from .isapi_debugger import ISAPIThermalDebugger

    def _print_processing_details(self, original_frame, vis_images, bboxes):
        """打印图像处理的详细信息"""
        print("\n" + "="*60)
        print("🔍 图像处理详细分析")
        print("="*60)

        # 分析原始图像
        if original_frame is not None:
            print(f"📷 原始图像:")
            print(f"   尺寸: {original_frame.shape}")
            print(f"   类型: {original_frame.dtype}")

            if len(original_frame.shape) == 3:
                gray = cv2.cvtColor(original_frame, cv2.COLOR_BGR2GRAY)
            else:
                gray = original_frame

            print(f"   灰度范围: {np.min(gray)} - {np.max(gray)}")
            print(f"   平均值: {np.mean(gray):.1f}")
            print(f"   标准差: {np.std(gray):.1f}")

        # 分析每个处理步骤
        step_analysis = {}
        for step_name, image in vis_images.items():
            if image is not None:
                analysis = self._analyze_image_step(step_name, image)
                step_analysis[step_name] = analysis
                print(f"\n🔧 {step_name}:")
                for key, value in analysis.items():
                    print(f"   {key}: {value}")

        # 分析检测结果
        print(f"\n🎯 检测结果:")
        print(f"   检测到的热源数量: {len(bboxes)}")
        if bboxes:
            total_area = 0
            for i, bbox in enumerate(bboxes):
                x, y, w, h = bbox
                area = w * h
                total_area += area
                print(f"   热源{i+1}: 位置({x},{y}) 尺寸({w}x{h}) 面积({area})")
            print(f"   总面积: {total_area}")

            # 分析热源位置分布
            if len(bboxes) > 0:
                x_coords = [bbox[0] for bbox in bboxes]
                y_coords = [bbox[1] for bbox in bboxes]
                print(f"   X坐标范围: {min(x_coords)} - {max(x_coords)}")
                print(f"   Y坐标范围: {min(y_coords)} - {max(y_coords)}")

        # 给出诊断建议
        self._print_diagnostic_suggestions(step_analysis, bboxes, original_frame)
        print("="*60)

    def _analyze_image_step(self, step_name, image):
        """分析单个图像处理步骤"""
        analysis = {}

        if image is None:
            return {"状态": "图像为空"}

        analysis["尺寸"] = f"{image.shape}"
        analysis["数据类型"] = str(image.dtype)

        if len(image.shape) == 2:  # 灰度图像
            analysis["最小值"] = f"{np.min(image)}"
            analysis["最大值"] = f"{np.max(image)}"
            analysis["平均值"] = f"{np.mean(image):.1f}"
            analysis["标准差"] = f"{np.std(image):.1f}"

            # 对于二值图像，计算白色像素比例
            if "Binary" in step_name or "Thresh" in step_name:
                white_pixels = np.sum(image == 255)
                total_pixels = image.shape[0] * image.shape[1]
                white_ratio = (white_pixels / total_pixels) * 100
                analysis["白色像素"] = f"{white_pixels} ({white_ratio:.1f}%)"

                # 分析连通区域
                contours, _ = cv2.findContours(image, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                analysis["连通区域数"] = f"{len(contours)}"
                if contours:
                    areas = [cv2.contourArea(c) for c in contours]
                    analysis["区域面积范围"] = f"{min(areas):.0f} - {max(areas):.0f}"

        elif len(image.shape) == 3:  # 彩色图像
            for i, channel in enumerate(['B', 'G', 'R']):
                channel_data = image[:, :, i]
                analysis[f"{channel}通道范围"] = f"{np.min(channel_data)} - {np.max(channel_data)}"

        return analysis

    def _print_diagnostic_suggestions(self, step_analysis, bboxes, original_frame):
        """打印诊断建议"""
        print(f"\n💡 诊断建议:")

        # 检查阈值设置
        if "02 - Binary Mask (Thresholded)" in step_analysis:
            binary_info = step_analysis["02 - Binary Mask (Thresholded)"]
            if "白色像素" in binary_info:
                white_ratio = float(binary_info["白色像素"].split('(')[1].split('%')[0])
                if white_ratio > 50:
                    print("   ⚠️ 白色像素比例过高(>50%)，阈值可能过低")
                    print("   建议: 增加阈值或检查是否检测到了背景/边框")
                elif white_ratio < 1:
                    print("   ⚠️ 白色像素比例过低(<1%)，阈值可能过高")
                    print("   建议: 降低阈值")
                else:
                    print(f"   ✅ 白色像素比例合理({white_ratio:.1f}%)")

        # 检查检测结果
        if len(bboxes) == 0:
            print("   ⚠️ 未检测到任何热源")
            print("   建议: 1) 降低阈值 2) 减少最小面积要求 3) 检查图像是否包含热源")
        elif len(bboxes) > 10:
            print("   ⚠️ 检测到过多热源，可能存在噪声")
            print("   建议: 1) 增加阈值 2) 增加最小面积要求 3) 启用形态学操作")

        # 检查边界框位置
        if bboxes:
            edge_detections = 0
            for bbox in bboxes:
                x, y, w, h = bbox
                if x <= 5 or y <= 5:  # 检测框在边缘
                    edge_detections += 1

            if edge_detections > 0:
                print(f"   ⚠️ 有{edge_detections}个检测框位于图像边缘")
                print("   可能原因: 检测到了界面元素、边框或文字标签")
                print("   建议: 1) 调整阈值 2) 添加边缘排除逻辑")

        # 检查图像质量
        if original_frame is not None:
            if len(original_frame.shape) == 3:
                gray = cv2.cvtColor(original_frame, cv2.COLOR_BGR2GRAY)
            else:
                gray = original_frame

            dynamic_range = np.max(gray) - np.min(gray)
            if dynamic_range < 50:
                print("   ⚠️ 图像动态范围较小，可能影响检测效果")
                print("   建议: 检查摄像头设置或图像预处理")

    def show_processing_steps(self, thermal_frame):
        """显示图像处理步骤 - Qt版本不显示OpenCV窗口"""
        if thermal_frame is None:
            return

        # 执行检测并获取中间步骤
        bboxes, vis_images = self.detector.detect(thermal_frame)

        # Qt版本不使用OpenCV窗口显示调试步骤
        # 调试信息保存到文件中供查看
        print(f"📊 处理步骤数量: {len(vis_images)}")
        for step_name in vis_images.keys():
            print(f"   - {step_name}")
        print(f"📊 检测到 {len(bboxes)} 个热源")

        # Qt版本不显示OpenCV窗口
        # 最终结果保存到文件中供查看
        print(f"📊 检测到 {len(bboxes)} 个热源")

    def close_debug_windows(self):
        """关闭所有调试窗口 - Qt版本不需要"""
        # Qt版本不使用OpenCV窗口
        pass
