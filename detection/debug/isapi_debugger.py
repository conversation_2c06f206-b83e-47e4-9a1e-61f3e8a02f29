#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ISAPI热源检测调试器
基于原始温度数据的热源检测调试工具
"""

import cv2
import numpy as np
import os
import sys
from datetime import datetime
from .debug_base import BaseDebugger
from ..core.heat_source_detector import HeatSourceDetector

# 添加项目根目录到路径以导入温度读取器
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from temperature.readers.real_temp_reader import RealTemperatureReader


class ISAPIThermalDebugger(BaseDebugger):
    """基于ISAPI原始温度数据的热源检测调试器"""

    def __init__(self, camera_ip="************", username="admin", password="dxwx12345",
                 debug_dir="detection/debug_test"):
        super().__init__(debug_dir)
        
        self.camera_ip = camera_ip
        self.username = username
        self.password = password

        # 温度读取器
        self.temp_reader = None

        # 检测参数
        self.use_adaptive_threshold = True  # 使用自适应阈值
        self.manual_temperature_threshold = 40.0  # 手动温度阈值（摄氏度）
        self.adaptive_method = "mean_plus_std"  # 自适应方法
        self.adaptive_factor = 1.5  # 自适应因子

        # 检测器
        self.detector = None

        # 初始化
        self._init_temp_reader()
        self._init_detector()

    def _init_temp_reader(self):
        """初始化温度读取器"""
        try:
            self.temp_reader = RealTemperatureReader(
                ip=self.camera_ip,
                username=self.username,
                password=self.password
            )

            if self.temp_reader.initialize():
                print("✅ ISAPI温度读取器初始化成功")
                return True
            else:
                print("❌ ISAPI温度读取器初始化失败")
                return False

        except Exception as e:
            print(f"❌ 温度读取器初始化异常: {e}")
            return False

    def _init_detector(self):
        """初始化检测器"""
        try:
            self.detector = HeatSourceDetector(
                threshold_value=200,  # 这个阈值用于图像处理，不是温度阈值
                min_contour_area=self.min_area,
                enable_morphology=True,
                morph_kernel_size=self.morph_kernel_size,
                display_steps=True,
                enable_flame_analysis=True,  # 启用火焰分析功能
                sample_rate_hz=25.0  # 设置采样率（帧率）
            )

            # 设置默认的阈值方法为固定阈值
            self.detector.set_threshold_method("fixed")

            # 设置保守的形态学策略（防止分裂）
            self.detector.set_morphology_strategy("conservative", prevent_splitting=True, min_component_area=30)

            # 确保使用分离策略，为每个热源绘制独立边界框
            self.detector.set_detection_strategy("merge_all")

            print("✅ 热源检测器初始化成功")
            print(f"🔧 默认阈值方法: {self.detector.get_threshold_method_info()}")
            print(f"🔧 形态学策略: {self.detector.get_morphology_info()}")

            # 显示火焰分析功能状态
            flame_stats = self.detector.get_flame_analysis_statistics()
            if flame_stats['flame_analysis_enabled']:
                print("🔥 火焰分析功能已启用")
                if 'flicker_analyzer' in flame_stats:
                    flicker_stats = flame_stats['flicker_analyzer']
                    print(f"   - 闪烁分析: 采样率{flicker_stats['sample_rate_hz']}Hz, 频率范围{flicker_stats['flicker_freq_range_hz']}Hz")
                if 'morphology_analyzer' in flame_stats:
                    print(f"   - 形状分析: 已配置")
            else:
                print("⚠️ 火焰分析功能未启用")
            print(f"🔧 检测策略: {self.detector.get_detection_strategy_info()}")
        except Exception as e:
            print(f"❌ 检测器初始化失败: {e}")
            self.detector = None

    def get_temperature_data(self):
        """获取原始温度数据"""
        if not self.temp_reader:
            return None

        try:
            result = self.temp_reader.get_real_temperature_data()
            if result and result.get('temp_data'):
                return result['temp_data']
            return None
        except Exception as e:
            print(f"❌ 获取温度数据失败: {e}")
            return None

    def get_original_dimensions(self):
        """获取原始热成像尺寸"""
        try:
            temp_data = self.get_temperature_data()
            if temp_data and temp_data.get('matrix') is not None:
                height, width = temp_data['matrix'].shape
                return height, width

            # 默认海康威视热成像分辨率
            return 384, 288

        except Exception as e:
            print(f"❌ 获取原始尺寸失败: {e}")
            return 384, 288

    def calculate_adaptive_threshold(self, temp_matrix):
        """计算自适应温度阈值"""
        if not self.detector or not self.detector.temperature_processor:
            return self.manual_temperature_threshold
        
        return self.detector.temperature_processor.calculate_adaptive_threshold(
            temp_matrix, self.adaptive_method, self.adaptive_factor
        )

    def detect_heat_sources(self):
        """检测热源"""
        # 获取温度数据
        temp_data = self.get_temperature_data()
        if not temp_data:
            print("❌ 无法获取温度数据")
            return [], {}

        temp_matrix = temp_data['matrix']

        try:
            # 使用温度阈值进行检测
            bboxes, vis_images = self._detect_from_temperature_matrix(temp_matrix)

            # 始终生成分析文件（用于热源分析器）
            self._save_analysis_file_only(temp_data, bboxes)

            # 如果需要保存调试图像
            if self.save_debug_images:
                self._save_isapi_debug_images(temp_data, bboxes, vis_images)
                self.save_debug_images = False

            return bboxes, vis_images

        except Exception as e:
            print(f"❌ 热源检测失败: {e}")
            return [], {}

    def _detect_from_temperature_matrix(self, temp_matrix):
        """从温度矩阵检测热源"""
        if not self.detector:
            return [], {}
        
        try:
            # 计算阈值（自适应或手动）
            if self.use_adaptive_threshold:
                current_threshold = self.calculate_adaptive_threshold(temp_matrix)
            else:
                current_threshold = self.manual_temperature_threshold

            # 使用检测器的温度矩阵检测方法
            bboxes, vis_images = self.detector.detect_from_temperature_matrix(
                temp_matrix, current_threshold
            )

            # 显示检测结果（已注释）
            # threshold_info = f"自适应阈值: {current_threshold:.1f}°C" if self.use_adaptive_threshold else f"手动阈值: {current_threshold:.1f}°C"
            # print(f"🔥 ISAPI检测结果: 找到 {len(bboxes)} 个热源 ({threshold_info})")

            return bboxes, vis_images

        except Exception as e:
            print(f"❌ 温度矩阵检测失败: {e}")
            return [], {}

    def _save_isapi_debug_images(self, temp_data, bboxes, vis_images):
        """保存ISAPI调试图像"""
        timestamp = datetime.now().strftime("%H%M%S")
        
        try:
            # 保存处理步骤图像
            step_counter = 1
            for step_name, image in vis_images.items():
                if image is not None:
                    # 清理文件名
                    clean_name = step_name.replace(' ', '_').replace('-', '_').replace('(', '').replace(')', '')
                    filename = f"isapi_{timestamp}_{step_counter:02d}_{step_counter:02d}__{clean_name}.jpg"
                    cv2.imwrite(os.path.join(self.debug_dir, filename), image)
                    step_counter += 1

            # 创建分析报告
            temp_matrix = temp_data['matrix']
            analysis_data = {
                'min_temp': float(np.min(temp_matrix)),
                'max_temp': float(np.max(temp_matrix)),
                'avg_temp': float(np.mean(temp_matrix)),
                'width': temp_matrix.shape[1],
                'height': temp_matrix.shape[0]
            }
            
            detection_params = {
                'threshold_mode': 'adaptive' if self.use_adaptive_threshold else 'manual',
                'threshold_temp': self.calculate_adaptive_threshold(temp_matrix) if self.use_adaptive_threshold else self.manual_temperature_threshold,
                'min_area': self.min_area
            }
            
            heat_sources = []
            for i, (x, y, w, h) in enumerate(bboxes):
                heat_sources.append({
                    'id': i + 1,
                    'x': x,
                    'y': y,
                    'width': w,
                    'height': h,
                    'area': w * h
                })
            
            self.create_analysis_report(analysis_data, detection_params, heat_sources)

            print(f"🔍 ISAPI调试图像已保存到: {self.debug_dir}")
            print(f"   时间戳: {timestamp}")
            print(f"   检测到 {len(bboxes)} 个热源")

        except Exception as e:
            print(f"❌ 保存ISAPI调试图像失败: {e}")

    def _save_analysis_file_only(self, temp_data, bboxes):
        """只保存分析文件，不保存调试图像"""
        try:
            temp_matrix = temp_data['matrix']

            # 准备分析数据
            analysis_data = {
                'min_temp': float(np.min(temp_matrix)),
                'max_temp': float(np.max(temp_matrix)),
                'avg_temp': float(np.mean(temp_matrix)),
                'width': temp_matrix.shape[1],
                'height': temp_matrix.shape[0]
            }

            detection_params = {
                'threshold_mode': 'adaptive' if self.use_adaptive_threshold else 'manual',
                'threshold_temp': self.calculate_adaptive_threshold(temp_matrix) if self.use_adaptive_threshold else self.manual_temperature_threshold,
                'min_area': self.min_area
            }

            heat_sources = []
            for i, (x, y, w, h) in enumerate(bboxes):
                heat_sources.append({
                    'id': i + 1,
                    'x': x,
                    'y': y,
                    'width': w,
                    'height': h,
                    'area': w * h
                })

            # 创建分析报告
            self.create_analysis_report(analysis_data, detection_params, heat_sources)

        except Exception as e:
            print(f"❌ 保存分析文件失败: {e}")

    def adjust_temperature_threshold(self, delta):
        """调整温度阈值"""
        old_threshold = self.manual_temperature_threshold
        self.manual_temperature_threshold = max(0, self.manual_temperature_threshold + delta)
        
        if self.manual_temperature_threshold != old_threshold:
            print(f"🌡️ 温度阈值调整: {old_threshold:.1f}°C → {self.manual_temperature_threshold:.1f}°C")
        else:
            print(f"⚠️ 温度阈值已达到边界: {self.manual_temperature_threshold:.1f}°C")

    def set_adaptive_method(self, method, factor=1.5):
        """设置自适应阈值方法"""
        valid_methods = ["mean_plus_std", "percentile", "otsu_like", "max_minus_range"]
        if method in valid_methods:
            self.adaptive_method = method
            self.adaptive_factor = factor
            print(f"🔄 自适应方法设置为: {method} (因子: {factor})")
        else:
            print(f"❌ 无效的自适应方法: {method}")
            print(f"   可用方法: {', '.join(valid_methods)}")

    def toggle_adaptive_mode(self):
        """切换自适应模式"""
        self.use_adaptive_threshold = not self.use_adaptive_threshold
        mode = "自适应" if self.use_adaptive_threshold else "手动"
        print(f"🔄 阈值模式切换为: {mode}")

    def get_current_threshold(self):
        """获取当前温度阈值"""
        return self.manual_temperature_threshold

    def is_adaptive_mode(self):
        """检查是否为自适应模式"""
        return self.use_adaptive_threshold

    def set_adaptive_mode(self, adaptive: bool):
        """设置自适应模式"""
        self.use_adaptive_threshold = adaptive
        mode = "自适应" if adaptive else "手动"
        print(f"🔄 阈值模式设置为: {mode}")

    def set_threshold(self, threshold: float):
        """设置温度阈值"""
        old_threshold = self.manual_temperature_threshold
        self.manual_temperature_threshold = max(0, threshold)
        print(f"🌡️ 温度阈值设置: {old_threshold:.1f}°C → {self.manual_temperature_threshold:.1f}°C")

    @property
    def is_initialized(self):
        """检查是否已初始化"""
        return (self.temp_reader is not None and
                self.detector is not None)

    def get_isapi_status(self):
        """获取ISAPI调试器状态"""
        status = self.get_debug_statistics()
        status.update({
            'camera_ip': self.camera_ip,
            'temp_reader_initialized': self.temp_reader is not None,
            'detector_initialized': self.detector is not None,
            'use_adaptive_threshold': self.use_adaptive_threshold,
            'manual_temperature_threshold': self.manual_temperature_threshold,
            'adaptive_method': self.adaptive_method,
            'adaptive_factor': self.adaptive_factor
        })
        return status
