#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级后处理模块
手动实现NMS算法和检测结果解析，替换PyTorch/Ultralytics的自动化处理
"""

import cv2
import numpy as np
from typing import Tuple, Dict, List, Optional, Union
from dataclasses import dataclass
import time

from utils.logger import get_logger


@dataclass
class PostprocessConfig:
    """后处理配置类"""
    confidence_threshold: float = 0.5  # 置信度阈值
    iou_threshold: float = 0.45  # IoU阈值
    max_detections: int = 1000  # 最大检测数量
    max_detections_per_class: int = 300  # 每类最大检测数量
    class_agnostic: bool = False  # 是否类别无关的NMS
    multi_label: bool = True  # 是否支持多标签
    merge_overlapping: bool = False  # 是否合并重叠框
    min_box_size: int = 2  # 最小框尺寸
    score_threshold: float = 0.1  # 分数阈值（预过滤）


class AdvancedPostprocessor:
    """高级后处理器"""
    
    def __init__(self, config: PostprocessConfig = None):
        """
        初始化后处理器
        
        Args:
            config: 后处理配置
        """
        self.config = config or PostprocessConfig()
        self.logger = get_logger("AdvancedPostprocessor")
        
        # 性能统计
        self.stats = {
            'total_processed': 0,
            'total_time': 0.0,
            'avg_time': 0.0,
            'nms_time': 0.0,
            'parse_time': 0.0
        }
        
        self.logger.info(f"后处理器初始化完成，置信度阈值: {self.config.confidence_threshold}")
    
    def parse_yolo_output(self, predictions: np.ndarray, 
                         input_shape: Tuple[int, int],
                         num_classes: int) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        解析YOLO模型输出
        
        Args:
            predictions: 模型预测结果 (batch_size, num_anchors, 5+num_classes)
            input_shape: 输入图像尺寸 (width, height)
            num_classes: 类别数量
            
        Returns:
            boxes, scores, class_ids
        """
        start_time = time.time()
        
        # 确保是2D数组
        if predictions.ndim == 3:
            predictions = predictions[0]  # 移除batch维度
        
        # 分离坐标和分数
        boxes = predictions[:, :4]  # x, y, w, h
        obj_scores = predictions[:, 4]  # 目标置信度
        class_scores = predictions[:, 5:5+num_classes]  # 类别分数
        
        # 计算最终分数
        if self.config.multi_label:
            # 多标签模式：每个类别独立计算
            scores = obj_scores[:, np.newaxis] * class_scores
            class_ids = np.argmax(scores, axis=1)
            max_scores = np.max(scores, axis=1)
        else:
            # 单标签模式：选择最高分数的类别
            class_ids = np.argmax(class_scores, axis=1)
            max_scores = obj_scores * class_scores[np.arange(len(class_scores)), class_ids]
        
        # 转换坐标格式：中心点+宽高 -> 左上角+右下角
        boxes = self._xywh_to_xyxy(boxes)
        
        # 预过滤低分数检测
        valid_mask = max_scores > self.config.score_threshold
        boxes = boxes[valid_mask]
        scores = max_scores[valid_mask]
        class_ids = class_ids[valid_mask]
        
        self.stats['parse_time'] += time.time() - start_time
        
        return boxes, scores, class_ids
    
    def _xywh_to_xyxy(self, boxes: np.ndarray) -> np.ndarray:
        """
        转换边界框格式：中心点+宽高 -> 左上角+右下角
        
        Args:
            boxes: 边界框数组 (N, 4) [x_center, y_center, width, height]
            
        Returns:
            转换后的边界框 (N, 4) [x1, y1, x2, y2]
        """
        xyxy_boxes = boxes.copy()
        xyxy_boxes[:, 0] = boxes[:, 0] - boxes[:, 2] / 2  # x1
        xyxy_boxes[:, 1] = boxes[:, 1] - boxes[:, 3] / 2  # y1
        xyxy_boxes[:, 2] = boxes[:, 0] + boxes[:, 2] / 2  # x2
        xyxy_boxes[:, 3] = boxes[:, 1] + boxes[:, 3] / 2  # y2
        return xyxy_boxes
    
    def _xyxy_to_xywh(self, boxes: np.ndarray) -> np.ndarray:
        """
        转换边界框格式：左上角+右下角 -> 中心点+宽高
        
        Args:
            boxes: 边界框数组 (N, 4) [x1, y1, x2, y2]
            
        Returns:
            转换后的边界框 (N, 4) [x_center, y_center, width, height]
        """
        xywh_boxes = boxes.copy()
        xywh_boxes[:, 0] = (boxes[:, 0] + boxes[:, 2]) / 2  # x_center
        xywh_boxes[:, 1] = (boxes[:, 1] + boxes[:, 3]) / 2  # y_center
        xywh_boxes[:, 2] = boxes[:, 2] - boxes[:, 0]        # width
        xywh_boxes[:, 3] = boxes[:, 3] - boxes[:, 1]        # height
        return xywh_boxes
    
    def calculate_iou(self, boxes1: np.ndarray, boxes2: np.ndarray) -> np.ndarray:
        """
        计算IoU (Intersection over Union)
        
        Args:
            boxes1: 边界框数组1 (N, 4) [x1, y1, x2, y2]
            boxes2: 边界框数组2 (M, 4) [x1, y1, x2, y2]
            
        Returns:
            IoU矩阵 (N, M)
        """
        # 计算交集
        x1 = np.maximum(boxes1[:, 0:1], boxes2[:, 0])
        y1 = np.maximum(boxes1[:, 1:2], boxes2[:, 1])
        x2 = np.minimum(boxes1[:, 2:3], boxes2[:, 2])
        y2 = np.minimum(boxes1[:, 3:4], boxes2[:, 3])
        
        # 交集面积
        intersection = np.maximum(0, x2 - x1) * np.maximum(0, y2 - y1)
        
        # 计算各自面积
        area1 = (boxes1[:, 2] - boxes1[:, 0]) * (boxes1[:, 3] - boxes1[:, 1])
        area2 = (boxes2[:, 2] - boxes2[:, 0]) * (boxes2[:, 3] - boxes2[:, 1])
        
        # 并集面积
        union = area1[:, np.newaxis] + area2 - intersection
        
        # IoU
        iou = intersection / (union + 1e-6)
        
        return iou
    
    def nms_single_class(self, boxes: np.ndarray, scores: np.ndarray) -> List[int]:
        """
        单类别NMS
        
        Args:
            boxes: 边界框数组 (N, 4) [x1, y1, x2, y2]
            scores: 分数数组 (N,)
            
        Returns:
            保留的索引列表
        """
        if len(boxes) == 0:
            return []
        
        # 按分数降序排序
        order = np.argsort(scores)[::-1]
        
        keep = []
        while len(order) > 0:
            # 选择分数最高的框
            i = order[0]
            keep.append(i)
            
            if len(order) == 1:
                break
            
            # 计算与其他框的IoU
            iou = self.calculate_iou(boxes[i:i+1], boxes[order[1:]])[0]
            
            # 保留IoU小于阈值的框
            order = order[1:][iou <= self.config.iou_threshold]
        
        return keep
    
    def nms_multi_class(self, boxes: np.ndarray, scores: np.ndarray, 
                       class_ids: np.ndarray) -> List[int]:
        """
        多类别NMS
        
        Args:
            boxes: 边界框数组 (N, 4) [x1, y1, x2, y2]
            scores: 分数数组 (N,)
            class_ids: 类别ID数组 (N,)
            
        Returns:
            保留的索引列表
        """
        if len(boxes) == 0:
            return []
        
        keep = []
        unique_classes = np.unique(class_ids)
        
        for cls in unique_classes:
            # 获取当前类别的检测
            cls_mask = class_ids == cls
            cls_boxes = boxes[cls_mask]
            cls_scores = scores[cls_mask]
            cls_indices = np.where(cls_mask)[0]
            
            # 对当前类别执行NMS
            cls_keep = self.nms_single_class(cls_boxes, cls_scores)
            
            # 限制每类检测数量
            if len(cls_keep) > self.config.max_detections_per_class:
                cls_keep = cls_keep[:self.config.max_detections_per_class]
            
            # 添加到总的保留列表
            keep.extend(cls_indices[cls_keep])
        
        return keep
    
    def apply_nms(self, boxes: np.ndarray, scores: np.ndarray, 
                  class_ids: np.ndarray) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        应用非极大值抑制
        
        Args:
            boxes: 边界框数组 (N, 4) [x1, y1, x2, y2]
            scores: 分数数组 (N,)
            class_ids: 类别ID数组 (N,)
            
        Returns:
            过滤后的boxes, scores, class_ids
        """
        start_time = time.time()
        
        if len(boxes) == 0:
            return np.array([]), np.array([]), np.array([])
        
        # 过滤置信度
        conf_mask = scores >= self.config.confidence_threshold
        boxes = boxes[conf_mask]
        scores = scores[conf_mask]
        class_ids = class_ids[conf_mask]
        
        if len(boxes) == 0:
            return np.array([]), np.array([]), np.array([])
        
        # 过滤小框
        widths = boxes[:, 2] - boxes[:, 0]
        heights = boxes[:, 3] - boxes[:, 1]
        size_mask = (widths >= self.config.min_box_size) & (heights >= self.config.min_box_size)
        boxes = boxes[size_mask]
        scores = scores[size_mask]
        class_ids = class_ids[size_mask]
        
        if len(boxes) == 0:
            return np.array([]), np.array([]), np.array([])
        
        # 执行NMS
        if self.config.class_agnostic:
            # 类别无关的NMS
            keep = self.nms_single_class(boxes, scores)
        else:
            # 多类别NMS
            keep = self.nms_multi_class(boxes, scores, class_ids)
        
        # 限制总检测数量
        if len(keep) > self.config.max_detections:
            # 按分数排序，保留最高分的检测
            keep_scores = scores[keep]
            top_indices = np.argsort(keep_scores)[::-1][:self.config.max_detections]
            keep = [keep[i] for i in top_indices]
        
        self.stats['nms_time'] += time.time() - start_time
        
        return boxes[keep], scores[keep], class_ids[keep]

    def postprocess_detections(self, predictions: np.ndarray,
                             input_shape: Tuple[int, int],
                             original_shape: Tuple[int, int],
                             transform_info: Dict,
                             num_classes: int,
                             class_names: Optional[Dict[int, str]] = None) -> List[Dict]:
        """
        完整的检测后处理流程

        Args:
            predictions: 模型预测结果
            input_shape: 输入图像尺寸 (width, height)
            original_shape: 原始图像尺寸 (width, height)
            transform_info: 预处理变换信息
            num_classes: 类别数量
            class_names: 类别名称映射

        Returns:
            检测结果列表
        """
        start_time = time.time()

        # 1. 解析模型输出
        boxes, scores, class_ids = self.parse_yolo_output(
            predictions, input_shape, num_classes
        )

        if len(boxes) == 0:
            return []

        # 2. 应用NMS
        boxes, scores, class_ids = self.apply_nms(boxes, scores, class_ids)

        if len(boxes) == 0:
            return []

        # 3. 坐标变换回原图
        if transform_info:
            boxes = self._transform_boxes_to_original(boxes, transform_info)

        # 4. 确保坐标在原图范围内
        boxes = self._clip_boxes_to_image(boxes, original_shape)

        # 5. 格式化输出
        detections = []
        for i in range(len(boxes)):
            detection = {
                'bbox': boxes[i].tolist(),  # [x1, y1, x2, y2]
                'score': float(scores[i]),
                'class_id': int(class_ids[i]),
                'class_name': class_names.get(int(class_ids[i]), f'class_{int(class_ids[i])}') if class_names else f'class_{int(class_ids[i])}',
                'confidence': float(scores[i])  # 兼容性别名
            }
            detections.append(detection)

        # 更新统计信息
        self.stats['total_processed'] += 1
        process_time = time.time() - start_time
        self.stats['total_time'] += process_time
        self.stats['avg_time'] = self.stats['total_time'] / self.stats['total_processed']

        return detections

    def _transform_boxes_to_original(self, boxes: np.ndarray,
                                   transform_info: Dict) -> np.ndarray:
        """
        将边界框坐标变换回原图

        Args:
            boxes: 边界框数组 (N, 4) [x1, y1, x2, y2]
            transform_info: 变换信息

        Returns:
            变换后的边界框
        """
        transformed_boxes = boxes.copy()

        # 移除padding
        transformed_boxes[:, [0, 2]] -= transform_info['pad_x']  # x坐标
        transformed_boxes[:, [1, 3]] -= transform_info['pad_y']  # y坐标

        # 逆缩放
        transformed_boxes /= transform_info['scale']

        return transformed_boxes

    def _clip_boxes_to_image(self, boxes: np.ndarray,
                           image_shape: Tuple[int, int]) -> np.ndarray:
        """
        将边界框裁剪到图像范围内

        Args:
            boxes: 边界框数组 (N, 4) [x1, y1, x2, y2]
            image_shape: 图像尺寸 (width, height)

        Returns:
            裁剪后的边界框
        """
        width, height = image_shape
        clipped_boxes = boxes.copy()

        clipped_boxes[:, [0, 2]] = np.clip(clipped_boxes[:, [0, 2]], 0, width)
        clipped_boxes[:, [1, 3]] = np.clip(clipped_boxes[:, [1, 3]], 0, height)

        return clipped_boxes

    def merge_overlapping_detections(self, detections: List[Dict],
                                   iou_threshold: float = 0.5) -> List[Dict]:
        """
        合并重叠的检测结果

        Args:
            detections: 检测结果列表
            iou_threshold: IoU阈值

        Returns:
            合并后的检测结果
        """
        if not detections or not self.config.merge_overlapping:
            return detections

        # 按类别分组
        class_groups = {}
        for det in detections:
            class_id = det['class_id']
            if class_id not in class_groups:
                class_groups[class_id] = []
            class_groups[class_id].append(det)

        merged_detections = []

        for class_id, class_dets in class_groups.items():
            if len(class_dets) <= 1:
                merged_detections.extend(class_dets)
                continue

            # 转换为数组格式
            boxes = np.array([det['bbox'] for det in class_dets])
            scores = np.array([det['score'] for det in class_dets])

            # 计算IoU矩阵
            iou_matrix = self.calculate_iou(boxes, boxes)

            # 合并重叠的检测
            merged = []
            used = set()

            for i, det in enumerate(class_dets):
                if i in used:
                    continue

                # 找到与当前检测重叠的其他检测
                overlapping = [i]
                for j in range(i + 1, len(class_dets)):
                    if j not in used and iou_matrix[i, j] > iou_threshold:
                        overlapping.append(j)
                        used.add(j)

                if len(overlapping) == 1:
                    # 没有重叠，直接添加
                    merged.append(det)
                else:
                    # 合并重叠的检测
                    merged_det = self._merge_detections([class_dets[k] for k in overlapping])
                    merged.append(merged_det)

                used.add(i)

            merged_detections.extend(merged)

        return merged_detections

    def _merge_detections(self, detections: List[Dict]) -> Dict:
        """
        合并多个检测结果

        Args:
            detections: 要合并的检测结果列表

        Returns:
            合并后的检测结果
        """
        if len(detections) == 1:
            return detections[0]

        # 使用加权平均合并边界框
        boxes = np.array([det['bbox'] for det in detections])
        scores = np.array([det['score'] for det in detections])

        # 权重为分数
        weights = scores / scores.sum()

        # 加权平均边界框
        merged_bbox = np.average(boxes, axis=0, weights=weights).tolist()

        # 最高分数
        max_score_idx = np.argmax(scores)
        merged_detection = detections[max_score_idx].copy()
        merged_detection['bbox'] = merged_bbox
        merged_detection['score'] = float(np.max(scores))
        merged_detection['confidence'] = float(np.max(scores))

        return merged_detection

    def filter_detections_by_area(self, detections: List[Dict],
                                min_area: float = 0,
                                max_area: float = float('inf')) -> List[Dict]:
        """
        根据面积过滤检测结果

        Args:
            detections: 检测结果列表
            min_area: 最小面积
            max_area: 最大面积

        Returns:
            过滤后的检测结果
        """
        filtered = []

        for det in detections:
            x1, y1, x2, y2 = det['bbox']
            area = (x2 - x1) * (y2 - y1)

            if min_area <= area <= max_area:
                filtered.append(det)

        return filtered

    def get_statistics(self) -> Dict:
        """
        获取后处理器统计信息

        Returns:
            统计信息字典
        """
        return {
            'config': {
                'confidence_threshold': self.config.confidence_threshold,
                'iou_threshold': self.config.iou_threshold,
                'max_detections': self.config.max_detections,
                'max_detections_per_class': self.config.max_detections_per_class,
                'class_agnostic': self.config.class_agnostic,
                'multi_label': self.config.multi_label,
                'merge_overlapping': self.config.merge_overlapping,
                'min_box_size': self.config.min_box_size,
                'score_threshold': self.config.score_threshold
            },
            'performance': self.stats.copy(),
            'capabilities': [
                'yolo_output_parsing',
                'multi_class_nms',
                'coordinate_transformation',
                'detection_merging',
                'area_filtering',
                'performance_monitoring'
            ]
        }

    def reset_statistics(self):
        """重置统计信息"""
        self.stats = {
            'total_processed': 0,
            'total_time': 0.0,
            'avg_time': 0.0,
            'nms_time': 0.0,
            'parse_time': 0.0
        }
