#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级预处理模块
手动实现完整的图像预处理流程，替换PyTorch/Ultralytics的自动化处理
"""

import cv2
import numpy as np
from typing import Tuple, Dict, List, Optional, Union
import math
from dataclasses import dataclass

from utils.logger import get_logger


@dataclass
class PreprocessConfig:
    """预处理配置类"""
    input_size: Tuple[int, int] = (640, 640)  # 输入尺寸 (width, height)
    normalize: bool = True  # 是否归一化到[0,1]
    rgb_format: bool = True  # 是否转换为RGB格式
    mean: Tuple[float, float, float] = (0.0, 0.0, 0.0)  # 均值
    std: Tuple[float, float, float] = (1.0, 1.0, 1.0)  # 标准差
    fill_value: int = 114  # letterbox填充值
    auto_size: bool = False  # 是否自动调整尺寸
    stride: int = 32  # 步长约束
    scale_up: bool = True  # 是否允许放大
    center_pad: bool = True  # 是否居中填充


class AdvancedPreprocessor:
    """高级预处理器"""
    
    def __init__(self, config: PreprocessConfig = None):
        """
        初始化预处理器
        
        Args:
            config: 预处理配置
        """
        self.config = config or PreprocessConfig()
        self.logger = get_logger("AdvancedPreprocessor")
        
        # 预计算的常量
        self._mean_array = np.array(self.config.mean, dtype=np.float32).reshape(1, 1, 3)
        self._std_array = np.array(self.config.std, dtype=np.float32).reshape(1, 1, 3)
        
        self.logger.info(f"预处理器初始化完成，输入尺寸: {self.config.input_size}")
    
    def letterbox_resize(self, 
                        image: np.ndarray, 
                        target_size: Optional[Tuple[int, int]] = None,
                        auto: Optional[bool] = None,
                        stride: Optional[int] = None) -> Tuple[np.ndarray, Dict]:
        """
        高级letterbox缩放，支持多种模式
        
        Args:
            image: 输入图像 (H, W, C)
            target_size: 目标尺寸 (width, height)
            auto: 是否自动调整尺寸
            stride: 步长约束
            
        Returns:
            缩放后的图像和变换信息
        """
        if target_size is None:
            target_size = self.config.input_size
        if auto is None:
            auto = self.config.auto_size
        if stride is None:
            stride = self.config.stride
            
        h, w = image.shape[:2]
        target_w, target_h = target_size
        
        # 计算缩放比例
        scale = min(target_w / w, target_h / h)
        
        # 是否允许放大
        if not self.config.scale_up and scale > 1.0:
            scale = 1.0
        
        # 计算新的尺寸
        new_w = int(w * scale)
        new_h = int(h * scale)
        
        # 自动调整模式：确保尺寸是stride的倍数
        if auto:
            new_w = math.ceil(new_w / stride) * stride
            new_h = math.ceil(new_h / stride) * stride
            target_w = new_w
            target_h = new_h
        
        # 缩放图像
        if scale != 1.0:
            resized = cv2.resize(image, (new_w, new_h), interpolation=cv2.INTER_LINEAR)
        else:
            resized = image.copy()
        
        # 创建目标尺寸的画布
        if len(image.shape) == 3:
            canvas = np.full((target_h, target_w, image.shape[2]), 
                           self.config.fill_value, dtype=image.dtype)
        else:
            canvas = np.full((target_h, target_w), 
                           self.config.fill_value, dtype=image.dtype)
        
        # 计算粘贴位置
        if self.config.center_pad:
            # 居中填充
            start_x = (target_w - new_w) // 2
            start_y = (target_h - new_h) // 2
        else:
            # 左上角填充
            start_x = 0
            start_y = 0
        
        # 粘贴缩放后的图像
        if len(image.shape) == 3:
            canvas[start_y:start_y + new_h, start_x:start_x + new_w] = resized
        else:
            canvas[start_y:start_y + new_h, start_x:start_x + new_w] = resized
        
        # 变换信息
        transform_info = {
            'scale': scale,
            'pad_x': start_x,
            'pad_y': start_y,
            'new_w': new_w,
            'new_h': new_h,
            'original_size': (w, h),
            'target_size': (target_w, target_h),
            'auto_adjusted': auto
        }
        
        return canvas, transform_info
    
    def normalize_image(self, image: np.ndarray, 
                       mean: Optional[Tuple[float, float, float]] = None,
                       std: Optional[Tuple[float, float, float]] = None) -> np.ndarray:
        """
        高级图像归一化
        
        Args:
            image: 输入图像，值范围[0, 255]
            mean: 均值
            std: 标准差
            
        Returns:
            归一化后的图像
        """
        # 转换为float32
        normalized = image.astype(np.float32)
        
        # 基础归一化到[0,1]
        if self.config.normalize:
            normalized /= 255.0
        
        # 使用均值和标准差进行标准化
        if mean is not None or std is not None:
            mean_vals = np.array(mean or self.config.mean, dtype=np.float32).reshape(1, 1, 3)
            std_vals = np.array(std or self.config.std, dtype=np.float32).reshape(1, 1, 3)
            
            normalized = (normalized - mean_vals) / std_vals
        
        return normalized
    
    def convert_color_format(self, image: np.ndarray, 
                           source_format: str = 'BGR',
                           target_format: str = 'RGB') -> np.ndarray:
        """
        颜色格式转换
        
        Args:
            image: 输入图像
            source_format: 源格式 ('BGR', 'RGB', 'GRAY')
            target_format: 目标格式 ('BGR', 'RGB', 'GRAY')
            
        Returns:
            转换后的图像
        """
        if source_format == target_format:
            return image.copy()
        
        conversion_map = {
            ('BGR', 'RGB'): cv2.COLOR_BGR2RGB,
            ('RGB', 'BGR'): cv2.COLOR_RGB2BGR,
            ('BGR', 'GRAY'): cv2.COLOR_BGR2GRAY,
            ('RGB', 'GRAY'): cv2.COLOR_RGB2GRAY,
            ('GRAY', 'BGR'): cv2.COLOR_GRAY2BGR,
            ('GRAY', 'RGB'): cv2.COLOR_GRAY2RGB,
        }
        
        conversion_code = conversion_map.get((source_format, target_format))
        if conversion_code is None:
            raise ValueError(f"不支持的颜色格式转换: {source_format} -> {target_format}")
        
        return cv2.cvtColor(image, conversion_code)
    
    def transpose_dimensions(self, image: np.ndarray, 
                           source_order: str = 'HWC',
                           target_order: str = 'CHW') -> np.ndarray:
        """
        调整维度顺序
        
        Args:
            image: 输入图像
            source_order: 源维度顺序 ('HWC', 'CHW', 'WHC')
            target_order: 目标维度顺序 ('HWC', 'CHW', 'WHC')
            
        Returns:
            调整后的图像
        """
        if source_order == target_order:
            return image.copy()
        
        # 定义维度映射
        order_map = {
            'HWC': (0, 1, 2),
            'CHW': (2, 0, 1),
            'WHC': (1, 0, 2),
            'CWH': (2, 1, 0),
            'WCH': (1, 2, 0),
            'HCW': (0, 2, 1)
        }
        
        if source_order not in order_map or target_order not in order_map:
            raise ValueError(f"不支持的维度顺序: {source_order} -> {target_order}")
        
        # 计算转换轴
        source_axes = order_map[source_order]
        target_axes = order_map[target_order]
        
        # 创建逆映射
        inverse_source = [0] * 3
        for i, axis in enumerate(source_axes):
            inverse_source[axis] = i
        
        # 计算最终的转换轴
        transpose_axes = [inverse_source[target_axes[i]] for i in range(3)]
        
        return np.transpose(image, transpose_axes)
    
    def add_batch_dimension(self, image: np.ndarray, axis: int = 0) -> np.ndarray:
        """
        添加batch维度
        
        Args:
            image: 输入图像
            axis: 添加维度的位置
            
        Returns:
            添加batch维度后的图像
        """
        return np.expand_dims(image, axis=axis)
    
    def ensure_contiguous(self, image: np.ndarray) -> np.ndarray:
        """
        确保数组是连续的
        
        Args:
            image: 输入图像
            
        Returns:
            连续的数组
        """
        if not image.flags['C_CONTIGUOUS']:
            return np.ascontiguousarray(image)
        return image
    
    def preprocess_single(self, image: np.ndarray, 
                         config_override: Optional[PreprocessConfig] = None) -> Tuple[np.ndarray, Dict]:
        """
        单张图像的完整预处理流程
        
        Args:
            image: 输入图像 (BGR格式)
            config_override: 配置覆盖
            
        Returns:
            预处理后的数据和变换信息
        """
        config = config_override or self.config
        
        # 1. Letterbox缩放
        resized_image, transform_info = self.letterbox_resize(
            image, 
            target_size=config.input_size,
            auto=config.auto_size,
            stride=config.stride
        )
        
        # 2. 颜色格式转换
        if config.rgb_format:
            processed_image = self.convert_color_format(resized_image, 'BGR', 'RGB')
        else:
            processed_image = resized_image.copy()
        
        # 3. 归一化
        processed_image = self.normalize_image(
            processed_image, 
            mean=config.mean, 
            std=config.std
        )
        
        # 4. 调整维度顺序 (H,W,C) -> (C,H,W)
        processed_image = self.transpose_dimensions(processed_image, 'HWC', 'CHW')
        
        # 5. 确保数组连续
        processed_image = self.ensure_contiguous(processed_image)
        
        # 6. 添加batch维度 (C,H,W) -> (1,C,H,W)
        processed_image = self.add_batch_dimension(processed_image, axis=0)
        
        return processed_image, transform_info

    def preprocess_batch(self, images: List[np.ndarray],
                        config_override: Optional[PreprocessConfig] = None) -> Tuple[np.ndarray, List[Dict]]:
        """
        批量图像预处理

        Args:
            images: 输入图像列表
            config_override: 配置覆盖

        Returns:
            批处理后的数据和变换信息列表
        """
        if not images:
            raise ValueError("输入图像列表不能为空")

        config = config_override or self.config
        processed_images = []
        transform_infos = []

        # 检查是否所有图像尺寸相同
        shapes = [img.shape for img in images]
        same_shapes = len(set(shapes)) == 1

        # 如果尺寸相同且启用auto模式，可以优化处理
        if same_shapes and config.auto_size:
            # 使用第一张图像计算目标尺寸
            first_processed, first_transform = self.preprocess_single(images[0], config)
            processed_images.append(first_processed[0])  # 移除batch维度
            transform_infos.append(first_transform)

            # 使用相同的变换处理其余图像
            target_size = first_transform['target_size']
            for img in images[1:]:
                processed, transform = self.preprocess_single(img, config)
                processed_images.append(processed[0])  # 移除batch维度
                transform_infos.append(transform)
        else:
            # 逐个处理
            for img in images:
                processed, transform = self.preprocess_single(img, config)
                processed_images.append(processed[0])  # 移除batch维度
                transform_infos.append(transform)

        # 堆叠为批处理数组
        batch_array = np.stack(processed_images, axis=0)

        return batch_array, transform_infos

    def preprocess_with_augmentation(self, image: np.ndarray,
                                   augment_config: Optional[Dict] = None) -> Tuple[np.ndarray, Dict]:
        """
        带数据增强的预处理

        Args:
            image: 输入图像
            augment_config: 增强配置

        Returns:
            预处理后的数据和变换信息
        """
        augment_config = augment_config or {}

        # 应用数据增强
        augmented_image = image.copy()
        augment_info = {}

        # 随机翻转
        if augment_config.get('flip_horizontal', False) and np.random.random() > 0.5:
            augmented_image = cv2.flip(augmented_image, 1)
            augment_info['flipped_horizontal'] = True

        # 随机旋转
        rotation_angle = augment_config.get('rotation_angle', 0)
        if rotation_angle > 0:
            angle = np.random.uniform(-rotation_angle, rotation_angle)
            h, w = augmented_image.shape[:2]
            center = (w // 2, h // 2)
            rotation_matrix = cv2.getRotationMatrix2D(center, angle, 1.0)
            augmented_image = cv2.warpAffine(augmented_image, rotation_matrix, (w, h))
            augment_info['rotation_angle'] = angle

        # 亮度调整
        brightness_factor = augment_config.get('brightness_factor', 0)
        if brightness_factor > 0:
            factor = np.random.uniform(1 - brightness_factor, 1 + brightness_factor)
            augmented_image = cv2.convertScaleAbs(augmented_image, alpha=factor, beta=0)
            augment_info['brightness_factor'] = factor

        # 执行标准预处理
        processed_image, transform_info = self.preprocess_single(augmented_image)

        # 合并变换信息
        transform_info['augmentation'] = augment_info

        return processed_image, transform_info

    def get_inverse_transform(self, transform_info: Dict) -> Dict:
        """
        获取逆变换信息，用于将预测结果映射回原图

        Args:
            transform_info: 变换信息

        Returns:
            逆变换信息
        """
        return {
            'scale': 1.0 / transform_info['scale'],
            'pad_x': -transform_info['pad_x'],
            'pad_y': -transform_info['pad_y'],
            'original_size': transform_info['target_size'],
            'target_size': transform_info['original_size']
        }

    def apply_inverse_transform(self, coordinates: np.ndarray,
                              transform_info: Dict) -> np.ndarray:
        """
        将坐标从预处理后的图像映射回原图

        Args:
            coordinates: 坐标数组 (N, 4) [x1, y1, x2, y2]
            transform_info: 变换信息

        Returns:
            映射后的坐标
        """
        coords = coordinates.copy().astype(np.float32)

        # 移除padding
        coords[:, [0, 2]] -= transform_info['pad_x']  # x坐标
        coords[:, [1, 3]] -= transform_info['pad_y']  # y坐标

        # 逆缩放
        coords = coords / transform_info['scale']

        # 确保坐标在原图范围内
        orig_w, orig_h = transform_info['original_size']
        coords[:, [0, 2]] = np.clip(coords[:, [0, 2]], 0, orig_w)
        coords[:, [1, 3]] = np.clip(coords[:, [1, 3]], 0, orig_h)

        return coords

    def get_statistics(self) -> Dict:
        """
        获取预处理器统计信息

        Returns:
            统计信息字典
        """
        return {
            'config': {
                'input_size': self.config.input_size,
                'normalize': self.config.normalize,
                'rgb_format': self.config.rgb_format,
                'mean': self.config.mean,
                'std': self.config.std,
                'fill_value': self.config.fill_value,
                'auto_size': self.config.auto_size,
                'stride': self.config.stride,
                'scale_up': self.config.scale_up,
                'center_pad': self.config.center_pad
            },
            'capabilities': [
                'letterbox_resize',
                'normalization',
                'color_conversion',
                'dimension_transpose',
                'batch_processing',
                'data_augmentation',
                'inverse_transform'
            ]
        }
