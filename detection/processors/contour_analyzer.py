#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
轮廓分析器模块
处理轮廓检测、分析和可视化相关功能
"""

import cv2
import numpy as np


class ContourAnalyzer:
    """轮廓分析器 - 处理轮廓相关功能"""
    
    def __init__(self, min_contour_area=15):
        self.min_contour_area = min_contour_area
    
    def filter_contours(self, contours):
        """过滤轮廓，返回有效轮廓和被过滤的轮廓"""
        valid_contours = []
        filtered_contours = []
        
        for i, contour in enumerate(contours):
            area = cv2.contourArea(contour)
            if area >= self.min_contour_area:
                valid_contours.append(contour)
                print(f"  ✅ 轮廓 {i}: 面积 {area:.1f} >= {self.min_contour_area} (有效)")
            else:
                filtered_contours.append(contour)
                print(f"  ❌ 轮廓 {i}: 面积 {area:.1f} < {self.min_contour_area} (过滤)")
        
        print(f"🔍 轮廓统计: 总数 {len(contours)}, 有效 {len(valid_contours)}, 过滤 {len(filtered_contours)}")
        
        return valid_contours, filtered_contours
    
    def generate_bboxes_by_strategy(self, valid_contours, strategy="merge_all"):
        """根据策略生成边界框"""
        final_bboxes = []
        
        if not valid_contours:
            print("⚠️ 未找到有效的热源轮廓")
            return final_bboxes
        
        if strategy == "separate_all":
            # Option A: 每个轮廓单独处理（推荐策略）
            for i, contour in enumerate(valid_contours):
                x, y, w, h = cv2.boundingRect(contour)
                final_bboxes.append((x, y, w, h))
            print(f"📊 [分离策略] 生成 {len(valid_contours)} 个独立热源")

        elif strategy == "merge_all":
            # Option B: 合并所有轮廓（适用于人体热源检测）
            all_points_from_valid_contours = np.concatenate([c for c in valid_contours])
            x, y, w, h = cv2.boundingRect(all_points_from_valid_contours)
            final_bboxes.append((x, y, w, h))
            print(f"🔥 [合并策略] 合并 {len(valid_contours)} 个轮廓为一个热源: {(x,y,w,h)}")

        elif strategy == "largest_only":
            # Option C: 选择最大轮廓
            largest_contour = max(valid_contours, key=cv2.contourArea)
            x, y, w, h = cv2.boundingRect(largest_contour)
            final_bboxes.append((x, y, w, h))
            print(f"🎯 [最大策略] 选择最大轮廓: {(x,y,w,h)}")

        else:
            # 默认使用分离策略
            for i, contour in enumerate(valid_contours):
                x, y, w, h = cv2.boundingRect(contour)
                final_bboxes.append((x, y, w, h))
            print(f"📊 [默认策略] 生成 {len(valid_contours)} 个独立热源")
        
        return final_bboxes
    
    def analyze_contours(self, contours, frame_shape=None):
        """分析轮廓详细信息，帮助诊断问题"""
        if not contours:
            print("📊 轮廓分析: 没有找到轮廓")
            return

        print(f"📊 轮廓详细分析 (总数: {len(contours)}):")
        print("-" * 60)

        for i, contour in enumerate(contours):
            area = cv2.contourArea(contour)
            perimeter = cv2.arcLength(contour, True)

            # 计算边界框
            x, y, w, h = cv2.boundingRect(contour)

            # 计算轮廓的一些特征
            aspect_ratio = float(w) / h if h > 0 else 0
            extent = float(area) / (w * h) if w > 0 and h > 0 else 0

            # 判断是否有效
            is_valid = area >= self.min_contour_area
            status = "✅ 有效" if is_valid else "❌ 过滤"

            print(f"轮廓 {i+1:2d}: {status}")
            print(f"  📐 面积: {area:6.1f} (阈值: {self.min_contour_area})")
            print(f"  📏 周长: {perimeter:6.1f}")
            print(f"  📦 边界框: ({x}, {y}, {w}, {h})")
            print(f"  📊 长宽比: {aspect_ratio:.2f}")
            print(f"  🎯 填充度: {extent:.2f}")

            # 检查是否在图像边界
            if frame_shape:
                h_img, w_img = frame_shape[:2]
                on_edge = (x <= 2 or y <= 2 or x+w >= w_img-2 or y+h >= h_img-2)
                if on_edge:
                    print(f"  ⚠️  位于图像边缘")

            print()
    
    def create_enhanced_contour_visualization(self, thermal_frame_bgr, all_contours, valid_contours):
        """创建增强的轮廓可视化"""
        try:
            enhanced_vis = {}
            
            # 1. 所有轮廓可视化（红色）
            all_contours_img = thermal_frame_bgr.copy()
            if all_contours:
                cv2.drawContours(all_contours_img, all_contours, -1, (0, 0, 255), 1)
                # 添加轮廓编号
                for i, contour in enumerate(all_contours):
                    M = cv2.moments(contour)
                    if M["m00"] != 0:
                        cx = int(M["m10"] / M["m00"])
                        cy = int(M["m01"] / M["m00"])
                        cv2.putText(all_contours_img, str(i), (cx, cy), 
                                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            enhanced_vis["All_Contours_Numbered"] = all_contours_img
            
            # 2. 有效轮廓可视化（绿色）
            valid_contours_img = thermal_frame_bgr.copy()
            if valid_contours:
                cv2.drawContours(valid_contours_img, valid_contours, -1, (0, 255, 0), 2)
                # 添加面积信息
                for i, contour in enumerate(valid_contours):
                    area = cv2.contourArea(contour)
                    M = cv2.moments(contour)
                    if M["m00"] != 0:
                        cx = int(M["m10"] / M["m00"])
                        cy = int(M["m01"] / M["m00"])
                        cv2.putText(valid_contours_img, f"A:{area:.0f}", (cx, cy), 
                                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
            enhanced_vis["Valid_Contours_With_Area"] = valid_contours_img
            
            # 3. 轮廓对比图
            comparison_img = thermal_frame_bgr.copy()
            if all_contours:
                cv2.drawContours(comparison_img, all_contours, -1, (0, 0, 255), 1)  # 红色：所有轮廓
            if valid_contours:
                cv2.drawContours(comparison_img, valid_contours, -1, (0, 255, 0), 2)  # 绿色：有效轮廓
            enhanced_vis["Contours_Comparison"] = comparison_img
            
            # 4. 边界框可视化
            bbox_img = thermal_frame_bgr.copy()
            for i, contour in enumerate(valid_contours):
                x, y, w, h = cv2.boundingRect(contour)
                cv2.rectangle(bbox_img, (x, y), (x + w, y + h), (255, 0, 0), 2)
                cv2.putText(bbox_img, f"Box{i}", (x, y-5), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 0, 0), 1)
            enhanced_vis["Bounding_Boxes"] = bbox_img
            
            return enhanced_vis
            
        except Exception as e:
            print(f"❌ 增强轮廓可视化创建失败: {e}")
            return {}
    
    def set_min_contour_area(self, min_area):
        """设置最小轮廓面积"""
        old_area = self.min_contour_area
        self.min_contour_area = max(1, min_area)
        print(f"🔧 最小轮廓面积: {old_area} → {self.min_contour_area}")
    
    def get_contour_statistics(self, contours):
        """获取轮廓统计信息"""
        if not contours:
            return {
                'total_count': 0,
                'valid_count': 0,
                'filtered_count': 0,
                'total_area': 0,
                'avg_area': 0,
                'max_area': 0,
                'min_area': 0
            }
        
        areas = [cv2.contourArea(contour) for contour in contours]
        valid_areas = [area for area in areas if area >= self.min_contour_area]
        
        return {
            'total_count': len(contours),
            'valid_count': len(valid_areas),
            'filtered_count': len(contours) - len(valid_areas),
            'total_area': sum(areas),
            'avg_area': sum(areas) / len(areas) if areas else 0,
            'max_area': max(areas) if areas else 0,
            'min_area': min(areas) if areas else 0,
            'valid_total_area': sum(valid_areas),
            'valid_avg_area': sum(valid_areas) / len(valid_areas) if valid_areas else 0
        }
