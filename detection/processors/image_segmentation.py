#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
热成像图像分割模块
"""

import cv2
import numpy as np


class ThermalPreprocessor:
    """热成像预处理器"""

    def to_grayscale(self, thermal_frame_bgr):
        """
        将BGR热成像帧转换为灰度图
        """
        if thermal_frame_bgr is None:
            return None
        if len(thermal_frame_bgr.shape) == 3 and thermal_frame_bgr.shape[2] == 3:
            return cv2.cvtColor(thermal_frame_bgr, cv2.COLOR_BGR2GRAY)
        elif len(thermal_frame_bgr.shape) == 2:  # Already grayscale
            return thermal_frame_bgr
        else:
            print(f"Warning: Unexpected frame format for grayscale conversion: {thermal_frame_bgr.shape}")
            return None




class ThermalSegmenter:
    """热成像分割器 - 改进版本，支持多种自适应阈值方法"""

    def __init__(self, threshold_value, enable_morphology=True, morph_kernel_size=5):
        self.threshold_value = threshold_value
        self.enable_morphology = enable_morphology
        self.morph_kernel_size = morph_kernel_size
        if self.enable_morphology:
            self.kernel = np.ones((morph_kernel_size, morph_kernel_size), np.uint8)

        # 局部自适应参数
        self.use_local_adaptive = False
        self.adaptive_block_size = 11  # 必须是奇数
        self.adaptive_c = 2  # 常数C

        # 形态学策略参数
        self.morphology_strategy = "minimal"  # "minimal", "conservative", "aggressive", "custom"
        self.prevent_splitting = True  # 防止热源分裂
        self.min_component_area = 30  # 最小连通分量面积

    def segment_hot_regions(self, thermal_frame_gray, display_steps=False, temp_matrix=None):
        """
        分割热区域 - 改进版本，支持多种阈值方法
        Args:
            thermal_frame_gray: 灰度热成像图像
            display_steps: 是否显示中间步骤
            temp_matrix: 温度矩阵（可选，用于温差检测）
        Returns:
            binary_mask: 最终的二值掩码
            visualization_steps (dict): 中间步骤的可视化图像
        """
        vis_images = {}

        if thermal_frame_gray is None:
            print("Error: Grayscale frame is None in segment_hot_regions.")
            return None, vis_images

        # 选择阈值方法
        if self.use_local_adaptive:
            binary_mask_thresh = self._local_adaptive_threshold(thermal_frame_gray, display_steps, vis_images)
        else:
            # 传统固定阈值
            binary_mask_thresh = self._fixed_threshold(thermal_frame_gray, display_steps, vis_images)

        current_mask = binary_mask_thresh

        # 2. 智能形态学操作
        if self.enable_morphology:
            current_mask = self._apply_morphology_strategy(current_mask, display_steps, vis_images)

        # 3. 后处理：防止热源分裂
        if self.prevent_splitting:
            current_mask = self._post_process_to_prevent_splitting(current_mask, display_steps, vis_images)

        # 最终处理后的掩码
        final_binary_mask = current_mask

        return final_binary_mask, vis_images

    def _fixed_threshold(self, thermal_frame_gray, display_steps, vis_images):
        """传统固定阈值方法"""
        clamped_threshold = max(0, min(255, self.threshold_value))
        _, binary_mask = cv2.threshold(thermal_frame_gray,
                                      clamped_threshold,
                                      255,
                                      cv2.THRESH_BINARY)
        if display_steps:
            vis_images["02 - Fixed Threshold"] = binary_mask.copy()
        return binary_mask



    def _local_adaptive_threshold(self, thermal_frame_gray, display_steps, vis_images):
        """局部自适应阈值方法"""
        # 确保block_size是奇数
        block_size = self.adaptive_block_size if self.adaptive_block_size % 2 == 1 else self.adaptive_block_size + 1

        # 使用高斯加权的局部自适应阈值
        binary_mask = cv2.adaptiveThreshold(
            thermal_frame_gray,
            255,
            cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
            cv2.THRESH_BINARY,
            block_size,
            self.adaptive_c
        )

        if display_steps:
            vis_images["02 - Local Adaptive Threshold"] = binary_mask.copy()

        return binary_mask





    def set_local_adaptive_params(self, use_local=False, block_size=11, c=2):
        """设置局部自适应参数"""
        self.use_local_adaptive = use_local
        self.adaptive_block_size = block_size if block_size % 2 == 1 else block_size + 1
        self.adaptive_c = c
        print(f"🔧 局部自适应: {'启用' if use_local else '禁用'}, 块大小: {self.adaptive_block_size}, C: {c}")

    def get_threshold_method_info(self):
        """获取当前阈值方法信息"""
        if self.use_local_adaptive:
            return f"局部自适应 (块: {self.adaptive_block_size}, C: {self.adaptive_c})"
        else:
            return f"固定阈值 ({self.threshold_value})"
        
    def _minimal_morphology(self, binary_mask, display_steps, vis_images):
        """最小形态学策略 - 不腐蚀，只做必要的填充"""
        current_mask = binary_mask

        # 只做闭运算来填充小孔，完全不做开运算（开运算包含腐蚀）
        kernel = np.ones((3, 3), np.uint8)
        mask_closed = cv2.morphologyEx(current_mask, cv2.MORPH_CLOSE, kernel, iterations=1)

        if display_steps:
            vis_images["03a - Close Only (No Erosion)"] = mask_closed.copy()
            # 显示差异
            diff = cv2.absdiff(current_mask, mask_closed)
            vis_images["03b - Fill Difference"] = diff

        print("🔧 使用无腐蚀策略 - 只填充小孔，完全不腐蚀")
        return mask_closed

    def _conservative_morphology(self, binary_mask, display_steps, vis_images):
        """保守形态学策略 - 减少腐蚀，主要填充"""
        current_mask = binary_mask

        # 1. 闭运算 - 填充小孔和连接近距离区域
        kernel = np.ones((3, 3), np.uint8)
        mask_closed = cv2.morphologyEx(current_mask, cv2.MORPH_CLOSE, kernel, iterations=1)
        if display_steps:
            vis_images["03a - Close Fill"] = mask_closed.copy()
        current_mask = mask_closed

        # 2. 不做开运算，避免腐蚀热源
        # 如果需要去噪，使用连通分量分析代替
        if display_steps:
            vis_images["03b - No Erosion Applied"] = current_mask.copy()

        print("🔧 保守策略 - 只填充，不腐蚀")
        return current_mask

  
    def _post_process_to_prevent_splitting(self, binary_mask, display_steps, vis_images):
        """后处理：防止热源分裂"""
        # 1. 分析连通分量
        num_labels, labels, stats, _ = cv2.connectedComponentsWithStats(binary_mask)

        if num_labels <= 2:  # 只有背景和一个前景，无需处理
            return binary_mask

        # 2. 找到可能需要合并的小分量
        small_components = []
        large_components = []

        for label in range(1, num_labels):
            area = stats[label, cv2.CC_STAT_AREA]
            if area < self.min_component_area:
                small_components.append(label)
            else:
                large_components.append(label)

        if not small_components:  # 没有小分量，无需处理
            return binary_mask

        if display_steps:
            vis_images["04a - Before Merge"] = binary_mask.copy()

        # 3. 尝试将小分量合并到最近的大分量
        result_mask = binary_mask.copy()

        for small_label in small_components:
            small_mask = (labels == small_label).astype(np.uint8) * 255

            # 找到最近的大分量
            closest_large_label = self._find_closest_component(
                small_label, large_components, labels, stats
            )

            if closest_large_label is not None:
                # 尝试连接小分量到大分量
                merged_mask = self._connect_components(
                    small_mask,
                    (labels == closest_large_label).astype(np.uint8) * 255
                )

                # 更新结果
                result_mask = cv2.bitwise_or(result_mask, merged_mask)

        if display_steps:
            vis_images["04b - After Merge"] = result_mask.copy()

        return result_mask

    def _find_closest_component(self, small_label, large_components, labels, stats):
        """找到最近的大分量"""
        if not large_components:
            return None

        small_centroid = self._get_component_centroid(small_label, labels, stats)
        min_distance = float('inf')
        closest_label = None

        for large_label in large_components:
            large_centroid = self._get_component_centroid(large_label, labels, stats)
            distance = np.sqrt(
                (small_centroid[0] - large_centroid[0])**2 +
                (small_centroid[1] - large_centroid[1])**2
            )

            if distance < min_distance:
                min_distance = distance
                closest_label = large_label

        # 只有距离足够近才合并（避免错误合并）
        if min_distance < 50:  # 可调节的距离阈值
            return closest_label

        return None

    def _get_component_centroid(self, label, labels, stats):
        """获取连通分量的质心"""
        x = stats[label, cv2.CC_STAT_LEFT] + stats[label, cv2.CC_STAT_WIDTH] // 2
        y = stats[label, cv2.CC_STAT_TOP] + stats[label, cv2.CC_STAT_HEIGHT] // 2
        return (x, y)

    def _connect_components(self, small_mask, large_mask):
        """连接两个分量"""
        # 使用形态学膨胀来连接分量
        kernel = np.ones((5, 5), np.uint8)

        # 膨胀小分量
        dilated_small = cv2.dilate(small_mask, kernel, iterations=2)

        # 找到与大分量的交集
        intersection = cv2.bitwise_and(dilated_small, large_mask)

        if np.sum(intersection) > 0:  # 有交集，可以连接
            # 创建连接路径
            combined = cv2.bitwise_or(small_mask, large_mask)
            combined = cv2.bitwise_or(combined, intersection)

            # 轻微的闭运算来平滑连接
            kernel_smooth = np.ones((3, 3), np.uint8)
            combined = cv2.morphologyEx(combined, cv2.MORPH_CLOSE, kernel_smooth, iterations=1)

            return combined

        return small_mask  # 无法连接，返回原始小分量

    def _visualize_components(self, labels, num_labels):
        """可视化连通分量"""
        # 为每个分量分配不同的灰度值
        vis_image = np.zeros_like(labels, dtype=np.uint8)

        for label in range(1, num_labels):
            gray_value = min(255, (label * 50) % 255 + 50)
            vis_image[labels == label] = gray_value

        return vis_image

    def set_morphology_strategy(self, strategy="minimal", prevent_splitting=True, min_component_area=30):
        """设置形态学策略
        Args:
            strategy: "minimal", "conservative", "aggressive", "custom"
            prevent_splitting: 是否防止热源分裂
            min_component_area: 最小连通分量面积
        """
        valid_strategies = ["minimal", "conservative", "aggressive", "custom"]
        if strategy in valid_strategies:
            self.morphology_strategy = strategy
            self.prevent_splitting = prevent_splitting
            self.min_component_area = min_component_area
            print(f"🔧 形态学策略: {strategy}, 防分裂: {'启用' if prevent_splitting else '禁用'}, 最小面积: {min_component_area}")
        else:
            print(f"❌ 无效的形态学策略: {strategy}")
            print(f"   可用策略: {', '.join(valid_strategies)}")

    def get_morphology_info(self):
        """获取形态学配置信息"""
        return f"策略: {self.morphology_strategy}, 防分裂: {'是' if self.prevent_splitting else '否'}, 最小面积: {self.min_component_area}"

    def _apply_morphology_strategy(self, binary_mask, display_steps, vis_images):
        """根据策略应用形态学操作"""
        if self.morphology_strategy == "minimal":
            return self._minimal_morphology(binary_mask, display_steps, vis_images)
        elif self.morphology_strategy == "conservative":
            return self._conservative_morphology(binary_mask, display_steps, vis_images)
        elif self.morphology_strategy == "aggressive":
            return self._aggressive_morphology(binary_mask, display_steps, vis_images)
        else:
            # 默认使用最小策略
            return self._minimal_morphology(binary_mask, display_steps, vis_images)

    def _aggressive_morphology(self, binary_mask, display_steps, vis_images):
        """激进形态学策略 - 包含开运算和闭运算"""
        current_mask = binary_mask

        # 1. 开运算 - 去除噪声
        kernel = np.ones((self.morph_kernel_size, self.morph_kernel_size), np.uint8)
        mask_opened = cv2.morphologyEx(current_mask, cv2.MORPH_OPEN, kernel, iterations=1)
        if display_steps:
            vis_images["03a - Open Operation"] = mask_opened.copy()
        current_mask = mask_opened

        # 2. 闭运算 - 填充小孔
        mask_closed = cv2.morphologyEx(current_mask, cv2.MORPH_CLOSE, kernel, iterations=1)
        if display_steps:
            vis_images["03b - Close Operation"] = mask_closed.copy()

        print("🔧 激进策略 - 开运算 + 闭运算")
        return mask_closed