#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
集成数据处理管道
整合预处理和后处理模块，提供完整的数据处理流程
"""

import cv2
import numpy as np
from typing import Tuple, Dict, List, Optional, Union
from dataclasses import dataclass
import time

from .advanced_preprocessor import AdvancedPreprocessor, PreprocessConfig
from .advanced_postprocessor import AdvancedPostprocessor, PostprocessConfig
from utils.logger import get_logger


@dataclass
class PipelineConfig:
    """数据处理管道配置"""
    # 预处理配置
    preprocess: PreprocessConfig = None
    # 后处理配置
    postprocess: PostprocessConfig = None
    # 性能配置
    enable_profiling: bool = False
    cache_transforms: bool = True
    batch_size: int = 1


class IntegratedDataPipeline:
    """集成数据处理管道"""
    
    def __init__(self, config: PipelineConfig = None):
        """
        初始化数据处理管道
        
        Args:
            config: 管道配置
        """
        self.config = config or PipelineConfig()
        
        # 初始化子模块
        if self.config.preprocess is None:
            self.config.preprocess = PreprocessConfig()
        if self.config.postprocess is None:
            self.config.postprocess = PostprocessConfig()
            
        self.preprocessor = AdvancedPreprocessor(self.config.preprocess)
        self.postprocessor = AdvancedPostprocessor(self.config.postprocess)
        
        self.logger = get_logger("IntegratedDataPipeline")
        
        # 性能统计
        self.stats = {
            'total_processed': 0,
            'preprocess_time': 0.0,
            'inference_time': 0.0,
            'postprocess_time': 0.0,
            'total_time': 0.0,
            'avg_fps': 0.0
        }
        
        # 变换缓存
        self.transform_cache = {} if self.config.cache_transforms else None
        
        self.logger.info("集成数据处理管道初始化完成")
    
    def process_single_image(self, 
                           image: np.ndarray,
                           inference_func: callable,
                           num_classes: int,
                           class_names: Optional[Dict[int, str]] = None) -> Tuple[List[Dict], Dict]:
        """
        处理单张图像的完整流程
        
        Args:
            image: 输入图像 (BGR格式)
            inference_func: 推理函数
            num_classes: 类别数量
            class_names: 类别名称映射
            
        Returns:
            检测结果和性能统计
        """
        start_time = time.time()
        
        # 1. 预处理
        preprocess_start = time.time()
        processed_image, transform_info = self.preprocessor.preprocess_single(image)
        preprocess_time = time.time() - preprocess_start
        
        # 2. 推理
        inference_start = time.time()
        predictions = inference_func(processed_image)
        inference_time = time.time() - inference_start
        
        # 3. 后处理
        postprocess_start = time.time()
        detections = self.postprocessor.postprocess_detections(
            predictions=predictions,
            input_shape=self.config.preprocess.input_size,
            original_shape=image.shape[1::-1],  # (width, height)
            transform_info=transform_info,
            num_classes=num_classes,
            class_names=class_names
        )
        postprocess_time = time.time() - postprocess_start
        
        # 统计信息
        total_time = time.time() - start_time
        
        if self.config.enable_profiling:
            self._update_stats(preprocess_time, inference_time, postprocess_time, total_time)
        
        performance_info = {
            'preprocess_time': preprocess_time,
            'inference_time': inference_time,
            'postprocess_time': postprocess_time,
            'total_time': total_time,
            'fps': 1.0 / total_time if total_time > 0 else 0,
            'transform_info': transform_info
        }
        
        return detections, performance_info
    
    def process_batch_images(self,
                           images: List[np.ndarray],
                           inference_func: callable,
                           num_classes: int,
                           class_names: Optional[Dict[int, str]] = None) -> Tuple[List[List[Dict]], Dict]:
        """
        批量处理图像
        
        Args:
            images: 输入图像列表
            inference_func: 推理函数
            num_classes: 类别数量
            class_names: 类别名称映射
            
        Returns:
            批量检测结果和性能统计
        """
        start_time = time.time()
        
        if not images:
            return [], {}
        
        # 1. 批量预处理
        preprocess_start = time.time()
        batch_data, transform_infos = self.preprocessor.preprocess_batch(images)
        preprocess_time = time.time() - preprocess_start
        
        # 2. 批量推理
        inference_start = time.time()
        batch_predictions = inference_func(batch_data)
        inference_time = time.time() - inference_start
        
        # 3. 批量后处理
        postprocess_start = time.time()
        all_detections = []
        
        for i, (predictions, transform_info, original_image) in enumerate(
            zip(batch_predictions, transform_infos, images)
        ):
            detections = self.postprocessor.postprocess_detections(
                predictions=predictions,
                input_shape=self.config.preprocess.input_size,
                original_shape=original_image.shape[1::-1],
                transform_info=transform_info,
                num_classes=num_classes,
                class_names=class_names
            )
            all_detections.append(detections)
        
        postprocess_time = time.time() - postprocess_start
        
        # 统计信息
        total_time = time.time() - start_time
        batch_size = len(images)
        
        if self.config.enable_profiling:
            self._update_stats(preprocess_time, inference_time, postprocess_time, total_time)
        
        performance_info = {
            'batch_size': batch_size,
            'preprocess_time': preprocess_time,
            'inference_time': inference_time,
            'postprocess_time': postprocess_time,
            'total_time': total_time,
            'avg_time_per_image': total_time / batch_size,
            'batch_fps': batch_size / total_time if total_time > 0 else 0,
            'transform_infos': transform_infos
        }
        
        return all_detections, performance_info
    
    def process_video_stream(self,
                           video_source: Union[str, int, cv2.VideoCapture],
                           inference_func: callable,
                           num_classes: int,
                           class_names: Optional[Dict[int, str]] = None,
                           output_path: Optional[str] = None,
                           display: bool = False) -> Dict:
        """
        处理视频流
        
        Args:
            video_source: 视频源（文件路径、摄像头ID或VideoCapture对象）
            inference_func: 推理函数
            num_classes: 类别数量
            class_names: 类别名称映射
            output_path: 输出视频路径
            display: 是否显示结果
            
        Returns:
            处理统计信息
        """
        # 打开视频源
        if isinstance(video_source, cv2.VideoCapture):
            cap = video_source
        else:
            cap = cv2.VideoCapture(video_source)
        
        if not cap.isOpened():
            raise ValueError(f"无法打开视频源: {video_source}")
        
        # 获取视频信息
        fps = cap.get(cv2.CAP_PROP_FPS)
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        # 初始化输出视频
        writer = None
        if output_path:
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            writer = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
        
        # 处理统计
        frame_count = 0
        total_detections = 0
        start_time = time.time()
        
        try:
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                # 处理当前帧
                detections, perf_info = self.process_single_image(
                    frame, inference_func, num_classes, class_names
                )
                
                total_detections += len(detections)
                frame_count += 1
                
                # 可视化检测结果
                if detections and (display or output_path):
                    annotated_frame = self._draw_detections(frame, detections)
                else:
                    annotated_frame = frame
                
                # 显示结果
                if display:
                    cv2.imshow('Detection Results', annotated_frame)
                    if cv2.waitKey(1) & 0xFF == ord('q'):
                        break
                
                # 保存到输出视频
                if writer:
                    writer.write(annotated_frame)
                
                # 打印进度
                if frame_count % 30 == 0:
                    elapsed = time.time() - start_time
                    current_fps = frame_count / elapsed
                    self.logger.info(f"处理进度: {frame_count}/{total_frames}, "
                                   f"FPS: {current_fps:.2f}, "
                                   f"检测数: {total_detections}")
        
        finally:
            cap.release()
            if writer:
                writer.release()
            if display:
                cv2.destroyAllWindows()
        
        # 返回统计信息
        total_time = time.time() - start_time
        return {
            'total_frames': frame_count,
            'total_detections': total_detections,
            'total_time': total_time,
            'avg_fps': frame_count / total_time if total_time > 0 else 0,
            'avg_detections_per_frame': total_detections / frame_count if frame_count > 0 else 0
        }

    def _draw_detections(self, image: np.ndarray, detections: List[Dict]) -> np.ndarray:
        """
        在图像上绘制检测结果

        Args:
            image: 输入图像
            detections: 检测结果列表

        Returns:
            标注后的图像
        """
        annotated = image.copy()

        for det in detections:
            x1, y1, x2, y2 = map(int, det['bbox'])
            class_name = det['class_name']
            confidence = det['confidence']

            # 绘制边界框
            cv2.rectangle(annotated, (x1, y1), (x2, y2), (0, 255, 0), 2)

            # 绘制标签
            label = f"{class_name}: {confidence:.2f}"
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 2)[0]
            cv2.rectangle(annotated, (x1, y1 - label_size[1] - 10),
                         (x1 + label_size[0], y1), (0, 255, 0), -1)
            cv2.putText(annotated, label, (x1, y1 - 5),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 2)

        return annotated

    def _update_stats(self, preprocess_time: float, inference_time: float,
                     postprocess_time: float, total_time: float):
        """
        更新性能统计

        Args:
            preprocess_time: 预处理时间
            inference_time: 推理时间
            postprocess_time: 后处理时间
            total_time: 总时间
        """
        self.stats['total_processed'] += 1
        self.stats['preprocess_time'] += preprocess_time
        self.stats['inference_time'] += inference_time
        self.stats['postprocess_time'] += postprocess_time
        self.stats['total_time'] += total_time

        # 计算平均FPS
        if self.stats['total_time'] > 0:
            self.stats['avg_fps'] = self.stats['total_processed'] / self.stats['total_time']

    def optimize_for_inference(self, target_fps: float = 30.0) -> Dict:
        """
        根据目标FPS优化管道配置

        Args:
            target_fps: 目标FPS

        Returns:
            优化建议
        """
        current_fps = self.stats.get('avg_fps', 0)

        suggestions = {
            'current_fps': current_fps,
            'target_fps': target_fps,
            'optimizations': []
        }

        if current_fps < target_fps:
            # 性能不足，提供优化建议
            if self.stats.get('preprocess_time', 0) > self.stats.get('inference_time', 0):
                suggestions['optimizations'].append({
                    'type': 'preprocess',
                    'suggestion': '减小输入尺寸或禁用某些预处理步骤',
                    'config_changes': {
                        'input_size': (416, 416),  # 从640减小到416
                        'normalize': False  # 如果模型支持，可以禁用归一化
                    }
                })

            if self.stats.get('postprocess_time', 0) > 0.01:  # 10ms
                suggestions['optimizations'].append({
                    'type': 'postprocess',
                    'suggestion': '提高置信度阈值或减少最大检测数量',
                    'config_changes': {
                        'confidence_threshold': 0.7,  # 提高阈值
                        'max_detections': 100  # 减少最大检测数
                    }
                })

            suggestions['optimizations'].append({
                'type': 'batch',
                'suggestion': '使用批处理提高吞吐量',
                'config_changes': {
                    'batch_size': min(4, max(1, int(target_fps / current_fps)))
                }
            })

        return suggestions

    def get_pipeline_info(self) -> Dict:
        """
        获取管道信息

        Returns:
            管道信息字典
        """
        return {
            'config': {
                'preprocess': self.preprocessor.get_statistics(),
                'postprocess': self.postprocessor.get_statistics(),
                'pipeline': {
                    'enable_profiling': self.config.enable_profiling,
                    'cache_transforms': self.config.cache_transforms,
                    'batch_size': self.config.batch_size
                }
            },
            'performance': self.stats.copy(),
            'capabilities': [
                'single_image_processing',
                'batch_processing',
                'video_stream_processing',
                'performance_profiling',
                'automatic_optimization',
                'visualization'
            ]
        }

    def reset_statistics(self):
        """重置所有统计信息"""
        self.stats = {
            'total_processed': 0,
            'preprocess_time': 0.0,
            'inference_time': 0.0,
            'postprocess_time': 0.0,
            'total_time': 0.0,
            'avg_fps': 0.0
        }
        self.preprocessor.get_statistics()  # 重置预处理器统计
        self.postprocessor.reset_statistics()  # 重置后处理器统计

    def update_config(self,
                     preprocess_config: Optional[PreprocessConfig] = None,
                     postprocess_config: Optional[PostprocessConfig] = None):
        """
        更新管道配置

        Args:
            preprocess_config: 新的预处理配置
            postprocess_config: 新的后处理配置
        """
        if preprocess_config:
            self.config.preprocess = preprocess_config
            self.preprocessor = AdvancedPreprocessor(preprocess_config)
            self.logger.info("预处理配置已更新")

        if postprocess_config:
            self.config.postprocess = postprocess_config
            self.postprocessor = AdvancedPostprocessor(postprocess_config)
            self.logger.info("后处理配置已更新")

    def benchmark_performance(self,
                            test_images: List[np.ndarray],
                            inference_func: callable,
                            num_classes: int,
                            iterations: int = 10) -> Dict:
        """
        性能基准测试

        Args:
            test_images: 测试图像列表
            inference_func: 推理函数
            num_classes: 类别数量
            iterations: 测试迭代次数

        Returns:
            基准测试结果
        """
        self.reset_statistics()

        results = {
            'iterations': iterations,
            'test_images': len(test_images),
            'times': {
                'preprocess': [],
                'inference': [],
                'postprocess': [],
                'total': []
            }
        }

        for i in range(iterations):
            for image in test_images:
                _, perf_info = self.process_single_image(
                    image, inference_func, num_classes
                )

                results['times']['preprocess'].append(perf_info['preprocess_time'])
                results['times']['inference'].append(perf_info['inference_time'])
                results['times']['postprocess'].append(perf_info['postprocess_time'])
                results['times']['total'].append(perf_info['total_time'])

        # 计算统计信息
        for key in results['times']:
            times = results['times'][key]
            results['times'][key] = {
                'mean': np.mean(times),
                'std': np.std(times),
                'min': np.min(times),
                'max': np.max(times),
                'median': np.median(times)
            }

        results['avg_fps'] = 1.0 / results['times']['total']['mean']
        results['throughput'] = len(test_images) * iterations / sum(
            [perf['total_time'] for perf in [self.process_single_image(img, inference_func, num_classes)[1]
                                           for img in test_images for _ in range(iterations)]]
        )

        return results
