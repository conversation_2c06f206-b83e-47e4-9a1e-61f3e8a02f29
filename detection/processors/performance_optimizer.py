#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据处理性能优化器
针对RKNN推理引擎优化数据处理管道的性能
"""

import numpy as np
import cv2
from typing import Tuple, Dict, List, Optional, Union, Callable
from dataclasses import dataclass
import time
import threading
from concurrent.futures import ThreadPoolExecutor
import queue

from utils.logger import get_logger


@dataclass
class OptimizationConfig:
    """性能优化配置"""
    # 并行处理
    enable_parallel: bool = True
    max_workers: int = 4
    
    # 内存优化
    enable_memory_pool: bool = True
    pool_size: int = 10
    
    # 缓存优化
    enable_caching: bool = True
    cache_size: int = 100
    
    # 批处理优化
    enable_batching: bool = True
    batch_size: int = 4
    batch_timeout: float = 0.01  # 10ms
    
    # 数据预取
    enable_prefetch: bool = True
    prefetch_size: int = 2


class MemoryPool:
    """内存池管理器"""
    
    def __init__(self, pool_size: int = 10):
        """
        初始化内存池
        
        Args:
            pool_size: 池大小
        """
        self.pool_size = pool_size
        self.pools = {}  # 按形状和类型分组的内存池
        self.lock = threading.Lock()
        self.logger = get_logger("MemoryPool")
    
    def get_array(self, shape: Tuple[int, ...], dtype: np.dtype) -> np.ndarray:
        """
        从内存池获取数组
        
        Args:
            shape: 数组形状
            dtype: 数据类型
            
        Returns:
            数组对象
        """
        key = (shape, dtype)
        
        with self.lock:
            if key not in self.pools:
                self.pools[key] = queue.Queue(maxsize=self.pool_size)
            
            pool = self.pools[key]
            
            if not pool.empty():
                try:
                    array = pool.get_nowait()
                    array.fill(0)  # 清零
                    return array
                except queue.Empty:
                    pass
        
        # 池中没有可用数组，创建新的
        return np.zeros(shape, dtype=dtype)
    
    def return_array(self, array: np.ndarray):
        """
        将数组返回到内存池
        
        Args:
            array: 要返回的数组
        """
        key = (array.shape, array.dtype)
        
        with self.lock:
            if key in self.pools:
                pool = self.pools[key]
                if not pool.full():
                    try:
                        pool.put_nowait(array)
                    except queue.Full:
                        pass  # 池已满，丢弃数组
    
    def get_pool_stats(self) -> Dict:
        """
        获取内存池统计信息
        
        Returns:
            统计信息字典
        """
        stats = {}
        with self.lock:
            for key, pool in self.pools.items():
                shape, dtype = key
                stats[f"{shape}_{dtype}"] = {
                    'size': pool.qsize(),
                    'maxsize': pool.maxsize
                }
        return stats


class DataCache:
    """数据缓存管理器"""
    
    def __init__(self, cache_size: int = 100):
        """
        初始化数据缓存
        
        Args:
            cache_size: 缓存大小
        """
        self.cache_size = cache_size
        self.cache = {}
        self.access_order = []
        self.lock = threading.Lock()
        self.logger = get_logger("DataCache")
    
    def get(self, key: str) -> Optional[np.ndarray]:
        """
        从缓存获取数据
        
        Args:
            key: 缓存键
            
        Returns:
            缓存的数据或None
        """
        with self.lock:
            if key in self.cache:
                # 更新访问顺序
                self.access_order.remove(key)
                self.access_order.append(key)
                return self.cache[key].copy()
        return None
    
    def put(self, key: str, data: np.ndarray):
        """
        将数据放入缓存
        
        Args:
            key: 缓存键
            data: 要缓存的数据
        """
        with self.lock:
            # 如果缓存已满，移除最久未访问的项
            if len(self.cache) >= self.cache_size and key not in self.cache:
                oldest_key = self.access_order.pop(0)
                del self.cache[oldest_key]
            
            # 添加或更新缓存
            if key in self.cache:
                self.access_order.remove(key)
            
            self.cache[key] = data.copy()
            self.access_order.append(key)
    
    def clear(self):
        """清空缓存"""
        with self.lock:
            self.cache.clear()
            self.access_order.clear()
    
    def get_cache_stats(self) -> Dict:
        """
        获取缓存统计信息
        
        Returns:
            统计信息字典
        """
        with self.lock:
            return {
                'size': len(self.cache),
                'maxsize': self.cache_size,
                'hit_rate': getattr(self, '_hit_count', 0) / max(getattr(self, '_access_count', 1), 1)
            }


class BatchProcessor:
    """批处理器"""
    
    def __init__(self, batch_size: int = 4, timeout: float = 0.01):
        """
        初始化批处理器
        
        Args:
            batch_size: 批大小
            timeout: 超时时间
        """
        self.batch_size = batch_size
        self.timeout = timeout
        self.batch_queue = queue.Queue()
        self.result_queues = {}
        self.lock = threading.Lock()
        self.logger = get_logger("BatchProcessor")
    
    def process_batch(self, data: np.ndarray, 
                     process_func: Callable, 
                     request_id: str) -> np.ndarray:
        """
        批处理数据
        
        Args:
            data: 输入数据
            process_func: 处理函数
            request_id: 请求ID
            
        Returns:
            处理结果
        """
        # 创建结果队列
        result_queue = queue.Queue()
        with self.lock:
            self.result_queues[request_id] = result_queue
        
        # 添加到批处理队列
        self.batch_queue.put((data, request_id))
        
        # 检查是否可以组成批次
        batch_data = []
        batch_ids = []
        
        # 收集批次数据
        start_time = time.time()
        while len(batch_data) < self.batch_size:
            try:
                timeout_remaining = self.timeout - (time.time() - start_time)
                if timeout_remaining <= 0:
                    break
                
                item = self.batch_queue.get(timeout=timeout_remaining)
                batch_data.append(item[0])
                batch_ids.append(item[1])
            except queue.Empty:
                break
        
        if batch_data:
            # 执行批处理
            batch_array = np.stack(batch_data, axis=0)
            batch_results = process_func(batch_array)
            
            # 分发结果
            for i, batch_id in enumerate(batch_ids):
                if batch_id in self.result_queues:
                    self.result_queues[batch_id].put(batch_results[i])
        
        # 获取结果
        try:
            result = result_queue.get(timeout=1.0)
            with self.lock:
                del self.result_queues[request_id]
            return result
        except queue.Empty:
            self.logger.error(f"批处理超时，请求ID: {request_id}")
            return None


class PerformanceOptimizer:
    """性能优化器"""
    
    def __init__(self, config: OptimizationConfig = None):
        """
        初始化性能优化器
        
        Args:
            config: 优化配置
        """
        self.config = config or OptimizationConfig()
        self.logger = get_logger("PerformanceOptimizer")
        
        # 初始化组件
        self.memory_pool = MemoryPool(self.config.pool_size) if self.config.enable_memory_pool else None
        self.data_cache = DataCache(self.config.cache_size) if self.config.enable_caching else None
        self.batch_processor = BatchProcessor(
            self.config.batch_size, 
            self.config.batch_timeout
        ) if self.config.enable_batching else None
        
        # 线程池
        self.thread_pool = ThreadPoolExecutor(
            max_workers=self.config.max_workers
        ) if self.config.enable_parallel else None
        
        # 性能统计
        self.stats = {
            'total_processed': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'memory_pool_hits': 0,
            'memory_pool_misses': 0,
            'batch_processed': 0,
            'parallel_processed': 0
        }
        
        self.logger.info("性能优化器初始化完成")
    
    def optimize_preprocessing(self, 
                             preprocess_func: Callable,
                             enable_cache: bool = True) -> Callable:
        """
        优化预处理函数
        
        Args:
            preprocess_func: 原始预处理函数
            enable_cache: 是否启用缓存
            
        Returns:
            优化后的预处理函数
        """
        def optimized_preprocess(image: np.ndarray, *args, **kwargs):
            # 生成缓存键
            if enable_cache and self.data_cache:
                cache_key = self._generate_cache_key(image, args, kwargs)
                cached_result = self.data_cache.get(cache_key)
                if cached_result is not None:
                    self.stats['cache_hits'] += 1
                    return cached_result
                self.stats['cache_misses'] += 1
            
            # 使用内存池分配输出数组（可选功能）
            if self.memory_pool:
                # 预估输出形状（这里需要根据具体预处理逻辑调整）
                output_shape = self._estimate_output_shape(image.shape, *args, **kwargs)
                if output_shape:
                    try:
                        output_array = self.memory_pool.get_array(output_shape, np.float32)
                        # 只有当函数支持output_array参数时才传递
                        import inspect
                        sig = inspect.signature(preprocess_func)
                        if 'output_array' in sig.parameters:
                            kwargs['output_array'] = output_array
                        self.stats['memory_pool_hits'] += 1
                    except Exception:
                        self.stats['memory_pool_misses'] += 1
            
            # 执行预处理
            result = preprocess_func(image, *args, **kwargs)
            
            # 缓存结果
            if enable_cache and self.data_cache:
                self.data_cache.put(cache_key, result)
            
            self.stats['total_processed'] += 1
            return result
        
        return optimized_preprocess
    
    def optimize_postprocessing(self, 
                              postprocess_func: Callable) -> Callable:
        """
        优化后处理函数
        
        Args:
            postprocess_func: 原始后处理函数
            
        Returns:
            优化后的后处理函数
        """
        def optimized_postprocess(predictions: np.ndarray, *args, **kwargs):
            # 并行处理多个预测结果
            if self.config.enable_parallel and self.thread_pool and len(predictions) > 1:
                # 将预测结果分割为多个块
                chunk_size = max(1, len(predictions) // self.config.max_workers)
                chunks = [predictions[i:i+chunk_size] for i in range(0, len(predictions), chunk_size)]
                
                # 并行处理
                futures = []
                for chunk in chunks:
                    future = self.thread_pool.submit(postprocess_func, chunk, *args, **kwargs)
                    futures.append(future)
                
                # 收集结果
                results = []
                for future in futures:
                    results.extend(future.result())
                
                self.stats['parallel_processed'] += 1
                return results
            else:
                return postprocess_func(predictions, *args, **kwargs)
        
        return optimized_postprocess
    
    def optimize_batch_processing(self, 
                                process_func: Callable) -> Callable:
        """
        优化批处理函数
        
        Args:
            process_func: 原始处理函数
            
        Returns:
            优化后的处理函数
        """
        if not self.batch_processor:
            return process_func
        
        def optimized_batch_process(data: np.ndarray, *args, **kwargs):
            import uuid
            request_id = str(uuid.uuid4())
            
            result = self.batch_processor.process_batch(
                data, 
                lambda batch: process_func(batch, *args, **kwargs),
                request_id
            )
            
            if result is not None:
                self.stats['batch_processed'] += 1
            
            return result
        
        return optimized_batch_process
    
    def _generate_cache_key(self, image: np.ndarray, args: tuple, kwargs: dict) -> str:
        """
        生成缓存键
        
        Args:
            image: 输入图像
            args: 位置参数
            kwargs: 关键字参数
            
        Returns:
            缓存键
        """
        # 使用图像的哈希值和参数生成键
        import hashlib
        
        # 图像哈希
        img_hash = hashlib.md5(image.tobytes()).hexdigest()[:16]
        
        # 参数哈希
        args_str = str(args) + str(sorted(kwargs.items()))
        args_hash = hashlib.md5(args_str.encode()).hexdigest()[:16]
        
        return f"{img_hash}_{args_hash}"
    
    def _estimate_output_shape(self, input_shape: Tuple[int, ...], 
                             *args, **kwargs) -> Optional[Tuple[int, ...]]:
        """
        估算输出形状
        
        Args:
            input_shape: 输入形状
            args: 位置参数
            kwargs: 关键字参数
            
        Returns:
            估算的输出形状
        """
        # 这里需要根据具体的预处理逻辑来估算
        # 简单示例：假设输出是固定尺寸的
        target_size = kwargs.get('target_size', (640, 640))
        if len(input_shape) == 3:  # HWC
            return (1, 3, target_size[1], target_size[0])  # NCHW
        return None
    
    def prefetch_data(self, data_generator: Callable, 
                     prefetch_size: Optional[int] = None) -> Callable:
        """
        数据预取优化
        
        Args:
            data_generator: 数据生成器
            prefetch_size: 预取大小
            
        Returns:
            优化后的数据生成器
        """
        if not self.config.enable_prefetch:
            return data_generator
        
        prefetch_size = prefetch_size or self.config.prefetch_size
        
        def prefetched_generator():
            prefetch_queue = queue.Queue(maxsize=prefetch_size)
            
            def producer():
                try:
                    for data in data_generator():
                        prefetch_queue.put(data)
                    prefetch_queue.put(None)  # 结束标记
                except Exception as e:
                    prefetch_queue.put(e)
            
            # 启动生产者线程
            producer_thread = threading.Thread(target=producer)
            producer_thread.start()
            
            # 消费数据
            while True:
                item = prefetch_queue.get()
                if item is None:  # 结束标记
                    break
                elif isinstance(item, Exception):
                    raise item
                else:
                    yield item
            
            producer_thread.join()
        
        return prefetched_generator
    
    def get_optimization_stats(self) -> Dict:
        """
        获取优化统计信息
        
        Returns:
            统计信息字典
        """
        stats = self.stats.copy()
        
        # 添加组件统计
        if self.memory_pool:
            stats['memory_pool'] = self.memory_pool.get_pool_stats()
        
        if self.data_cache:
            stats['cache'] = self.data_cache.get_cache_stats()
        
        # 计算命中率
        total_cache_access = stats['cache_hits'] + stats['cache_misses']
        if total_cache_access > 0:
            stats['cache_hit_rate'] = stats['cache_hits'] / total_cache_access
        
        total_pool_access = stats['memory_pool_hits'] + stats['memory_pool_misses']
        if total_pool_access > 0:
            stats['memory_pool_hit_rate'] = stats['memory_pool_hits'] / total_pool_access
        
        return stats
    
    def cleanup(self):
        """清理资源"""
        if self.thread_pool:
            self.thread_pool.shutdown(wait=True)
        
        if self.data_cache:
            self.data_cache.clear()
        
        self.logger.info("性能优化器资源清理完成")
