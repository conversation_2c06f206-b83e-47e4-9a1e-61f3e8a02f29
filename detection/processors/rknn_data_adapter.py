#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RKNN数据格式适配器
专门处理RKNN推理引擎的输入输出数据格式转换和适配
"""

import numpy as np
from typing import Tuple, Dict, List, Optional, Union, Any
from dataclasses import dataclass
import struct

from utils.logger import get_logger


@dataclass
class RKNNDataFormat:
    """RKNN数据格式配置"""
    # 输入格式
    input_dtype: str = 'uint8'  # RKNN通常使用uint8输入
    input_layout: str = 'NHWC'  # RKNN输入布局
    input_range: Tuple[float, float] = (0.0, 255.0)  # 输入数据范围
    
    # 输出格式
    output_dtype: str = 'float32'  # RKNN输出通常是float32
    output_layout: str = 'NC'  # 输出布局
    
    # 量化参数
    quantized: bool = True  # 是否使用量化模型
    scale: float = 1.0  # 量化缩放因子
    zero_point: int = 0  # 量化零点


class RKNNDataAdapter:
    """RKNN数据格式适配器"""
    
    def __init__(self, format_config: RKNNDataFormat = None):
        """
        初始化数据适配器
        
        Args:
            format_config: 数据格式配置
        """
        self.config = format_config or RKNNDataFormat()
        self.logger = get_logger("RKNNDataAdapter")
        
        # 数据类型映射
        self.dtype_map = {
            'uint8': np.uint8,
            'int8': np.int8,
            'uint16': np.uint16,
            'int16': np.int16,
            'float16': np.float16,
            'float32': np.float32,
            'float64': np.float64
        }
        
        self.logger.info(f"RKNN数据适配器初始化完成，输入类型: {self.config.input_dtype}")
    
    def prepare_input_data(self, data: np.ndarray, 
                          target_shape: Optional[Tuple[int, ...]] = None) -> np.ndarray:
        """
        准备RKNN输入数据
        
        Args:
            data: 输入数据 (通常是预处理后的图像)
            target_shape: 目标形状
            
        Returns:
            适配后的输入数据
        """
        # 1. 数据类型转换
        adapted_data = self._convert_input_dtype(data)
        
        # 2. 布局转换
        adapted_data = self._convert_input_layout(adapted_data)
        
        # 3. 形状调整
        if target_shape:
            adapted_data = self._reshape_input(adapted_data, target_shape)
        
        # 4. 数据范围调整
        adapted_data = self._adjust_input_range(adapted_data)
        
        # 5. 确保连续内存布局
        adapted_data = np.ascontiguousarray(adapted_data)
        
        return adapted_data
    
    def _convert_input_dtype(self, data: np.ndarray) -> np.ndarray:
        """
        转换输入数据类型
        
        Args:
            data: 输入数据
            
        Returns:
            转换后的数据
        """
        target_dtype = self.dtype_map[self.config.input_dtype]
        
        if data.dtype == target_dtype:
            return data
        
        # 处理浮点到整数的转换
        if data.dtype in [np.float32, np.float64] and target_dtype in [np.uint8, np.int8]:
            # 假设输入数据在[0,1]范围内，需要缩放到[0,255]
            if data.max() <= 1.0:
                data = data * 255.0
            
            # 裁剪到有效范围
            if target_dtype == np.uint8:
                data = np.clip(data, 0, 255)
            elif target_dtype == np.int8:
                data = np.clip(data, -128, 127)
        
        return data.astype(target_dtype)
    
    def _convert_input_layout(self, data: np.ndarray) -> np.ndarray:
        """
        转换输入数据布局
        
        Args:
            data: 输入数据
            
        Returns:
            转换后的数据
        """
        if len(data.shape) == 4:  # 批处理数据
            current_layout = 'NCHW'  # 假设当前是NCHW
            target_layout = self.config.input_layout
            
            if current_layout == target_layout:
                return data
            
            # NCHW -> NHWC
            if current_layout == 'NCHW' and target_layout == 'NHWC':
                return np.transpose(data, (0, 2, 3, 1))
            # NHWC -> NCHW
            elif current_layout == 'NHWC' and target_layout == 'NCHW':
                return np.transpose(data, (0, 3, 1, 2))
        
        elif len(data.shape) == 3:  # 单张图像
            current_layout = 'CHW'  # 假设当前是CHW
            target_layout = self.config.input_layout.replace('N', '')  # 移除N维度
            
            if current_layout == target_layout:
                return data
            
            # CHW -> HWC
            if current_layout == 'CHW' and target_layout == 'HWC':
                return np.transpose(data, (1, 2, 0))
            # HWC -> CHW
            elif current_layout == 'HWC' and target_layout == 'CHW':
                return np.transpose(data, (2, 0, 1))
        
        return data
    
    def _reshape_input(self, data: np.ndarray, target_shape: Tuple[int, ...]) -> np.ndarray:
        """
        调整输入数据形状
        
        Args:
            data: 输入数据
            target_shape: 目标形状
            
        Returns:
            调整后的数据
        """
        if data.shape == target_shape:
            return data
        
        # 尝试reshape
        try:
            return data.reshape(target_shape)
        except ValueError:
            # 如果无法直接reshape，尝试resize
            self.logger.warning(f"无法将形状从 {data.shape} 调整为 {target_shape}，尝试插值调整")
            
            # 对于图像数据，使用OpenCV进行resize
            if len(target_shape) == 4 and len(data.shape) == 4:  # NHWC格式
                import cv2
                resized_batch = []
                for i in range(data.shape[0]):
                    img = data[i]
                    if len(img.shape) == 3:  # HWC
                        resized = cv2.resize(img, (target_shape[2], target_shape[1]))
                        if len(resized.shape) == 2:  # 灰度图
                            resized = np.expand_dims(resized, axis=-1)
                    else:
                        resized = img
                    resized_batch.append(resized)
                return np.array(resized_batch)
            
            return data
    
    def _adjust_input_range(self, data: np.ndarray) -> np.ndarray:
        """
        调整输入数据范围
        
        Args:
            data: 输入数据
            
        Returns:
            调整后的数据
        """
        min_val, max_val = self.config.input_range
        
        # 如果数据已经在目标范围内，直接返回
        if data.min() >= min_val and data.max() <= max_val:
            return data
        
        # 线性缩放到目标范围
        data_min, data_max = data.min(), data.max()
        if data_max > data_min:
            normalized = (data - data_min) / (data_max - data_min)
            scaled = normalized * (max_val - min_val) + min_val
            return scaled
        
        return data
    
    def parse_output_data(self, raw_output: Union[np.ndarray, List[np.ndarray]], 
                         model_info: Dict) -> np.ndarray:
        """
        解析RKNN输出数据
        
        Args:
            raw_output: 原始输出数据
            model_info: 模型信息
            
        Returns:
            解析后的输出数据
        """
        # 处理多输出情况
        if isinstance(raw_output, list):
            if len(raw_output) == 1:
                output_data = raw_output[0]
            else:
                # 多输出需要根据模型信息进行处理
                output_data = self._merge_multiple_outputs(raw_output, model_info)
        else:
            output_data = raw_output
        
        # 1. 数据类型转换
        parsed_data = self._convert_output_dtype(output_data)
        
        # 2. 反量化（如果需要）
        if self.config.quantized:
            parsed_data = self._dequantize_output(parsed_data)
        
        # 3. 布局转换
        parsed_data = self._convert_output_layout(parsed_data)
        
        # 4. 形状调整
        parsed_data = self._adjust_output_shape(parsed_data, model_info)
        
        return parsed_data
    
    def _convert_output_dtype(self, data: np.ndarray) -> np.ndarray:
        """
        转换输出数据类型
        
        Args:
            data: 输出数据
            
        Returns:
            转换后的数据
        """
        target_dtype = self.dtype_map[self.config.output_dtype]
        
        if data.dtype == target_dtype:
            return data
        
        return data.astype(target_dtype)
    
    def _dequantize_output(self, data: np.ndarray) -> np.ndarray:
        """
        反量化输出数据
        
        Args:
            data: 量化的输出数据
            
        Returns:
            反量化后的数据
        """
        if not self.config.quantized:
            return data
        
        # 反量化公式: real_value = (quantized_value - zero_point) * scale
        dequantized = (data.astype(np.float32) - self.config.zero_point) * self.config.scale
        
        return dequantized
    
    def _convert_output_layout(self, data: np.ndarray) -> np.ndarray:
        """
        转换输出数据布局
        
        Args:
            data: 输出数据
            
        Returns:
            转换后的数据
        """
        # RKNN输出通常不需要布局转换，但保留接口以备扩展
        return data
    
    def _adjust_output_shape(self, data: np.ndarray, model_info: Dict) -> np.ndarray:
        """
        调整输出数据形状
        
        Args:
            data: 输出数据
            model_info: 模型信息
            
        Returns:
            调整后的数据
        """
        expected_shape = model_info.get('output_shape')
        if expected_shape and data.shape != expected_shape:
            try:
                return data.reshape(expected_shape)
            except ValueError:
                self.logger.warning(f"无法将输出形状从 {data.shape} 调整为 {expected_shape}")
        
        return data
    
    def _merge_multiple_outputs(self, outputs: List[np.ndarray], 
                              model_info: Dict) -> np.ndarray:
        """
        合并多个输出
        
        Args:
            outputs: 输出列表
            model_info: 模型信息
            
        Returns:
            合并后的输出
        """
        # 对于YOLO模型，通常需要将多个尺度的输出合并
        if len(outputs) == 3:  # YOLOv8通常有3个输出
            # 将不同尺度的输出reshape并合并
            merged_outputs = []
            for output in outputs:
                # 假设输出格式为 (1, anchors, 5+classes)
                if len(output.shape) == 3:
                    merged_outputs.append(output.reshape(output.shape[1], -1))
                else:
                    merged_outputs.append(output.reshape(-1, output.shape[-1]))
            
            # 沿着anchor维度合并
            return np.concatenate(merged_outputs, axis=0)
        else:
            # 简单合并
            return np.concatenate(outputs, axis=0)
    
    def get_memory_layout_info(self, data: np.ndarray) -> Dict:
        """
        获取数据内存布局信息
        
        Args:
            data: 数据数组
            
        Returns:
            内存布局信息
        """
        return {
            'shape': data.shape,
            'dtype': str(data.dtype),
            'strides': data.strides,
            'is_contiguous': data.flags['C_CONTIGUOUS'],
            'is_fortran': data.flags['F_CONTIGUOUS'],
            'memory_size': data.nbytes,
            'itemsize': data.itemsize
        }
    
    def optimize_memory_layout(self, data: np.ndarray) -> np.ndarray:
        """
        优化内存布局以提高RKNN推理性能
        
        Args:
            data: 输入数据
            
        Returns:
            优化后的数据
        """
        # 确保C连续内存布局
        if not data.flags['C_CONTIGUOUS']:
            data = np.ascontiguousarray(data)
        
        # 对于RKNN，通常NHWC布局性能更好
        if len(data.shape) == 4 and self.config.input_layout == 'NHWC':
            # 已经是NHWC布局，检查是否需要进一步优化
            pass
        
        return data
    
    def get_adapter_info(self) -> Dict:
        """
        获取适配器信息
        
        Returns:
            适配器信息字典
        """
        return {
            'config': {
                'input_dtype': self.config.input_dtype,
                'input_layout': self.config.input_layout,
                'input_range': self.config.input_range,
                'output_dtype': self.config.output_dtype,
                'output_layout': self.config.output_layout,
                'quantized': self.config.quantized,
                'scale': self.config.scale,
                'zero_point': self.config.zero_point
            },
            'capabilities': [
                'dtype_conversion',
                'layout_conversion',
                'shape_adjustment',
                'range_normalization',
                'quantization_support',
                'multi_output_handling',
                'memory_optimization'
            ],
            'supported_dtypes': list(self.dtype_map.keys()),
            'supported_layouts': ['NCHW', 'NHWC', 'CHW', 'HWC']
        }
