#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RKNN数据处理模块
手动实现预处理和后处理逻辑，替换PyTorch的自动化处理
"""

import cv2
import numpy as np
from typing import List, Dict, Tuple, Optional, Any

from utils.logger import get_logger


class RKNNPreprocessor:
    """RKNN预处理器"""
    
    def __init__(self, 
                 input_size: Tuple[int, int] = (640, 640),
                 normalize: bool = True,
                 rgb_format: bool = True):
        """
        初始化预处理器
        
        Args:
            input_size: 输入尺寸 (width, height)
            normalize: 是否归一化到[0,1]
            rgb_format: 是否转换为RGB格式
        """
        self.logger = get_logger("RKNNPreprocessor")
        self.input_size = input_size
        self.normalize = normalize
        self.rgb_format = rgb_format
        
        self.logger.info(f"RKNN预处理器初始化: 输入尺寸={input_size}, 归一化={normalize}, RGB格式={rgb_format}")
    
    def letterbox_resize(self, 
                        image: np.ndarray, 
                        target_size: Tuple[int, int],
                        fill_value: int = 114) -> Tuple[np.ndarray, Dict]:
        """
        Letterbox缩放，保持宽高比
        
        Args:
            image: 输入图像 (H, W, C)
            target_size: 目标尺寸 (width, height)
            fill_value: 填充值
            
        Returns:
            缩放后的图像和变换信息
        """
        h, w = image.shape[:2]
        target_w, target_h = target_size
        
        # 计算缩放比例
        scale = min(target_w / w, target_h / h)
        new_w = int(w * scale)
        new_h = int(h * scale)
        
        # 缩放图像
        if scale != 1.0:
            resized = cv2.resize(image, (new_w, new_h), interpolation=cv2.INTER_LINEAR)
        else:
            resized = image.copy()
        
        # 创建目标尺寸的画布
        canvas = np.full((target_h, target_w, 3), fill_value, dtype=np.uint8)
        
        # 计算粘贴位置（居中）
        start_x = (target_w - new_w) // 2
        start_y = (target_h - new_h) // 2
        
        # 粘贴缩放后的图像
        canvas[start_y:start_y + new_h, start_x:start_x + new_w] = resized
        
        # 变换信息
        transform_info = {
            'scale': scale,
            'pad_x': start_x,
            'pad_y': start_y,
            'new_w': new_w,
            'new_h': new_h,
            'original_size': (w, h),
            'target_size': target_size
        }
        
        return canvas, transform_info
    
    def normalize_image(self, image: np.ndarray) -> np.ndarray:
        """
        图像归一化
        
        Args:
            image: 输入图像 (H, W, C)，值范围[0, 255]
            
        Returns:
            归一化后的图像，值范围[0, 1]
        """
        return image.astype(np.float32) / 255.0
    
    def convert_color_format(self, image: np.ndarray, to_rgb: bool = True) -> np.ndarray:
        """
        颜色格式转换
        
        Args:
            image: 输入图像
            to_rgb: 是否转换为RGB格式（默认输入为BGR）
            
        Returns:
            转换后的图像
        """
        if to_rgb:
            return cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        else:
            return cv2.cvtColor(image, cv2.COLOR_RGB2BGR)
    
    def transpose_dimensions(self, image: np.ndarray) -> np.ndarray:
        """
        调整维度顺序
        
        Args:
            image: 输入图像 (H, W, C)
            
        Returns:
            调整后的图像 (C, H, W)
        """
        return np.transpose(image, (2, 0, 1))
    
    def add_batch_dimension(self, image: np.ndarray) -> np.ndarray:
        """
        添加batch维度
        
        Args:
            image: 输入图像 (C, H, W)
            
        Returns:
            添加batch维度后的图像 (1, C, H, W)
        """
        return np.expand_dims(image, axis=0)
    
    def preprocess(self, image: np.ndarray) -> Tuple[np.ndarray, Dict]:
        """
        完整的预处理流程
        
        Args:
            image: 输入图像 (BGR格式)
            
        Returns:
            预处理后的数据和变换信息
        """
        # 1. Letterbox缩放
        resized_image, transform_info = self.letterbox_resize(image, self.input_size)
        
        # 2. 归一化
        if self.normalize:
            processed_image = self.normalize_image(resized_image)
        else:
            processed_image = resized_image.astype(np.float32)
        
        # 3. 颜色格式转换
        if self.rgb_format:
            processed_image = self.convert_color_format(processed_image, to_rgb=True)
        
        # 4. 调整维度顺序
        processed_image = self.transpose_dimensions(processed_image)
        
        # 5. 添加batch维度
        processed_image = self.add_batch_dimension(processed_image)
        
        return processed_image, transform_info


class RKNNPostprocessor:
    """RKNN后处理器"""
    
    def __init__(self, 
                 confidence_threshold: float = 0.5,
                 iou_threshold: float = 0.45,
                 class_names: Optional[List[str]] = None):
        """
        初始化后处理器
        
        Args:
            confidence_threshold: 置信度阈值
            iou_threshold: IoU阈值
            class_names: 类别名称列表
        """
        self.logger = get_logger("RKNNPostprocessor")
        self.confidence_threshold = confidence_threshold
        self.iou_threshold = iou_threshold
        self.class_names = class_names or []
        
        self.logger.info(f"RKNN后处理器初始化: conf={confidence_threshold}, iou={iou_threshold}")
    
    def parse_yolo_outputs(self, 
                          outputs: List[np.ndarray],
                          input_size: Tuple[int, int]) -> List[Dict]:
        """
        解析YOLO模型输出
        
        Args:
            outputs: RKNN推理输出
            input_size: 输入图像尺寸 (width, height)
            
        Returns:
            解析后的检测结果
        """
        if not outputs or len(outputs) == 0:
            return []
        
        detections = []
        
        try:
            # 假设输出格式为 [batch, num_detections, 5+num_classes]
            # 其中5包括: x_center, y_center, width, height, objectness
            output = outputs[0]
            
            if len(output.shape) == 3:
                output = output[0]  # 移除batch维度
            
            input_w, input_h = input_size
            
            for detection in output:
                if len(detection) < 5:
                    continue
                
                # 解析基本信息
                x_center, y_center, width, height, objectness = detection[:5]
                
                # 检查objectness阈值
                if objectness < self.confidence_threshold:
                    continue
                
                # 解析类别信息
                if len(detection) > 5:
                    class_scores = detection[5:]
                    class_id = np.argmax(class_scores)
                    class_confidence = class_scores[class_id]
                    
                    # 计算最终置信度
                    final_confidence = objectness * class_confidence
                else:
                    # 单类别情况
                    class_id = 0
                    final_confidence = objectness
                
                if final_confidence < self.confidence_threshold:
                    continue
                
                # 转换坐标（相对坐标转绝对坐标）
                x_center *= input_w
                y_center *= input_h
                width *= input_w
                height *= input_h
                
                # 计算边界框
                x1 = x_center - width / 2
                y1 = y_center - height / 2
                x2 = x_center + width / 2
                y2 = y_center + height / 2
                
                # 确保坐标在有效范围内
                x1 = max(0, min(x1, input_w))
                y1 = max(0, min(y1, input_h))
                x2 = max(0, min(x2, input_w))
                y2 = max(0, min(y2, input_h))
                
                detection_dict = {
                    'bbox': [float(x1), float(y1), float(x2), float(y2)],
                    'confidence': float(final_confidence),
                    'class_id': int(class_id),
                    'class_name': (self.class_names[class_id] 
                                 if class_id < len(self.class_names) 
                                 else f'class_{class_id}')
                }
                
                detections.append(detection_dict)
        
        except Exception as e:
            self.logger.error(f"解析YOLO输出失败: {e}")
        
        return detections
    
    def apply_nms(self, detections: List[Dict]) -> List[Dict]:
        """
        应用非极大值抑制
        
        Args:
            detections: 检测结果列表
            
        Returns:
            NMS后的检测结果
        """
        if not detections:
            return []
        
        try:
            # 提取边界框和置信度
            boxes = []
            scores = []
            
            for det in detections:
                boxes.append(det['bbox'])
                scores.append(det['confidence'])
            
            # 转换为numpy数组
            boxes = np.array(boxes, dtype=np.float32)
            scores = np.array(scores, dtype=np.float32)
            
            # 使用OpenCV的NMS
            indices = cv2.dnn.NMSBoxes(
                boxes.tolist(), 
                scores.tolist(), 
                self.confidence_threshold, 
                self.iou_threshold
            )
            
            if len(indices) == 0:
                return []
            
            # 返回保留的检测结果
            filtered_detections = []
            for i in indices.flatten():
                filtered_detections.append(detections[i])
            
            return filtered_detections
        
        except Exception as e:
            self.logger.error(f"NMS处理失败: {e}")
            return detections  # 返回原始结果
    
    def scale_coordinates(self, 
                         detections: List[Dict],
                         transform_info: Dict) -> List[Dict]:
        """
        将坐标缩放回原始图像尺寸
        
        Args:
            detections: 检测结果列表
            transform_info: 预处理时的变换信息
            
        Returns:
            缩放后的检测结果
        """
        if not detections:
            return []
        
        try:
            scale = transform_info['scale']
            pad_x = transform_info['pad_x']
            pad_y = transform_info['pad_y']
            orig_w, orig_h = transform_info['original_size']
            
            scaled_detections = []
            
            for det in detections:
                x1, y1, x2, y2 = det['bbox']
                
                # 移除padding
                x1 -= pad_x
                y1 -= pad_y
                x2 -= pad_x
                y2 -= pad_y
                
                # 缩放回原始尺寸
                x1 /= scale
                y1 /= scale
                x2 /= scale
                y2 /= scale
                
                # 确保坐标在有效范围内
                x1 = max(0, min(x1, orig_w))
                y1 = max(0, min(y1, orig_h))
                x2 = max(0, min(x2, orig_w))
                y2 = max(0, min(y2, orig_h))
                
                scaled_det = det.copy()
                scaled_det['bbox'] = [x1, y1, x2, y2]
                scaled_detections.append(scaled_det)
            
            return scaled_detections
        
        except Exception as e:
            self.logger.error(f"坐标缩放失败: {e}")
            return detections
    
    def postprocess(self, 
                   outputs: List[np.ndarray],
                   transform_info: Dict,
                   input_size: Tuple[int, int]) -> List[Dict]:
        """
        完整的后处理流程
        
        Args:
            outputs: RKNN推理输出
            transform_info: 预处理变换信息
            input_size: 输入图像尺寸
            
        Returns:
            最终的检测结果
        """
        # 1. 解析模型输出
        detections = self.parse_yolo_outputs(outputs, input_size)
        
        # 2. 应用NMS
        filtered_detections = self.apply_nms(detections)
        
        # 3. 坐标缩放
        final_detections = self.scale_coordinates(filtered_detections, transform_info)
        
        return final_detections


class RKNNDataProcessor:
    """RKNN数据处理器（预处理+后处理）"""
    
    def __init__(self, 
                 input_size: Tuple[int, int] = (640, 640),
                 confidence_threshold: float = 0.5,
                 iou_threshold: float = 0.45,
                 class_names: Optional[List[str]] = None):
        """
        初始化数据处理器
        
        Args:
            input_size: 输入尺寸
            confidence_threshold: 置信度阈值
            iou_threshold: IoU阈值
            class_names: 类别名称列表
        """
        self.logger = get_logger("RKNNDataProcessor")
        
        # 初始化预处理器和后处理器
        self.preprocessor = RKNNPreprocessor(
            input_size=input_size,
            normalize=True,
            rgb_format=True
        )
        
        self.postprocessor = RKNNPostprocessor(
            confidence_threshold=confidence_threshold,
            iou_threshold=iou_threshold,
            class_names=class_names
        )
        
        self.input_size = input_size
        
        self.logger.info("RKNN数据处理器初始化完成")
    
    def process_input(self, image: np.ndarray) -> Tuple[np.ndarray, Dict]:
        """处理输入图像"""
        return self.preprocessor.preprocess(image)
    
    def process_output(self, 
                      outputs: List[np.ndarray],
                      transform_info: Dict) -> List[Dict]:
        """处理输出结果"""
        return self.postprocessor.postprocess(outputs, transform_info, self.input_size)
    
    def update_thresholds(self, confidence: float, iou: float):
        """更新阈值"""
        self.postprocessor.confidence_threshold = confidence
        self.postprocessor.iou_threshold = iou
        self.logger.info(f"阈值已更新: conf={confidence}, iou={iou}")
