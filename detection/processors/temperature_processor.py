#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
温度处理器模块
处理温度矩阵相关的功能
"""

import cv2
import numpy as np


class TemperatureProcessor:
    """温度处理器 - 处理温度矩阵相关功能"""
    
    def __init__(self):
        pass
    
    def is_temperature_matrix(self, data):
        """检测输入是否为温度矩阵"""
        if isinstance(data, np.ndarray):
            # 温度矩阵通常是2D的浮点数组，值在合理的温度范围内
            if len(data.shape) == 2 and data.dtype in [np.float32, np.float64]:
                min_val, max_val = np.min(data), np.max(data)
                # 检查是否在合理的温度范围内（-50°C到150°C）
                if -50 <= min_val <= 150 and -50 <= max_val <= 150:
                    return True
        return False
    
    def temperature_matrix_to_image(self, temp_matrix):
        """将温度矩阵转换为可视化图像（使用JET颜色映射）"""
        try:
            # 使用JET颜色映射方法
            min_temp = np.min(temp_matrix)
            max_temp = np.max(temp_matrix)

            if max_temp > min_temp:
                normalized = ((temp_matrix - min_temp) / (max_temp - min_temp) * 255).astype(np.uint8)
            else:
                normalized = np.zeros_like(temp_matrix, dtype=np.uint8)

            # 应用JET伪彩色映射（热成像常用的彩色映射）
            colored = cv2.applyColorMap(normalized, cv2.COLORMAP_JET)

            return colored

        except Exception as e:
            print(f"❌ 温度矩阵转换失败: {e}")
            # 返回一个默认的黑色图像
            return np.zeros((temp_matrix.shape[0], temp_matrix.shape[1], 3), dtype=np.uint8)
    
    def detect_from_temperature_matrix(self, temp_matrix, threshold_temp=40.0, 
                                     min_contour_area=15, display_steps=False):
        """
        直接从温度矩阵检测热源
        Args:
            temp_matrix: 温度矩阵 (numpy array)
            threshold_temp: 温度阈值（摄氏度）
            min_contour_area: 最小轮廓面积
            display_steps: 是否显示调试步骤
        Returns:
            final_bboxes, visualization_images
        """
        all_vis_images = {}

        if temp_matrix is None:
            return [], all_vis_images

        try:
            # 直接基于温度阈值创建二值掩码
            binary_mask = (temp_matrix > threshold_temp).astype(np.uint8) * 255

            if display_steps:
                # 为调试创建可视化图像
                all_vis_images["00 - Temperature Matrix"] = self.temperature_matrix_to_image(temp_matrix)
                all_vis_images["01 - Temperature Threshold"] = binary_mask.copy()

            # 形态学操作 - 无腐蚀策略
            kernel = np.ones((3, 3), np.uint8)  # 使用较小的核

            # 只做闭运算填充小孔，不做开运算避免腐蚀
            binary_mask = cv2.morphologyEx(binary_mask, cv2.MORPH_CLOSE, kernel, iterations=1)
            if display_steps:
                all_vis_images["02 - Close Only (No Erosion)"] = binary_mask.copy()

            # 不做开运算，避免腐蚀热源
            if display_steps:
                all_vis_images["03 - No Open Applied"] = binary_mask.copy()

            # print("🔧 温度矩阵检测: 使用无腐蚀形态学策略")

            # 查找轮廓
            contours, _ = cv2.findContours(binary_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            # 过滤轮廓
            valid_contours = []
            for contour in contours:
                area = cv2.contourArea(contour)
                if area >= min_contour_area:
                    valid_contours.append(contour)

            # 生成边界框 - 使用分离策略为每个热源创建独立边界框
            final_bboxes = []
            if valid_contours:
                # 为每个有效轮廓创建独立边界框
                for i, contour in enumerate(valid_contours):
                    x, y, w, h = cv2.boundingRect(contour)
                    final_bboxes.append((x, y, w, h))
                # print(f"📊 生成 {len(valid_contours)} 个独立热源边界框")

            if display_steps:
                # 创建轮廓可视化
                contour_image = np.zeros_like(binary_mask)
                cv2.drawContours(contour_image, valid_contours, -1, 255, 2)
                all_vis_images["04 - Valid Contours"] = contour_image

                # 创建最终结果
                result_image = self.temperature_matrix_to_image(temp_matrix)
                for bbox in final_bboxes:
                    x, y, w, h = bbox
                    cv2.rectangle(result_image, (x, y), (x + w, y + h), (0, 0, 255), 2)
                all_vis_images["05 - Final Result"] = result_image

            # print(f"🔥 温度矩阵检测: 找到 {len(final_bboxes)} 个热源 (阈值: {threshold_temp}°C)")

            return final_bboxes, all_vis_images

        except Exception as e:
            print(f"❌ 温度矩阵检测失败: {e}")
            return [], all_vis_images
    
    def calculate_adaptive_threshold(self, temp_matrix, method="mean_plus_std", factor=1.5):
        """计算自适应温度阈值"""
        try:
            if temp_matrix is None:
                return 40.0  # 默认阈值

            min_temp = np.min(temp_matrix)
            max_temp = np.max(temp_matrix)
            mean_temp = np.mean(temp_matrix)
            std_temp = np.std(temp_matrix)

            if method == "mean_plus_std":
                # 平均值 + 标准差 * 因子
                threshold = mean_temp + std_temp * factor
            elif method == "percentile":
                # 使用百分位数（如85%分位数）
                threshold = np.percentile(temp_matrix, 85)
            elif method == "otsu_like":
                # 类似Otsu的方法，找到最佳分割点
                threshold = self._calculate_otsu_temperature_threshold(temp_matrix)
            elif method == "max_minus_range":
                # 最大值减去温度范围的一定比例
                temp_range = max_temp - min_temp
                threshold = max_temp - temp_range * 0.3
            else:
                # 默认方法
                threshold = mean_temp + std_temp * factor

            # 确保阈值在合理范围内
            threshold = max(min_temp + 1, min(threshold, max_temp - 0.5))

            return float(threshold)

        except Exception as e:
            print(f"❌ 计算自适应阈值失败: {e}")
            return 40.0
    
    def _calculate_otsu_temperature_threshold(self, temp_matrix):
        """计算类似Otsu的温度阈值"""
        try:
            # 将温度值转换为整数范围以便计算直方图
            min_temp = np.min(temp_matrix)
            max_temp = np.max(temp_matrix)

            if max_temp <= min_temp:
                return min_temp + 1

            # 归一化到0-255范围
            normalized = ((temp_matrix - min_temp) / (max_temp - min_temp) * 255).astype(np.uint8)

            # 计算Otsu阈值
            _, otsu_thresh = cv2.threshold(normalized, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

            # 转换回温度值
            temp_threshold = min_temp + (otsu_thresh / 255.0) * (max_temp - min_temp)

            return temp_threshold

        except Exception as e:
            print(f"❌ Otsu阈值计算失败: {e}")
            return np.mean(temp_matrix) + np.std(temp_matrix)
    
    def analyze_temperature_range(self, temp_matrix):
        """分析温度矩阵的统计信息"""
        if temp_matrix is None:
            return None
        
        try:
            min_temp = np.min(temp_matrix)
            max_temp = np.max(temp_matrix)
            mean_temp = np.mean(temp_matrix)
            std_temp = np.std(temp_matrix)
            
            # 计算建议阈值
            suggested_threshold = mean_temp + 2 * std_temp
            
            analysis = {
                'min': float(min_temp),
                'max': float(max_temp),
                'mean': float(mean_temp),
                'std': float(std_temp),
                'suggested_threshold': float(suggested_threshold),
                'temperature_range': float(max_temp - min_temp)
            }
            
            return analysis
            
        except Exception as e:
            print(f"❌ 温度范围分析失败: {e}")
            return None
