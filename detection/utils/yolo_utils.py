#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YOLO相关工具函数
提供模型下载、配置管理等功能
"""

import os
import requests
from typing import Dict, List, Optional
from utils.logger import get_logger

# YOLO模型下载URL
YOLO_MODEL_URLS = {
    # 通用YOLOv8模型
    "models/human_detection/yolov8n_human.rknn": "https://github.com/ultralytics/assets/releases/download/v8.3.0/models/human_detection/yolov8n_human.rknn",
    "models/human_detection/yolov8s_human.rknn": "https://github.com/ultralytics/assets/releases/download/v8.3.0/models/human_detection/yolov8s_human.rknn",
    "models/human_detection/yolov8m_human.rknn": "https://github.com/ultralytics/assets/releases/download/v8.3.0/models/human_detection/yolov8m_human.rknn",
    "models/human_detection/yolov8l_human.rknn": "https://github.com/ultralytics/assets/releases/download/v8.3.0/models/human_detection/yolov8l_human.rknn",
    "models/human_detection/yolov8x_human.rknn": "https://github.com/ultralytics/assets/releases/download/v8.3.0/models/human_detection/yolov8x_human.rknn",

    # 火焰烟雾检测专用模型（本地训练）
    "models/fire_detection/fire_smoke.rknn": "https://github.com/ultralytics/assets/releases/download/v8.3.0/models/human_detection/yolov8n_human.rknn",  # 使用通用模型作为基础
    "fire_smoke_models/human_detection/yolov8n_human.rknn": "https://github.com/ultralytics/assets/releases/download/v8.3.0/models/human_detection/yolov8n_human.rknn",
    "fire_smoke_models/human_detection/yolov8s_human.rknn": "https://github.com/ultralytics/assets/releases/download/v8.3.0/models/human_detection/yolov8s_human.rknn"
}

# COCO数据集类别名称（中文）
COCO_CLASS_NAMES_CN = {
    0: '人',
    1: '自行车',
    2: '汽车',
    3: '摩托车',
    4: '飞机',
    5: '公交车',
    6: '火车',
    7: '卡车',
    8: '船',
    9: '交通灯',
    10: '消防栓',
    11: '停车标志',
    12: '停车计时器',
    13: '长椅',
    14: '鸟',
    15: '猫',
    16: '狗',
    17: '马',
    18: '羊',
    19: '牛',
    20: '大象',
    21: '熊',
    22: '斑马',
    23: '长颈鹿',
    24: '背包',
    25: '雨伞',
    26: '手提包',
    27: '领带',
    28: '行李箱',
    29: '飞盘',
    30: '滑雪板',
    31: '滑雪板',
    32: '运动球',
    33: '风筝',
    34: '棒球棒',
    35: '棒球手套',
    36: '滑板',
    37: '冲浪板',
    38: '网球拍',
    39: '瓶子',
    40: '酒杯',
    41: '杯子',
    42: '叉子',
    43: '刀',
    44: '勺子',
    45: '碗',
    46: '香蕉',
    47: '苹果',
    48: '三明治',
    49: '橙子',
    50: '西兰花',
    51: '胡萝卜',
    52: '热狗',
    53: '披萨',
    54: '甜甜圈',
    55: '蛋糕',
    56: '椅子',
    57: '沙发',
    58: '盆栽植物',
    59: '床',
    60: '餐桌',
    61: '厕所',
    62: '电视',
    63: '笔记本电脑',
    64: '鼠标',
    65: '遥控器',
    66: '键盘',
    67: '手机',
    68: '微波炉',
    69: '烤箱',
    70: '烤面包机',
    71: '水槽',
    72: '冰箱',
    73: '书',
    74: '时钟',
    75: '花瓶',
    76: '剪刀',
    77: '泰迪熊',
    78: '吹风机',
    79: '牙刷'
}

# 火焰烟雾检测类别名称（中文）
FIRE_SMOKE_CLASS_NAMES_CN = {
    0: '火焰',
    1: '烟雾'
}


class YOLOModelManager:
    """YOLO模型管理器"""
    
    def __init__(self, models_dir: str = "models"):
        """
        初始化模型管理器
        
        Args:
            models_dir: 模型存储目录
        """
        self.models_dir = models_dir
        self.logger = get_logger("YOLOModelManager")
        
        # 确保模型目录存在
        os.makedirs(self.models_dir, exist_ok=True)
    
    def download_model(self, model_name: str, force_download: bool = False) -> str:
        """
        下载YOLO模型
        
        Args:
            model_name: 模型名称 (如 "models/human_detection/yolov8n_human.rknn")
            force_download: 是否强制重新下载
            
        Returns:
            模型文件路径
        """
        model_path = os.path.join(self.models_dir, model_name)
        
        # 检查模型是否已存在
        if os.path.exists(model_path) and not force_download:
            self.logger.info(f"模型已存在: {model_path}")
            return model_path
        
        # 检查模型URL是否存在
        if model_name not in YOLO_MODEL_URLS:
            available_models = list(YOLO_MODEL_URLS.keys())
            self.logger.error(f"不支持的模型: {model_name}，可用模型: {available_models}")
            raise ValueError(f"不支持的模型: {model_name}")
        
        url = YOLO_MODEL_URLS[model_name]
        
        try:
            self.logger.info(f"正在下载模型: {model_name}")
            self.logger.info(f"下载URL: {url}")
            
            # 下载模型文件
            response = requests.get(url, stream=True)
            response.raise_for_status()
            
            total_size = int(response.headers.get('content-length', 0))
            downloaded_size = 0
            
            with open(model_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        downloaded_size += len(chunk)
                        
                        # 显示下载进度
                        if total_size > 0:
                            progress = (downloaded_size / total_size) * 100
                            if downloaded_size % (1024 * 1024) == 0:  # 每MB显示一次
                                self.logger.info(f"下载进度: {progress:.1f}%")
            
            self.logger.info(f"✅ 模型下载完成: {model_path}")
            return model_path
            
        except Exception as e:
            self.logger.error(f"❌ 模型下载失败: {e}")
            # 清理不完整的文件
            if os.path.exists(model_path):
                os.remove(model_path)
            raise
    
    def list_available_models(self) -> List[str]:
        """列出可用的模型"""
        return list(YOLO_MODEL_URLS.keys())
    
    def list_downloaded_models(self) -> List[str]:
        """列出已下载的模型"""
        if not os.path.exists(self.models_dir):
            return []
        
        models = []
        for file in os.listdir(self.models_dir):
            if file.endswith('models/general/.rknn'):
                models.append(file)
        
        return models
    
    def get_model_info(self, model_name: str) -> Dict:
        """获取模型信息"""
        model_path = os.path.join(self.models_dir, model_name)
        
        info = {
            'name': model_name,
            'path': model_path,
            'exists': os.path.exists(model_path),
            'size': 0,
            'download_url': YOLO_MODEL_URLS.get(model_name, '')
        }
        
        if info['exists']:
            info['size'] = os.path.getsize(model_path)
        
        return info
    
    def get_recommended_model(self, use_case: str = "realtime") -> str:
        """
        根据使用场景推荐模型
        
        Args:
            use_case: 使用场景 ("realtime", "accuracy", "balanced")
            
        Returns:
            推荐的模型名称
        """
        recommendations = {
            "realtime": "models/human_detection/yolov8n_human.rknn",    # 实时检测，速度优先
            "balanced": "models/human_detection/yolov8s_human.rknn",    # 平衡速度和精度
            "accuracy": "models/human_detection/yolov8m_human.rknn"     # 精度优先
        }
        
        return recommendations.get(use_case, "models/human_detection/yolov8n_human.rknn")

    def get_fire_smoke_model(self, use_case: str = "realtime") -> str:
        """
        根据使用场景推荐火焰烟雾检测模型

        Args:
            use_case: 使用场景 ("realtime", "accuracy", "balanced")

        Returns:
            推荐的火焰烟雾检测模型名称
        """
        recommendations = {
            "realtime": "fire_smoke_models/human_detection/yolov8n_human.rknn",    # 实时检测，速度优先
            "balanced": "models/fire_detection/fire_smoke.rknn",     # 平衡速度和精度
            "accuracy": "fire_smoke_models/human_detection/yolov8s_human.rknn"     # 精度优先
        }

        return recommendations.get(use_case, "models/fire_detection/fire_smoke.rknn")

    def download_fire_smoke_model(self, model_name: str = None, force_download: bool = False) -> str:
        """
        下载火焰烟雾检测模型

        Args:
            model_name: 模型名称，如果为None则使用默认模型
            force_download: 是否强制重新下载

        Returns:
            模型文件路径
        """
        if model_name is None:
            model_name = self.get_fire_smoke_model("balanced")

        # 如果是火焰烟雾专用模型但URL指向通用模型，先尝试下载通用模型作为临时方案
        if model_name in ["fire_smoke_models/human_detection/yolov8n_human.rknn", "fire_smoke_models/human_detection/yolov8s_human.rknn"]:
            self.logger.warning(f"火焰烟雾专用模型 {model_name} 暂时使用通用YOLOv8模型")
            # 下载对应的通用模型
            base_model = model_name.replace("fire_smoke_", "")
            return self.download_model(base_model, force_download)

        return self.download_model(model_name, force_download)


class YOLOConfigManager:
    """YOLO配置管理器"""
    
    def __init__(self):
        self.logger = get_logger("YOLOConfigManager")
    
    def get_default_config(self) -> Dict:
        """获取默认配置"""
        return {
            'model_name': 'models/human_detection/yolov8n_human.rknn',
            'confidence_threshold': 0.5,
            'iou_threshold': 0.45,
            'device': 'cpu',
            'max_detections': 100,
            'classes': [0],  # 只检测人
            'input_size': 640,
            'half_precision': False,
            'augment': False
        }
    
    def validate_config(self, config: Dict) -> bool:
        """验证配置参数"""
        try:
            # 检查必需的参数
            required_params = ['model_name', 'confidence_threshold', 'iou_threshold']
            for param in required_params:
                if param not in config:
                    self.logger.error(f"缺少必需参数: {param}")
                    return False
            
            # 检查参数范围
            if not (0.0 <= config['confidence_threshold'] <= 1.0):
                self.logger.error("confidence_threshold必须在0.0-1.0之间")
                return False
            
            if not (0.0 <= config['iou_threshold'] <= 1.0):
                self.logger.error("iou_threshold必须在0.0-1.0之间")
                return False
            
            # 检查设备设置
            valid_devices = ['cpu', 'cuda', 'mps']
            if config.get('device', 'cpu') not in valid_devices:
                self.logger.error(f"无效的设备设置: {config.get('device')}")
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"配置验证失败: {e}")
            return False
    
    def optimize_config_for_device(self, config: Dict, device: str) -> Dict:
        """根据设备优化配置"""
        optimized_config = config.copy()
        
        if device == 'cpu':
            # CPU优化
            optimized_config['half_precision'] = False
            optimized_config['input_size'] = 416  # 较小的输入尺寸
            optimized_config['max_detections'] = 50
            
        elif device == 'cuda':
            # GPU优化
            optimized_config['half_precision'] = True
            optimized_config['input_size'] = 640
            optimized_config['max_detections'] = 100
            
        self.logger.info(f"已为设备 {device} 优化配置")
        return optimized_config


def get_class_name_chinese(class_id: int) -> str:
    """获取类别的中文名称"""
    return COCO_CLASS_NAMES_CN.get(class_id, f"未知类别_{class_id}")


def get_fire_smoke_class_name_chinese(class_id: int) -> str:
    """获取火焰烟雾检测类别的中文名称"""
    return FIRE_SMOKE_CLASS_NAMES_CN.get(class_id, f"未知类别_{class_id}")


def filter_fire_detections(detections: List[Dict]) -> List[Dict]:
    """过滤出火焰检测结果"""
    return [det for det in detections if det.get('class_name') == 'fire']


def filter_smoke_detections(detections: List[Dict]) -> List[Dict]:
    """过滤出烟雾检测结果"""
    return [det for det in detections if det.get('class_name') == 'smoke']


def get_fire_smoke_statistics(detections: List[Dict]) -> Dict:
    """获取火焰烟雾检测统计信息"""
    if not detections:
        return {
            'total_count': 0,
            'fire_count': 0,
            'smoke_count': 0,
            'max_fire_confidence': 0.0,
            'max_smoke_confidence': 0.0,
            'avg_confidence': 0.0
        }

    fire_detections = filter_fire_detections(detections)
    smoke_detections = filter_smoke_detections(detections)

    all_confidences = [det['confidence'] for det in detections]
    fire_confidences = [det['confidence'] for det in fire_detections]
    smoke_confidences = [det['confidence'] for det in smoke_detections]

    return {
        'total_count': len(detections),
        'fire_count': len(fire_detections),
        'smoke_count': len(smoke_detections),
        'max_fire_confidence': max(fire_confidences) if fire_confidences else 0.0,
        'max_smoke_confidence': max(smoke_confidences) if smoke_confidences else 0.0,
        'avg_confidence': sum(all_confidences) / len(all_confidences) if all_confidences else 0.0
    }


def filter_human_detections(detections: List[Dict]) -> List[Dict]:
    """过滤出人体检测结果"""
    return [det for det in detections if det.get('class_id') == 0]


def calculate_detection_density(detections: List[Dict], frame_width: int, frame_height: int) -> float:
    """计算检测密度（每平方像素的检测数量）"""
    if not detections:
        return 0.0
    
    total_area = frame_width * frame_height
    return len(detections) / total_area


def get_detection_statistics(detections: List[Dict]) -> Dict:
    """获取检测统计信息"""
    if not detections:
        return {
            'count': 0,
            'avg_confidence': 0.0,
            'max_confidence': 0.0,
            'min_confidence': 0.0
        }
    
    confidences = [det['confidence'] for det in detections]
    
    return {
        'count': len(detections),
        'avg_confidence': sum(confidences) / len(confidences),
        'max_confidence': max(confidences),
        'min_confidence': min(confidences)
    }
