# 📐 位置调整功能使用说明

## 🎯 功能概述

位置调整功能解决了可见光标注同步到热成像画面后位置不完全对齐的问题。通过手动调整偏移量，可以精确校准同步标注的位置。

## 🔍 问题背景

由于以下原因，可见光标注同步到热成像后可能存在位置偏差：

1. **传感器差异** - 可见光和热成像传感器的分辨率不同
2. **镜头畸变** - 不同镜头的光学特性差异
3. **安装偏差** - 双摄像头的物理安装位置偏差
4. **配准精度** - 双光配准算法的精度限制

## 🖥️ 界面位置

位置调整控件位于标注同步面板中：

```
右侧侧边栏
├── 配置与工具
    ├── 标注同步 (Annotation Sync)
        ├── 自动同步设置
        ├── 手动同步操作
        └── 位置调整 ← 这里
```

## 🔧 调整控件说明

### 偏移量控制

| 控件 | 范围 | 说明 |
|------|------|------|
| **X偏移** | -200 ~ +200 px | 水平方向偏移量 |
| **Y偏移** | -200 ~ +200 px | 垂直方向偏移量 |
| **缩放** | 50% ~ 200% | 标注框大小缩放比例 |

### 操作按钮

| 按钮 | 功能 | 说明 |
|------|------|------|
| **📐 应用偏移** | 应用调整 | 将偏移量应用到所有已同步的热成像标注 |
| **🔄 重置** | 重置偏移 | 将所有偏移量重置为0 |
| **👁️ 预览** | 预览效果 | 显示调整后的位置信息，不实际应用 |

## 🚀 使用步骤

### 第一步：创建测试标注
1. 在可见光画面上创建一个测试标注
2. 系统自动同步到热成像画面
3. 观察热成像标注的位置是否准确

### 第二步：调整偏移量
1. 在位置调整面板中调整偏移量：
   - **向右移动**：增加X偏移值
   - **向左移动**：减少X偏移值
   - **向下移动**：增加Y偏移值
   - **向上移动**：减少Y偏移值
   - **放大标注**：增加缩放值
   - **缩小标注**：减少缩放值

### 第三步：预览效果
1. 点击"👁️ 预览"按钮
2. 查看弹窗中显示的调整效果
3. 确认调整方向和幅度是否正确

### 第四步：应用调整
1. 点击"📐 应用偏移"按钮
2. 系统将偏移量应用到所有已同步的热成像标注
3. 查看调整后的标注位置

### 第五步：验证效果
1. 创建新的可见光标注
2. 新标注会自动应用当前偏移量
3. 验证同步位置是否准确

## 💡 使用技巧

### 精确调整方法
1. **粗调** - 先使用较大的偏移量快速接近目标位置
2. **细调** - 再使用小幅度调整精确对齐
3. **验证** - 创建多个测试标注验证一致性

### 最佳实践
- **标准化流程** - 每次使用前先校准偏移量
- **记录设置** - 记录最佳偏移量设置供后续使用
- **定期校验** - 定期检查和调整偏移量

### 调整策略
```
常见偏差类型及调整方法：

1. 整体右偏 → X偏移设为负值
2. 整体左偏 → X偏移设为正值
3. 整体下偏 → Y偏移设为负值
4. 整体上偏 → Y偏移设为正值
5. 标注过大 → 缩放设为小于100%
6. 标注过小 → 缩放设为大于100%
```

## 📊 功能特性

### 实时预览
- 调整偏移量时按钮文本会实时更新
- 显示当前设置的偏移量参数
- 预览功能显示详细的位置变化

### 批量应用
- 一次调整影响所有已同步的热成像标注
- 新创建的标注自动应用当前偏移量
- 支持随时重新调整偏移量

### 边界保护
- 自动进行边界检查，防止标注超出画面
- 极端偏移量会被自动限制在有效范围内
- 确保调整后的标注尺寸有效

## ⚠️ 注意事项

### 偏移量影响范围
- 偏移量会影响**所有已同步的热成像标注**
- 新创建的标注会**自动应用当前偏移量**
- 删除标注不会影响偏移量设置

### 调整限制
- X/Y偏移量范围：-200 到 +200 像素
- 缩放范围：50% 到 200%
- 超出范围的调整会被自动限制

### 性能考虑
- 大量标注时应用偏移量可能需要一些时间
- 建议在标注数量较少时进行调整
- 频繁调整可能影响系统性能

## 🔍 故障排除

### 偏移量不生效
- 检查是否有已同步的热成像标注
- 确认偏移量设置不为0
- 尝试重新应用偏移量

### 标注位置异常
- 检查偏移量是否过大
- 使用预览功能确认调整效果
- 尝试重置偏移量后重新调整

### 调整后位置仍不准确
- 可能需要多次微调
- 考虑使用不同的缩放比例
- 检查双光配准是否需要重新校准

## 📈 高级用法

### 批量标注校准
1. 创建多个测试标注覆盖画面不同区域
2. 找到最适合的偏移量设置
3. 应用到所有标注并验证效果

### 区域性调整
- 对于画面不同区域可能需要不同的偏移量
- 可以分批创建和调整标注
- 考虑使用多个偏移量配置

---

**版本**: V1.0  
**更新日期**: 2025-07-28  
**兼容性**: 标注同步功能 V1.0+
