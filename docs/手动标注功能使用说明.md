# 手动标注功能使用说明

## 🎯 功能概述

手动标注功能允许您在可见光画面上手动指定区域，标注物体名称，并用边界框框出这些区域。这个功能对于：
- 标记重要设备和物体
- 创建训练数据
- 区域监控设置
- 物体识别辅助

## 🖥️ 界面位置

手动标注控制面板位于右侧侧边栏的底部，包含以下组件：
- **启用标注模式** - 复选框开关
- **默认标签** - 输入框设置默认物体名称
- **边界框颜色** - 颜色选择按钮
- **标注列表** - 显示所有已创建的标注
- **操作按钮** - 编辑、删除、清空功能

## 🚀 使用步骤

### 第一步：启用标注模式
1. 在右侧侧边栏找到"手动标注"面板
2. 勾选"启用标注模式"复选框
3. 系统提示：🖱️ 标注模式已启用

### 第二步：设置默认标签
1. 在"默认标签"输入框中输入物体名称（如"配电箱"、"灭火器"等）
2. 这个标签将作为新标注的默认名称

### 第三步：选择边界框颜色
1. 点击颜色按钮选择边界框颜色
2. 不同颜色可以区分不同类型的物体

### 第四步：创建标注
1. 在可见光画面上按住鼠标左键
2. 拖拽鼠标创建矩形区域
3. 释放鼠标后弹出对话框
4. 输入或确认物体名称
5. 点击"确定"完成标注

## 🎮 操作说明

### 鼠标操作
- **左键拖拽** - 在可见光画面上创建标注区域
- **双击列表项** - 编辑选中的标注

### 标注管理
- **编辑标注** - 双击列表项或点击"编辑"按钮
- **删除标注** - 选中列表项后点击"删除"按钮
- **清空所有** - 点击"清空"按钮删除所有标注

### 标注显示
- **实时显示** - 所有标注实时显示在可见光画面上
- **颜色区分** - 不同标注使用不同颜色边界框
- **标签显示** - 边界框上方显示物体名称

## 📊 标注信息

每个标注包含以下信息：
- **ID** - 唯一标识符（如 manual_0001）
- **边界框** - 位置和尺寸信息
- **标签** - 物体名称
- **颜色** - 边界框颜色
- **时间戳** - 创建时间
- **可见性** - 是否显示

## 💾 数据保存

- **自动保存** - 标注数据自动保存到 `manual_annotations.json`
- **持久化** - 重启程序后标注数据自动加载
- **格式** - JSON格式，便于导出和处理

## 🎨 界面样式

### 标注列表显示格式
```
[manual_0001] 配电箱
[manual_0002] 灭火器 (隐藏)
```

### 边界框样式
- **线条粗细** - 2像素
- **标签背景** - 与边界框同色
- **文字颜色** - 白色
- **字体** - HERSHEY_SIMPLEX

## ⚙️ 配置选项

### 默认设置
- **默认标签** - "物体"
- **默认颜色** - 绿色 (0, 255, 0)
- **最小边界框** - 10x10像素
- **字体大小** - 0.6

### 可调整参数
- 边界框颜色
- 默认标签文本
- 标注可见性

## 🔧 技术特性

### 坐标系统
- **画面坐标** - 基于可见光画面的像素坐标
- **自动转换** - 鼠标坐标自动转换为画面坐标
- **精确定位** - 支持像素级精确标注

### 数据格式
```json
{
  "manual_0001": {
    "bbox": [100, 150, 200, 100],
    "label": "配电箱",
    "confidence": 1.0,
    "color": [0, 255, 0],
    "timestamp": "2025-01-17T10:30:00",
    "visible": true
  }
}
```

## 🚨 注意事项

### 使用限制
1. **仅可见光区域** - 只能在可见光画面（左半部分）进行标注
2. **最小尺寸** - 边界框最小尺寸为10x10像素
3. **标注模式** - 必须启用标注模式才能创建新标注

### 性能考虑
1. **标注数量** - 建议单个场景标注数量不超过50个
2. **实时渲染** - 大量标注可能影响画面刷新率
3. **内存使用** - 标注数据占用内存较少

### 数据安全
1. **定期备份** - 建议定期备份 `manual_annotations.json` 文件
2. **文件权限** - 确保程序有读写权限
3. **数据恢复** - 删除操作不可撤销，请谨慎操作

## 🔍 故障排除

### 常见问题

**Q: 无法创建标注？**
A: 检查是否启用了标注模式，确保在可见光区域拖拽

**Q: 标注不显示？**
A: 检查标注的可见性设置，确保没有被隐藏

**Q: 颜色显示异常？**
A: 重新选择颜色，确保颜色值有效

**Q: 数据丢失？**
A: 检查 `manual_annotations.json` 文件是否存在和完整

### 调试信息
程序会在控制台输出标注操作的调试信息：
```
✅ 添加标注: 配电箱 at (100, 150, 200, 100)
✏️ 更新标注: manual_0001 -> 配电箱主控
🗑️ 删除标注: manual_0001
```

## 📈 使用建议

### 最佳实践
1. **命名规范** - 使用清晰、一致的物体名称
2. **颜色分类** - 为不同类型物体使用不同颜色
3. **精确标注** - 边界框应紧贴物体边缘
4. **定期整理** - 删除不需要的标注，保持列表整洁

### 应用场景
1. **设备监控** - 标记重要设备位置
2. **安全区域** - 标注禁入区域或危险区域
3. **数据收集** - 为机器学习创建训练数据
4. **文档记录** - 记录现场设备布局

## 🔗 相关功能

- **火焰烟雾检测** - 自动检测功能的补充
- **热源检测** - 与手动标注结合使用
- **数据导出** - 标注数据可用于训练
- **报警系统** - 可基于标注区域设置报警

## 📞 技术支持

如遇到问题，请检查：
1. 控制台输出的错误信息
2. `manual_annotations.json` 文件状态
3. 程序运行权限
4. 系统资源使用情况
