# 🔄 标注同步功能使用说明

## 🎯 功能概述

标注同步功能允许您在可见光画面上手动创建标注区域，然后自动在热成像画面上创建对应的标注区域。这个功能对于：

- **监控区域设置** - 在可见光画面上标注重要设备，自动在热成像画面监控温度
- **双光对比分析** - 同时在两个画面上显示相同的监控区域
- **提高标注效率** - 避免在两个画面上重复标注相同区域
- **精确温度监控** - 基于可见光标注的精确位置进行热成像温度检测

## 🖥️ 界面位置

标注同步控制面板位于右侧侧边栏的"配置与工具"卡片中的"标注同步"区块：

1. 展开右侧侧边栏
2. 找到"配置与工具"卡片
3. 展开"标注同步 (Annotation Sync)"区块

## 🚀 使用步骤

### 第一步：启用自动同步
1. 在标注同步面板中勾选"启用自动同步"
2. 系统提示：自动同步已启用

### 第二步：创建可见光标注
1. 在手动标注面板中启用标注模式
2. 在可见光画面上拖拽创建标注区域
3. 输入物体名称（如"配电箱"、"变压器"等）
4. 确认创建标注

### 第三步：自动同步到热成像
- 系统会自动在热成像画面创建对应标注
- 热成像标注会添加"_热成像"后缀
- 标注颜色会有轻微调整以便区分

## 🔧 手动同步操作

### 批量同步
- 点击"同步所有可见光标注"按钮
- 系统会同步所有未同步的可见光标注
- 进度条显示同步进度
- 完成后显示同步结果

### 同步状态监控
实时显示：
- **可见光标注数量** - 当前可见光画面的标注总数
- **热成像标注数量** - 当前热成像画面的标注总数
- **已同步对数** - 成功同步的标注对数量
- **自动同步状态** - 当前自动同步开关状态

## 📊 坐标转换机制

### 转换方法
1. **双光配准转换**（优先）
   - 使用海康威视SDK的双光配准数据
   - 提供最精确的坐标转换
   - 考虑镜头畸变和安装偏差

2. **比例缩放转换**（备用）
   - 基于传感器尺寸比例
   - 简单可靠的转换方法
   - 适用于配准数据不可用的情况

### 转换精度
- **热成像传感器**: 384×288 像素
- **可见光传感器**: 640×480 像素
- **转换比例**: X=0.600, Y=0.600
- **边界检查**: 确保转换后坐标在有效范围内

## 💡 使用技巧

### 标注命名规范
- 使用描述性名称：如"1号配电箱"、"主变压器"
- 避免特殊字符，使用中文或英文
- 热成像标注会自动添加"_热成像"后缀

### 颜色管理
- 可见光标注：使用原始颜色
- 热成像标注：颜色会自动调整（增加蓝色分量）
- 便于在双画面中区分标注类型

### 同步管理
- 删除可见光标注时会自动删除对应的热成像标注
- 可以单独删除热成像标注而不影响可见光标注
- 支持批量操作和单个操作

## ⚠️ 注意事项

### 坐标精度
- 转换精度取决于双光配准质量
- 建议定期校准双光配准参数
- 在关键区域可手动微调热成像标注位置

### 性能考虑
- 大量标注可能影响渲染性能
- 建议合理控制标注数量
- 可以隐藏不需要的标注以提高性能

### 兼容性
- 支持所有标注形状（矩形边界框）
- 兼容现有的手动标注系统
- 支持标注的导入导出功能

## 🔍 故障排除

### 同步失败
- **坐标超出范围**: 检查可见光标注是否在有效区域内
- **配准数据缺失**: 系统会自动使用比例缩放备用方案
- **权限问题**: 确保有写入标注文件的权限

### 显示问题
- **标注不显示**: 检查标注可见性设置
- **位置偏差**: 可能需要重新校准双光配准
- **颜色异常**: 检查颜色设置和渲染配置

## 📈 高级功能

### API接口
```python
# 获取同步管理器
sync_manager = main_window.annotation_sync_manager

# 手动同步单个标注
result = sync_manager.sync_visible_to_thermal(annotation_id)

# 批量同步所有标注
results = sync_manager.sync_all_visible_annotations()

# 获取同步状态
status = sync_manager.get_sync_status()
```

### 配置选项
- 自动同步开关
- 颜色偏移设置
- 标签后缀自定义
- 转换方法选择

---

**版本**: V1.0  
**更新日期**: 2025-07-28  
**兼容性**: 热成像摄像头系统 V3.0+
