# 🧹 缓存清理功能说明

## 🎯 功能概述

为了确保程序每次启动都有一个干净的运行环境，系统提供了完整的缓存清理功能。该功能可以清理各种临时文件、缓存文件和旧数据，避免因缓存问题导致的程序异常。

## 🗂️ 清理内容

### 1. Python缓存文件
- **`__pycache__/`** 目录
- **`*.pyc`** 编译后的Python字节码文件
- **`*.pyo`** 优化的Python字节码文件

### 2. 日志文件
- **旧日志文件** - 保留最近3天的日志
- **临时日志文件** - `*.log.tmp`

### 3. 临时文件
- **`*.tmp`** 临时文件
- **`*.temp`** 临时文件
- **`*~`** 备份文件
- **`.DS_Store`** macOS系统文件

### 4. 标注缓存
- **`manual_annotations.json.backup`** 标注备份文件
- **`manual_annotations.json.tmp`** 标注临时文件

### 5. 导出缓存
- **旧导出文件** - 保留最新的5个导出文件
- **`*.tmp`** 导出临时文件
- **`*_temp.xlsx`** 临时Excel文件

### 6. 截图缓存
- **`captures/`** 目录内容（保留目录结构）

### 7. 测试文件
- **`test_*.py`** 测试脚本（排除当前运行的测试）

## 🚀 使用方法

### 方法1：图形界面启动（推荐）

#### Windows用户
```batch
# 双击运行
start_clean.bat
```

#### Linux用户
```bash
# 终端运行
./start_clean.sh
```

### 方法2：命令行启动

#### 交互模式
```bash
python start_with_cache_cleanup.py
```

#### 快速命令
```bash
# 只清理缓存
python start_with_cache_cleanup.py -c

# 清理缓存并启动程序
python start_with_cache_cleanup.py -cs

# 直接启动程序（不清理）
python start_with_cache_cleanup.py -s

# 显示帮助
python start_with_cache_cleanup.py -h
```

### 方法3：单独清理缓存
```bash
# 详细模式清理
python startup_cache_cleaner.py

# 静默模式清理
python startup_cache_cleaner.py --quiet
```

## 📊 清理效果示例

```
🧹 启动前缓存清理
==================================================

🔍 清理 Python缓存文件...
   ✅ 清理了 514 项，释放 71.3 MB

🔍 清理 旧日志文件...
   ✅ 清理了 123 项，释放 3.0 MB

🔍 清理 临时文件...
   ℹ️ 没有找到需要清理的项目

🔍 清理 标注缓存...
   ℹ️ 没有找到需要清理的项目

🔍 清理 导出缓存...
   ℹ️ 没有找到需要清理的项目

🔍 清理 截图缓存...
   ℹ️ 没有找到需要清理的项目

🔍 清理 测试文件...
   ✅ 清理了 5 项，释放 32.0 KB

📊 清理总结
------------------------------
总清理项目: 642
释放空间: 74.3 MB
清理的空目录: 1

🎉 缓存清理完成！
```

## ⚙️ 配置选项

### 保留策略
- **日志文件**: 保留最近3天
- **导出文件**: 保留最新5个
- **截图文件**: 清空但保留目录结构

### 安全保护
- **核心文件**: 不会删除任何程序核心文件
- **配置文件**: 保留所有配置文件
- **用户数据**: 保留当前的标注数据
- **模型文件**: 保留所有AI模型文件

## 🛡️ 安全特性

### 1. 智能识别
- 自动识别核心文件和临时文件
- 避免误删重要数据
- 保护用户配置和标注

### 2. 错误处理
- 清理失败不影响程序启动
- 详细的错误日志记录
- 优雅的异常恢复

### 3. 备份保护
- 重要文件清理前自动备份
- 可恢复的删除操作
- 详细的清理日志

## 📋 启动选项说明

### 选项1：清理缓存后启动（推荐）
- ✅ 清理所有缓存文件
- ✅ 释放磁盘空间
- ✅ 确保干净的运行环境
- ✅ 启动主程序

**适用场景**: 日常使用，特别是程序出现异常后

### 选项2：直接启动
- ⚡ 快速启动
- 🔄 保留现有缓存
- ⚠️ 可能存在缓存问题

**适用场景**: 紧急使用，确认缓存无问题时

### 选项3：只清理缓存
- 🧹 清理所有缓存
- 💾 释放磁盘空间
- 🚫 不启动程序

**适用场景**: 定期维护，磁盘空间不足时

## 🔧 高级用法

### 自定义清理策略
可以修改 `startup_cache_cleaner.py` 中的配置：

```python
# 修改日志保留天数
'keep_days': 7  # 改为保留7天

# 修改导出文件保留数量
'keep_latest': 10  # 改为保留10个

# 添加自定义清理模式
'patterns': ['custom_*.tmp', 'my_cache_*']
```

### 集成到其他脚本
```python
from startup_cache_cleaner import clean_startup_cache

# 静默清理
results = clean_startup_cache(verbose=False)

# 检查清理结果
if results['total_cleaned'] > 0:
    print(f"清理了 {results['total_cleaned']} 项")
```

## ⚠️ 注意事项

### 1. 虚拟环境
- 确保在正确的虚拟环境中运行
- Windows: `tj\Scripts\python.exe`
- Linux: `tj/bin/python`

### 2. 权限要求
- 需要对项目目录的写权限
- Linux下可能需要执行权限

### 3. 磁盘空间
- 清理前确保有足够的临时空间
- 大量缓存清理可能需要一些时间

### 4. 数据安全
- 清理操作不可逆
- 重要数据请提前备份
- 建议先使用预览模式

## 🐛 故障排除

### 清理失败
```bash
# 检查权限
ls -la startup_cache_cleaner.py

# 手动清理Python缓存
find . -name "__pycache__" -type d -exec rm -rf {} +

# 手动清理日志
find logs/ -name "*.log" -mtime +3 -delete
```

### 启动失败
```bash
# 检查Python环境
python --version

# 检查依赖
pip list | grep PyQt5

# 重新安装依赖
pip install -r requirements.txt
```

---

**版本**: V1.0  
**更新日期**: 2025-07-28  
**兼容性**: Windows 10+, Linux (Ubuntu 18.04+)
