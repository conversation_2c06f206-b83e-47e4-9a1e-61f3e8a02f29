# 视频抽帧训练数据生成指南

## 🎯 功能概述

视频抽帧功能可以从可见光画面中智能抽取帧，同时保留手动标注的边界框，生成用于配电箱模型训练的高质量数据集。

## 🔧 核心功能

### 1. 智能抽帧
- **自动抽帧** - 按时间间隔自动抽取帧
- **手动抽帧** - 手动触发抽取当前帧
- **质量控制** - 自动过滤模糊、低质量的帧
- **重复过滤** - 避免抽取相似的重复帧

### 2. 标注保留
- **边界框渲染** - 将手动标注的边界框渲染到图像上
- **标签信息** - 保留物体名称和位置信息
- **YOLO格式** - 自动生成YOLO格式的标签文件

### 3. 数据集管理
- **智能分割** - 自动分割训练集、验证集、测试集
- **目录组织** - 创建标准的训练数据集目录结构
- **配置文件** - 生成dataset.yaml配置文件

## 🚀 使用流程

### 第一步：准备手动标注
1. 在可见光画面上创建手动标注
2. 标注配电箱等目标物体
3. 确保标注准确且完整

### 第二步：配置抽帧参数
```
右侧侧边栏 → 视频抽帧面板
```

**抽帧控制设置：**
- **间隔(秒)** - 自动抽帧的时间间隔（1-300秒）
- **质量阈值** - 帧质量最低要求（10-100%）

**选项设置：**
- ✅ **包含手动标注** - 将标注渲染到图像上
- ✅ **过滤重复帧** - 自动跳过相似帧
- ✅ **保存YOLO标签** - 生成训练用的标签文件

### 第三步：开始抽帧
```
1. 点击"开始抽帧" - 启动自动抽帧
2. 点击"手动抽帧" - 立即抽取当前帧
3. 点击"停止抽帧" - 停止自动抽帧
```

### 第四步：创建训练数据集
```
右侧侧边栏 → 训练数据集面板
```

**数据源设置：**
- 点击"使用抽帧目录" - 自动使用抽帧输出
- 或点击"选择数据源" - 手动选择目录

**数据集配置：**
- **训练集** - 70%（可调整50-90%）
- **验证集** - 20%（可调整10-40%）
- **测试集** - 10%（自动计算）

**创建选项：**
- ✅ **随机打乱数据** - 随机分配样本
- ✅ **复制文件** - 保留原始文件
- ✅ **创建dataset.yaml** - 生成配置文件

### 第五步：生成数据集
```
1. 点击"扫描数据" - 检查数据完整性
2. 点击"创建数据集" - 生成训练数据集
3. 点击"打开目录" - 查看生成的数据集
```

## 📁 输出结构

### 抽帧输出目录
```
extracted_frames/
├── auto_20250117_143022_123.jpg     # 自动抽取的图像
├── auto_20250117_143022_123.txt     # YOLO格式标签
├── auto_20250117_143022_123.json    # 元数据信息
├── manual_20250117_143055_456.jpg   # 手动抽取的图像
├── manual_20250117_143055_456.txt   # YOLO格式标签
└── manual_20250117_143055_456.json  # 元数据信息
```

### 训练数据集目录
```
training_dataset/
├── dataset.yaml              # 数据集配置文件
├── dataset_info.json         # 数据集信息
├── train/
│   ├── images/               # 训练图像
│   └── labels/               # 训练标签
├── val/
│   ├── images/               # 验证图像
│   └── labels/               # 验证标签
└── test/
    ├── images/               # 测试图像
    └── labels/               # 测试标签
```

## 🎨 质量控制

### 帧质量评估
系统会自动评估每一帧的质量，包括：
- **清晰度** - 基于拉普拉斯方差
- **亮度** - 避免过暗或过亮的帧
- **对比度** - 确保图像有足够的对比度

### 重复帧过滤
- **相似度检测** - 基于直方图比较
- **阈值控制** - 相似度超过95%的帧会被跳过
- **智能判断** - 保留质量更好的帧

## 📊 数据格式

### YOLO标签格式
```
# 每行一个目标，格式：class_id center_x center_y width height
0 0.5 0.3 0.2 0.4
```

### 元数据格式
```json
{
  "timestamp": "20250117_143022_123",
  "quality_score": 0.85,
  "manual": false,
  "annotations_count": 1,
  "image_size": [480, 640],
  "annotations": [
    {
      "id": "manual_0001",
      "label": "配电箱",
      "bbox": [100, 150, 200, 100],
      "confidence": 1.0
    }
  ]
}
```

### dataset.yaml配置
```yaml
# 配电箱检测数据集配置
path: /path/to/training_dataset
train: train/images
val: val/images
test: test/images

nc: 1
names:
  0: electrical_box
```

## ⚙️ 参数调优

### 抽帧间隔建议
- **密集采集** - 1-3秒（快速变化场景）
- **标准采集** - 5-10秒（一般监控场景）
- **稀疏采集** - 30-60秒（静态场景）

### 质量阈值建议
- **高质量** - 80-100%（严格筛选）
- **中等质量** - 60-80%（平衡数量和质量）
- **低质量** - 40-60%（最大化数据量）

### 数据集分割建议
- **小数据集**（<100样本）- 80%/15%/5%
- **中等数据集**（100-1000样本）- 70%/20%/10%
- **大数据集**（>1000样本）- 70%/20%/10%

## 🔍 使用技巧

### 最佳实践
1. **多角度标注** - 在不同角度和距离下标注配电箱
2. **光照变化** - 在不同光照条件下抽帧
3. **场景多样** - 包含不同背景和环境
4. **质量检查** - 定期检查抽取的图像质量

### 数据增强建议
1. **自然增强** - 通过时间变化获得自然的数据增强
2. **角度变化** - 移动摄像头获得不同视角
3. **距离变化** - 调整摄像头距离获得不同尺度

### 标注质量控制
1. **边界框精确** - 确保边界框紧贴目标边缘
2. **标签一致** - 使用统一的标签命名
3. **完整标注** - 不遗漏画面中的目标物体

## 🚨 注意事项

### 存储空间
- **图像大小** - 每张图像约100KB-1MB
- **数据集大小** - 1000张图像约100MB-1GB
- **预留空间** - 建议预留2倍的存储空间

### 性能影响
- **抽帧频率** - 过高的抽帧频率可能影响系统性能
- **质量检测** - 质量分析会消耗一定的CPU资源
- **文件操作** - 大量文件操作可能影响磁盘性能

### 数据安全
- **备份重要** - 定期备份抽取的数据
- **路径检查** - 确保输出路径有足够权限
- **文件完整** - 检查图像和标签文件的完整性

## 🔗 后续使用

生成的数据集可以直接用于：
1. **YOLOv8训练** - 使用我们提供的训练脚本
2. **其他框架** - 转换为其他格式使用
3. **数据分析** - 分析配电箱的分布和特征
4. **模型评估** - 使用测试集评估模型性能

## 📞 故障排除

### 常见问题
**Q: 抽帧质量太低？**
A: 降低质量阈值或改善光照条件

**Q: 抽取的帧太少？**
A: 减少抽帧间隔或降低质量要求

**Q: 标注没有保存？**
A: 检查"包含手动标注"选项是否启用

**Q: 数据集创建失败？**
A: 检查输出目录权限和磁盘空间

通过这个完整的工作流程，您可以从实时视频中生成高质量的配电箱检测训练数据集！
