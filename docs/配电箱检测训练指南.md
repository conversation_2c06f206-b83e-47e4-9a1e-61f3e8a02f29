# 配电箱检测模型训练指南

## 🎯 项目概述

本项目提供了一个完整的配电箱检测模型训练解决方案，基于YOLOv8架构，专门针对只有实体配电箱但没有标注数据集的情况。

## 🚀 快速开始

### 第一步：环境准备

```bash
# 安装依赖
pip install ultralytics torch opencv-python pillow

# 检查GPU可用性（可选）
python -c "import torch; print(f'GPU可用: {torch.cuda.is_available()}')"
```

### 第二步：初始化项目

```bash
# 设置训练环境
python train_electrical_box_detector.py --action setup
```

这将创建：
- 📁 数据集目录结构
- 📖 标注指南文档
- 📜 训练脚本

### 第三步：数据采集准备

```bash
# 创建数据采集工具和指南
python train_electrical_box_detector.py --action collect
```

这将创建：
- 📖 `data_collection_guide.md` - 详细的拍摄指南
- 🛠️ `quick_annotate.py` - 快速标注工具

## 📸 数据采集策略

### 最小可行数据集（推荐起步）

**目标：50-100张图像**

1. **基础拍摄**（30张）
   - 正面拍摄：15张
   - 侧面拍摄：10张
   - 斜角拍摄：5张

2. **不同距离**（20张）
   - 近距离（配电箱占画面70%+）：10张
   - 中距离（配电箱占画面40-70%）：7张
   - 远距离（配电箱占画面10-40%）：3张

3. **不同光照**（20张）
   - 自然光：10张
   - 室内光：7张
   - 阴影/低光：3张

### 扩展数据集（提升性能）

**目标：200-500张图像**

在最小数据集基础上增加：
- 更多角度和距离组合
- 不同背景环境
- 配电箱门开关状态
- 部分遮挡情况

## 🏷️ 快速标注流程

### 方法1：使用内置标注工具（推荐新手）

```bash
# 1. 将拍摄的图像放入images目录
mkdir images
# 复制你的配电箱图像到images/目录

# 2. 使用快速标注工具
python quick_annotate.py images/ labels/

# 3. 操作说明：
#    - 鼠标拖拽绘制边界框
#    - 空格键保存并下一张
#    - 's'键跳过当前图像
#    - 'q'键退出
```

### 方法2：使用在线工具（推荐）

1. **Roboflow**（免费额度）
   - 访问：https://roboflow.com/
   - 上传图像 → 自动预标注 → 手动调整 → 导出YOLO格式

2. **labelImg**（本地工具）
   ```bash
   pip install labelImg
   labelImg
   ```

## 📊 数据质量检查

```bash
# 分析数据集统计信息
python train_electrical_box_detector.py --action analyze
```

检查项目：
- ✅ 图像和标签数量匹配
- ✅ 标注格式正确
- ✅ 数据分布合理
- ✅ 平均标注框数量

## 🚀 模型训练

### 基础训练（推荐设置）

```bash
# 使用默认参数训练
python train_electrical_box_detector.py --action train

# 自定义参数训练
python train_electrical_box_detector.py --action train --model-size n --epochs 50 --batch 8
```

### 训练参数说明

- `--model-size`: 模型大小
  - `n`: 最快，精度较低（推荐起步）
  - `s`: 平衡速度和精度
  - `m`: 更高精度，速度较慢
  - `l`, `x`: 最高精度，速度最慢

- `--epochs`: 训练轮数
  - 小数据集：50-100轮
  - 大数据集：100-200轮

- `--batch`: 批次大小
  - GPU内存4GB：batch=8
  - GPU内存8GB：batch=16
  - CPU训练：batch=4

### 继续训练

```bash
# 从上次中断处继续训练
python train_electrical_box_detector.py --action train --resume
```

## 🧪 模型测试

```bash
# 测试训练好的模型
python train_electrical_box_detector.py --action test

# 测试指定模型
python train_electrical_box_detector.py --action test --model-path path/to/best.pt
```

## 📦 模型导出

```bash
# 导出为ONNX和TorchScript格式
python train_electrical_box_detector.py --action export

# 导出指定模型
python train_electrical_box_detector.py --action export --model-path path/to/best.pt
```

## 🎯 训练效果优化建议

### 数据不足时的解决方案

1. **数据增强**
   - 系统自动应用旋转、翻转、亮度调整等
   - 可以将50张图像扩展到200+张训练样本

2. **迁移学习**
   - 使用预训练的YOLOv8模型
   - 在配电箱数据上微调

3. **渐进式训练**
   - 先用少量数据训练基础模型
   - 逐步增加数据量重新训练

### 提升检测精度

1. **增加数据多样性**
   - 不同品牌/型号的配电箱
   - 不同安装环境
   - 不同磨损程度

2. **优化标注质量**
   - 边界框紧贴配电箱边缘
   - 保持标注一致性
   - 避免遗漏小目标

3. **调整训练参数**
   - 降低学习率：`--lr 0.001`
   - 增加训练轮数
   - 使用更大的模型

## 📁 文件结构

训练完成后的目录结构：
```
training_electrical_box_detection_20250117_123456/
├── dataset/
│   ├── train/images/          # 训练图像
│   ├── train/labels/          # 训练标签
│   ├── val/images/            # 验证图像
│   └── val/labels/            # 验证标签
├── electrical_box_model/
│   └── weights/
│       ├── best.pt            # 最佳模型
│       └── last.pt            # 最后一轮模型
├── data_collection_guide.md   # 数据采集指南
├── annotation_guide.md        # 标注指南
├── quick_annotate.py          # 快速标注工具
└── train.sh                   # 训练脚本
```

## ❓ 常见问题

### Q: 只有一个配电箱，能训练出好模型吗？
A: 可以！通过多角度拍摄（50-100张）+ 数据增强，可以训练出基础可用的模型。

### Q: 训练需要多长时间？
A: 
- CPU训练：2-4小时（50轮）
- GPU训练：30-60分钟（50轮）

### Q: 如何判断模型训练效果？
A: 查看训练日志中的mAP50指标：
- mAP50 > 0.8：优秀
- mAP50 > 0.6：良好
- mAP50 > 0.4：可用
- mAP50 < 0.4：需要改进

### Q: 模型文件在哪里？
A: 训练完成后在 `training_*/electrical_box_model/weights/best.pt`

## 🔗 相关资源

- [YOLOv8官方文档](https://docs.ultralytics.com/)
- [Roboflow标注平台](https://roboflow.com/)
- [labelImg标注工具](https://github.com/tzutalin/labelImg)

## 📞 技术支持

如遇到问题，请检查：
1. 依赖库是否正确安装
2. 数据集格式是否正确
3. 训练日志中的错误信息
4. GPU内存是否足够（如使用GPU）
