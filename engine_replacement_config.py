#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI推理引擎替换配置
定义从PyTorch到RKNN的迁移配置和映射关系
"""

from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from pathlib import Path


@dataclass
class ModelMigrationConfig:
    """模型迁移配置"""
    original_pytorch_path: str
    target_rknn_path: str
    model_type: str  # 'human', 'fire', 'smoke', 'fire_smoke_combined'
    input_size: tuple = (640, 640)
    confidence_threshold: float = 0.5
    iou_threshold: float = 0.45
    class_names: List[str] = None
    conversion_notes: str = ""


@dataclass
class DetectorMigrationConfig:
    """检测器迁移配置"""
    detector_name: str
    original_class_path: str
    target_class_path: str
    original_import: str
    target_import: str
    models: List[ModelMigrationConfig]
    migration_priority: int = 1  # 1=高优先级, 3=低优先级


class EngineReplacementConfig:
    """AI推理引擎替换配置管理器"""
    
    def __init__(self):
        """初始化配置"""
        self.migration_configs = self._create_migration_configs()
        self.file_replacements = self._create_file_replacements()
        self.import_replacements = self._create_import_replacements()
        self.dependency_updates = self._create_dependency_updates()
    
    def _create_migration_configs(self) -> List[DetectorMigrationConfig]:
        """创建迁移配置"""
        configs = []
        
        # 人体检测器迁移配置
        human_detector_config = DetectorMigrationConfig(
            detector_name="HumanDetector",
            original_class_path="detection.core.human_detector.HumanDetector",
            target_class_path="detection.core.rknn_human_detector.RKNNHumanDetectorAdapter",
            original_import="from detection.core.human_detector import HumanDetector",
            target_import="from detection.core.rknn_human_detector import RKNNHumanDetectorAdapter as HumanDetector",
            models=[
                ModelMigrationConfig(
                    original_pytorch_path="yolov8n.pt",
                    target_rknn_path="models/human_detection/yolov8n_human.rknn",
                    model_type="human",
                    input_size=(640, 640),
                    confidence_threshold=0.45,
                    iou_threshold=0.45,
                    class_names=['person'],
                    conversion_notes="COCO预训练模型，只检测人体类别"
                )
            ],
            migration_priority=1
        )
        configs.append(human_detector_config)
        
        # 火焰烟雾检测器迁移配置
        fire_smoke_detector_config = DetectorMigrationConfig(
            detector_name="FireSmokeDetectionEngine",
            original_class_path="2.modules.detection_module.FireSmokeDetectionEngine",
            target_class_path="detection.core.rknn_fire_smoke_detector.RKNNFireSmokeDetectorAdapter",
            original_import="from detection_module import FireSmokeDetectionEngine",
            target_import="from detection.core.rknn_fire_smoke_detector import RKNNFireSmokeDetectorAdapter as FireSmokeDetectionEngine",
            models=[
                ModelMigrationConfig(
                    original_pytorch_path="models/fire_detection/fire_smoke_combined.pt",
                    target_rknn_path="models/fire_detection/fire_smoke.rknn",
                    model_type="fire_smoke_combined",
                    input_size=(640, 640),
                    confidence_threshold=0.5,
                    iou_threshold=0.45,
                    class_names=['fire', 'smoke'],
                    conversion_notes="火焰和烟雾综合检测模型"
                ),
                ModelMigrationConfig(
                    original_pytorch_path="models/fire_detection/fire.pt",
                    target_rknn_path="models/fire_detection/fire.rknn",
                    model_type="fire",
                    input_size=(640, 640),
                    confidence_threshold=0.5,
                    iou_threshold=0.45,
                    class_names=['fire'],
                    conversion_notes="专用火焰检测模型"
                ),
                ModelMigrationConfig(
                    original_pytorch_path="models/fire_detection/smoke.pt",
                    target_rknn_path="models/fire_detection/smoke.rknn",
                    model_type="smoke",
                    input_size=(640, 640),
                    confidence_threshold=0.5,
                    iou_threshold=0.45,
                    class_names=['smoke'],
                    conversion_notes="专用烟雾检测模型"
                )
            ],
            migration_priority=2
        )
        configs.append(fire_smoke_detector_config)
        
        return configs
    
    def _create_file_replacements(self) -> Dict[str, Dict[str, str]]:
        """创建文件替换映射"""
        return {
            # 主要检测器文件
            "detection/core/human_detector.py": {
                "backup_suffix": ".pytorch_backup",
                "replacement_file": "detection/core/rknn_human_detector.py",
                "migration_type": "replace_with_adapter"
            },
            
            # 模块化检测系统
            "2/modules/detection_module.py": {
                "backup_suffix": ".pytorch_backup",
                "replacement_file": "detection/core/rknn_fire_smoke_detector.py",
                "migration_type": "replace_with_adapter"
            },
            
            # 帧处理器（需要修改）
            "core/frame_processor.py": {
                "backup_suffix": ".pytorch_backup",
                "replacement_file": None,  # 需要手动修改
                "migration_type": "manual_modification"
            },
            
            # 系统初始化器（需要修改）
            "system_initializer.py": {
                "backup_suffix": ".pytorch_backup",
                "replacement_file": None,  # 需要手动修改
                "migration_type": "manual_modification"
            }
        }
    
    def _create_import_replacements(self) -> Dict[str, str]:
        """创建导入语句替换映射"""
        return {
            # PyTorch相关导入
            "import torch": "# import torch  # 已替换为RKNN",
            "from torch import": "# from torch import  # 已替换为RKNN",
            "import torchvision": "# import torchvision  # 已替换为RKNN",
            
            # Ultralytics相关导入
            "from ultralytics import YOLO": "# from ultralytics import YOLO  # 已替换为RKNN",
            "import ultralytics": "# import ultralytics  # 已替换为RKNN",
            
            # 检测器导入替换
            "from detection.core.human_detector import HumanDetector": 
                "from detection.core.rknn_human_detector import RKNNHumanDetectorAdapter as HumanDetector",
            
            "from detection_module import FireSmokeDetectionEngine":
                "from detection.core.rknn_fire_smoke_detector import RKNNFireSmokeDetectorAdapter as FireSmokeDetectionEngine",
            
            # RKNN相关导入
            "# RKNN imports": """
# RKNN推理引擎导入
from detection.core.rknn_inference_engine import RKNNInferenceEngine
from detection.core.rknn_human_detector import RKNNHumanDetector, RKNNHumanDetectorAdapter
from detection.core.rknn_fire_smoke_detector import RKNNFireSmokeDetector, RKNNFireSmokeDetectorAdapter
from detection.processors.rknn_data_processor import RKNNDataProcessor, RKNNPreprocessor, RKNNPostprocessor
"""
        }
    
    def _create_dependency_updates(self) -> Dict[str, Any]:
        """创建依赖更新配置"""
        return {
            "remove_dependencies": [
                "torch>=1.9.0",
                "torchvision>=0.10.0",
                "ultralytics>=8.0.0",
                "dill>=0.3.4"
            ],
            
            "add_dependencies": [
                "rknnlite>=1.6.0",  # RKNN推理库
                "opencv-python>=4.5.0",  # 保留OpenCV
                "numpy>=1.21.0",  # 保留NumPy
                "Pillow>=8.0.0"  # 保留Pillow
            ],
            
            "requirements_file_updates": {
                "requirements.txt": {
                    "comment_out": [
                        "torch>=1.9.0",
                        "torchvision>=0.10.0", 
                        "ultralytics>=8.0.0",
                        "dill>=0.3.4"
                    ],
                    "add_section": """
# RKNN推理引擎依赖 (替换PyTorch)
# ============================================
rknnlite>=1.6.0  # 瑞芯微NPU推理库
# 注意：rknnlite需要从瑞芯微官方获取，不在PyPI中

# 保留的计算机视觉依赖
opencv-python>=4.5.0
numpy>=1.21.0
Pillow>=8.0.0
"""
                },
                
                "requirements_linux.txt": {
                    "add_section": """
# ============================================
# RKNN推理引擎 (Linux边缘计算优化)
# ============================================
rknnlite>=1.6.0  # 瑞芯微NPU推理库
"""
                }
            }
        }
    
    def get_migration_plan(self) -> Dict[str, Any]:
        """获取完整的迁移计划"""
        return {
            "migration_configs": self.migration_configs,
            "file_replacements": self.file_replacements,
            "import_replacements": self.import_replacements,
            "dependency_updates": self.dependency_updates,
            "migration_steps": self._get_migration_steps()
        }
    
    def _get_migration_steps(self) -> List[Dict[str, Any]]:
        """获取迁移步骤"""
        return [
            {
                "step": 1,
                "name": "备份原始文件",
                "description": "备份所有需要修改的PyTorch相关文件",
                "files": list(self.file_replacements.keys()),
                "action": "backup"
            },
            {
                "step": 2,
                "name": "创建RKNN推理引擎",
                "description": "创建RKNN推理引擎基础类和适配器",
                "files": [
                    "detection/core/rknn_inference_engine.py",
                    "detection/processors/rknn_data_processor.py"
                ],
                "action": "create"
            },
            {
                "step": 3,
                "name": "替换人体检测器",
                "description": "将PyTorch人体检测器替换为RKNN版本",
                "files": [
                    "detection/core/rknn_human_detector.py"
                ],
                "action": "replace"
            },
            {
                "step": 4,
                "name": "替换火焰烟雾检测器",
                "description": "将PyTorch火焰烟雾检测器替换为RKNN版本",
                "files": [
                    "detection/core/rknn_fire_smoke_detector.py"
                ],
                "action": "replace"
            },
            {
                "step": 5,
                "name": "创建AI工作线程",
                "description": "创建多线程AI处理架构",
                "files": [
                    "ai_worker.py",
                    "ai_worker_config.py",
                    "ai_worker_utils.py"
                ],
                "action": "create"
            },
            {
                "step": 6,
                "name": "更新导入语句",
                "description": "更新所有文件中的导入语句",
                "files": "all_python_files",
                "action": "update_imports"
            },
            {
                "step": 7,
                "name": "更新依赖文件",
                "description": "更新requirements.txt和相关依赖文件",
                "files": [
                    "requirements.txt",
                    "requirements_linux.txt"
                ],
                "action": "update_dependencies"
            },
            {
                "step": 8,
                "name": "集成AI工作线程",
                "description": "将AI工作线程集成到主系统中",
                "files": [
                    "main.py",
                    "system_initializer.py",
                    "core/frame_processor.py"
                ],
                "action": "integrate"
            },
            {
                "step": 9,
                "name": "测试和验证",
                "description": "测试RKNN推理引擎和多线程架构",
                "files": "test_files",
                "action": "test"
            }
        ]
    
    def get_model_conversion_guide(self) -> Dict[str, Any]:
        """获取模型转换指南"""
        return {
            "conversion_tools": {
                "rknn_toolkit2": {
                    "description": "瑞芯微官方模型转换工具",
                    "installation": "pip install rknn-toolkit2",
                    "supported_formats": ["ONNX", "TensorFlow", "PyTorch", "Caffe"]
                }
            },
            
            "conversion_steps": [
                {
                    "step": 1,
                    "name": "导出ONNX模型",
                    "description": "将PyTorch模型导出为ONNX格式",
                    "command": "python export_onnx.py --model yolov8n.pt --output yolov8n.onnx"
                },
                {
                    "step": 2,
                    "name": "转换为RKNN",
                    "description": "使用rknn-toolkit2将ONNX转换为RKNN",
                    "command": "python convert_to_rknn.py --input yolov8n.onnx --output yolov8n.rknn"
                },
                {
                    "step": 3,
                    "name": "量化优化",
                    "description": "对RKNN模型进行量化优化",
                    "command": "python quantize_rknn.py --model yolov8n.rknn --dataset calibration_data"
                }
            ],
            
            "model_mapping": {
                model_config.original_pytorch_path: model_config.target_rknn_path
                for detector_config in self.migration_configs
                for model_config in detector_config.models
            }
        }
    
    def validate_migration_readiness(self) -> Dict[str, Any]:
        """验证迁移准备情况"""
        results = {
            "ready": True,
            "warnings": [],
            "errors": [],
            "missing_files": [],
            "missing_models": []
        }
        
        # 检查原始文件是否存在
        for file_path in self.file_replacements.keys():
            if not Path(file_path).exists():
                results["missing_files"].append(file_path)
                results["ready"] = False
        
        # 检查模型文件
        for detector_config in self.migration_configs:
            for model_config in detector_config.models:
                if not Path(model_config.original_pytorch_path).exists():
                    results["missing_models"].append(model_config.original_pytorch_path)
                    results["warnings"].append(f"原始模型文件不存在: {model_config.original_pytorch_path}")
        
        # 检查RKNN库是否可用
        try:
            import rknnlite
            results["rknn_available"] = True
        except ImportError:
            results["rknn_available"] = False
            results["errors"].append("rknnlite库未安装")
            results["ready"] = False
        
        return results


# 创建全局配置实例
engine_replacement_config = EngineReplacementConfig()


def get_migration_config() -> EngineReplacementConfig:
    """获取迁移配置实例"""
    return engine_replacement_config


def print_migration_summary():
    """打印迁移摘要"""
    config = get_migration_config()
    plan = config.get_migration_plan()
    
    print("\n" + "="*60)
    print("PyTorch到RKNN迁移计划摘要")
    print("="*60)
    
    print(f"\n📋 迁移步骤总数: {len(plan['migration_steps'])}")
    for step in plan['migration_steps']:
        print(f"  {step['step']}. {step['name']}")
    
    print(f"\n🔧 需要修改的文件: {len(plan['file_replacements'])}")
    for file_path in plan['file_replacements'].keys():
        print(f"  - {file_path}")
    
    print(f"\n🤖 检测器迁移: {len(plan['migration_configs'])}")
    for detector_config in plan['migration_configs']:
        print(f"  - {detector_config.detector_name} (优先级: {detector_config.migration_priority})")
    
    print(f"\n📦 依赖更新:")
    print(f"  移除: {len(plan['dependency_updates']['remove_dependencies'])} 个")
    print(f"  添加: {len(plan['dependency_updates']['add_dependencies'])} 个")
    
    print("="*60)
