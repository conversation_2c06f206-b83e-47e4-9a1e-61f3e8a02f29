#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复模型路径脚本
修复重复路径和错误路径问题
"""

import os
import re
from pathlib import Path
from utils.logger import get_logger


def fix_duplicate_paths():
    """修复重复路径问题"""
    logger = get_logger("PathFixer")
    
    # 需要修复的文件和对应的修复规则
    fixes = [
        {
            'file': '2/config/config.yaml',
            'old': '2/models/models/fire_detection/fire.rknn',
            'new': 'models/fire_detection/fire.rknn'
        },
        {
            'file': 'config/human_detection_config.py',
            'old': 'models/human_detection/models/human_detection/yolov8n_human.rknn',
            'new': 'models/human_detection/yolov8n_human.rknn'
        }
    ]
    
    for fix in fixes:
        file_path = fix['file']
        if not Path(file_path).exists():
            logger.warning(f"文件不存在，跳过: {file_path}")
            continue
        
        try:
            # 读取文件
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 应用修复
            if fix['old'] in content:
                content = content.replace(fix['old'], fix['new'])
                
                # 写回文件
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                logger.info(f"✅ 已修复 {file_path}: {fix['old']} -> {fix['new']}")
            else:
                logger.info(f"⚪ 无需修复 {file_path}")
                
        except Exception as e:
            logger.error(f"❌ 修复失败 {file_path}: {e}")


def main():
    """主函数"""
    print("🔧 开始修复模型路径问题")
    fix_duplicate_paths()
    print("✅ 模型路径修复完成")


if __name__ == "__main__":
    main()
