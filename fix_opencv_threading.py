import cv2
import os

# 设置OpenCV线程数量为1，避免多线程冲突
cv2.setNumThreads(1)

# 设置环境变量来禁用OpenCV的多线程
os.environ['OPENCV_VIDEOIO_PRIORITY_MSMF'] = '0'
os.environ['OPENCV_VIDEOIO_PRIORITY_INTEL_MFX'] = '0'

print("✅ OpenCV线程设置已应用")
print(f"OpenCV线程数: {cv2.getNumThreads()}")

# 测试视频读取
video_path = 'videos/archive/train_dataset/fire_video_(10).avi'
cap = cv2.VideoCapture(video_path)

if cap.isOpened():
    print("✅ 视频文件打开成功")
    
    # 读取几帧测试
    for i in range(5):
        ret, frame = cap.read()
        if ret:
            print(f"✅ 成功读取第 {i+1} 帧")
        else:
            print(f"❌ 读取第 {i+1} 帧失败")
            break
    
    cap.release()
    print("✅ 视频测试完成")
else:
    print("❌ 无法打开视频文件")
