#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
海康威视双光谱热成像监控系统 - 主程序
基于Qt界面的现代化热成像监控系统
"""
import os
import sys
import signal
from pathlib import Path

# OpenCV线程安全修复 - 必须在任何其他模块之前导入
import opencv_fix

# 跨平台Qt插件路径配置
def setup_qt_plugin_path():
    """设置Qt插件路径，支持跨平台"""
    try:
        # 尝试自动检测Qt插件路径
        if hasattr(sys, 'exec_prefix'):
            # Windows路径
            qt_plugins_path = Path(sys.exec_prefix) / 'Lib' / 'site-packages' / 'PyQt5' / 'Qt5' / 'plugins'
            if not qt_plugins_path.exists():
                # Linux路径
                qt_plugins_path = Path(sys.exec_prefix) / 'lib' / 'python3.12' / 'site-packages' / 'PyQt5' / 'Qt5' / 'plugins'
            if not qt_plugins_path.exists():
                # 尝试其他可能的路径
                import PyQt5
                pyqt5_path = Path(PyQt5.__file__).parent
                qt_plugins_path = pyqt5_path / 'Qt5' / 'plugins'

            if qt_plugins_path.exists():
                os.environ['QT_QPA_PLATFORM_PLUGIN_PATH'] = str(qt_plugins_path)
                # 显示相对于项目的路径或简化路径
                try:
                    relative_path = qt_plugins_path.relative_to(Path.cwd())
                    print(f"✅ Qt插件路径设置为: ./{relative_path}")
                except ValueError:
                    # 如果无法计算相对路径，显示简化的路径信息
                    print(f"✅ Qt插件路径设置为: .../{qt_plugins_path.parent.name}/{qt_plugins_path.name}")
            else:
                print("⚠️ 未找到Qt插件路径，使用系统默认配置")
    except Exception as e:
        print(f"⚠️ Qt插件路径配置失败: {e}")

setup_qt_plugin_path()

# 启动前清理缓存
def cleanup_startup_cache():
    """启动前清理缓存"""
    try:
        from startup_cache_cleaner import clean_startup_cache
        print("🧹 正在清理启动缓存...")
        results = clean_startup_cache(verbose=False)  # 静默模式，避免过多输出

        if results['total_cleaned'] > 0:
            size_freed = results['total_size_freed']
            size_str = format_size(size_freed)
            print(f"✅ 缓存清理完成：清理了 {results['total_cleaned']} 项，释放 {size_str}")
        else:
            print("ℹ️ 没有需要清理的缓存")

        if results['errors']:
            print(f"⚠️ 清理过程中有 {len(results['errors'])} 个警告")

        return True

    except Exception as e:
        print(f"⚠️ 缓存清理失败: {e}")
        print("程序将继续启动...")
        return False

def format_size(size_bytes):
    """格式化文件大小"""
    for unit in ['B', 'KB', 'MB', 'GB']:
        if size_bytes < 1024.0:
            return f"{size_bytes:.1f} {unit}"
        size_bytes /= 1024.0
    return f"{size_bytes:.1f} TB"

# 执行启动前缓存清理
print("🚀 海康威视双光谱热成像监控系统启动")
print("=" * 50)
cleanup_startup_cache()
print("=" * 50)

from PyQt5.QtWidgets import QApplication, QMessageBox

# 导入配置模块
from config.camera_config import CAMERA_CONFIG
from config.detection_config import get_heat_detection_renderer

# 导入温度模块
from temperature.calculators.simple_calculator import HSVTemperatureCalculator

# 导入工具模块
from utils.logger import get_logger, log_system_info, log_camera_info

# 导入新的模块化组件
from system_initializer import SystemInitializer
from core.heat_detection_processor import HeatDetectionProcessor

# 导入Qt组件
from qt_ui.qt_system_adapter import QtSystemAdapter, QtDisplayManager

# 全局系统实例引用
_current_system_instance = None


class ThermalCameraSystem:
    """热成像摄像头系统主类"""

    def __init__(self):
        # 初始化日志
        self.logger = get_logger("ThermalCameraSystem")

        # 设置全局系统实例引用
        global _current_system_instance
        _current_system_instance = self

        # 初始化各个模块
        self.camera_capture = None
        self.display_manager = None
        self.temperature_manager = None
        self.mouse_handler = None
        self.performance_monitor = None
        self.frame_processor = None

        # 温度计算器
        self.temperature_calculator = HSVTemperatureCalculator()

        # 热源检测器
        self.heat_detector = None
        self.detection_enabled = False

        # 热源检测渲染器
        self.heat_detection_renderer = get_heat_detection_renderer()

        # 人体检测器
        self.human_detector = None
        self.human_detection_enabled = False

        # 火焰烟雾检测器 - 使用集成检测器
        self.fire_smoke_detector = None
        self.fire_smoke_detection_enabled = False



        # 检测模式配置
        self.detection_mode = "none"  # "none", "video", "camera"
        self.use_video_files_for_testing = False  # 默认关闭视频文件测试模式
        self.test_video_folder = "videos/archive/train_dataset"  # 测试视频文件夹路径

        # 双光配准集成器
        self.dual_camera_integration = None
        self.projection_enabled = False

        # UI控制处理器
        self.ui_control_handler = None

        # 运行状态
        self.is_running = False
        self.camera_initialized = False

        # 初始化辅助类
        self.system_initializer = SystemInitializer(self)
        self.heat_detection_processor = HeatDetectionProcessor(self)

    # ==================== 检测模式控制方法 ====================

    def set_detection_mode(self, mode: str) -> bool:
        """
        设置检测模式

        Args:
            mode: 检测模式 ("none", "video", "camera")

        Returns:
            bool: 设置是否成功
        """
        try:
            if mode not in ["none", "video", "camera"]:
                self.logger.error(f"无效的检测模式: {mode}")
                return False

            old_mode = self.detection_mode
            self.detection_mode = mode

            # 如果从其他模式切换，先停止当前的摄像头
            if old_mode != "none" and self.camera_capture:
                self.camera_capture.release()
                self.camera_capture = None
                self.camera_initialized = False

            if mode == "none":
                self.logger.info("检测模式设置为: 无检测")
                return True
            elif mode == "video":
                self.use_video_files_for_testing = True
                self.logger.info("检测模式设置为: 视频文件检测")
                # 视频模式不自动初始化摄像头，等待用户选择视频文件
                return True
            elif mode == "camera":
                self.use_video_files_for_testing = False
                self.logger.info("检测模式设置为: 实时摄像头检测")
                return self._initialize_camera_for_mode()

        except Exception as e:
            self.logger.error(f"设置检测模式失败: {e}")
            return False

    def _initialize_camera_for_mode(self) -> bool:
        """根据当前模式初始化摄像头"""
        try:
            if not self.system_initializer.initialize_camera_capture():
                self.logger.error("摄像头初始化失败")
                return False

            if not self.camera_capture.start():
                self.logger.error("摄像头启动失败")
                return False

            self.camera_initialized = True
            self.logger.info("摄像头初始化成功")
            return True

        except Exception as e:
            self.logger.error(f"摄像头初始化失败: {e}")
            return False

    def get_detection_mode(self) -> str:
        """获取当前检测模式"""
        return self.detection_mode

    def is_camera_ready(self) -> bool:
        """检查摄像头是否就绪"""
        return self.camera_initialized and self.camera_capture is not None

    def set_video_file(self, file_path: str) -> bool:
        """设置视频文件"""
        try:
            if self.detection_mode != "video":
                self.logger.error("只有在视频检测模式下才能设置视频文件")
                return False

            # 停止当前的摄像头捕获
            if self.camera_capture:
                self.camera_capture.release()
                self.camera_capture = None
                self.camera_initialized = False

            # 创建新的视频捕获
            from core.video_capture import DualCameraCapture
            self.camera_capture = DualCameraCapture(
                file_path,  # 使用选择的视频文件
                None,  # 视频模式下没有红外摄像头
                self.system_initializer.config.VIDEO_CONFIG if hasattr(self.system_initializer, 'config') else None,
                use_video_files=True,
                video_folder=None  # 单个文件模式
            )

            # 启动视频捕获
            if self.camera_capture.start():
                self.camera_initialized = True
                self.logger.info(f"视频文件设置成功: {file_path}")
                return True
            else:
                self.logger.error(f"视频文件启动失败: {file_path}")
                return False

        except Exception as e:
            self.logger.error(f"设置视频文件失败: {e}")
            return False

    # ==================== 检测控制方法 ====================

    def toggle_heat_detection(self, enabled: bool = None):
        """切换热源检测"""
        try:
            if enabled is None:
                self.detection_enabled = not self.detection_enabled
            else:
                self.detection_enabled = enabled

            # 更新热源检测处理器状态
            if hasattr(self, 'heat_detection_processor') and self.heat_detection_processor:
                self.heat_detection_processor.set_detection_enabled(self.detection_enabled)

            # 更新帧处理器状态
            if self.frame_processor:
                if self.detection_enabled:
                    # 启用热源检测 - 设置检测应用函数
                    if hasattr(self, 'heat_detection_processor') and self.heat_detection_processor:
                        self.frame_processor.set_heat_detection_applier(
                            self.heat_detection_processor.apply_isapi_heat_detection
                        )
                else:
                    # 禁用热源检测 - 移除检测应用函数
                    self.frame_processor.set_heat_detection_applier(None)

            self.logger.info(f"热源检测: {'开启' if self.detection_enabled else '关闭'}")
            return self.detection_enabled

        except Exception as e:
            self.logger.error(f"切换热源检测失败: {e}")
            return False

    def toggle_human_detection(self, enabled: bool = None):
        """切换人体检测"""
        try:
            if enabled is None:
                self.human_detection_enabled = not self.human_detection_enabled
            else:
                self.human_detection_enabled = enabled

            # 更新帧处理器状态
            if self.frame_processor:
                self.frame_processor.enable_human_detection(self.human_detection_enabled)

            self.logger.info(f"人体检测: {'开启' if self.human_detection_enabled else '关闭'}")
            return self.human_detection_enabled

        except Exception as e:
            self.logger.error(f"切换人体检测失败: {e}")
            return False

    def toggle_thermal_human_detection(self, enabled: bool = None):
        """切换热成像人体检测"""
        try:
            if enabled is None:
                # 如果没有指定状态，则切换当前状态
                if self.frame_processor:
                    current_state = self.frame_processor.is_thermal_human_detection_enabled()
                    new_state = not current_state
                else:
                    new_state = True
            else:
                new_state = enabled

            # 更新帧处理器状态
            if self.frame_processor:
                self.frame_processor.enable_thermal_human_detection(new_state)

            self.logger.info(f"热成像人体检测: {'开启' if new_state else '关闭'}")
            return new_state

        except Exception as e:
            self.logger.error(f"切换热成像人体检测失败: {e}")
            return False

    def toggle_fire_smoke_detection(self, enabled: bool = None):
        """切换火焰烟雾检测"""
        try:
            if enabled is None:
                self.fire_smoke_detection_enabled = not self.fire_smoke_detection_enabled
            else:
                self.fire_smoke_detection_enabled = enabled

            # 更新帧处理器状态
            if self.frame_processor:
                self.frame_processor.set_fire_smoke_detection_enabled(self.fire_smoke_detection_enabled)

            self.logger.info(f"火焰烟雾检测: {'开启' if self.fire_smoke_detection_enabled else '关闭'}")
            return self.fire_smoke_detection_enabled

        except Exception as e:
            self.logger.error(f"切换火焰烟雾检测失败: {e}")
            return False



    def set_temperature_threshold(self, threshold: float):
        """设置温度阈值"""
        try:
            if self.heat_detector:
                # 检查是否是ISAPIThermalDebugger
                if hasattr(self.heat_detector, 'manual_temperature_threshold'):
                    self.heat_detector.manual_temperature_threshold = threshold
                    self.logger.info(f"温度阈值设置为: {threshold:.1f}°C")
                    return True
                # 检查是否是标准HeatSourceDetector
                elif hasattr(self.heat_detector, 'set_threshold'):
                    self.heat_detector.set_threshold(threshold)
                    self.logger.info(f"温度阈值设置为: {threshold:.1f}°C")
                    return True
                else:
                    self.logger.warning("热源检测器不支持阈值设置")
                    return False
            else:
                self.logger.warning("热源检测器未初始化")
                return False

        except Exception as e:
            self.logger.error(f"设置温度阈值失败: {e}")
            return False

    def set_adaptive_threshold(self, adaptive: bool):
        """设置自适应阈值模式"""
        try:
            if self.heat_detector:
                # 检查是否是ISAPIThermalDebugger
                if hasattr(self.heat_detector, 'use_adaptive_threshold'):
                    self.heat_detector.use_adaptive_threshold = adaptive
                    mode_name = "自适应" if adaptive else "手动"
                    self.logger.info(f"阈值模式设置为: {mode_name}")
                    return True
                # 检查是否是标准HeatSourceDetector
                elif hasattr(self.heat_detector, 'set_adaptive_mode'):
                    self.heat_detector.set_adaptive_mode(adaptive)
                    mode_name = "自适应" if adaptive else "手动"
                    self.logger.info(f"阈值模式设置为: {mode_name}")
                    return True
                else:
                    self.logger.warning("热源检测器不支持自适应模式")
                    return False
            else:
                self.logger.warning("热源检测器未初始化")
                return False

        except Exception as e:
            self.logger.error(f"设置自适应阈值模式失败: {e}")
            return False

    def get_current_threshold(self):
        """获取当前温度阈值"""
        try:
            if self.heat_detector:
                # 检查是否是ISAPIThermalDebugger
                if hasattr(self.heat_detector, 'manual_temperature_threshold'):
                    return self.heat_detector.manual_temperature_threshold
                # 检查是否是标准HeatSourceDetector
                elif hasattr(self.heat_detector, 'get_current_threshold'):
                    return self.heat_detector.get_current_threshold()
                else:
                    return 35.0  # 默认值
            else:
                return 35.0  # 默认值
        except Exception as e:
            self.logger.error(f"获取当前阈值失败: {e}")
            return 35.0

    def is_adaptive_mode(self):
        """检查是否为自适应模式"""
        try:
            if self.heat_detector:
                # 检查是否是ISAPIThermalDebugger
                if hasattr(self.heat_detector, 'use_adaptive_threshold'):
                    return self.heat_detector.use_adaptive_threshold
                # 检查是否是标准HeatSourceDetector
                elif hasattr(self.heat_detector, 'is_adaptive_mode'):
                    return self.heat_detector.is_adaptive_mode()
                else:
                    return True  # 默认自适应模式
            else:
                return True  # 默认自适应模式
        except Exception as e:
            self.logger.error(f"检查自适应模式失败: {e}")
            return True

    def save_current_frame(self):
        """保存当前帧"""
        try:
            if self.frame_processor and hasattr(self.frame_processor, 'save_current_frame'):
                return self.frame_processor.save_current_frame()
            else:
                self.logger.warning("帧处理器不支持保存功能")
                return None

        except Exception as e:
            self.logger.error(f"保存当前帧失败: {e}")
            return None

    def save_debug_info(self):
        """保存调试信息"""
        try:
            if self.frame_processor and hasattr(self.frame_processor, 'save_debug_info'):
                self.frame_processor.save_debug_info()
                return True
            else:
                self.logger.warning("帧处理器不支持调试信息保存")
                return False

        except Exception as e:
            self.logger.error(f"保存调试信息失败: {e}")
            return False

    def get_detection_status(self):
        """获取检测状态"""
        # 获取热成像人体检测的实际状态
        thermal_human_enabled = False
        if self.frame_processor:
            thermal_human_enabled = self.frame_processor.is_thermal_human_detection_enabled()

        # 获取检测计数和变化率
        detection_counts = {'human_count': 0, 'fire_count': 0, 'smoke_count': 0}
        change_rates = {'fire_count_change_rate': 0.0, 'smoke_count_change_rate': 0.0, 'fire_area_change_rate': 0.0, 'smoke_area_change_rate': 0.0}

        if self.frame_processor and hasattr(self.frame_processor, 'detection_count_manager'):
            try:
                # 获取当前计数
                current_counts = self.frame_processor.detection_count_manager.get_current_counts()
                detection_counts = {
                    'human_count': current_counts.human_count,
                    'fire_count': current_counts.fire_count,
                    'smoke_count': current_counts.smoke_count
                }

                # 获取变化率
                rates = self.frame_processor.detection_count_manager.get_change_rates()
                change_rates.update({
                    'fire_count_change_rate': rates.get('fire_count_change_rate', 0.0),
                    'smoke_count_change_rate': rates.get('smoke_count_change_rate', 0.0),
                    # 使用真正的面积变化率
                    'fire_area_change_rate': rates.get('fire_area_change_rate', 0.0),
                    'smoke_area_change_rate': rates.get('smoke_area_change_rate', 0.0)
                })

                # 添加调试信息
                if any(rate != 0.0 for rate in rates.values()):
                    print(f"📊 主程序获取到变化率:")
                    print(f"   火焰计数变化率: {rates.get('fire_count_change_rate', 0.0):.3f}%/秒")
                    print(f"   烟雾计数变化率: {rates.get('smoke_count_change_rate', 0.0):.3f}%/秒")
                    print(f"   火焰面积变化率: {rates.get('fire_area_change_rate', 0.0):.1f}像素²/秒")
                    print(f"   烟雾面积变化率: {rates.get('smoke_area_change_rate', 0.0):.1f}像素²/秒")

                # 定期保存历史记录以计算变化率
                self.frame_processor.detection_count_manager.save_current_to_history()

            except Exception as e:
                print(f"⚠️ 获取检测计数失败: {e}")

        status = {
            'heat_detection_enabled': self.detection_enabled,
            'human_detection_enabled': self.human_detection_enabled,
            'thermal_human_detection_enabled': thermal_human_enabled,
            'fire_smoke_detection_enabled': self.fire_smoke_detection_enabled,
            'projection_enabled': self.projection_enabled
        }

        # 添加检测计数和变化率
        status.update(detection_counts)
        status.update(change_rates)

        return status

    def initialize(self) -> bool:
        """初始化系统（不包括摄像头）"""
        try:
            log_system_info()
            self.logger.info("初始化热成像摄像头系统...")

            # 跳过摄像头初始化，等待用户选择模式
            self.logger.info("跳过摄像头初始化，等待用户选择检测模式")

            if not self.system_initializer.initialize_display_manager():
                return False

            if not self.system_initializer.initialize_temperature_manager():
                return False

            if not self.system_initializer.initialize_mouse_handler():
                return False

            if not self.system_initializer.initialize_ui_control_handler():
                return False

            # 初始化检测器（允许失败）
            self.system_initializer.initialize_heat_detector()
            self.system_initializer.initialize_human_detector()
            self.system_initializer.initialize_fire_smoke_detector()

            # 初始化Excel数据导出器（独立初始化，确保总是可用）
            self.system_initializer.initialize_excel_data_exporter()

            if not self.system_initializer.initialize_performance_monitor():
                return False

            # 初始化帧处理器
            if not self.system_initializer.initialize_frame_processor(
                self.heat_detection_processor.apply_isapi_heat_detection
            ):
                return False

            self.logger.info("系统初始化完成（摄像头待初始化）")
            return True

        except Exception as e:
            self.logger.exception(f"系统初始化失败: {e}")
            return False

    def start(self) -> bool:
        """启动系统（不启动摄像头）"""
        try:
            self.logger.info("启动热成像摄像头系统...")

            # 不启动摄像头，等待用户选择模式
            self.logger.info("系统启动，等待用户选择检测模式")

            self.is_running = True
            self.logger.info("系统启动成功")

            # 启动信息
            print(f"\n🚀 海康威视双光摄像头系统已启动")
            print(f"🎛️ 请先选择检测模式：")
            print(f"   📹 视频检测模式 - 使用本地视频文件进行检测")
            print(f"   📡 摄像头检测模式 - 使用实时摄像头流进行检测")
            print(f"❌ 按 'q' 键或点击退出按钮退出程序\n")

            return True

        except Exception as e:
            self.logger.exception(f"系统启动失败: {e}")
            return False

    def run(self):
        """运行主循环"""
        if not self.is_running:
            self.logger.error("系统未启动，无法运行")
            return

        try:
            self.logger.info("进入主循环...")

            while self.is_running:
                # 检查是否有摄像头初始化
                if not self.is_camera_ready():
                    # 没有摄像头，等待用户选择模式
                    import time
                    time.sleep(0.1)  # 避免CPU占用过高
                    continue

                # 读取双摄像头帧
                ret_visible, ret_thermal, frame_visible, frame_thermal = self.camera_capture.read_frames()

                # 更新性能监控
                current_fps = self.performance_monitor.update()

                # 更新温度范围
                if self.temperature_manager:
                    self.temperature_manager.update_all_ranges()

                if ret_visible and ret_thermal:
                    # 处理双摄像头画面
                    self._process_dual_frames(frame_visible, frame_thermal, current_fps)

                elif ret_visible:
                    # 只有可见光
                    self.logger.warning("红外摄像头连接中断，仅显示可见光")
                    self._process_single_frame(frame_visible, "Visible Light", current_fps)

                elif ret_thermal:
                    # 只有红外
                    self.logger.warning("可见光摄像头连接中断，仅显示红外")
                    self._process_single_frame(frame_thermal, "Thermal Infrared", current_fps)

                else:
                    self.logger.error("摄像头连接断开")
                    if self.detection_mode == "camera":
                        print("❌ 摄像头连接失败:")
                        print(f"   可见光摄像头: {CAMERA_CONFIG.visible_url}")
                        print(f"   红外摄像头: {CAMERA_CONFIG.thermal_url}")
                        print("🔧 请检查:")
                        print("   1. 摄像头是否正常连接")
                        print("   2. 网络连接是否正常")
                        print("   3. 摄像头IP地址是否正确")
                        print("   4. 用户名密码是否正确")
                        print("💡 您可以切换到视频检测模式继续使用")
                    # 重置摄像头状态，让用户重新选择
                    self.camera_initialized = False
                    if self.camera_capture:
                        self.camera_capture.release()
                        self.camera_capture = None

        except KeyboardInterrupt:
            self.logger.info("程序被用户中断")
        except Exception as e:
            self.logger.exception(f"主循环异常: {e}")
            print(f"❌ 程序异常退出: {e}")
            print("📋 详细错误信息请查看日志文件")
            import traceback
            print("🔍 完整错误堆栈:")
            traceback.print_exc()
        finally:
            self.stop()

    def _process_dual_frames(self, visible_frame, thermal_frame, fps: float):
        """处理双摄像头画面"""
        if self.frame_processor:
            self.frame_processor.process_dual_frames(visible_frame, thermal_frame, fps)
        else:
            self.logger.error("帧处理器未初始化")

    def _process_single_frame(self, frame, title: str, fps: float):
        """处理单个画面"""
        if self.frame_processor:
            self.frame_processor.process_single_frame(frame, title, fps)
        else:
            self.logger.error("帧处理器未初始化")

    def stop(self):
        """停止系统"""
        self.logger.info("停止热成像摄像头系统...")

        self.is_running = False

        # 停止温度监控
        if self.temperature_manager:
            self.temperature_manager.stop_all_monitoring()

        # 释放摄像头资源
        if self.camera_capture:
            self.camera_capture.release()

        # 清理检测器资源
        if self.heat_detector:
            self.heat_detector = None

        if self.fire_smoke_detector:
            self.fire_smoke_detector = None



        # 清理双光配准集成器资源
        if self.dual_camera_integration:
            self.dual_camera_integration.cleanup()
            self.dual_camera_integration = None

        self.logger.info("系统已停止")


class ThermalCameraSystemQt(ThermalCameraSystem):
    """热成像摄像头系统Qt界面版本"""
    
    def __init__(self):
        # 调用父类初始化
        super().__init__()
        
        # Qt相关组件
        self.qt_adapter = None
        self.qt_display_manager = None
        
        # 重写日志器名称
        self.logger = get_logger("ThermalCameraSystemQt")
    
    def initialize(self) -> bool:
        """初始化系统 - Qt界面版本（不包括摄像头）"""
        try:
            log_system_info()
            self.logger.info("初始化热成像摄像头系统 (Qt界面版本)...")

            # 初始化Qt适配器
            if not self._initialize_qt_adapter():
                return False

            # 跳过摄像头初始化，等待用户选择模式
            self.logger.info("跳过摄像头初始化，等待用户选择检测模式")

            # 替换显示管理器为Qt版本
            self.display_manager = self.qt_display_manager

            if not self.system_initializer.initialize_temperature_manager():
                return False

            if not self.system_initializer.initialize_mouse_handler():
                return False

            if not self.system_initializer.initialize_ui_control_handler():
                return False

            # 初始化检测器（允许失败）
            self.system_initializer.initialize_heat_detector()
            self.system_initializer.initialize_human_detector()
            self.system_initializer.initialize_fire_smoke_detector()

            # 初始化Excel数据导出器（独立初始化，确保总是可用）
            self.system_initializer.initialize_excel_data_exporter()

            if not self.system_initializer.initialize_performance_monitor():
                return False

            # 初始化帧处理器
            if not self.system_initializer.initialize_frame_processor(
                self.heat_detection_processor.apply_isapi_heat_detection
            ):
                return False

            # 设置工具栏回调函数（Qt版本暂时跳过）
            # self.toolbar_callback_handler.setup_toolbar_callbacks()

            self.logger.info("系统初始化完成 (Qt界面版本，摄像头待初始化)")
            return True

        except Exception as e:
            self.logger.exception(f"系统初始化失败: {e}")
            return False
    
    def _initialize_qt_adapter(self) -> bool:
        """初始化Qt适配器"""
        try:
            self.qt_adapter = QtSystemAdapter(self)
            self.qt_display_manager = QtDisplayManager(self.qt_adapter)
            self.logger.info("✅ Qt适配器初始化成功")
            return True
        except Exception as e:
            self.logger.exception(f"Qt适配器初始化失败: {e}")
            return False
    
    def start(self) -> bool:
        """启动系统 - Qt界面版本（不启动摄像头）"""
        try:
            self.logger.info("启动热成像摄像头系统 (Qt界面版本)...")

            # 不启动摄像头，等待用户选择模式
            self.logger.info("系统启动，等待用户选择检测模式")

            # 显示Qt窗口
            self.qt_adapter.show_window()

            self.is_running = True
            self.logger.info("系统启动成功 (Qt界面版本)")

            # 启动信息
            print(f"\n🚀 海康威视双光摄像头系统已启动 (Qt界面版本)")
            print(f"🎛️ 请在Qt界面中选择检测模式：")
            print(f"   📹 视频检测模式 - 使用本地视频文件进行检测")
            print(f"   📡 摄像头检测模式 - 使用实时摄像头流进行检测")
            print(f"❌ 按 'q' 键或关闭窗口退出程序\n")

            return True

        except Exception as e:
            self.logger.exception(f"系统启动失败: {e}")
            return False
    
    def run(self):
        """运行主循环 - Qt界面版本"""
        if not self.is_running:
            self.logger.error("系统未启动，无法运行")
            return
        
        try:
            self.logger.info("进入Qt事件循环...")
            
            # 启动帧处理线程
            self.qt_adapter.start_frame_processing()
            
            # 运行Qt事件循环
            return self.qt_adapter.run_qt_event_loop()
            
        except KeyboardInterrupt:
            self.logger.info("程序被用户中断")
        except Exception as e:
            self.logger.exception(f"主循环异常: {e}")
            print(f"❌ 程序异常退出: {e}")
            print("📋 详细错误信息请查看日志文件")
        finally:
            self.stop()
    
    def stop(self):
        """停止系统 - Qt界面版本"""
        self.logger.info("停止热成像摄像头系统 (Qt界面版本)...")
        
        self.is_running = False
        
        # 停止Qt适配器
        if self.qt_adapter:
            self.qt_adapter.cleanup()
        
        # 停止温度监控
        if self.temperature_manager:
            self.temperature_manager.stop_all_monitoring()
        
        # 释放摄像头资源
        if self.camera_capture:
            self.camera_capture.release()
        
        # 清理检测器资源
        if self.heat_detector:
            self.heat_detector = None
        
        # 清理双光配准集成器资源
        if self.dual_camera_integration:
            self.dual_camera_integration.cleanup()
            self.dual_camera_integration = None
        
        self.logger.info("系统已停止 (Qt界面版本)")


def signal_handler(signum, frame):
    """信号处理器"""
    _ = signum, frame  # 忽略未使用的参数
    print("\n收到中断信号，正在退出...")
    QApplication.quit()


def main():
    """主函数 - Qt界面版本"""
    print("\n🎯 开始初始化系统...")

    # 设置信号处理器
    signal.signal(signal.SIGINT, signal_handler)

    # 创建系统实例
    system = ThermalCameraSystemQt()
    
    try:
        # 初始化系统
        if not system.initialize():
            print("❌ 系统初始化失败")
            QMessageBox.critical(None, "错误", "系统初始化失败，请检查配置和设备连接")
            return 1
        
        # 启动系统
        if not system.start():
            print("❌ 系统启动失败")
            QMessageBox.critical(None, "错误", "系统启动失败，请检查摄像头连接")
            return 1
        
        # 运行主循环
        return system.run()
        
    except Exception as e:
        print(f"❌ 程序异常: {e}")
        QMessageBox.critical(None, "严重错误", f"程序发生异常：{e}")
        return 1
    finally:
        system.stop()


if __name__ == "__main__":
    sys.exit(main())
