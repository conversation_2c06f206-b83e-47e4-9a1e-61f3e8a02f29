#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主线程适配器
为现有的同步AI处理调用提供异步适配接口
"""

import time
import threading
from typing import Dict, List, Optional, Any, Callable, Tuple
from dataclasses import dataclass
import numpy as np
from PyQt5.QtCore import QObject, pyqtSignal, QEventLoop, QTimer

from ai_processing_migrator import AIProcessingMigrator, MigrationConfig
from utils.logger import get_logger


@dataclass
class AdapterConfig:
    """适配器配置"""
    sync_timeout: float = 3.0  # 同步调用超时时间
    enable_caching: bool = True  # 启用结果缓存
    cache_ttl: float = 1.0  # 缓存生存时间（秒）
    fallback_enabled: bool = True  # 启用降级处理
    max_concurrent_requests: int = 10  # 最大并发请求数


class MainThreadAdapter(QObject):
    """主线程适配器"""
    
    # 信号定义
    detection_completed = pyqtSignal(str, dict)  # task_id, result
    detection_failed = pyqtSignal(str, str)  # task_id, error
    
    def __init__(self, config: AdapterConfig = None, parent=None):
        """
        初始化主线程适配器
        
        Args:
            config: 适配器配置
            parent: 父对象
        """
        super().__init__(parent)
        
        self.config = config or AdapterConfig()
        self.logger = get_logger("MainThreadAdapter")
        
        # 初始化AI处理迁移器
        migration_config = MigrationConfig(
            max_queue_size=50,
            processing_timeout=self.config.sync_timeout,
            result_cache_size=100
        )
        self.migrator = AIProcessingMigrator(migration_config, self)
        
        # 连接信号
        self._connect_signals()
        
        # 同步调用管理
        self.sync_requests = {}  # task_id -> event_loop
        self.sync_results = {}   # task_id -> result
        self.sync_lock = threading.Lock()
        
        # 结果缓存
        self.result_cache = {}   # cache_key -> (result, timestamp)
        self.cache_lock = threading.Lock()
        
        # 并发控制
        self.active_requests = 0
        self.request_lock = threading.Lock()
        
        self.logger.info("主线程适配器初始化完成")
    
    def _connect_signals(self):
        """连接信号槽"""
        self.migrator.human_detection_result.connect(self._on_detection_result)
        self.migrator.fire_smoke_detection_result.connect(self._on_detection_result)
        self.migrator.heat_detection_result.connect(self._on_detection_result)
        self.migrator.error_occurred.connect(self._on_detection_error)
    
    def initialize_detectors(self, 
                           human_detector=None,
                           fire_smoke_detector=None,
                           heat_detector=None):
        """
        初始化AI检测器
        
        Args:
            human_detector: 人体检测器实例
            fire_smoke_detector: 火焰烟雾检测器实例
            heat_detector: 热源检测器实例
        """
        self.migrator.initialize_detectors(
            human_detector=human_detector,
            fire_smoke_detector=fire_smoke_detector,
            heat_detector=heat_detector
        )
    
    def detect_humans_sync(self, frame: np.ndarray) -> Tuple[List[Dict], np.ndarray]:
        """
        同步人体检测（兼容原有接口）
        
        Args:
            frame: 输入图像帧
            
        Returns:
            (detections, annotated_frame)
        """
        # 检查缓存
        cache_key = self._generate_cache_key('human', frame)
        cached_result = self._get_cached_result(cache_key)
        if cached_result:
            return cached_result['detections'], cached_result['annotated_frame']
        
        # 检查并发限制
        if not self._check_concurrent_limit():
            self.logger.warning("达到最大并发限制，使用降级处理")
            return self._fallback_human_detection(frame)
        
        try:
            # 异步调用
            task_id = self.migrator.migrate_human_detection(frame)
            if not task_id:
                return self._fallback_human_detection(frame)
            
            # 等待结果
            result = self._wait_for_result(task_id)
            if result:
                detections = result.get('detections', [])
                annotated_frame = result.get('annotated_frame', frame.copy())
                
                # 缓存结果
                self._cache_result(cache_key, {
                    'detections': detections,
                    'annotated_frame': annotated_frame
                })
                
                return detections, annotated_frame
            else:
                return self._fallback_human_detection(frame)
                
        except Exception as e:
            self.logger.error(f"同步人体检测失败: {e}")
            return self._fallback_human_detection(frame)
        finally:
            self._release_concurrent_slot()
    
    def detect_fire_smoke_sync(self, frame: np.ndarray, 
                              detection_mode: str = 'single') -> List[Dict]:
        """
        同步火焰烟雾检测（兼容原有接口）
        
        Args:
            frame: 输入图像帧
            detection_mode: 检测模式
            
        Returns:
            检测结果列表
        """
        # 检查缓存
        cache_key = self._generate_cache_key(f'fire_smoke_{detection_mode}', frame)
        cached_result = self._get_cached_result(cache_key)
        if cached_result:
            return cached_result['detections']
        
        # 检查并发限制
        if not self._check_concurrent_limit():
            self.logger.warning("达到最大并发限制，使用降级处理")
            return self._fallback_fire_smoke_detection(frame)
        
        try:
            # 异步调用
            task_id = self.migrator.migrate_fire_smoke_detection(
                frame, detection_mode=detection_mode
            )
            if not task_id:
                return self._fallback_fire_smoke_detection(frame)
            
            # 等待结果
            result = self._wait_for_result(task_id)
            if result:
                detections = result.get('detections', [])
                
                # 缓存结果
                self._cache_result(cache_key, {'detections': detections})
                
                return detections
            else:
                return self._fallback_fire_smoke_detection(frame)
                
        except Exception as e:
            self.logger.error(f"同步火焰烟雾检测失败: {e}")
            return self._fallback_fire_smoke_detection(frame)
        finally:
            self._release_concurrent_slot()
    
    def detect_heat_sources_sync(self, frame: np.ndarray) -> Tuple[List[Dict], Dict]:
        """
        同步热源检测（兼容原有接口）
        
        Args:
            frame: 输入图像帧
            
        Returns:
            (detections, visualization_images)
        """
        # 检查缓存
        cache_key = self._generate_cache_key('heat', frame)
        cached_result = self._get_cached_result(cache_key)
        if cached_result:
            return cached_result['detections'], cached_result['vis_images']
        
        # 检查并发限制
        if not self._check_concurrent_limit():
            self.logger.warning("达到最大并发限制，使用降级处理")
            return self._fallback_heat_detection(frame)
        
        try:
            # 异步调用
            task_id = self.migrator.migrate_heat_detection(frame)
            if not task_id:
                return self._fallback_heat_detection(frame)
            
            # 等待结果
            result = self._wait_for_result(task_id)
            if result:
                detections = result.get('detections', [])
                annotated_frame = result.get('annotated_frame', frame.copy())
                vis_images = {'thermal_with_detections': annotated_frame}
                
                # 缓存结果
                self._cache_result(cache_key, {
                    'detections': detections,
                    'vis_images': vis_images
                })
                
                return detections, vis_images
            else:
                return self._fallback_heat_detection(frame)
                
        except Exception as e:
            self.logger.error(f"同步热源检测失败: {e}")
            return self._fallback_heat_detection(frame)
        finally:
            self._release_concurrent_slot()
    
    def detect_humans_async(self, frame: np.ndarray, 
                           callback: Callable[[List[Dict], np.ndarray], None]) -> str:
        """
        异步人体检测
        
        Args:
            frame: 输入图像帧
            callback: 结果回调函数
            
        Returns:
            任务ID
        """
        def result_callback(result_data):
            detections = result_data.detections
            annotated_frame = result_data.annotated_frame
            callback(detections, annotated_frame)
        
        return self.migrator.migrate_human_detection(frame, callback=result_callback)
    
    def detect_fire_smoke_async(self, frame: np.ndarray,
                               detection_mode: str = 'single',
                               callback: Callable[[List[Dict]], None] = None) -> str:
        """
        异步火焰烟雾检测
        
        Args:
            frame: 输入图像帧
            detection_mode: 检测模式
            callback: 结果回调函数
            
        Returns:
            任务ID
        """
        def result_callback(result_data):
            detections = result_data.detections
            if callback:
                callback(detections)
        
        return self.migrator.migrate_fire_smoke_detection(
            frame, detection_mode=detection_mode, callback=result_callback
        )
    
    def _wait_for_result(self, task_id: str) -> Optional[Dict]:
        """等待异步结果"""
        # 创建事件循环
        loop = QEventLoop()
        
        # 设置超时定时器
        timeout_timer = QTimer()
        timeout_timer.setSingleShot(True)
        timeout_timer.timeout.connect(loop.quit)
        timeout_timer.start(int(self.config.sync_timeout * 1000))
        
        # 注册等待
        with self.sync_lock:
            self.sync_requests[task_id] = loop
        
        # 等待结果或超时
        loop.exec_()
        
        # 清理
        timeout_timer.stop()
        with self.sync_lock:
            if task_id in self.sync_requests:
                del self.sync_requests[task_id]
            
            result = self.sync_results.get(task_id)
            if task_id in self.sync_results:
                del self.sync_results[task_id]
        
        return result
    
    def _on_detection_result(self, result_dict: Dict):
        """处理检测结果"""
        task_id = result_dict['task_id']
        
        with self.sync_lock:
            # 保存结果
            self.sync_results[task_id] = result_dict
            
            # 唤醒等待的事件循环
            if task_id in self.sync_requests:
                loop = self.sync_requests[task_id]
                loop.quit()
        
        # 发送信号
        self.detection_completed.emit(task_id, result_dict)
    
    def _on_detection_error(self, task_id: str, error_message: str):
        """处理检测错误"""
        with self.sync_lock:
            # 唤醒等待的事件循环
            if task_id in self.sync_requests:
                loop = self.sync_requests[task_id]
                loop.quit()
        
        # 发送信号
        self.detection_failed.emit(task_id, error_message)
    
    def _generate_cache_key(self, detection_type: str, frame: np.ndarray) -> str:
        """生成缓存键"""
        # 使用帧的哈希值作为缓存键的一部分
        frame_hash = hash(frame.tobytes())
        return f"{detection_type}_{frame_hash}_{frame.shape}"
    
    def _get_cached_result(self, cache_key: str) -> Optional[Dict]:
        """获取缓存结果"""
        if not self.config.enable_caching:
            return None
        
        with self.cache_lock:
            if cache_key in self.result_cache:
                result, timestamp = self.result_cache[cache_key]
                if time.time() - timestamp < self.config.cache_ttl:
                    return result
                else:
                    # 缓存过期，删除
                    del self.result_cache[cache_key]
        
        return None
    
    def _cache_result(self, cache_key: str, result: Dict):
        """缓存结果"""
        if not self.config.enable_caching:
            return
        
        with self.cache_lock:
            self.result_cache[cache_key] = (result, time.time())
            
            # 限制缓存大小
            if len(self.result_cache) > 100:
                # 删除最旧的缓存项
                oldest_key = min(self.result_cache.keys(),
                               key=lambda k: self.result_cache[k][1])
                del self.result_cache[oldest_key]
    
    def _check_concurrent_limit(self) -> bool:
        """检查并发限制"""
        with self.request_lock:
            if self.active_requests >= self.config.max_concurrent_requests:
                return False
            self.active_requests += 1
            return True
    
    def _release_concurrent_slot(self):
        """释放并发槽位"""
        with self.request_lock:
            if self.active_requests > 0:
                self.active_requests -= 1
    
    def _fallback_human_detection(self, frame: np.ndarray) -> Tuple[List[Dict], np.ndarray]:
        """人体检测降级处理"""
        if self.config.fallback_enabled:
            # 返回空结果
            return [], frame.copy()
        else:
            raise RuntimeError("人体检测失败且降级处理已禁用")
    
    def _fallback_fire_smoke_detection(self, frame: np.ndarray) -> List[Dict]:
        """火焰烟雾检测降级处理"""
        if self.config.fallback_enabled:
            # 返回空结果
            return []
        else:
            raise RuntimeError("火焰烟雾检测失败且降级处理已禁用")
    
    def _fallback_heat_detection(self, frame: np.ndarray) -> Tuple[List[Dict], Dict]:
        """热源检测降级处理"""
        if self.config.fallback_enabled:
            # 返回空结果
            return [], {'thermal_with_detections': frame.copy()}
        else:
            raise RuntimeError("热源检测失败且降级处理已禁用")
    
    def get_adapter_stats(self) -> Dict:
        """获取适配器统计信息"""
        with self.request_lock:
            active_requests = self.active_requests
        
        with self.cache_lock:
            cache_size = len(self.result_cache)
        
        return {
            'active_requests': active_requests,
            'max_concurrent_requests': self.config.max_concurrent_requests,
            'cache_size': cache_size,
            'migrator_stats': self.migrator.get_performance_stats(),
            'queue_status': self.migrator.get_queue_status()
        }
    
    def shutdown(self):
        """关闭适配器"""
        self.logger.info("正在关闭主线程适配器...")
        
        # 关闭迁移器
        self.migrator.shutdown()
        
        # 清理资源
        with self.sync_lock:
            self.sync_requests.clear()
            self.sync_results.clear()
        
        with self.cache_lock:
            self.result_cache.clear()
        
        self.logger.info("主线程适配器已关闭")
