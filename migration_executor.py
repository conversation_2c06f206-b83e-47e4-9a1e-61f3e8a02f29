#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PyTorch到RKNN迁移执行器
自动化执行从PyTorch到RKNN的迁移过程
"""

import os
import shutil
import re
from pathlib import Path
from typing import Dict, List, Any, Optional
import json
import time

from engine_replacement_config import get_migration_config
from utils.logger import get_logger


class MigrationExecutor:
    """迁移执行器"""
    
    def __init__(self, dry_run: bool = False):
        """
        初始化迁移执行器
        
        Args:
            dry_run: 是否为试运行模式（不实际修改文件）
        """
        self.logger = get_logger("MigrationExecutor")
        self.dry_run = dry_run
        self.config = get_migration_config()
        self.migration_plan = self.config.get_migration_plan()
        
        # 迁移状态
        self.migration_log = []
        self.backup_dir = Path("migration_backup") / f"backup_{int(time.time())}"
        self.errors = []
        self.warnings = []
        
        if dry_run:
            self.logger.info("🔍 迁移执行器启动 (试运行模式)")
        else:
            self.logger.info("🚀 迁移执行器启动 (实际执行模式)")
    
    def execute_migration(self) -> bool:
        """
        执行完整的迁移过程
        
        Returns:
            是否成功完成迁移
        """
        try:
            self.logger.info("开始执行PyTorch到RKNN迁移")
            
            # 1. 验证迁移准备情况
            if not self._validate_migration_readiness():
                return False
            
            # 2. 创建备份目录
            if not self._create_backup_directory():
                return False
            
            # 3. 执行迁移步骤
            for step in self.migration_plan['migration_steps']:
                if not self._execute_migration_step(step):
                    self.logger.error(f"迁移步骤失败: {step['name']}")
                    return False
            
            # 4. 生成迁移报告
            self._generate_migration_report()
            
            self.logger.info("✅ PyTorch到RKNN迁移完成")
            return True
            
        except Exception as e:
            self.logger.error(f"迁移执行失败: {e}")
            self.errors.append(f"迁移执行异常: {e}")
            return False
    
    def _validate_migration_readiness(self) -> bool:
        """验证迁移准备情况"""
        self.logger.info("🔍 验证迁移准备情况...")
        
        validation_result = self.config.validate_migration_readiness()
        
        if not validation_result['ready']:
            self.logger.error("迁移准备验证失败:")
            for error in validation_result['errors']:
                self.logger.error(f"  ❌ {error}")
                self.errors.append(error)
            
            for warning in validation_result['warnings']:
                self.logger.warning(f"  ⚠️ {warning}")
                self.warnings.append(warning)
            
            return False
        
        if validation_result['warnings']:
            self.logger.info("发现警告信息:")
            for warning in validation_result['warnings']:
                self.logger.warning(f"  ⚠️ {warning}")
                self.warnings.append(warning)
        
        self.logger.info("✅ 迁移准备验证通过")
        return True
    
    def _create_backup_directory(self) -> bool:
        """创建备份目录"""
        try:
            if not self.dry_run:
                self.backup_dir.mkdir(parents=True, exist_ok=True)
                self.logger.info(f"📁 备份目录已创建: {self.backup_dir}")
            else:
                self.logger.info(f"📁 [试运行] 将创建备份目录: {self.backup_dir}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"创建备份目录失败: {e}")
            self.errors.append(f"创建备份目录失败: {e}")
            return False
    
    def _execute_migration_step(self, step: Dict[str, Any]) -> bool:
        """执行单个迁移步骤"""
        step_name = step['name']
        step_action = step['action']
        
        self.logger.info(f"🔄 执行步骤 {step['step']}: {step_name}")
        
        try:
            if step_action == "backup":
                return self._backup_files(step['files'])
            elif step_action == "create":
                return self._create_files(step['files'])
            elif step_action == "replace":
                return self._replace_files(step['files'])
            elif step_action == "update_imports":
                return self._update_imports()
            elif step_action == "update_dependencies":
                return self._update_dependencies(step['files'])
            elif step_action == "integrate":
                return self._integrate_ai_worker(step['files'])
            elif step_action == "test":
                return self._run_tests()
            else:
                self.logger.warning(f"未知的迁移动作: {step_action}")
                return True
                
        except Exception as e:
            self.logger.error(f"执行迁移步骤失败 [{step_name}]: {e}")
            self.errors.append(f"步骤 {step['step']} 失败: {e}")
            return False
    
    def _backup_files(self, files: List[str]) -> bool:
        """备份文件"""
        self.logger.info("📋 备份原始文件...")
        
        for file_path in files:
            try:
                source_path = Path(file_path)
                if not source_path.exists():
                    self.logger.warning(f"文件不存在，跳过备份: {file_path}")
                    continue
                
                # 创建备份路径
                backup_path = self.backup_dir / file_path
                backup_path.parent.mkdir(parents=True, exist_ok=True)
                
                if not self.dry_run:
                    shutil.copy2(source_path, backup_path)
                    self.logger.info(f"  ✅ 已备份: {file_path}")
                else:
                    self.logger.info(f"  📋 [试运行] 将备份: {file_path}")
                
                self.migration_log.append(f"备份文件: {file_path}")
                
            except Exception as e:
                self.logger.error(f"备份文件失败 [{file_path}]: {e}")
                self.errors.append(f"备份文件失败 [{file_path}]: {e}")
                return False
        
        return True
    
    def _create_files(self, files: List[str]) -> bool:
        """创建新文件"""
        self.logger.info("📝 创建新文件...")
        
        # 这些文件已经在之前的步骤中创建了
        created_files = [
            "detection/core/rknn_inference_engine.py",
            "detection/core/rknn_human_detector.py", 
            "detection/core/rknn_fire_smoke_detector.py",
            "detection/processors/rknn_data_processor.py",
            "ai_worker.py",
            "ai_worker_config.py",
            "ai_worker_utils.py",
            "engine_replacement_config.py"
        ]
        
        for file_path in files:
            if file_path in created_files:
                if Path(file_path).exists():
                    self.logger.info(f"  ✅ 文件已存在: {file_path}")
                    self.migration_log.append(f"创建文件: {file_path}")
                else:
                    self.logger.warning(f"  ⚠️ 文件不存在: {file_path}")
                    self.warnings.append(f"预期文件不存在: {file_path}")
            else:
                self.logger.info(f"  📝 [待创建] {file_path}")
        
        return True
    
    def _replace_files(self, files: List[str]) -> bool:
        """替换文件"""
        self.logger.info("🔄 替换文件...")
        
        # 这一步主要是确认RKNN检测器文件已创建
        for file_path in files:
            if Path(file_path).exists():
                self.logger.info(f"  ✅ RKNN检测器已创建: {file_path}")
                self.migration_log.append(f"替换文件: {file_path}")
            else:
                self.logger.warning(f"  ⚠️ RKNN检测器文件不存在: {file_path}")
        
        return True
    
    def _update_imports(self) -> bool:
        """更新导入语句"""
        self.logger.info("📦 更新导入语句...")
        
        import_replacements = self.migration_plan['import_replacements']
        python_files = list(Path('.').rglob('*.py'))
        
        updated_files = []
        
        for py_file in python_files:
            # 跳过备份文件和虚拟环境
            if ('backup' in str(py_file) or 
                'venv' in str(py_file) or 
                'tj310' in str(py_file) or
                'tj' in str(py_file)):
                continue
            
            try:
                if not py_file.exists():
                    continue
                
                # 读取文件内容
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                original_content = content
                
                # 应用导入替换
                for old_import, new_import in import_replacements.items():
                    if old_import in content:
                        content = content.replace(old_import, new_import)
                        self.logger.info(f"  🔄 更新导入: {py_file.name}")
                
                # 如果内容有变化，写回文件
                if content != original_content and not self.dry_run:
                    with open(py_file, 'w', encoding='utf-8') as f:
                        f.write(content)
                    updated_files.append(str(py_file))
                elif content != original_content:
                    self.logger.info(f"  📝 [试运行] 将更新: {py_file}")
                    updated_files.append(str(py_file))
                
            except Exception as e:
                self.logger.error(f"更新导入失败 [{py_file}]: {e}")
                self.errors.append(f"更新导入失败 [{py_file}]: {e}")
        
        self.logger.info(f"✅ 已更新 {len(updated_files)} 个文件的导入语句")
        self.migration_log.extend([f"更新导入: {f}" for f in updated_files])
        
        return True
    
    def _update_dependencies(self, files: List[str]) -> bool:
        """更新依赖文件"""
        self.logger.info("📦 更新依赖文件...")
        
        dependency_updates = self.migration_plan['dependency_updates']
        
        for file_path in files:
            try:
                file_path_obj = Path(file_path)
                if not file_path_obj.exists():
                    self.logger.warning(f"依赖文件不存在: {file_path}")
                    continue
                
                # 读取文件内容
                with open(file_path_obj, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                original_content = content
                
                # 注释掉要移除的依赖
                for dep in dependency_updates['remove_dependencies']:
                    if dep in content:
                        content = content.replace(dep, f"# {dep}  # 已替换为RKNN")
                        self.logger.info(f"  🔄 注释依赖: {dep}")
                
                # 添加新的依赖部分
                if file_path in dependency_updates['requirements_file_updates']:
                    updates = dependency_updates['requirements_file_updates'][file_path]
                    if 'add_section' in updates:
                        content += "\n" + updates['add_section']
                        self.logger.info(f"  ➕ 添加RKNN依赖部分")
                
                # 写回文件
                if content != original_content and not self.dry_run:
                    with open(file_path_obj, 'w', encoding='utf-8') as f:
                        f.write(content)
                    self.logger.info(f"  ✅ 已更新: {file_path}")
                elif content != original_content:
                    self.logger.info(f"  📝 [试运行] 将更新: {file_path}")
                
                self.migration_log.append(f"更新依赖: {file_path}")
                
            except Exception as e:
                self.logger.error(f"更新依赖文件失败 [{file_path}]: {e}")
                self.errors.append(f"更新依赖文件失败 [{file_path}]: {e}")
                return False
        
        return True
    
    def _integrate_ai_worker(self, files: List[str]) -> bool:
        """集成AI工作线程"""
        self.logger.info("🔗 集成AI工作线程...")
        
        # 这一步需要手动修改主要文件
        integration_notes = [
            "需要在main.py中集成AI_Worker线程",
            "需要在system_initializer.py中初始化RKNN检测器",
            "需要在frame_processor.py中使用AI工作线程",
            "需要在Qt界面中连接AI工作线程的信号"
        ]
        
        for note in integration_notes:
            self.logger.info(f"  📋 {note}")
            self.migration_log.append(f"集成说明: {note}")
        
        return True
    
    def _run_tests(self) -> bool:
        """运行测试"""
        self.logger.info("🧪 运行测试...")
        
        # 基本的验证测试
        test_results = []
        
        # 测试RKNN库是否可用
        try:
            import rknnlite
            test_results.append("✅ rknnlite库可用")
        except ImportError:
            test_results.append("❌ rknnlite库不可用")
            self.errors.append("rknnlite库测试失败")
        
        # 测试新创建的模块是否可导入
        test_modules = [
            "detection.core.rknn_inference_engine",
            "detection.core.rknn_human_detector", 
            "detection.core.rknn_fire_smoke_detector",
            "ai_worker",
            "ai_worker_config"
        ]
        
        for module_name in test_modules:
            try:
                __import__(module_name)
                test_results.append(f"✅ 模块导入成功: {module_name}")
            except ImportError as e:
                test_results.append(f"❌ 模块导入失败: {module_name} - {e}")
                self.errors.append(f"模块导入测试失败: {module_name}")
        
        for result in test_results:
            self.logger.info(f"  {result}")
        
        return len(self.errors) == 0
    
    def _generate_migration_report(self):
        """生成迁移报告"""
        report_path = self.backup_dir / "migration_report.json"
        
        report = {
            "migration_timestamp": time.time(),
            "migration_mode": "dry_run" if self.dry_run else "actual",
            "migration_log": self.migration_log,
            "errors": self.errors,
            "warnings": self.warnings,
            "backup_directory": str(self.backup_dir),
            "migration_plan": self.migration_plan
        }
        
        if not self.dry_run:
            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"📊 迁移报告已生成: {report_path}")
        else:
            self.logger.info(f"📊 [试运行] 迁移报告将生成: {report_path}")
        
        # 打印摘要
        self._print_migration_summary()
    
    def _print_migration_summary(self):
        """打印迁移摘要"""
        print("\n" + "="*60)
        print("PyTorch到RKNN迁移摘要")
        print("="*60)
        
        print(f"迁移模式: {'试运行' if self.dry_run else '实际执行'}")
        print(f"备份目录: {self.backup_dir}")
        print(f"迁移日志条目: {len(self.migration_log)}")
        print(f"错误数量: {len(self.errors)}")
        print(f"警告数量: {len(self.warnings)}")
        
        if self.errors:
            print("\n❌ 错误:")
            for error in self.errors:
                print(f"  - {error}")
        
        if self.warnings:
            print("\n⚠️ 警告:")
            for warning in self.warnings:
                print(f"  - {warning}")
        
        if not self.errors:
            print("\n✅ 迁移成功完成!")
        else:
            print("\n❌ 迁移过程中发现错误，请检查日志")
        
        print("="*60)


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="PyTorch到RKNN迁移执行器")
    parser.add_argument("--dry-run", action="store_true", 
                       help="试运行模式，不实际修改文件")
    parser.add_argument("--show-plan", action="store_true",
                       help="显示迁移计划")
    
    args = parser.parse_args()
    
    if args.show_plan:
        from engine_replacement_config import print_migration_summary
        print_migration_summary()
        return
    
    # 执行迁移
    executor = MigrationExecutor(dry_run=args.dry_run)
    success = executor.execute_migration()
    
    if success:
        print("\n🎉 迁移执行完成!")
        if args.dry_run:
            print("💡 这是试运行模式，没有实际修改文件")
            print("💡 要执行实际迁移，请运行: python migration_executor.py")
    else:
        print("\n❌ 迁移执行失败，请检查错误信息")
        exit(1)


if __name__ == "__main__":
    main()
