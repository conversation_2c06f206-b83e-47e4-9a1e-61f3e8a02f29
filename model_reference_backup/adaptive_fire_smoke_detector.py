#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自适应火焰烟雾检测器
集成测试验证的自适应阈值策略到正式系统
"""

import cv2
import numpy as np
import time
from typing import List, Dict, Tuple, Optional
from pathlib import Path

from utils.logger import get_logger
from .fire_smoke_detector import FireSmokeDetector


class AdaptiveFireSmokeDetector:
    """
    自适应火焰烟雾检测器
    基于测试验证的策略，提供更准确的火焰检测
    """
    
    def __init__(self, 
                 model_path: str = "models/fire_detection/best.pt",
                 device: str = "cpu"):
        """
        初始化自适应检测器
        
        Args:
            model_path: 模型文件路径
            device: 推理设备
        """
        self.logger = get_logger("AdaptiveFireSmokeDetector")
        
        # 基础检测器
        self.base_detector = FireSmokeDetector(
            model_path=model_path,
            confidence_threshold=0.1,  # 使用低阈值获取所有检测
            device=device,
            iou_threshold=0.45
        )
        
        # 自适应阈值配置
        self.fire_confidence_threshold = 0.1    # 火焰使用更低阈值
        self.smoke_confidence_threshold = 0.5   # 烟雾使用更高阈值
        
        # 火焰增强策略配置
        self.enable_fire_enhancement = True     # 启用火焰增强检测
        self.fire_color_boost = True           # 启用颜色增强
        self.color_enhancement_threshold = 0.7  # 颜色增强阈值
        self.fire_color_score_weight = 0.7     # 颜色特征权重
        self.brightness_weight = 0.3           # 亮度权重
        
        # 统计信息
        self.total_detections = 0
        self.enhanced_detections = 0
        self.fire_detections = 0
        self.smoke_detections = 0
        
        self.logger.info("自适应火焰烟雾检测器初始化完成")
        self.logger.info(f"火焰阈值: {self.fire_confidence_threshold}")
        self.logger.info(f"烟雾阈值: {self.smoke_confidence_threshold}")
        self.logger.info(f"火焰增强: {'启用' if self.enable_fire_enhancement else '禁用'}")
    
    def detect(self, frame: np.ndarray) -> Tuple[List[Dict], List[Dict], np.ndarray]:
        """
        自适应检测方法
        
        Args:
            frame: 输入图像帧
            
        Returns:
            (fire_detections, smoke_detections, annotated_frame)
        """
        try:
            # 1. 获取原始检测结果
            raw_detections, annotated_frame = self.base_detector.detect(frame)
            
            # 2. 分离并过滤检测结果
            fire_detections = []
            smoke_detections = []
            
            for detection in raw_detections:
                class_name = detection.get('class_name', 'unknown')
                confidence = detection.get('confidence', 0.0)
                
                if class_name == 'fire':
                    # 火焰使用更宽松的阈值
                    if confidence >= self.fire_confidence_threshold:
                        fire_detections.append(detection)
                        self.fire_detections += 1
                elif class_name == 'smoke':
                    # 烟雾使用更严格的阈值
                    if confidence >= self.smoke_confidence_threshold:
                        smoke_detections.append(detection)
                        self.smoke_detections += 1
            
            # 3. 火焰增强检测
            if self.enable_fire_enhancement:
                enhanced_fire = self._enhance_fire_detection(frame, raw_detections)
                fire_detections.extend(enhanced_fire)
            
            # 4. 更新统计
            self.total_detections += len(fire_detections) + len(smoke_detections)
            
            # 5. 重新绘制检测结果
            final_frame = self._draw_adaptive_results(frame, fire_detections, smoke_detections)
            
            return fire_detections, smoke_detections, final_frame
            
        except Exception as e:
            self.logger.error(f"自适应检测失败: {e}")
            return [], [], frame.copy()
    
    def _enhance_fire_detection(self, frame: np.ndarray, raw_detections: List[Dict]) -> List[Dict]:
        """
        火焰增强检测策略
        将高置信度的烟雾检测重新评估为火焰
        """
        enhanced_fire = []
        
        if not self.fire_color_boost:
            return enhanced_fire
        
        for detection in raw_detections:
            if detection.get('class_name') == 'smoke':
                confidence = detection.get('confidence', 0.0)
                bbox = detection.get('bbox', [0, 0, 0, 0])
                
                # 如果烟雾置信度很高，检查是否可能是火焰
                if confidence > self.color_enhancement_threshold:
                    # 提取检测区域
                    x, y, w, h = bbox
                    if x >= 0 and y >= 0 and x + w <= frame.shape[1] and y + h <= frame.shape[0]:
                        roi = frame[y:y+h, x:x+w]
                        
                        if roi.size > 0:
                            # 分析颜色特征
                            fire_score = self._analyze_fire_color(roi)
                            
                            if fire_score > 0.3:  # 如果有火焰颜色特征
                                # 创建增强的火焰检测
                                enhanced_detection = detection.copy()
                                enhanced_detection['class_name'] = 'fire'
                                enhanced_detection['confidence'] = fire_score
                                enhanced_detection['source'] = 'color_enhanced'
                                enhanced_detection['original_confidence'] = confidence
                                enhanced_fire.append(enhanced_detection)
                                
                                self.enhanced_detections += 1
                                self.logger.debug(f"颜色增强: 烟雾(置信度:{confidence:.3f})重新分类为火焰(分数:{fire_score:.3f})")
        
        return enhanced_fire
    
    def _analyze_fire_color(self, roi: np.ndarray) -> float:
        """
        分析区域的火焰颜色特征
        
        Args:
            roi: 感兴趣区域
            
        Returns:
            火焰颜色分数 (0-1)
        """
        if roi.size == 0:
            return 0.0
        
        try:
            # 转换到HSV颜色空间
            hsv = cv2.cvtColor(roi, cv2.COLOR_BGR2HSV)
            
            # 定义火焰颜色范围
            # 红色范围1 (0-10)
            lower_red1 = np.array([0, 50, 50])
            upper_red1 = np.array([10, 255, 255])
            mask1 = cv2.inRange(hsv, lower_red1, upper_red1)
            
            # 红色范围2 (170-180)
            lower_red2 = np.array([170, 50, 50])
            upper_red2 = np.array([180, 255, 255])
            mask2 = cv2.inRange(hsv, lower_red2, upper_red2)
            
            # 橙色范围 (10-25)
            lower_orange = np.array([10, 50, 50])
            upper_orange = np.array([25, 255, 255])
            mask3 = cv2.inRange(hsv, lower_orange, upper_orange)
            
            # 黄色范围 (25-35)
            lower_yellow = np.array([25, 50, 50])
            upper_yellow = np.array([35, 255, 255])
            mask4 = cv2.inRange(hsv, lower_yellow, upper_yellow)
            
            # 合并所有火焰颜色掩码
            fire_mask = mask1 + mask2 + mask3 + mask4
            
            # 计算火焰颜色像素比例
            fire_pixels = np.sum(fire_mask > 0)
            total_pixels = roi.shape[0] * roi.shape[1]
            
            fire_ratio = fire_pixels / total_pixels if total_pixels > 0 else 0
            
            # 额外检查亮度
            gray = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)
            brightness = np.mean(gray) / 255.0
            
            # 综合评分
            fire_score = fire_ratio * self.fire_color_score_weight + brightness * self.brightness_weight
            
            return min(fire_score, 1.0)
            
        except Exception as e:
            self.logger.error(f"颜色分析失败: {e}")
            return 0.0
    
    def _draw_adaptive_results(self, frame: np.ndarray, fire_detections: List[Dict], smoke_detections: List[Dict]) -> np.ndarray:
        """绘制自适应检测结果"""
        result_frame = frame.copy()
        
        # 绘制火焰检测
        for detection in fire_detections:
            bbox = detection.get('bbox', [0, 0, 0, 0])
            confidence = detection.get('confidence', 0.0)
            source = detection.get('source', 'original')
            
            x, y, w, h = bbox
            
            # 根据来源选择颜色和标签
            if source == 'color_enhanced':
                color = (0, 255, 255)  # 黄色 - 增强检测
                label = f"FIRE(Enhanced): {confidence:.2f}"
            else:
                color = (0, 0, 255)    # 红色 - 原始检测
                label = f"FIRE: {confidence:.2f}"
            
            cv2.rectangle(result_frame, (x, y), (x+w, y+h), color, 3)
            cv2.putText(result_frame, label, (x, y-10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
        
        # 绘制烟雾检测
        for detection in smoke_detections:
            bbox = detection.get('bbox', [0, 0, 0, 0])
            confidence = detection.get('confidence', 0.0)
            
            x, y, w, h = bbox
            color = (128, 128, 128)  # 灰色
            label = f"SMOKE: {confidence:.2f}"
            
            cv2.rectangle(result_frame, (x, y), (x+w, y+h), color, 2)
            cv2.putText(result_frame, label, (x, y-10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
        
        return result_frame
    
    def get_statistics(self) -> Dict:
        """获取检测统计信息"""
        return {
            'total_detections': self.total_detections,
            'fire_detections': self.fire_detections,
            'smoke_detections': self.smoke_detections,
            'enhanced_detections': self.enhanced_detections,
            'enhancement_ratio': self.enhanced_detections / max(self.fire_detections, 1),
            'fire_confidence_threshold': self.fire_confidence_threshold,
            'smoke_confidence_threshold': self.smoke_confidence_threshold
        }
    
    def update_thresholds(self, fire_threshold: float = None, smoke_threshold: float = None):
        """动态更新阈值"""
        if fire_threshold is not None:
            self.fire_confidence_threshold = fire_threshold
            self.logger.info(f"火焰阈值更新为: {fire_threshold}")
        
        if smoke_threshold is not None:
            self.smoke_confidence_threshold = smoke_threshold
            self.logger.info(f"烟雾阈值更新为: {smoke_threshold}")
    
    def enable_enhancement(self, enable: bool = True):
        """启用/禁用火焰增强"""
        self.enable_fire_enhancement = enable
        self.logger.info(f"火焰增强: {'启用' if enable else '禁用'}")
