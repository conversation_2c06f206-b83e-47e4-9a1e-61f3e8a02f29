"""
配置管理模块
兼容原config.yaml文件，提供配置加载和管理功能
"""

import yaml
import json
import logging
from pathlib import Path
from typing import Dict, Any, Optional, Union
import copy


class ConfigManager:
    """配置管理器 - 兼容原config.yaml"""
    
    def __init__(self, config_source: Union[str, Dict, None] = None):
        """
        初始化配置管理器
        
        Args:
            config_source: 配置来源，可以是文件路径、配置字典或None
        """
        self.logger = logging.getLogger("ConfigManager")
        if not self.logger.handlers:
            # 如果没有处理器，添加一个控制台处理器
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
        self._config = {}
        self._default_config = self._get_default_config()
        
        if config_source is not None:
            self.load_config(config_source)
        else:
            self._config = copy.deepcopy(self._default_config)
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'model': {
                'weights_path': 'models/best.pt',
                'fire_weights_path': 'models/fire_model.pt',
                'smoke_weights_path': 'models/smoke_model.pt',
                'confidence_threshold': 0.5,
                'iou_threshold': 0.45,
                'device': 'auto',
                'img_size': 640,
                'smoke_class_mapping': {0: 1, 1: 0}  # 烟雾模型类别反转
            },
            'classes': {
                'fire': 0,
                'smoke': 1
            },
            'class_names': {
                0: 'fire',
                1: 'smoke'
            },
            'colors': {
                'fire': [0, 0, 255],    # 红色 (BGR)
                'smoke': [128, 128, 128]  # 灰色 (BGR)
            },
            'tracking': {
                'enabled': False,
                'max_disappeared': 10,
                'max_distance': 100,
                'output_path': 'tracking_analysis.xlsx'
            },
            'analysis': {
                'flicker_window_size': 15,
                'extract_geometric_features': True,
                'extract_image_features': True,
                'extract_motion_features': True
            },
            'output': {
                'save_results': True,
                'output_dir': 'data/output/',
                'show_results': True,
                'save_txt': True,
                'save_conf': True
            },
            'logging': {
                'level': 'INFO',
                'log_file': 'logs/detection.log',
                'console_output': True
            },
            'alert': {
                'enabled': True,
                'frame_threshold': 3,
                'cooldown_time': 10,
                'methods': ['console']
            },
            'performance': {
                'max_fps': 30,
                'use_threading': True,
                'num_threads': 0
            }
        }
    
    def load_config(self, config_source: Union[str, Dict]):
        """
        加载配置
        
        Args:
            config_source: 配置来源，文件路径或配置字典
        """
        if isinstance(config_source, dict):
            # 从字典加载
            self._config = self._merge_config(self._default_config, config_source)
            self.logger.info("从字典加载配置完成")
        elif isinstance(config_source, str):
            # 从文件加载
            self._load_from_file(config_source)
        else:
            raise ValueError(f"不支持的配置源类型: {type(config_source)}")
    
    def _load_from_file(self, config_path: str):
        """从文件加载配置"""
        config_path = Path(config_path)
        
        if not config_path.exists():
            self.logger.warning(f"配置文件不存在: {config_path}，使用默认配置")
            self._config = copy.deepcopy(self._default_config)
            return
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                if config_path.suffix.lower() in ['.yaml', '.yml']:
                    file_config = yaml.safe_load(f)
                elif config_path.suffix.lower() == '.json':
                    file_config = json.load(f)
                else:
                    raise ValueError(f"不支持的配置文件格式: {config_path.suffix}")
            
            # 合并配置
            self._config = self._merge_config(self._default_config, file_config)
            self.logger.info(f"成功加载配置文件: {config_path}")
            
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            self._config = copy.deepcopy(self._default_config)
    
    def _merge_config(self, default: Dict, override: Dict) -> Dict:
        """递归合并配置字典"""
        result = copy.deepcopy(default)
        
        for key, value in override.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_config(result[key], value)
            else:
                result[key] = value
        
        return result
    
    def get_detection_config(self) -> Dict:
        """
        获取检测相关配置
        
        Returns:
            检测配置字典
        """
        return {
            'confidence_threshold': self._config['model']['confidence_threshold'],
            'iou_threshold': self._config['model']['iou_threshold'],
            'device': self._config['model']['device'],
            'class_names': self._config['class_names'],
            'colors': self._config['colors'],
            'smoke_class_mapping': self._config['model']['smoke_class_mapping'],
            'single_model_path': self._config['model']['weights_path'],
            'fire_model_path': self._config['model']['fire_weights_path'],
            'smoke_model_path': self._config['model']['smoke_weights_path']
        }
    
    def get_tracking_config(self) -> Dict:
        """
        获取跟踪相关配置
        
        Returns:
            跟踪配置字典
        """
        return {
            'enabled': self._config['tracking']['enabled'],
            'max_disappeared': self._config['tracking']['max_disappeared'],
            'max_distance': self._config['tracking']['max_distance'],
            'output_path': self._config['tracking']['output_path']
        }
    
    def get_analysis_config(self) -> Dict:
        """
        获取分析相关配置
        
        Returns:
            分析配置字典
        """
        return {
            'flicker_window_size': self._config['analysis']['flicker_window_size'],
            'extract_geometric_features': self._config['analysis']['extract_geometric_features'],
            'extract_image_features': self._config['analysis']['extract_image_features'],
            'extract_motion_features': self._config['analysis']['extract_motion_features']
        }
    
    def get_data_config(self) -> Dict:
        """
        获取数据导出相关配置
        
        Returns:
            数据配置字典
        """
        return {
            'save_results': self._config['output']['save_results'],
            'output_dir': self._config['output']['output_dir'],
            'save_txt': self._config['output']['save_txt'],
            'save_conf': self._config['output']['save_conf']
        }
    
    def get_full_config(self) -> Dict:
        """
        获取完整配置
        
        Returns:
            完整配置字典
        """
        return copy.deepcopy(self._config)
    
    def update_config(self, updates: Dict):
        """
        动态更新配置
        
        Args:
            updates: 更新的配置项
        """
        self._config = self._merge_config(self._config, updates)
        self.logger.info(f"配置已更新: {list(updates.keys())}")
    
    def update_detection_thresholds(self, confidence: float, iou: float):
        """
        更新检测阈值
        
        Args:
            confidence: 置信度阈值
            iou: IoU阈值
        """
        self._config['model']['confidence_threshold'] = confidence
        self._config['model']['iou_threshold'] = iou
        self.logger.info(f"检测阈值已更新: confidence={confidence:.2f}, iou={iou:.2f}")
    
    def update_model_paths(self, single_path: Optional[str] = None, 
                          fire_path: Optional[str] = None, 
                          smoke_path: Optional[str] = None):
        """
        更新模型路径
        
        Args:
            single_path: 单权重模型路径
            fire_path: 火焰检测模型路径
            smoke_path: 烟雾检测模型路径
        """
        if single_path is not None:
            self._config['model']['weights_path'] = single_path
        if fire_path is not None:
            self._config['model']['fire_weights_path'] = fire_path
        if smoke_path is not None:
            self._config['model']['smoke_weights_path'] = smoke_path
        
        self.logger.info("模型路径已更新")
    
    def save_config(self, output_path: str):
        """
        保存配置到文件
        
        Args:
            output_path: 输出文件路径
        """
        output_path = Path(output_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                if output_path.suffix.lower() in ['.yaml', '.yml']:
                    yaml.dump(self._config, f, default_flow_style=False, 
                             allow_unicode=True, indent=2)
                elif output_path.suffix.lower() == '.json':
                    json.dump(self._config, f, ensure_ascii=False, indent=2)
                else:
                    raise ValueError(f"不支持的输出格式: {output_path.suffix}")
            
            self.logger.info(f"配置已保存到: {output_path}")
            
        except Exception as e:
            self.logger.error(f"保存配置失败: {e}")
            raise
    
    def validate_config(self) -> bool:
        """
        验证配置的有效性
        
        Returns:
            配置是否有效
        """
        try:
            # 检查必要的配置项
            required_keys = [
                'model', 'class_names', 'colors', 'tracking', 
                'analysis', 'output', 'logging'
            ]
            
            for key in required_keys:
                if key not in self._config:
                    self.logger.error(f"缺少必要配置项: {key}")
                    return False
            
            # 检查阈值范围
            conf_threshold = self._config['model']['confidence_threshold']
            iou_threshold = self._config['model']['iou_threshold']
            
            if not (0.0 <= conf_threshold <= 1.0):
                self.logger.error(f"置信度阈值超出范围: {conf_threshold}")
                return False
            
            if not (0.0 <= iou_threshold <= 1.0):
                self.logger.error(f"IoU阈值超出范围: {iou_threshold}")
                return False
            
            self.logger.info("配置验证通过")
            return True
            
        except Exception as e:
            self.logger.error(f"配置验证失败: {e}")
            return False