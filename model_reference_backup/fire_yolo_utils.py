#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
火灾检测专用YOLO工具模块
提供火灾检测相关的YOLO模型管理和配置功能
"""

import os
import json
import yaml
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from pathlib import Path

from utils.logger import get_logger

try:
    from ultralytics import YOLO
    ULTRALYTICS_AVAILABLE = True
except ImportError:
    ULTRALYTICS_AVAILABLE = False


@dataclass
class FireModelConfig:
    """火灾检测模型配置"""
    model_name: str
    model_path: str
    model_type: str  # "flame", "smoke", "fire_combined"
    confidence_threshold: float
    iou_threshold: float
    input_size: int
    device: str
    class_names: List[str]
    description: str = ""
    version: str = "1.0"
    created_date: str = ""
    last_updated: str = ""


class FireYOLOModelManager:
    """火灾检测YOLO模型管理器"""
    
    def __init__(self, models_dir: str = "models/fire_detection"):
        """
        初始化模型管理器
        
        Args:
            models_dir: 模型存储目录
        """
        self.logger = get_logger("FireYOLOModelManager")
        self.models_dir = Path(models_dir)
        self.models_dir.mkdir(parents=True, exist_ok=True)
        
        # 配置文件路径
        self.config_file = self.models_dir / "models_config.json"
        
        # 加载模型配置
        self.models_config = self._load_models_config()
        
        # 默认模型配置
        self.default_configs = {
            "flame": FireModelConfig(
                model_name="flame_detector",
                model_path="models/fire_detection/flame_yolo.pt",
                model_type="flame",
                confidence_threshold=0.5,
                iou_threshold=0.45,
                input_size=640,
                device="cpu",
                class_names=["flame"],
                description="火焰检测专用模型"
            ),
            "smoke": FireModelConfig(
                model_name="smoke_detector",
                model_path="models/fire_detection/smoke_yolo.pt",
                model_type="smoke",
                confidence_threshold=0.5,
                iou_threshold=0.45,
                input_size=640,
                device="cpu",
                class_names=["smoke"],
                description="烟雾检测专用模型"
            ),
            "fire_combined": FireModelConfig(
                model_name="fire_combined_detector",
                model_path="models/fire_detection/fire_combined_yolo.pt",
                model_type="fire_combined",
                confidence_threshold=0.5,
                iou_threshold=0.45,
                input_size=640,
                device="cpu",
                class_names=["flame", "smoke"],
                description="火焰和烟雾综合检测模型"
            )
        }
    
    def _load_models_config(self) -> Dict[str, FireModelConfig]:
        """加载模型配置"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                
                models_config = {}
                for name, config in config_data.items():
                    models_config[name] = FireModelConfig(**config)
                
                self.logger.info(f"已加载 {len(models_config)} 个火灾检测模型配置")
                return models_config
            else:
                self.logger.info("模型配置文件不存在，将使用默认配置")
                return {}
                
        except Exception as e:
            self.logger.error(f"加载模型配置失败: {e}")
            return {}
    
    def _save_models_config(self):
        """保存模型配置"""
        try:
            config_data = {}
            for name, config in self.models_config.items():
                config_data[name] = asdict(config)
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info("模型配置已保存")
            
        except Exception as e:
            self.logger.error(f"保存模型配置失败: {e}")
    
    def register_model(self, config: FireModelConfig) -> bool:
        """注册新的模型配置"""
        try:
            # 验证模型文件是否存在
            if not os.path.exists(config.model_path):
                self.logger.warning(f"模型文件不存在: {config.model_path}")
            
            # 添加到配置中
            self.models_config[config.model_name] = config
            
            # 保存配置
            self._save_models_config()
            
            self.logger.info(f"模型 {config.model_name} 注册成功")
            return True
            
        except Exception as e:
            self.logger.error(f"注册模型失败: {e}")
            return False
    
    def get_model_config(self, model_name: str) -> Optional[FireModelConfig]:
        """获取模型配置"""
        # 首先检查已注册的模型
        if model_name in self.models_config:
            return self.models_config[model_name]
        
        # 检查默认配置
        if model_name in self.default_configs:
            return self.default_configs[model_name]
        
        self.logger.warning(f"未找到模型配置: {model_name}")
        return None
    
    def list_available_models(self) -> List[str]:
        """列出可用的模型"""
        available_models = []
        
        # 添加已注册的模型
        available_models.extend(self.models_config.keys())
        
        # 添加默认模型（如果不重复）
        for name in self.default_configs.keys():
            if name not in available_models:
                available_models.append(name)
        
        return available_models
    
    def load_model(self, model_name: str) -> Optional[Any]:
        """加载YOLO模型"""
        if not ULTRALYTICS_AVAILABLE:
            self.logger.error("ultralytics库未安装，无法加载模型")
            return None
        
        config = self.get_model_config(model_name)
        if not config:
            return None
        
        try:
            # 检查模型文件是否存在
            if not os.path.exists(config.model_path):
                self.logger.warning(f"模型文件不存在: {config.model_path}")
                # 尝试使用通用模型
                self.logger.info("尝试使用通用YOLO模型")
                model = YOLO("yolov8n.pt")
            else:
                model = YOLO(config.model_path)
            
            # 设置设备
            model.to(config.device)
            
            self.logger.info(f"模型 {model_name} 加载成功")
            return model
            
        except Exception as e:
            self.logger.error(f"加载模型失败: {e}")
            return None
    
    def update_model_config(self, model_name: str, **kwargs) -> bool:
        """更新模型配置"""
        try:
            config = self.get_model_config(model_name)
            if not config:
                self.logger.error(f"模型 {model_name} 不存在")
                return False
            
            # 更新配置
            for key, value in kwargs.items():
                if hasattr(config, key):
                    setattr(config, key, value)
                else:
                    self.logger.warning(f"未知配置项: {key}")
            
            # 保存配置
            self.models_config[model_name] = config
            self._save_models_config()
            
            self.logger.info(f"模型 {model_name} 配置已更新")
            return True
            
        except Exception as e:
            self.logger.error(f"更新模型配置失败: {e}")
            return False
    
    def remove_model(self, model_name: str) -> bool:
        """移除模型配置"""
        try:
            if model_name in self.models_config:
                del self.models_config[model_name]
                self._save_models_config()
                self.logger.info(f"模型 {model_name} 已移除")
                return True
            else:
                self.logger.warning(f"模型 {model_name} 不存在")
                return False
                
        except Exception as e:
            self.logger.error(f"移除模型失败: {e}")
            return False
    
    def get_model_info(self, model_name: str) -> Optional[Dict]:
        """获取模型详细信息"""
        config = self.get_model_config(model_name)
        if not config:
            return None
        
        info = asdict(config)
        
        # 添加文件状态信息
        info['file_exists'] = os.path.exists(config.model_path)
        if info['file_exists']:
            stat = os.stat(config.model_path)
            info['file_size'] = stat.st_size
            info['file_modified'] = stat.st_mtime
        
        return info
    
    def validate_model(self, model_name: str) -> Dict[str, Any]:
        """验证模型配置和文件"""
        validation_result = {
            'valid': False,
            'errors': [],
            'warnings': []
        }
        
        config = self.get_model_config(model_name)
        if not config:
            validation_result['errors'].append(f"模型配置不存在: {model_name}")
            return validation_result
        
        # 检查模型文件
        if not os.path.exists(config.model_path):
            validation_result['errors'].append(f"模型文件不存在: {config.model_path}")
        
        # 检查配置参数
        if not 0 <= config.confidence_threshold <= 1:
            validation_result['errors'].append("置信度阈值必须在0-1之间")
        
        if not 0 <= config.iou_threshold <= 1:
            validation_result['errors'].append("IoU阈值必须在0-1之间")
        
        if config.input_size <= 0:
            validation_result['errors'].append("输入尺寸必须大于0")
        
        # 检查类别名称
        if not config.class_names:
            validation_result['warnings'].append("未定义类别名称")
        
        # 如果没有错误，则验证通过
        validation_result['valid'] = len(validation_result['errors']) == 0
        
        return validation_result


class FireYOLOConfigManager:
    """火灾检测YOLO配置管理器"""
    
    def __init__(self, config_dir: str = "config"):
        """
        初始化配置管理器
        
        Args:
            config_dir: 配置文件目录
        """
        self.logger = get_logger("FireYOLOConfigManager")
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        # 配置文件路径
        self.fire_config_file = self.config_dir / "fire_detection_config.yaml"
    
    def create_default_config(self) -> Dict:
        """创建默认配置"""
        default_config = {
            'fire_detection': {
                'enabled': True,
                'models': {
                    'flame_detector': {
                        'model_path': 'models/fire_detection/flame_yolo.pt',
                        'confidence_threshold': 0.5,
                        'iou_threshold': 0.45,
                        'device': 'cpu'
                    },
                    'smoke_detector': {
                        'model_path': 'models/fire_detection/smoke_yolo.pt',
                        'confidence_threshold': 0.5,
                        'iou_threshold': 0.45,
                        'device': 'cpu'
                    }
                },
                'risk_assessment': {
                    'thresholds': {
                        'low': 0.3,
                        'medium': 0.5,
                        'high': 0.7,
                        'critical': 0.9
                    }
                },
                'alerts': {
                    'enabled': True,
                    'sound_alert': False,
                    'email_alert': False,
                    'log_alert': True
                }
            }
        }
        
        return default_config
    
    def save_config(self, config: Dict) -> bool:
        """保存配置到文件"""
        try:
            with open(self.fire_config_file, 'w', encoding='utf-8') as f:
                yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
            
            self.logger.info("火灾检测配置已保存")
            return True
            
        except Exception as e:
            self.logger.error(f"保存配置失败: {e}")
            return False
    
    def load_config(self) -> Dict:
        """加载配置文件"""
        try:
            if self.fire_config_file.exists():
                with open(self.fire_config_file, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
                
                self.logger.info("火灾检测配置已加载")
                return config
            else:
                # 创建默认配置
                default_config = self.create_default_config()
                self.save_config(default_config)
                return default_config
                
        except Exception as e:
            self.logger.error(f"加载配置失败: {e}")
            return self.create_default_config()


# 便捷函数
def create_fire_model_manager(models_dir: str = "models/fire_detection") -> FireYOLOModelManager:
    """创建火灾检测模型管理器实例"""
    return FireYOLOModelManager(models_dir)


def create_fire_config_manager(config_dir: str = "config") -> FireYOLOConfigManager:
    """创建火灾检测配置管理器实例"""
    return FireYOLOConfigManager(config_dir)
