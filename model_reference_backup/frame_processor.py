#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
帧处理器模块
负责双摄像头和单摄像头的帧处理逻辑
"""

import cv2
import time
import numpy as np
from typing import Optional, Tuple, Any
from config.camera_config import UI_CONFIG
from utils.chinese_text_renderer import draw_chinese_text
from config.chinese_text_config import get_font_size, get_text_color, get_background_color
from detection.analysis.detection_count_manager import get_detection_count_manager
from config.alternating_detection_config import get_alternating_detection_config, print_config_info


class FrameProcessor:
    """帧处理器 - 负责处理摄像头帧的核心逻辑"""

    def __init__(self, display_manager, mouse_handler, heat_detection_applier=None, human_detector=None, heat_detector=None, heat_detection_renderer=None, fire_smoke_detector=None):
        """
        初始化帧处理器

        Args:
            display_manager: 显示管理器实例
            mouse_handler: 鼠标处理器实例
            heat_detection_applier: 热源检测应用函数（可选）
            human_detector: 人体检测器实例（可选）
            heat_detector: 热源检测器实例（可选，用于获取边界框）
            heat_detection_renderer: 热源检测渲染器（可选，用于绘制边界框）
            fire_smoke_detector: 火焰烟雾检测器实例（可选）
        """
        self.display_manager = display_manager
        self.mouse_handler = mouse_handler
        self.heat_detection_applier = heat_detection_applier
        self.human_detector = human_detector
        self.heat_detector = heat_detector
        self.heat_detection_renderer = heat_detection_renderer
        self.fire_smoke_detector = fire_smoke_detector

        # Excel数据导出器引用
        self.excel_exporter = None

        # 保存最新的火焰烟雾检测结果（用于Qt界面显示）
        self.last_fire_smoke_detections = []
        self.last_flame_analysis_results = {}

        # 当前帧引用（用于保存功能）
        self.current_visible_frame = None
        self.current_thermal_frame = None
        self.current_combined_frame = None

        # 人体检测控制
        self.human_detection_enabled = False
        self.thermal_human_detection_enabled = False  # 热成像人体检测控制

        # 火焰烟雾检测控制
        self.fire_smoke_detection_enabled = False

        # 交替检测配置 - 默认使用4帧循环检测
        from config.alternating_detection_config import get_four_frame_cycle_config
        self.alternating_config = get_four_frame_cycle_config()  # 使用4帧循环作为默认配置
        self.alternating_detection_enabled = self.alternating_config.enabled
        self.frame_counter = 0  # 帧计数器
        self.detection_interval = self.alternating_config.detection_interval

        # 新物体检测器（第4帧使用）
        self.new_object_detector = None
        self.new_object_detection_enabled = False
        self.cached_new_object_detections = []

        # 启动时检查并加载新物体检测模型
        self._check_and_load_new_object_model()

        # 缓存最新的检测结果（用于交替检测时保持显示）
        self.cached_human_detections = []
        self.cached_fire_smoke_detections = {}

        # 打印交替检测配置信息（可选）
        # if self.alternating_config.debug_enabled:
        #     print_config_info(self.alternating_config)

        # IoU阈值，用于判断重叠
        self.iou_threshold = 0.3  # 重叠度超过30%认为是重叠

        # 检测计数管理器
        self.detection_count_manager = get_detection_count_manager()

        # 性能监控器
        self.performance_monitor = None

    def set_performance_monitor(self, performance_monitor):
        """设置性能监控器"""
        self.performance_monitor = performance_monitor
    
    def process_dual_frames(self, visible_frame, thermal_frame, fps: float) -> None:
        """
        处理双摄像头画面 - Qt版本

        Args:
            visible_frame: 可见光画面
            thermal_frame: 红外画面
            fps: 当前帧率
        """
        # 更新当前帧引用
        self.current_visible_frame = visible_frame
        self.current_thermal_frame = thermal_frame

        # Qt版本：更新鼠标处理器的帧引用（如果存在）
        if self.mouse_handler:
            self.mouse_handler.set_frames(thermal_frame, visible_frame, None)

        # 应用热成像检测（人体检测优先，只显示蓝色框，无人体时显示红色热源框）
        processed_thermal_frame = self._apply_thermal_detection_with_priority(thermal_frame)

        # 应用可见光检测（人体检测 + 火焰烟雾检测）
        processed_visible_frame = self._apply_visible_light_detection(visible_frame)

        # Qt版本：拼接画面（如果有显示管理器）
        if self.display_manager:
            combined_frame = self.display_manager.combine_frames(processed_visible_frame, processed_thermal_frame, fps)

            if combined_frame is not None:
                # 更新拼接画面引用
                self.current_combined_frame = combined_frame
                if self.mouse_handler:
                    self.mouse_handler.set_frames(thermal_frame, visible_frame, combined_frame)

                # 处理鼠标叠加信息
                combined_frame = self._add_mouse_overlay(combined_frame)

                # 显示画面
                self.display_manager.display_combined_frame(combined_frame)
        else:
            # Qt版本：直接保存处理后的帧，由Qt界面处理显示
            self.current_combined_frame = self._create_simple_combined_frame(processed_visible_frame, processed_thermal_frame)

        # 执行系统性能检查
        if self.performance_monitor:
            self.performance_monitor.check_system_performance()
    
    def process_single_frame(self, frame, title: str, fps: float) -> None:
        """
        处理单个画面 - Qt版本

        Args:
            frame: 视频帧
            title: 画面标题
            fps: 当前帧率
        """
        # 应用检测（根据帧类型）
        if "Thermal" in title:
            # 应用热成像检测（人体检测优先，只显示蓝色框，无人体时显示红色热源框）
            processed_frame = self._apply_thermal_detection_with_priority(frame)
        elif "Visible" in title or "Camera" in title:
            processed_frame = self._apply_visible_light_detection(frame)
        else:
            processed_frame = frame

        # Qt版本：调整尺寸并添加信息（如果有显示管理器）
        if self.display_manager:
            resized_frame = cv2.resize(processed_frame, self.display_manager.get_display_dimensions())
            # 添加标签和信息
            self._add_frame_labels(resized_frame, title, fps)
            # 显示画面
            self.display_manager.display_single_frame(resized_frame)
        else:
            # Qt版本：直接保存处理后的帧
            self.current_combined_frame = processed_frame

        # 更新处理器的帧引用
        self._update_handlers_for_single_frame(frame, title)
    
    def _apply_heat_detection(self, thermal_frame):
        """应用热源检测"""
        if self.heat_detection_applier:
            return self.heat_detection_applier(thermal_frame)
        return thermal_frame

    def _calculate_iou(self, box1, box2):
        """计算两个边界框的IoU（Intersection over Union）"""
        x1, y1, w1, h1 = box1
        x2, y2, w2, h2 = box2

        # 转换为 (x1, y1, x2, y2) 格式
        box1_coords = (x1, y1, x1 + w1, y1 + h1)
        box2_coords = (x2, y2, x2 + w2, y2 + h2)

        # 计算交集区域
        inter_x1 = max(box1_coords[0], box2_coords[0])
        inter_y1 = max(box1_coords[1], box2_coords[1])
        inter_x2 = min(box1_coords[2], box2_coords[2])
        inter_y2 = min(box1_coords[3], box2_coords[3])

        if inter_x1 < inter_x2 and inter_y1 < inter_y2:
            inter_area = (inter_x2 - inter_x1) * (inter_y2 - inter_y1)
        else:
            inter_area = 0

        # 计算并集区域
        box1_area = w1 * h1
        box2_area = w2 * h2
        union_area = box1_area + box2_area - inter_area

        # 计算IoU
        if union_area > 0:
            return inter_area / union_area
        return 0

    def _get_heat_source_bboxes(self, thermal_frame):
        """获取热源检测的边界框列表"""
        if self.heat_detector is None:
            return []

        try:
            # 使用热源检测器获取边界框列表
            bboxes, _ = self.heat_detector.detect_heat_sources()
            return bboxes
        except Exception as e:
            print(f"⚠️ 获取热源边界框失败: {e}")
            return []

    def _draw_heat_source_bboxes(self, frame, heat_bboxes, temperature_manager=None):
        """绘制热源检测框"""
        if not heat_bboxes or self.heat_detection_renderer is None:
            return frame

        try:
            # 使用热源检测渲染器绘制边界框
            return self.heat_detection_renderer.apply_heat_detection(
                thermal_frame=frame,
                bboxes=heat_bboxes,
                temperature_manager=temperature_manager,
                heat_detector=self.heat_detector,
                display_manager=self.display_manager,
                logger=None
            )
        except Exception as e:
            print(f"⚠️ 绘制热源边界框失败: {e}")
            return frame

    def _apply_thermal_detection_with_priority(self, thermal_frame):
        """
        应用热成像检测（基于IoU的智能替代策略）
        - 获取热源检测边界框和人体检测边界框
        - 计算IoU，过滤掉与人体框重叠度高的热源框
        - 绘制剩余的热源框（红色）和所有人体框（蓝色）
        - 结果：人体位置只显示蓝色框，非重叠热源位置显示红色框
        """
        result_frame = thermal_frame.copy()

        # 获取人体检测结果
        human_detections = []
        if self.thermal_human_detection_enabled and self.human_detector:
            try:
                human_detections, _ = self.human_detector.detect_humans(thermal_frame)
            except Exception as e:
                print(f"⚠️ 热成像人体检测失败: {e}")

        # 获取热源检测结果
        heat_bboxes = []
        if self.heat_detector is not None:
            heat_bboxes = self._get_heat_source_bboxes(thermal_frame)

        # 如果没有人体检测，直接显示所有热源
        if not human_detections:
            if heat_bboxes and self.heat_detection_renderer:
                result_frame = self._draw_heat_source_bboxes(result_frame, heat_bboxes)
            elif self.heat_detection_applier:
                # 回退到原有方法
                result_frame = self.heat_detection_applier(result_frame)
            return result_frame

        # 如果有人体检测，进行IoU过滤
        if human_detections:
            # 转换人体检测结果为边界框格式
            human_bboxes = []
            for detection in human_detections:
                x, y, w, h = detection['bbox']
                human_bboxes.append((x, y, w, h))

            # 过滤与人体重叠的热源框
            filtered_heat_bboxes = []
            for heat_bbox in heat_bboxes:
                is_overlapping = False
                for human_bbox in human_bboxes:
                    iou = self._calculate_iou(heat_bbox, human_bbox)
                    if iou > self.iou_threshold:
                        is_overlapping = True
                        break

                if not is_overlapping:
                    filtered_heat_bboxes.append(heat_bbox)

            # 绘制过滤后的热源框（红色）
            if filtered_heat_bboxes and self.heat_detection_renderer:
                result_frame = self._draw_heat_source_bboxes(result_frame, filtered_heat_bboxes)

            # 绘制人体检测框（蓝色）
            for detection in human_detections:
                x, y, w, h = detection['bbox']
                confidence = detection['confidence']

                # 绘制蓝色边界框
                cv2.rectangle(result_frame, (x, y), (x + w, y + h), (255, 0, 0), 1)

                # 使用中文文字渲染器绘制置信度标签
                conf_label = f"人体 {confidence:.2f}"
                result_frame = draw_chinese_text(
                    result_frame, conf_label, (x, y - 5),
                    font_size=get_font_size("small"),
                    color=(255, 0, 0),  # 蓝色文字
                    background_color=get_background_color("dark"),
                    padding=2
                )

            print(f"🔥🚶 热成像检测: {len(human_detections)} 个人体目标（蓝色框），{len(filtered_heat_bboxes)} 个非重叠热源（红色框）")

            # 更新热成像人体检测计数
            try:
                self.detection_count_manager.update_human_count(len(human_detections))
            except Exception as e:
                print(f"⚠️ 更新热成像人体计数失败: {e}")

        return result_frame
    
    def _add_mouse_overlay(self, combined_frame):
        """添加鼠标叠加信息 - Qt版本"""
        if not self.mouse_handler or not self.display_manager:
            return combined_frame

        mouse_x, mouse_y = self.mouse_handler.get_mouse_position()
        mouse_temp = self.mouse_handler.get_mouse_temperature()

        # 获取窗口坐标（包含工具栏的完整坐标）用于绘制十字线
        window_mouse_x, window_mouse_y = self.mouse_handler.get_window_mouse_position()

        # 只在红外区域显示温度
        show_temp = self.mouse_handler.is_in_thermal_area() and mouse_temp != 0.0

        if show_temp:
            combined_frame = self.display_manager.add_mouse_overlay(
                combined_frame, window_mouse_x, window_mouse_y, mouse_temp, True
            )
        elif mouse_x > 0 and mouse_y > 0:
            combined_frame = self.display_manager.add_mouse_overlay(
                combined_frame, window_mouse_x, window_mouse_y, 0.0, True
            )

        return combined_frame

    def _create_simple_combined_frame(self, visible_frame, thermal_frame):
        """创建简单的拼接画面 - Qt版本使用"""
        if visible_frame is None and thermal_frame is None:
            return None
        elif visible_frame is None:
            return thermal_frame
        elif thermal_frame is None:
            return visible_frame
        else:
            # 简单的水平拼接
            height = max(visible_frame.shape[0], thermal_frame.shape[0])
            visible_resized = cv2.resize(visible_frame, (visible_frame.shape[1], height))
            thermal_resized = cv2.resize(thermal_frame, (thermal_frame.shape[1], height))
            return cv2.hconcat([visible_resized, thermal_resized])
    
    def _add_frame_labels(self, frame, title: str, fps: float) -> None:
        """为单个帧添加标签和信息"""
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        fps_text = f"FPS: {fps:.1f}"
        
        cv2.putText(frame, title, (10, 30),
                   UI_CONFIG.font_face, UI_CONFIG.font_scale, 
                   UI_CONFIG.thermal_label_color, UI_CONFIG.font_thickness)
        cv2.putText(frame, timestamp, (10, frame.shape[0] - 60),
                   UI_CONFIG.font_face, 0.5, UI_CONFIG.text_color, 1)
        cv2.putText(frame, fps_text, (10, frame.shape[0] - 35),
                   UI_CONFIG.font_face, 0.5, UI_CONFIG.text_color, 1)
    
    def _update_handlers_for_single_frame(self, frame, title: str) -> None:
        """为单个帧更新处理器的帧引用 - Qt版本"""
        if "Thermal" in title:
            self.current_thermal_frame = frame
            if self.mouse_handler:
                self.mouse_handler.set_frames(frame, None, None)
        else:
            self.current_visible_frame = frame
            if self.mouse_handler:
                self.mouse_handler.set_frames(None, frame, None)
    
    def _apply_human_detection(self, visible_frame):
        """应用人体检测"""
        if self.human_detection_enabled and self.human_detector:
            try:
                detections, annotated_frame = self.human_detector.detect_humans(visible_frame)
                return annotated_frame
            except Exception as e:
                print(f"⚠️ 人体检测失败: {e}")
                return visible_frame
        return visible_frame

    def _apply_fire_smoke_detection(self, visible_frame):
        """应用火焰烟雾检测"""
        if self.fire_smoke_detection_enabled and self.fire_smoke_detector:
            try:
                # 执行火焰烟雾检测
                detection_results = self.fire_smoke_detector.detect(visible_frame)

                # 缓存检测结果
                self.last_fire_smoke_detections = detection_results

                # 添加调试输出
                if detection_results:
                    fire_count = len(detection_results.get('fire_detections', []))
                    smoke_count = len(detection_results.get('smoke_detections', []))
                    fire_objects = len(detection_results.get('fire_objects', {}))
                    smoke_objects = len(detection_results.get('smoke_objects', {}))

                    if fire_count > 0 or smoke_count > 0 or fire_objects > 0 or smoke_objects > 0:
                        print(f"🔥💨 帧处理器检测到: {fire_count}个火焰检测, {smoke_count}个烟雾检测, {fire_objects}个火焰对象, {smoke_objects}个烟雾对象")

                        # 手动触发界面回调（修复关键问题）
                        if hasattr(self.fire_smoke_detector, '_notify_ui_update'):
                            print(f"🔄 帧处理器手动触发界面回调")

                            # 检查回调是否设置
                            if hasattr(self.fire_smoke_detector, 'ui_update_callback') and self.fire_smoke_detector.ui_update_callback:
                                print(f"✅ 检测器有回调，直接调用")
                                self.fire_smoke_detector._notify_ui_update(detection_results)
                            else:
                                print(f"⚠️ 检测器没有回调，尝试从系统获取")
                                # 尝试从系统实例获取主窗口并设置回调
                                self._try_setup_callback_from_system(detection_results)

                # 在图像上绘制检测结果
                visible_frame = self._draw_fire_smoke_detections(visible_frame, detection_results)

                # 导出到Excel（如果启用）
                if self.excel_exporter:
                    self._export_fire_smoke_detections_to_excel(detection_results)

            except Exception as e:
                print(f"⚠️ 火焰烟雾检测失败: {e}")

        return visible_frame

    def _draw_fire_smoke_detections(self, frame, detection_results):
        """在图像上绘制火焰烟雾检测结果"""
        try:
            # 绘制火焰检测
            fire_detections = detection_results.get('fire_detections', [])
            for detection in fire_detections:
                bbox = detection['bbox']
                confidence = detection['confidence']

                # 绘制火焰边界框（红色）
                cv2.rectangle(frame, (bbox[0], bbox[1]), (bbox[2], bbox[3]), (0, 0, 255), 2)

                # 绘制标签
                label = f"Fire: {confidence:.2f}"
                cv2.putText(frame, label, (bbox[0], bbox[1] - 10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 2)

            # 绘制烟雾检测
            smoke_detections = detection_results.get('smoke_detections', [])
            for detection in smoke_detections:
                bbox = detection['bbox']
                confidence = detection['confidence']

                # 绘制烟雾边界框（灰色）
                cv2.rectangle(frame, (bbox[0], bbox[1]), (bbox[2], bbox[3]), (128, 128, 128), 2)

                # 绘制标签
                label = f"Smoke: {confidence:.2f}"
                cv2.putText(frame, label, (bbox[0], bbox[1] - 10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (128, 128, 128), 2)

            # 绘制跟踪轨迹（如果有）
            if 'fire_trajectories' in detection_results:
                self._draw_trajectories(frame, detection_results['fire_trajectories'], (0, 0, 255))

            if 'smoke_trajectories' in detection_results:
                self._draw_trajectories(frame, detection_results['smoke_trajectories'], (128, 128, 128))

        except Exception as e:
            print(f"⚠️ 绘制火焰烟雾检测结果失败: {e}")

        return frame

    def _draw_trajectories(self, frame, trajectories, color):
        """绘制跟踪轨迹"""
        try:
            for obj_id, trajectory in trajectories.items():
                if len(trajectory) > 1:
                    # 绘制轨迹线
                    points = np.array(trajectory, dtype=np.int32)
                    cv2.polylines(frame, [points], False, color, 2)

                    # 在最后一个点绘制目标ID
                    if len(trajectory) > 0:
                        last_point = trajectory[-1]
                        cv2.putText(frame, f"ID:{obj_id}",
                                   (last_point[0], last_point[1] - 5),
                                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)
        except Exception as e:
            print(f"⚠️ 绘制轨迹失败: {e}")

    def _apply_visible_light_detection(self, visible_frame):
        """应用可见光检测（交替检测：人体检测 + 火焰烟雾检测）"""
        result_frame = visible_frame.copy()

        # 更新帧计数器
        self.frame_counter += 1

        # 初始化计数
        human_count = 0
        fire_count = 0
        smoke_count = 0

        if self.alternating_detection_enabled:
            # 交替检测模式
            result_frame = self._apply_alternating_detection(result_frame)

            # 从缓存获取计数（支持独立的火焰和烟雾计数）
            human_count = len(self.cached_human_detections)

            # 获取火焰计数
            fire_count = 0
            if hasattr(self, 'cached_fire_detections') and self.cached_fire_detections:
                fire_count = len(self.cached_fire_detections.get('fire_detections', []))
            elif self.cached_fire_smoke_detections:
                fire_count = len(self.cached_fire_smoke_detections.get('fire_detections', []))

            # 获取烟雾计数
            smoke_count = 0
            if hasattr(self, 'cached_smoke_detections') and self.cached_smoke_detections:
                smoke_count = len(self.cached_smoke_detections.get('smoke_detections', []))
            elif self.cached_fire_smoke_detections:
                smoke_count = len(self.cached_fire_smoke_detections.get('smoke_detections', []))

            # 获取新物体检测计数
            new_object_count = len(self.cached_new_object_detections) if hasattr(self, 'cached_new_object_detections') else 0
        else:
            # 传统模式：每帧都检测
            result_frame = self._apply_traditional_detection(result_frame)

            # 直接获取计数
            human_count = len(getattr(self, '_current_human_detections', []))
            fire_count = len(self.last_fire_smoke_detections.get('fire_detections', []))
            smoke_count = len(self.last_fire_smoke_detections.get('smoke_detections', []))

        # 3. 更新检测计数到管理器
        try:
            self.detection_count_manager.update_all_counts(human_count, fire_count, smoke_count)
        except Exception as e:
            print(f"⚠️ 更新检测计数失败: {e}")

        return result_frame

    def _apply_alternating_detection(self, visible_frame):
        """应用交替检测逻辑（4帧循环）"""
        result_frame = visible_frame.copy()

        # 判断当前帧应该执行哪种检测
        detection_type = self._get_current_detection_type()

        if detection_type == "human":
            # 第1帧：执行人体检测
            result_frame = self._perform_human_detection(result_frame)
            # 绘制缓存的火焰烟雾检测结果
            result_frame = self._draw_cached_fire_smoke_detections(result_frame)
            if self.alternating_config.debug_enabled:
                print(f"🔄 帧{self.frame_counter}: 执行人体检测，使用缓存火焰烟雾结果")

        elif detection_type == "fire":
            # 第2帧：执行火焰检测
            result_frame = self._perform_fire_detection(result_frame)
            # 绘制缓存的人体和烟雾检测结果
            result_frame = self._draw_cached_human_detections(result_frame)
            result_frame = self._draw_cached_smoke_detections(result_frame)
            if self.alternating_config.debug_enabled:
                print(f"🔄 帧{self.frame_counter}: 执行火焰检测，使用缓存人体和烟雾结果")

        elif detection_type == "smoke":
            # 第3帧：执行烟雾检测
            result_frame = self._perform_smoke_detection(result_frame)
            # 绘制缓存的人体和火焰检测结果
            result_frame = self._draw_cached_human_detections(result_frame)
            result_frame = self._draw_cached_fire_detections(result_frame)
            if self.alternating_config.debug_enabled:
                print(f"🔄 帧{self.frame_counter}: 执行烟雾检测，使用缓存人体和火焰结果")

        elif detection_type == "reserved":
            # 第4帧：根据是否有新物体检测器决定行为
            if self.new_object_detection_enabled:
                # 执行新物体检测
                result_frame = self._perform_new_object_detection(result_frame)
                # 绘制缓存的其他检测结果
                result_frame = self._draw_cached_human_detections(result_frame)
                result_frame = self._draw_cached_fire_detections(result_frame)
                result_frame = self._draw_cached_smoke_detections(result_frame)
                if self.alternating_config.debug_enabled:
                    print(f"🔄 帧{self.frame_counter}: 执行新物体检测，使用缓存其他结果")
            else:
                # 预留帧，绘制所有缓存结果
                result_frame = self._draw_cached_human_detections(result_frame)
                result_frame = self._draw_cached_fire_smoke_detections(result_frame)
                if self.alternating_config.debug_enabled:
                    print(f"🔄 帧{self.frame_counter}: 预留帧，使用所有缓存结果")

        else:
            # 其他情况，使用缓存结果
            result_frame = self._draw_cached_human_detections(result_frame)
            result_frame = self._draw_cached_fire_smoke_detections(result_frame)

        return result_frame

    def _apply_traditional_detection(self, visible_frame):
        """应用传统检测逻辑（每帧都检测）"""
        result_frame = visible_frame.copy()

        # 1. 先应用人体检测
        if self.human_detection_enabled and self.human_detector:
            try:
                human_detections, result_frame = self.human_detector.detect_humans(result_frame)
                self._current_human_detections = human_detections or []
            except Exception as e:
                print(f"⚠️ 人体检测失败: {e}")
                self._current_human_detections = []

        # 2. 火焰烟雾检测
        if self.fire_smoke_detection_enabled and self.fire_smoke_detector:
            try:
                # 执行火焰烟雾检测
                detection_results = self.fire_smoke_detector.detect(result_frame)

                # 缓存检测结果
                self.last_fire_smoke_detections = detection_results

                # 设置火焰分析结果（用于Qt界面显示）
                if hasattr(self, 'last_flame_analysis_results'):
                    self.last_flame_analysis_results = detection_results

                # 获取检测计数
                fire_count = len(detection_results.get('fire_detections', []))
                smoke_count = len(detection_results.get('smoke_detections', []))

                # 手动触发界面回调（修复关键问题）
                if detection_results and (fire_count > 0 or smoke_count > 0):
                    if hasattr(self.fire_smoke_detector, '_notify_ui_update'):
                        # 检查回调是否设置
                        if hasattr(self.fire_smoke_detector, 'ui_update_callback') and self.fire_smoke_detector.ui_update_callback:
                            self.fire_smoke_detector._notify_ui_update(detection_results)
                        else:
                            # 尝试从系统获取主窗口并设置回调
                            self._try_setup_callback_from_system(detection_results)

                # 在图像上绘制检测结果
                result_frame = self._draw_fire_smoke_detections(result_frame, detection_results)

                # 导出到Excel（如果启用）
                if self.excel_exporter:
                    self._export_fire_smoke_detections_to_excel(detection_results)

            except Exception as e:
                print(f"⚠️ 火焰烟雾检测失败: {e}")
                # 清空检测结果
                self.last_fire_smoke_detections = {}
                if hasattr(self, 'last_flame_analysis_results'):
                    self.last_flame_analysis_results = {}

        return result_frame

    def _perform_fire_detection(self, result_frame):
        """执行独立的火焰检测"""
        try:
            if self.fire_smoke_detection_enabled and self.fire_smoke_detector:
                # 执行火焰烟雾检测，但只处理火焰结果
                detection_results = self.fire_smoke_detector.detect(result_frame)

                # 只缓存火焰检测结果
                if detection_results:
                    fire_detections = detection_results.get('fire_detections', [])
                    fire_objects = detection_results.get('fire_objects', {})

                    # 更新缓存的火焰检测结果
                    if not hasattr(self, 'cached_fire_detections'):
                        self.cached_fire_detections = {}

                    self.cached_fire_detections = {
                        'fire_detections': fire_detections,
                        'fire_objects': fire_objects
                    }

                    # 绘制火焰检测结果
                    result_frame = self._draw_fire_detections(result_frame, fire_detections, fire_objects)

                    if self.alternating_config.debug_enabled and fire_detections:
                        print(f"🔥 检测到 {len(fire_detections)} 个火焰")

        except Exception as e:
            print(f"⚠️ 火焰检测失败: {e}")

        return result_frame

    def _perform_smoke_detection(self, result_frame):
        """执行独立的烟雾检测"""
        try:
            if self.fire_smoke_detection_enabled and self.fire_smoke_detector:
                # 执行火焰烟雾检测，但只处理烟雾结果
                detection_results = self.fire_smoke_detector.detect(result_frame)

                # 只缓存烟雾检测结果
                if detection_results:
                    smoke_detections = detection_results.get('smoke_detections', [])
                    smoke_objects = detection_results.get('smoke_objects', {})

                    # 更新缓存的烟雾检测结果
                    if not hasattr(self, 'cached_smoke_detections'):
                        self.cached_smoke_detections = {}

                    self.cached_smoke_detections = {
                        'smoke_detections': smoke_detections,
                        'smoke_objects': smoke_objects
                    }

                    # 绘制烟雾检测结果
                    result_frame = self._draw_smoke_detections(result_frame, smoke_detections, smoke_objects)

                    if self.alternating_config.debug_enabled and smoke_detections:
                        print(f"💨 检测到 {len(smoke_detections)} 个烟雾")

        except Exception as e:
            print(f"⚠️ 烟雾检测失败: {e}")

        return result_frame

    def _perform_new_object_detection(self, result_frame):
        """执行新物体检测（第4帧）"""
        try:
            if not self.new_object_detection_enabled or not self.new_object_detector:
                return result_frame

            detector_type = self.new_object_detector.get('type', '')

            if detector_type == 'yolo_ultralytics':
                return self._detect_with_yolo_ultralytics(result_frame)
            elif detector_type == 'yolo_torch_hub':
                return self._detect_with_yolo_torch_hub(result_frame)
            elif detector_type == 'onnx':
                return self._detect_with_onnx(result_frame)
            elif detector_type == 'tensorflow':
                return self._detect_with_tensorflow(result_frame)
            elif detector_type == 'tflite':
                return self._detect_with_tflite(result_frame)
            elif detector_type == 'darknet':
                return self._detect_with_darknet(result_frame)
            elif detector_type == 'pytorch':
                return self._detect_with_pytorch(result_frame)
            else:
                print(f"⚠️ 不支持的检测器类型: {detector_type}")
                return result_frame

        except Exception as e:
            print(f"⚠️ 新物体检测失败: {e}")
            return result_frame

    def _detect_with_yolo_ultralytics(self, frame):
        """使用ultralytics YOLO进行检测"""
        try:
            model = self.new_object_detector['model']
            results = model(frame)

            # 清空之前的检测结果
            self.cached_new_object_detections = []

            # 处理检测结果
            for result in results:
                boxes = result.boxes
                if boxes is not None:
                    for box in boxes:
                        # 获取边界框坐标
                        x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                        confidence = box.conf[0].cpu().numpy()
                        class_id = int(box.cls[0].cpu().numpy())

                        # 获取类别名称
                        class_name = model.names.get(class_id, f"Class_{class_id}")

                        # 缓存检测结果
                        detection = {
                            'bbox': [int(x1), int(y1), int(x2), int(y2)],
                            'confidence': float(confidence),
                            'class_id': class_id,
                            'class_name': class_name
                        }
                        self.cached_new_object_detections.append(detection)

                        # 绘制检测结果
                        cv2.rectangle(frame, (int(x1), int(y1)), (int(x2), int(y2)), (255, 255, 0), 2)
                        label = f"{class_name}: {confidence:.2f}"
                        cv2.putText(frame, label, (int(x1), int(y1)-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 1)

            if self.alternating_config.debug_enabled and self.cached_new_object_detections:
                print(f"🔍 新物体检测到 {len(self.cached_new_object_detections)} 个对象")

        except Exception as e:
            print(f"⚠️ YOLO ultralytics检测失败: {e}")

        return frame

    def _detect_with_yolo_torch_hub(self, frame):
        """使用torch.hub YOLO进行检测"""
        try:
            model = self.new_object_detector['model']
            results = model(frame)

            # 清空之前的检测结果
            self.cached_new_object_detections = []

            # 处理检测结果
            detections = results.pandas().xyxy[0]

            for _, detection in detections.iterrows():
                x1, y1, x2, y2 = int(detection['xmin']), int(detection['ymin']), int(detection['xmax']), int(detection['ymax'])
                confidence = detection['confidence']
                class_name = detection['name']

                # 缓存检测结果
                detection_info = {
                    'bbox': [x1, y1, x2, y2],
                    'confidence': float(confidence),
                    'class_name': class_name
                }
                self.cached_new_object_detections.append(detection_info)

                # 绘制检测结果
                cv2.rectangle(frame, (x1, y1), (x2, y2), (255, 255, 0), 2)
                label = f"{class_name}: {confidence:.2f}"
                cv2.putText(frame, label, (x1, y1-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 1)

            if self.alternating_config.debug_enabled and self.cached_new_object_detections:
                print(f"🔍 新物体检测到 {len(self.cached_new_object_detections)} 个对象")

        except Exception as e:
            print(f"⚠️ YOLO torch.hub检测失败: {e}")

        return frame

    def _detect_with_onnx(self, frame):
        """使用ONNX模型进行检测"""
        try:
            session = self.new_object_detector['session']

            # 预处理图像（这里需要根据具体模型调整）
            input_name = session.get_inputs()[0].name
            input_shape = session.get_inputs()[0].shape

            # 简单的预处理示例
            import numpy as np
            processed_frame = cv2.resize(frame, (640, 640))
            processed_frame = processed_frame.astype(np.float32) / 255.0
            processed_frame = np.transpose(processed_frame, (2, 0, 1))
            processed_frame = np.expand_dims(processed_frame, axis=0)

            # 运行推理
            outputs = session.run(None, {input_name: processed_frame})

            # 这里需要根据具体模型的输出格式来解析结果
            # 示例：假设输出格式为 [batch, num_detections, 6] (x1, y1, x2, y2, conf, class)
            if outputs and len(outputs) > 0:
                detections = outputs[0]
                self.cached_new_object_detections = []

                for detection in detections[0]:  # 假设batch_size=1
                    if len(detection) >= 6:
                        x1, y1, x2, y2, conf, class_id = detection[:6]

                        if conf > 0.5:  # 置信度阈值
                            # 缓存检测结果
                            detection_info = {
                                'bbox': [int(x1), int(y1), int(x2), int(y2)],
                                'confidence': float(conf),
                                'class_id': int(class_id),
                                'class_name': f"Object_{int(class_id)}"
                            }
                            self.cached_new_object_detections.append(detection_info)

                            # 绘制检测结果
                            cv2.rectangle(frame, (int(x1), int(y1)), (int(x2), int(y2)), (255, 255, 0), 2)
                            label = f"Object_{int(class_id)}: {conf:.2f}"
                            cv2.putText(frame, label, (int(x1), int(y1)-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 1)

            if self.alternating_config.debug_enabled and self.cached_new_object_detections:
                print(f"🔍 新物体检测到 {len(self.cached_new_object_detections)} 个对象")

        except Exception as e:
            print(f"⚠️ ONNX检测失败: {e}")

        return frame

    def _detect_with_tensorflow(self, frame):
        """使用TensorFlow模型进行检测"""
        try:
            model = self.new_object_detector['model']

            # 预处理图像
            import numpy as np
            input_tensor = np.expand_dims(frame, axis=0)

            # 运行推理（这里需要根据具体模型调整）
            # 示例：假设模型有一个__call__方法
            outputs = model(input_tensor)

            # 解析输出（需要根据具体模型格式调整）
            self.cached_new_object_detections = []

            # 这里添加具体的结果解析逻辑
            print(f"🔍 TensorFlow模型推理完成，需要根据具体模型格式解析结果")

        except Exception as e:
            print(f"⚠️ TensorFlow检测失败: {e}")

        return frame

    def _detect_with_tflite(self, frame):
        """使用TensorFlow Lite模型进行检测"""
        try:
            interpreter = self.new_object_detector['interpreter']

            # 获取输入输出详情
            input_details = interpreter.get_input_details()
            output_details = interpreter.get_output_details()

            # 预处理图像
            import numpy as np
            input_shape = input_details[0]['shape']
            processed_frame = cv2.resize(frame, (input_shape[1], input_shape[2]))
            processed_frame = np.expand_dims(processed_frame, axis=0).astype(np.float32)

            # 设置输入
            interpreter.set_tensor(input_details[0]['index'], processed_frame)

            # 运行推理
            interpreter.invoke()

            # 获取输出
            outputs = []
            for output_detail in output_details:
                output = interpreter.get_tensor(output_detail['index'])
                outputs.append(output)

            # 解析结果（需要根据具体模型调整）
            self.cached_new_object_detections = []
            print(f"🔍 TensorFlow Lite模型推理完成，需要根据具体模型格式解析结果")

        except Exception as e:
            print(f"⚠️ TensorFlow Lite检测失败: {e}")

        return frame

    def _detect_with_darknet(self, frame):
        """使用Darknet模型进行检测"""
        try:
            net = self.new_object_detector['net']

            # 创建blob
            blob = cv2.dnn.blobFromImage(frame, 1/255.0, (416, 416), swapRB=True, crop=False)
            net.setInput(blob)

            # 获取输出层名称
            layer_names = net.getLayerNames()
            output_layers = [layer_names[i[0] - 1] for i in net.getUnconnectedOutLayers()]

            # 运行推理
            outputs = net.forward(output_layers)

            # 解析检测结果
            self.cached_new_object_detections = []
            height, width = frame.shape[:2]

            for output in outputs:
                for detection in output:
                    scores = detection[5:]
                    class_id = np.argmax(scores)
                    confidence = scores[class_id]

                    if confidence > 0.5:
                        # 获取边界框
                        center_x = int(detection[0] * width)
                        center_y = int(detection[1] * height)
                        w = int(detection[2] * width)
                        h = int(detection[3] * height)

                        x1 = int(center_x - w / 2)
                        y1 = int(center_y - h / 2)
                        x2 = int(center_x + w / 2)
                        y2 = int(center_y + h / 2)

                        # 缓存检测结果
                        detection_info = {
                            'bbox': [x1, y1, x2, y2],
                            'confidence': float(confidence),
                            'class_id': int(class_id),
                            'class_name': f"Object_{class_id}"
                        }
                        self.cached_new_object_detections.append(detection_info)

                        # 绘制检测结果
                        cv2.rectangle(frame, (x1, y1), (x2, y2), (255, 255, 0), 2)
                        label = f"Object_{class_id}: {confidence:.2f}"
                        cv2.putText(frame, label, (x1, y1-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 1)

            if self.alternating_config.debug_enabled and self.cached_new_object_detections:
                print(f"🔍 新物体检测到 {len(self.cached_new_object_detections)} 个对象")

        except Exception as e:
            print(f"⚠️ Darknet检测失败: {e}")

        return frame

    def _detect_with_pytorch(self, frame):
        """使用通用PyTorch模型进行检测"""
        try:
            model = self.new_object_detector['model']

            # 这里需要根据具体模型实现预处理和推理逻辑
            print(f"🔍 通用PyTorch模型推理，需要根据具体模型实现")

            # 示例：简单的前向传播
            import torch
            import numpy as np

            # 预处理（需要根据模型调整）
            processed_frame = cv2.resize(frame, (224, 224))
            processed_frame = processed_frame.astype(np.float32) / 255.0
            processed_frame = np.transpose(processed_frame, (2, 0, 1))
            input_tensor = torch.from_numpy(processed_frame).unsqueeze(0)

            # 推理
            with torch.no_grad():
                outputs = model(input_tensor)

            # 解析结果（需要根据模型输出格式调整）
            self.cached_new_object_detections = []
            print(f"🔍 PyTorch模型推理完成，需要根据具体模型格式解析结果")

        except Exception as e:
            print(f"⚠️ PyTorch检测失败: {e}")

        return frame

    def _draw_cached_new_object_detections(self, frame):
        """绘制缓存的新物体检测结果"""
        try:
            if self.cached_new_object_detections:
                for detection in self.cached_new_object_detections:
                    bbox = detection.get('bbox', [])
                    confidence = detection.get('confidence', 0.0)
                    class_name = detection.get('class_name', 'Object')

                    if len(bbox) >= 4:
                        x1, y1, x2, y2 = bbox
                        # 绘制边界框（黄色）
                        cv2.rectangle(frame, (x1, y1), (x2, y2), (255, 255, 0), 2)
                        # 绘制标签
                        label = f"{class_name}: {confidence:.2f}"
                        cv2.putText(frame, label, (x1, y1-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 1)

        except Exception as e:
            print(f"⚠️ 绘制缓存新物体检测结果失败: {e}")

        return frame

    def _check_and_load_new_object_model(self):
        """检查并加载新物体检测模型（支持多模型管理）"""
        try:
            import os
            from config.new_object_detection_config import get_primary_model

            print(f"🔍 检查新物体检测模型...")

            # 获取主要模型
            primary_model = get_primary_model()

            if not primary_model:
                print(f"� 未发现新物体检测模型，第4帧保持预留状态")
                self.new_object_detection_enabled = False
                # 不显示详细的命名指南，保持后台输出简洁
                # self._show_naming_guide()
                return

            # 简化模型状态输出，保持后台信息简洁
            # print_models_status()

            # 尝试加载主要模型
            print(f"� 加载主要模型: {primary_model.name}")
            # 简化输出信息，保持后台简洁
            # print(f"   文件: {os.path.basename(primary_model.file_path)}")
            # print(f"   类型: {primary_model.model_type}")
            # print(f"   描述: {primary_model.description}")

            # 保存模型配置信息
            self.current_model_config = primary_model

            # 根据模型类型选择加载方式
            success = self._load_new_object_detector(primary_model.file_path)

            if success:
                self.new_object_detection_enabled = True
                print(f"✅ 新物体检测器加载成功")
            else:
                self.new_object_detection_enabled = False
                print(f"❌ 新物体检测器加载失败，第4帧保持预留状态")

        except Exception as e:
            print(f"⚠️ 检查新物体检测模型失败: {e}")
            self.new_object_detection_enabled = False

    def _show_naming_guide(self):
        """显示命名指南"""
        try:
            from config.new_object_detection_config import get_naming_guide
            guide = get_naming_guide()
            print(guide)
        except Exception as e:
            print(f"⚠️ 显示命名指南失败: {e}")

    def _load_new_object_detector(self, weight_path):
        """加载新物体检测器"""
        try:
            import os
            file_ext = os.path.splitext(weight_path)[1].lower()

            if file_ext in ['.pt', '.pth']:
                # PyTorch模型
                return self._load_pytorch_detector(weight_path)
            elif file_ext == '.onnx':
                # ONNX模型
                return self._load_onnx_detector(weight_path)
            elif file_ext in ['.tflite', '.pb']:
                # TensorFlow模型
                return self._load_tensorflow_detector(weight_path)
            elif file_ext == '.weights':
                # Darknet权重
                return self._load_darknet_detector(weight_path)
            else:
                print(f"⚠️ 不支持的模型格式: {file_ext}")
                return False

        except Exception as e:
            print(f"⚠️ 加载新物体检测器失败: {e}")
            return False

    def _load_pytorch_detector(self, weight_path):
        """加载PyTorch检测器"""
        try:
            import torch

            # 检查是否为YOLOv5/YOLOv8模型
            if self._is_yolo_model(weight_path):
                return self._load_yolo_detector(weight_path)

            # 通用PyTorch模型加载
            print(f"📥 加载PyTorch模型: {weight_path}")
            model = torch.load(weight_path, map_location='cpu')

            # 创建简单的检测器包装
            self.new_object_detector = {
                'model': model,
                'type': 'pytorch',
                'weight_path': weight_path
            }

            return True

        except Exception as e:
            print(f"⚠️ PyTorch模型加载失败: {e}")
            return False

    def _is_yolo_model(self, weight_path):
        """检查是否为YOLO模型"""
        try:
            import torch
            checkpoint = torch.load(weight_path, map_location='cpu')

            # 检查模型结构特征
            if isinstance(checkpoint, dict):
                if 'model' in checkpoint or 'ema' in checkpoint:
                    return True
                if any('yolo' in str(key).lower() for key in checkpoint.keys()):
                    return True

            return False

        except:
            return False

    def _load_yolo_detector(self, weight_path):
        """加载YOLO检测器"""
        try:
            # 尝试使用ultralytics
            try:
                from ultralytics import YOLO
                model = YOLO(weight_path)

                self.new_object_detector = {
                    'model': model,
                    'type': 'yolo_ultralytics',
                    'weight_path': weight_path
                }

                print(f"✅ YOLO模型加载成功 (ultralytics)")
                return True

            except ImportError:
                print(f"⚠️ ultralytics未安装，尝试其他方式")

            # 尝试使用torch.hub
            try:
                import torch
                model = torch.hub.load('ultralytics/yolov5', 'custom', path=weight_path)

                self.new_object_detector = {
                    'model': model,
                    'type': 'yolo_torch_hub',
                    'weight_path': weight_path
                }

                print(f"✅ YOLO模型加载成功 (torch.hub)")
                return True

            except Exception as e:
                print(f"⚠️ torch.hub加载失败: {e}")

            return False

        except Exception as e:
            print(f"⚠️ YOLO检测器加载失败: {e}")
            return False

    def _load_onnx_detector(self, weight_path):
        """加载ONNX检测器"""
        try:
            import onnxruntime as ort

            session = ort.InferenceSession(weight_path)

            self.new_object_detector = {
                'session': session,
                'type': 'onnx',
                'weight_path': weight_path
            }

            print(f"✅ ONNX模型加载成功")
            return True

        except ImportError:
            print(f"⚠️ onnxruntime未安装，无法加载ONNX模型")
            return False
        except Exception as e:
            print(f"⚠️ ONNX模型加载失败: {e}")
            return False

    def _load_tensorflow_detector(self, weight_path):
        """加载TensorFlow检测器"""
        try:
            import tensorflow as tf

            if weight_path.endswith('.tflite'):
                # TensorFlow Lite模型
                interpreter = tf.lite.Interpreter(model_path=weight_path)
                interpreter.allocate_tensors()

                self.new_object_detector = {
                    'interpreter': interpreter,
                    'type': 'tflite',
                    'weight_path': weight_path
                }
            else:
                # 标准TensorFlow模型
                model = tf.saved_model.load(weight_path)

                self.new_object_detector = {
                    'model': model,
                    'type': 'tensorflow',
                    'weight_path': weight_path
                }

            print(f"✅ TensorFlow模型加载成功")
            return True

        except ImportError:
            print(f"⚠️ tensorflow未安装，无法加载TensorFlow模型")
            return False
        except Exception as e:
            print(f"⚠️ TensorFlow模型加载失败: {e}")
            return False

    def _load_darknet_detector(self, weight_path):
        """加载Darknet检测器"""
        try:
            # 查找对应的配置文件
            import os
            base_path = os.path.splitext(weight_path)[0]
            cfg_path = base_path + '.cfg'

            if not os.path.exists(cfg_path):
                print(f"⚠️ 未找到Darknet配置文件: {cfg_path}")
                return False

            # 尝试使用OpenCV加载Darknet模型
            import cv2
            net = cv2.dnn.readNetFromDarknet(cfg_path, weight_path)

            self.new_object_detector = {
                'net': net,
                'type': 'darknet',
                'weight_path': weight_path,
                'cfg_path': cfg_path
            }

            print(f"✅ Darknet模型加载成功")
            return True

        except Exception as e:
            print(f"⚠️ Darknet模型加载失败: {e}")
            return False

    def _get_current_detection_type(self):
        """获取当前帧应该执行的检测类型（4帧循环）"""
        # 计算在4帧循环中的位置
        cycle_position = self.frame_counter % (self.detection_interval * 4)

        if cycle_position < self.detection_interval:
            return "human"  # 第1帧：人体检测
        elif cycle_position < self.detection_interval * 2:
            return "fire"   # 第2帧：火焰检测
        elif cycle_position < self.detection_interval * 3:
            return "smoke"  # 第3帧：烟雾检测
        else:
            return "reserved"  # 第4帧：预留给后续其他识别

    def _perform_human_detection(self, visible_frame):
        """执行人体检测并更新缓存"""
        result_frame = visible_frame.copy()

        if self.human_detection_enabled and self.human_detector:
            try:
                human_detections, result_frame = self.human_detector.detect_humans(result_frame)
                self.cached_human_detections = human_detections or []
            except Exception as e:
                print(f"⚠️ 人体检测失败: {e}")
                self.cached_human_detections = []

        return result_frame

    def _perform_fire_smoke_detection(self, visible_frame):
        """执行火焰烟雾检测并更新缓存"""
        result_frame = visible_frame.copy()

        if self.fire_smoke_detection_enabled and self.fire_smoke_detector:
            try:
                # 执行火焰烟雾检测
                detection_results = self.fire_smoke_detector.detect(result_frame)

                # 更新缓存
                self.cached_fire_smoke_detections = detection_results
                self.last_fire_smoke_detections = detection_results

                # 设置火焰分析结果（用于Qt界面显示）
                if hasattr(self, 'last_flame_analysis_results'):
                    self.last_flame_analysis_results = detection_results

                # 获取检测计数
                fire_count = len(detection_results.get('fire_detections', []))
                smoke_count = len(detection_results.get('smoke_detections', []))

                # 手动触发界面回调
                if detection_results and (fire_count > 0 or smoke_count > 0):
                    if hasattr(self.fire_smoke_detector, '_notify_ui_update'):
                        if hasattr(self.fire_smoke_detector, 'ui_update_callback') and self.fire_smoke_detector.ui_update_callback:
                            self.fire_smoke_detector._notify_ui_update(detection_results)
                        else:
                            self._try_setup_callback_from_system(detection_results)

                # 在图像上绘制检测结果
                result_frame = self._draw_fire_smoke_detections(result_frame, detection_results)

                # 导出到Excel（如果启用）
                if self.excel_exporter:
                    self._export_fire_smoke_detections_to_excel(detection_results)

            except Exception as e:
                print(f"⚠️ 火焰烟雾检测失败: {e}")
                self.cached_fire_smoke_detections = {}
                self.last_fire_smoke_detections = {}

        return result_frame

    def _draw_fire_detections(self, frame, fire_detections, fire_objects):
        """绘制火焰检测结果"""
        try:
            if fire_detections:
                for detection in fire_detections:
                    bbox = detection.get('bbox', [])
                    confidence = detection.get('confidence', 0.0)

                    if len(bbox) >= 4:
                        x1, y1, x2, y2 = map(int, bbox)
                        # 绘制火焰边界框（红色）
                        cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 0, 255), 2)
                        # 绘制标签
                        label = f"Fire: {confidence:.2f}"
                        cv2.putText(frame, label, (x1, y1-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)

        except Exception as e:
            print(f"⚠️ 绘制火焰检测结果失败: {e}")

        return frame

    def _draw_smoke_detections(self, frame, smoke_detections, smoke_objects):
        """绘制烟雾检测结果"""
        try:
            if smoke_detections:
                for detection in smoke_detections:
                    bbox = detection.get('bbox', [])
                    confidence = detection.get('confidence', 0.0)

                    if len(bbox) >= 4:
                        x1, y1, x2, y2 = map(int, bbox)
                        # 绘制烟雾边界框（灰色）
                        cv2.rectangle(frame, (x1, y1), (x2, y2), (128, 128, 128), 2)
                        # 绘制标签
                        label = f"Smoke: {confidence:.2f}"
                        cv2.putText(frame, label, (x1, y1-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (128, 128, 128), 1)

        except Exception as e:
            print(f"⚠️ 绘制烟雾检测结果失败: {e}")

        return frame

    def _draw_cached_fire_detections(self, frame):
        """绘制缓存的火焰检测结果"""
        try:
            if hasattr(self, 'cached_fire_detections') and self.cached_fire_detections:
                fire_detections = self.cached_fire_detections.get('fire_detections', [])
                fire_objects = self.cached_fire_detections.get('fire_objects', {})
                frame = self._draw_fire_detections(frame, fire_detections, fire_objects)
        except Exception as e:
            print(f"⚠️ 绘制缓存火焰检测结果失败: {e}")
        return frame

    def _draw_cached_smoke_detections(self, frame):
        """绘制缓存的烟雾检测结果"""
        try:
            if hasattr(self, 'cached_smoke_detections') and self.cached_smoke_detections:
                smoke_detections = self.cached_smoke_detections.get('smoke_detections', [])
                smoke_objects = self.cached_smoke_detections.get('smoke_objects', {})
                frame = self._draw_smoke_detections(frame, smoke_detections, smoke_objects)
        except Exception as e:
            print(f"⚠️ 绘制缓存烟雾检测结果失败: {e}")
        return frame

    def _draw_cached_human_detections(self, frame):
        """绘制缓存的人体检测结果"""
        if not self.cached_human_detections:
            return frame

        result_frame = frame.copy()
        for detection in self.cached_human_detections:
            result_frame = self._draw_human_detection(result_frame, detection)

        return result_frame

    def _draw_cached_fire_smoke_detections(self, frame):
        """绘制缓存的火焰烟雾检测结果"""
        if not self.cached_fire_smoke_detections:
            return frame

        return self._draw_fire_smoke_detections(frame, self.cached_fire_smoke_detections)

    def _draw_human_detection(self, frame, detection):
        """绘制单个人体检测结果"""
        x, y, w, h = detection['bbox']
        confidence = detection['confidence']

        # 绘制边界框
        color = (0, 255, 0)  # 绿色
        thickness = 2
        cv2.rectangle(frame, (x, y), (x + w, y + h), color, thickness)

        # 绘制标签
        label = f"人体 {confidence:.2f}"
        cv2.putText(frame, label, (x, y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)

        return frame

    def set_alternating_detection_enabled(self, enabled: bool):
        """设置是否启用交替检测"""
        self.alternating_detection_enabled = enabled
        self.alternating_config.enabled = enabled
        print(f"🔄 交替检测模式: {'启用' if enabled else '禁用'}")

    def set_detection_interval(self, interval: int):
        """设置检测间隔"""
        self.detection_interval = max(1, interval)
        self.alternating_config.detection_interval = interval
        print(f"🔄 检测间隔设置为: {self.detection_interval}帧")

    def load_alternating_config(self, preset_name: str = "default"):
        """加载交替检测配置预设"""
        from config.alternating_detection_config import get_config_by_preset

        self.alternating_config = get_config_by_preset(preset_name)
        self.alternating_detection_enabled = self.alternating_config.enabled
        self.detection_interval = self.alternating_config.detection_interval

        print(f"🔄 已加载交替检测配置: {preset_name}")
        if self.alternating_config.debug_enabled:
            print_config_info(self.alternating_config)

    def get_detection_performance_stats(self):
        """获取检测性能统计"""
        return {
            'frame_counter': self.frame_counter,
            'alternating_enabled': self.alternating_detection_enabled,
            'detection_interval': self.detection_interval,
            'cached_human_count': len(self.cached_human_detections),
            'cached_fire_smoke_count': len(self.cached_fire_smoke_detections.get('fire_detections', [])) +
                                     len(self.cached_fire_smoke_detections.get('smoke_detections', []))
        }

    def set_heat_detection_applier(self, applier_func) -> None:
        """设置热源检测应用函数"""
        self.heat_detection_applier = applier_func

    def set_human_detector(self, human_detector) -> None:
        """设置人体检测器"""
        self.human_detector = human_detector

    def enable_human_detection(self, enabled: bool) -> None:
        """启用/禁用人体检测"""
        self.human_detection_enabled = enabled
        if enabled and self.human_detector:
            print("✅ 人体检测已启用")
        else:
            print("❌ 人体检测已禁用")

    def toggle_thermal_human_detection(self) -> bool:
        """切换热成像人体检测状态"""
        self.thermal_human_detection_enabled = not self.thermal_human_detection_enabled
        if self.thermal_human_detection_enabled and self.human_detector:
            print("✅ 热成像人体检测已启用")
        else:
            print("❌ 热成像人体检测已禁用")
        return self.thermal_human_detection_enabled

    def enable_thermal_human_detection(self, enabled: bool) -> None:
        """启用/禁用热成像人体检测"""
        self.thermal_human_detection_enabled = enabled
        if enabled and self.human_detector:
            print("✅ 热成像人体检测已启用")
        else:
            print("❌ 热成像人体检测已禁用")

    def is_thermal_human_detection_enabled(self) -> bool:
        """获取热成像人体检测状态"""
        return self.thermal_human_detection_enabled

    def set_fire_smoke_detection_enabled(self, enabled):
        """设置火焰烟雾检测启用状态"""
        self.fire_smoke_detection_enabled = enabled
        print(f"火焰烟雾检测: {'启用' if enabled else '禁用'}")

        if enabled and self.fire_smoke_detector and self.fire_smoke_detector.is_available():
            print("✅ 火焰烟雾检测已启用")
        elif enabled:
            print("⚠️ 火焰烟雾检测器不可用")
        else:
            print("❌ 火焰烟雾检测已禁用")

    def get_fire_smoke_detection_status(self):
        """获取火焰烟雾检测状态"""
        detector_available = self.fire_smoke_detector and self.fire_smoke_detector.is_available()
        detector_initialized = self.fire_smoke_detector and hasattr(self.fire_smoke_detector, 'is_initialized') and self.fire_smoke_detector.is_initialized

        return {
            'enabled': self.fire_smoke_detection_enabled,
            'detector_available': detector_available,
            'detector_initialized': detector_initialized
        }

    def is_fire_smoke_detection_enabled(self) -> bool:
        """获取火焰烟雾检测状态"""
        return self.fire_smoke_detection_enabled

    def set_excel_exporter(self, excel_exporter):
        """设置Excel数据导出器"""
        self.excel_exporter = excel_exporter

    def process_visible_frame(self, visible_frame):
        """处理可见光帧（包含火焰烟雾检测）"""
        if visible_frame is None:
            return None

        try:
            # 更新当前可见光帧引用
            self.current_visible_frame = visible_frame

            # 应用可见光检测（人体检测 + 火焰烟雾检测）
            processed_frame = self._apply_visible_light_detection(visible_frame)

            return processed_frame

        except Exception as e:
            print(f"⚠️ 处理可见光帧失败: {e}")
            return visible_frame

    def process_thermal_frame(self, thermal_frame):
        """处理红外帧（包含热源检测）"""
        if thermal_frame is None:
            return None

        try:
            # 更新当前红外帧引用
            self.current_thermal_frame = thermal_frame

            # 应用热成像检测
            processed_frame = self._apply_thermal_detection_with_priority(thermal_frame)

            return processed_frame

        except Exception as e:
            print(f"⚠️ 处理红外帧失败: {e}")
            return thermal_frame

    def _export_fire_smoke_detections_to_excel(self, detection_results):
        """导出火焰烟雾检测结果到Excel表格"""
        try:
            if not self.excel_exporter:
                return

            # 获取检测结果
            fire_detections = detection_results.get('fire_detections', [])
            smoke_detections = detection_results.get('smoke_detections', [])
            fire_objects = detection_results.get('fire_objects', {})
            smoke_objects = detection_results.get('smoke_objects', {})

            # 导出火焰检测结果（使用跟踪对象信息）
            for obj_id, obj_data in fire_objects.items():
                # 创建火焰事件对象，包含更多特征数据
                class FireEvent:
                    def __init__(self, obj_data, obj_id):
                        self.tracking_id = obj_id
                        bbox = obj_data.get('bbox', [0, 0, 0, 0])
                        self.bbox = bbox

                        # 计算几何特征
                        width = bbox[2] - bbox[0] if len(bbox) >= 4 else 0
                        height = bbox[3] - bbox[1] if len(bbox) >= 4 else 0
                        self.area = width * height

                        # 从跟踪数据获取变化率（如果可用）
                        self.area_change_rate = obj_data.get('area_change_rate', 0.0)

                        # 基本检测信息
                        self.confidence = obj_data.get('confidence', 0.0)
                        self.frame_count = obj_data.get('frame_count', 1)
                        self.total_distance = obj_data.get('total_distance', 0.0)

                        # 中心点（火焰根部）
                        centroid = obj_data.get('centroid', (bbox[0] + width/2, bbox[1] + height))
                        self.centroid_x = centroid[0]
                        self.centroid_y = centroid[1]

                        # 快照文件名
                        self.snapshot_filename = obj_data.get('snapshot_filename', None)

                fire_event = FireEvent(obj_data, obj_id)
                self.excel_exporter.add_flame_event(fire_event)

            # 导出烟雾检测结果（使用跟踪对象信息）
            for obj_id, obj_data in smoke_objects.items():
                # 创建烟雾事件对象，包含更多特征数据
                class SmokeEvent:
                    def __init__(self, obj_data, obj_id):
                        self.tracking_id = obj_id
                        bbox = obj_data.get('bbox', [0, 0, 0, 0])
                        self.bbox = bbox

                        # 计算几何特征
                        width = bbox[2] - bbox[0] if len(bbox) >= 4 else 0
                        height = bbox[3] - bbox[1] if len(bbox) >= 4 else 0
                        self.area = width * height

                        # 从跟踪数据获取变化率（如果可用）
                        self.area_change_rate = obj_data.get('area_change_rate', 0.0)

                        # 基本检测信息
                        self.confidence = obj_data.get('confidence', 0.0)
                        self.frame_count = obj_data.get('frame_count', 1)
                        self.total_distance = obj_data.get('total_distance', 0.0)

                        # 中心点（烟雾根部）
                        centroid = obj_data.get('centroid', (bbox[0] + width/2, bbox[1] + height))
                        self.centroid_x = centroid[0]
                        self.centroid_y = centroid[1]

                        # 快照文件名
                        self.snapshot_filename = obj_data.get('snapshot_filename', None)

                smoke_event = SmokeEvent(obj_data, obj_id)
                self.excel_exporter.add_smoke_event(smoke_event)

        except Exception as e:
            print(f"⚠️ 导出火焰烟雾检测结果到Excel失败: {e}")

    def _calculate_detection_area(self, bbox):
        """计算检测区域面积"""
        try:
            x, y, w, h = bbox
            return w * h
        except:
            return 0

    def _try_setup_callback_from_system(self, detection_results):
        """尝试从系统实例设置回调并直接更新界面"""
        try:
            # 尝试获取系统实例（通过全局变量或其他方式）
            import sys

            # 查找Qt应用实例
            try:
                from PyQt5.QtWidgets import QApplication
                app = QApplication.instance()
                if app:
                    # 查找主窗口
                    for widget in app.topLevelWidgets():
                        if hasattr(widget, '_on_fire_smoke_detection'):
                            print(f"🔍 找到主窗口，直接调用回调")
                            widget._on_fire_smoke_detection(detection_results)
                            return
            except ImportError:
                pass



        except Exception as e:
            pass  # 静默处理回调设置失败
