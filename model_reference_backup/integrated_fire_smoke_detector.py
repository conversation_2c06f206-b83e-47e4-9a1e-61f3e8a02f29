#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
集成火焰烟雾检测器
将模块化的火焰烟雾检测系统适配到现有的热成像监控系统中
"""

import sys
import os
import cv2
import numpy as np
import time
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any

# 添加模块化检测系统路径
sys.path.append(str(Path(__file__).parent.parent.parent / "2" / "modules"))

try:
    # 导入模块化检测系统
    from config_module import ConfigManager
    from detection_module import FireSmokeDetectionEngine
    from tracking_module import ObjectTrackingEngine
    from analysis_module import FeatureAnalysisEngine
    from data_module import DataExportEngine
    MODULES_AVAILABLE = True
    print("✅ 模块化检测系统导入成功")
except ImportError as e:
    print(f"⚠️ 模块化检测系统导入失败: {e}")
    print("💡 提示：请确保已安装模块化检测系统的依赖，或运行: pip install -r 2/requirements.txt")
    MODULES_AVAILABLE = False
except Exception as e:
    print(f"⚠️ 模块化检测系统初始化异常: {e}")
    MODULES_AVAILABLE = False

from utils.logger import get_logger


class IntegratedFireSmokeDetector:
    """集成火焰烟雾检测器 - 适配现有系统架构"""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化集成检测器
        
        Args:
            config_path: 配置文件路径
        """
        self.logger = get_logger("IntegratedFireSmokeDetector")
        
        # 初始化基本属性（无论模块是否可用都要设置）
        self.enabled = False
        self.is_initialized = False  # 添加初始化状态标志
        self.detection_mode = "single"  # "single" 或 "dual"
        self.enable_tracking = True
        self.enable_analysis = True

        # 统计信息
        self.detection_count = 0
        self.last_detection_time = 0
        self.total_fire_detections = 0
        self.total_smoke_detections = 0

        # 界面更新回调函数
        self.ui_update_callback = None

        # 检查模块可用性
        if not MODULES_AVAILABLE:
            self.logger.error("模块化检测系统不可用")
            self.enabled = False
            # 设置为已初始化，但不可用
            self.is_initialized = True
            # 初始化空的引擎引用
            self.config_manager = None
            self.detection_engine = None
            self.tracking_engine = None
            self.analysis_engine = None
            self.data_engine = None
            return

        # 初始化配置
        self.config_path = config_path or self._get_default_config_path()
        self.config_manager = None

        # 核心引擎
        self.detection_engine = None
        self.tracking_engine = None
        self.analysis_engine = None
        self.data_engine = None
        
        # 结果缓存
        self.last_results = {
            'fire_detections': [],
            'smoke_detections': [],
            'fire_objects': {},
            'smoke_objects': {},
            'statistics': {}
        }
        
        # 初始化系统
        self._initialize_system()
    
    def _get_default_config_path(self) -> str:
        """获取默认配置路径"""
        # 尝试找到模块化系统的配置文件
        possible_paths = [
            Path(__file__).parent.parent.parent / "2" / "config" / "config.yaml",
            Path(__file__).parent.parent.parent / "2" / "modules" / "config.yaml",
            Path(__file__).parent.parent.parent / "config" / "fire_detection_config.yaml"
        ]
        
        for path in possible_paths:
            if path.exists():
                return str(path)
        
        # 如果没有找到，返回None使用默认配置
        return None
    
    def _initialize_system(self):
        """初始化检测系统"""
        try:
            # 初始化配置管理器
            self.config_manager = ConfigManager(self.config_path)
            
            # 初始化检测引擎
            detection_config = self.config_manager.get_detection_config()
            self.detection_engine = FireSmokeDetectionEngine(detection_config)
            
            # 初始化跟踪引擎
            if self.enable_tracking:
                tracking_config = self.config_manager.get_tracking_config()
                self.tracking_engine = ObjectTrackingEngine(
                    max_disappeared=tracking_config.get('max_disappeared', 10),
                    max_distance=tracking_config.get('max_distance', 100)
                )
            
            # 初始化分析引擎
            if self.enable_analysis:
                analysis_config = self.config_manager.get_analysis_config()
                self.analysis_engine = FeatureAnalysisEngine(analysis_config)
                
                # 初始化数据导出引擎（使用独立路径避免与主系统冲突）
                tracking_config = self.config_manager.get_tracking_config()
                output_path = tracking_config.get('output_path', 'data_exports/modular_fire_smoke_analysis.xlsx')
                self.data_engine = DataExportEngine(output_path)
            
            self.logger.info("✅ 集成火焰烟雾检测器初始化成功")
            self.enabled = True
            self.is_initialized = True

            # 自动加载硬编码的模型路径
            self._load_hardcoded_models()
            
        except Exception as e:
            self.logger.error(f"❌ 集成火焰烟雾检测器初始化失败: {e}")
            self.enabled = False
            self.is_initialized = False

    def _load_hardcoded_models(self):
        """自动加载硬编码的模型路径"""
        try:
            # 使用相对于项目根目录的模型路径
            fire_model_path = "models/fs/best-1.pt"  # 火焰检测模型
            smoke_model_path = "models/fs/best-2.pt"  # 烟雾检测模型

            # 检查模型文件是否存在
            from pathlib import Path
            fire_path = Path(fire_model_path)
            smoke_path = Path(smoke_model_path)

            if fire_path.exists() and smoke_path.exists():
                self.logger.info(f"🔍 开始加载双权重模型...")
                self.logger.info(f"火焰模型: {fire_model_path}")
                self.logger.info(f"烟雾模型: {smoke_model_path}")

                # 加载双权重模型
                success = self.load_models(
                    fire_model_path=fire_model_path,
                    smoke_model_path=smoke_model_path
                )

                if success:
                    self.logger.info("🎉 双权重模型加载成功！火焰烟雾检测已就绪")
                    # 自动启用火焰烟雾检测
                    self.enable_detection(True)
                    self.logger.info("🔥 火焰烟雾检测已自动启用")
                else:
                    self.logger.error("❌ 双权重模型加载失败")
            else:
                self.logger.warning(f"⚠️ 模型文件不存在:")
                if not fire_path.exists():
                    self.logger.warning(f"   火焰模型: {fire_model_path}")
                if not smoke_path.exists():
                    self.logger.warning(f"   烟雾模型: {smoke_model_path}")
                self.logger.info("💡 请确保模型文件存在于指定路径")

        except Exception as e:
            self.logger.error(f"❌ 自动加载模型失败: {e}")
    
    def load_models(self, single_model_path: str = None, 
                   fire_model_path: str = None, smoke_model_path: str = None) -> bool:
        """
        加载检测模型
        
        Args:
            single_model_path: 单权重模型路径
            fire_model_path: 火焰检测模型路径
            smoke_model_path: 烟雾检测模型路径
            
        Returns:
            是否加载成功
        """
        if not self.enabled or not self.detection_engine:
            self.logger.error("检测器未正确初始化")
            return False
        
        try:
            if single_model_path:
                success = self.detection_engine.load_single_model(single_model_path)
                if success:
                    self.detection_mode = "single"
                    self.logger.info(f"✅ 单权重模型加载成功: {Path(single_model_path).name}")
                else:
                    self.logger.error(f"❌ 单权重模型加载失败: {self.detection_engine.last_error}")
                return success
            
            elif fire_model_path and smoke_model_path:
                success = self.detection_engine.load_dual_models(fire_model_path, smoke_model_path)
                if success:
                    self.detection_mode = "dual"
                    self.logger.info("✅ 双权重模型加载成功")
                else:
                    self.logger.error(f"❌ 双权重模型加载失败: {self.detection_engine.last_error}")
                return success
            
            else:
                self.logger.error("请提供有效的模型路径")
                return False
                
        except Exception as e:
            self.logger.error(f"模型加载异常: {e}")
            return False
    
    def detect(self, frame: np.ndarray) -> Dict[str, Any]:
        """
        执行火焰烟雾检测
        
        Args:
            frame: 输入图像帧
            
        Returns:
            检测结果字典
        """
        if not self.enabled or not self.detection_engine:
            return self._get_empty_result()

        try:
            start_time = time.time()

            # 调试信息：检测方法开始
            if hasattr(self, '_debug_frame_count'):
                self._debug_frame_count += 1
            else:
                self._debug_frame_count = 1

            if self._debug_frame_count % 30 == 0:  # 每30帧打印一次
                print(f"🔍 集成检测器执行检测，帧数: {self._debug_frame_count}")
            
            # 执行检测
            if self.detection_mode == "single":
                detections = self.detection_engine.detect_single_model(frame)
            else:
                detections = self.detection_engine.detect_dual_model(frame)
            
            # 分离火焰和烟雾检测
            fire_detections = [d for d in detections if d['class_name'] == 'fire']
            smoke_detections = [d for d in detections if d['class_name'] == 'smoke']
            
            # 更新统计
            self.detection_count += 1
            self.last_detection_time = time.time()
            self.total_fire_detections += len(fire_detections)
            self.total_smoke_detections += len(smoke_detections)
            
            # 构建基础结果
            result = {
                'fire_detections': fire_detections,
                'smoke_detections': smoke_detections,
                'total_detections': len(detections),
                'detection_time': time.time() - start_time,
                'frame_count': self.detection_count,
                'fire_objects': {},  # 总是包含这些键
                'smoke_objects': {},
                'fire_trajectories': {},
                'smoke_trajectories': {}
            }

            # 执行跟踪（如果启用）
            if self.tracking_engine:
                # 定期清理过期轨迹（每100帧清理一次）
                if hasattr(self, 'frame_count'):
                    self.frame_count += 1
                else:
                    self.frame_count = 1

                if self.frame_count % 100 == 0:
                    if hasattr(self.tracking_engine, 'cleanup_old_trajectories'):
                        self.tracking_engine.cleanup_old_trajectories(max_age_seconds=10.0)

                # 分别跟踪火焰和烟雾
                fire_objects = self.tracking_engine.update_tracks(fire_detections) if fire_detections else {}
                smoke_objects = self.tracking_engine.update_tracks(smoke_detections) if smoke_detections else {}

                result.update({
                    'fire_objects': fire_objects,
                    'smoke_objects': smoke_objects,
                    'fire_trajectories': self.tracking_engine.get_trajectories(),
                    'smoke_trajectories': self.tracking_engine.get_trajectories()
                })
            
            # 执行特征分析（如果启用）
            if self.analysis_engine and self.tracking_engine:
                try:
                    # 对火焰对象进行特征分析
                    for obj_id, obj_data in fire_objects.items():
                        bbox = obj_data.get('bbox', [0, 0, 0, 0])
                        if len(bbox) >= 4:
                            # 提取几何特征
                            geometric_features = self.analysis_engine.extract_geometric_features(bbox)
                            obj_data['geometric_features'] = geometric_features

                            # 更新闪烁分析（如果有置信度历史）
                            confidence = obj_data.get('confidence', 0.0)
                            # 使用正确的方法名：update 而不是 update_confidence
                            self.analysis_engine.flicker_analyzer.update(obj_id, confidence)
                            # 计算闪烁特征
                            flicker_frequency = self.analysis_engine.flicker_analyzer.calculate_flicker_frequency(obj_id)
                            flicker_intensity = self.analysis_engine.flicker_analyzer.calculate_flicker_intensity(obj_id)
                            if flicker_frequency > 0 or flicker_intensity > 0:
                                obj_data['flicker_analysis'] = {
                                    'frequency': flicker_frequency,
                                    'intensity': flicker_intensity
                                }

                    # 对烟雾对象进行特征分析
                    for obj_id, obj_data in smoke_objects.items():
                        bbox = obj_data.get('bbox', [0, 0, 0, 0])
                        if len(bbox) >= 4:
                            # 提取几何特征
                            geometric_features = self.analysis_engine.extract_geometric_features(bbox)
                            obj_data['geometric_features'] = geometric_features

                            # 更新闪烁分析
                            confidence = obj_data.get('confidence', 0.0)
                            # 使用正确的方法名：update 而不是 update_confidence
                            self.analysis_engine.flicker_analyzer.update(obj_id, confidence)
                            # 计算闪烁特征
                            flicker_frequency = self.analysis_engine.flicker_analyzer.calculate_flicker_frequency(obj_id)
                            flicker_intensity = self.analysis_engine.flicker_analyzer.calculate_flicker_intensity(obj_id)
                            if flicker_frequency > 0 or flicker_intensity > 0:
                                obj_data['flicker_analysis'] = {
                                    'frequency': flicker_frequency,
                                    'intensity': flicker_intensity
                                }

                except Exception as e:
                    self.logger.warning(f"特征分析失败: {e}")
            
            # 添加到数据引擎（如果启用）
            if self.data_engine:
                frame_data = {
                    'frame_num': self.detection_count,
                    'timestamp': self.last_detection_time,
                    'fire_objects': result.get('fire_objects', {}),
                    'smoke_objects': result.get('smoke_objects', {}),
                    'image': frame,
                    'fps': 30.0,  # 默认FPS
                    'fire_trajectories': result.get('fire_trajectories', {}),
                    'smoke_trajectories': result.get('smoke_trajectories', {})
                }
                self.data_engine.add_detection_data(frame_data)

            # 缓存结果
            self.last_results = result



            # 通知界面更新（新增）
            self._notify_ui_update(result)

            return result
            
        except Exception as e:
            self.logger.error(f"检测过程异常: {e}")
            return self._get_empty_result()
    
    def _get_empty_result(self) -> Dict[str, Any]:
        """获取空的检测结果"""
        return {
            'fire_detections': [],
            'smoke_detections': [],
            'total_detections': 0,
            'detection_time': 0.0,
            'frame_count': self.detection_count,
            'fire_objects': {},
            'smoke_objects': {},
            'fire_trajectories': {},
            'smoke_trajectories': {}
        }

    def set_detection_parameters(self, confidence: float, iou: float):
        """
        设置检测参数

        Args:
            confidence: 置信度阈值
            iou: IoU阈值
        """
        if self.detection_engine:
            self.detection_engine.set_thresholds(confidence, iou)
            self.logger.info(f"检测参数已更新: confidence={confidence:.2f}, iou={iou:.2f}")

    def set_tracking_parameters(self, max_disappeared: int, max_distance: float):
        """
        设置跟踪参数

        Args:
            max_disappeared: 最大消失帧数
            max_distance: 最大匹配距离
        """
        if self.tracking_engine:
            self.tracking_engine.set_parameters(max_disappeared, max_distance)
            self.logger.info(f"跟踪参数已更新: disappeared={max_disappeared}, distance={max_distance}")

    def get_statistics(self) -> Dict[str, Any]:
        """
        获取检测统计信息

        Returns:
            统计信息字典
        """
        stats = {
            'enabled': self.enabled,
            'detection_mode': self.detection_mode,
            'detection_count': self.detection_count,
            'total_fire_detections': self.total_fire_detections,
            'total_smoke_detections': self.total_smoke_detections,
            'last_detection_time': self.last_detection_time,
            'enable_tracking': self.enable_tracking,
            'enable_analysis': self.enable_analysis
        }

        # 添加引擎统计
        if self.detection_engine:
            detection_stats = self.detection_engine.get_statistics()
            stats['detection_engine'] = detection_stats

        if self.tracking_engine:
            tracking_stats = self.tracking_engine.get_statistics()
            stats['tracking_engine'] = tracking_stats

        if self.data_engine:
            data_stats = self.data_engine.get_statistics()
            stats['data_engine'] = data_stats

        return stats

    def export_data(self, output_path: str = None) -> str:
        """
        导出分析数据

        Args:
            output_path: 输出文件路径

        Returns:
            导出文件路径
        """
        if not self.data_engine:
            raise RuntimeError("数据导出引擎未初始化")

        if output_path:
            self.data_engine.output_path = Path(output_path)

        return self.data_engine.export_to_excel()

    def set_ui_update_callback(self, callback):
        """设置界面更新回调函数"""
        self.ui_update_callback = callback
        self.logger.info("界面更新回调已设置")

    def _notify_ui_update(self, result):
        """通知界面更新"""
        if self.ui_update_callback:
            try:
                # 添加调试输出
                fire_count = len(result.get('fire_objects', {}))
                smoke_count = len(result.get('smoke_objects', {}))
                if fire_count > 0 or smoke_count > 0:
                    print(f"🔥💨 检测器正在调用界面回调: {fire_count}个火焰对象, {smoke_count}个烟雾对象")

                self.ui_update_callback(result)

                if fire_count > 0 or smoke_count > 0:
                    print(f"✅ 界面回调调用完成")

            except Exception as e:
                self.logger.error(f"界面更新回调执行失败: {e}")
                print(f"❌ 界面回调执行失败: {e}")
        else:
            # 只在有检测结果时输出警告
            fire_count = len(result.get('fire_objects', {}))
            smoke_count = len(result.get('smoke_objects', {}))
            if fire_count > 0 or smoke_count > 0:
                self.logger.warning(f"检测到目标但没有设置界面更新回调: {fire_count}个火焰, {smoke_count}个烟雾")

    def reset_statistics(self):
        """重置统计信息"""
        self.detection_count = 0
        self.last_detection_time = 0
        self.total_fire_detections = 0
        self.total_smoke_detections = 0

        if self.detection_engine:
            self.detection_engine.reset_statistics()

        if self.tracking_engine:
            self.tracking_engine.reset()

        if self.analysis_engine:
            self.analysis_engine.flicker_analyzer.reset()

        if self.data_engine:
            self.data_engine.reset()

        self.logger.info("统计信息已重置")

    def is_available(self) -> bool:
        """检查检测器是否可用"""
        return MODULES_AVAILABLE and self.enabled

    def get_last_results(self) -> Dict[str, Any]:
        """获取最后一次检测结果"""
        return self.last_results.copy()

    def enable_detection(self, enable: bool):
        """启用/禁用检测"""
        if not MODULES_AVAILABLE:
            self.logger.error("模块化检测系统不可用，无法启用检测")
            return

        self.enabled = enable
        self.logger.info(f"火焰烟雾检测已{'启用' if enable else '禁用'}")

    def set_detection_mode(self, mode: str):
        """
        设置检测模式

        Args:
            mode: "single" 或 "dual"
        """
        if mode in ["single", "dual"]:
            self.detection_mode = mode
            self.logger.info(f"检测模式已设置为: {mode}")
        else:
            self.logger.error(f"无效的检测模式: {mode}")

    def enable_tracking(self, enable: bool):
        """启用/禁用跟踪"""
        self.enable_tracking = enable
        self.logger.info(f"目标跟踪已{'启用' if enable else '禁用'}")

    def enable_analysis(self, enable: bool):
        """启用/禁用特征分析"""
        self.enable_analysis = enable
        self.logger.info(f"特征分析已{'启用' if enable else '禁用'}")

    def get_config_info(self) -> Dict[str, Any]:
        """获取配置信息"""
        if not self.config_manager:
            return {}

        return {
            'config_path': self.config_path,
            'detection_config': self.config_manager.get_detection_config(),
            'tracking_config': self.config_manager.get_tracking_config(),
            'analysis_config': self.config_manager.get_analysis_config()
        }


