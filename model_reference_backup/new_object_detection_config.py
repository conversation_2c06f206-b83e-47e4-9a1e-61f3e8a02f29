#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新物体检测模型配置管理
支持多模型命名规范和优先级管理
"""

import os
import json
from dataclasses import dataclass
from typing import Dict, List, Optional, Any

@dataclass
class ModelConfig:
    """单个模型配置"""
    name: str                    # 模型名称
    file_path: str              # 文件路径
    priority: int               # 优先级 (数字越小优先级越高)
    enabled: bool               # 是否启用
    description: str            # 模型描述
    categories: List[str]       # 检测类别
    confidence_threshold: float # 置信度阈值
    model_type: str            # 模型类型 (yolo, onnx, tensorflow, etc.)
    
class NewObjectDetectionConfig:
    """新物体检测配置管理器"""
    
    def __init__(self):
        self.models_path = os.path.join("models", "new")
        self.config_file = os.path.join(self.models_path, "models_config.json")
        self.supported_formats = [".pt", ".pth", ".weights", ".onnx", ".tflite", ".pb"]
        self.model_configs: Dict[str, ModelConfig] = {}
        
        # 默认命名规范
        self.naming_conventions = {
            "vehicle": {
                "pattern": "vehicle_*.pt",
                "description": "车辆检测模型",
                "priority": 1,
                "categories": ["car", "truck", "bus", "motorcycle", "bicycle"]
            },
            "animal": {
                "pattern": "animal_*.pt", 
                "description": "动物检测模型",
                "priority": 2,
                "categories": ["dog", "cat", "bird", "horse", "cow"]
            },
            "tool": {
                "pattern": "tool_*.pt",
                "description": "工具检测模型", 
                "priority": 3,
                "categories": ["hammer", "screwdriver", "wrench", "drill"]
            },
            "safety": {
                "pattern": "safety_*.pt",
                "description": "安全设备检测模型",
                "priority": 1,
                "categories": ["helmet", "vest", "gloves", "mask"]
            },
            "general": {
                "pattern": "general_*.pt",
                "description": "通用物体检测模型",
                "priority": 5,
                "categories": ["person", "car", "chair", "table", "bottle"]
            }
        }
    
    def get_naming_guide(self) -> str:
        """获取命名指南"""
        guide = """
🏷️ 模型权重命名规范指南

📁 推荐的命名格式:
   {类别}_{具体描述}_{版本}.{扩展名}

🎯 预定义类别:
   • vehicle_*     - 车辆检测 (汽车、卡车、摩托车等)
   • animal_*      - 动物检测 (狗、猫、鸟类等)  
   • tool_*        - 工具检测 (锤子、螺丝刀、扳手等)
   • safety_*      - 安全设备 (头盔、防护服、手套等)
   • general_*     - 通用检测 (多类别通用模型)

📝 命名示例:
   ✅ 推荐命名:
      • vehicle_yolov8_v1.pt        - 车辆检测YOLOv8模型v1版本
      • animal_detection_v2.onnx    - 动物检测模型v2版本
      • tool_recognition_best.pt    - 工具识别最佳模型
      • safety_equipment_v3.tflite  - 安全设备检测v3版本
      • general_coco_yolo.pt        - 通用COCO数据集YOLO模型
   
   ❌ 不推荐命名:
      • model.pt                    - 名称不明确
      • yolo.weights               - 缺少类别信息
      • detection_model_final.onnx  - 类别不清晰

🔢 优先级规则:
   1. safety_*     (优先级1 - 安全相关最重要)
   2. vehicle_*    (优先级1 - 车辆检测重要)  
   3. animal_*     (优先级2 - 动物检测)
   4. tool_*       (优先级3 - 工具检测)
   5. general_*    (优先级5 - 通用检测)
   6. 其他自定义   (优先级10 - 默认优先级)

📋 配置文件:
   程序会在 models/new/models_config.json 中保存模型配置
   可以手动编辑此文件来调整优先级和设置

🔧 支持的格式:
   • PyTorch:     .pt, .pth
   • ONNX:        .onnx  
   • TensorFlow:  .pb, .tflite
   • Darknet:     .weights (需要同名.cfg文件)
"""
        return guide
    
    def scan_models(self) -> List[str]:
        """扫描models/new文件夹中的所有模型文件"""
        if not os.path.exists(self.models_path):
            os.makedirs(self.models_path, exist_ok=True)
            return []
        
        import glob
        model_files = []
        
        for ext in self.supported_formats:
            pattern = os.path.join(self.models_path, f"*{ext}")
            model_files.extend(glob.glob(pattern))
        
        return [os.path.basename(f) for f in model_files]
    
    def analyze_model_name(self, filename: str) -> Dict[str, Any]:
        """分析模型文件名，提取信息"""
        name_lower = filename.lower()
        
        # 检查是否匹配预定义类别
        for category, config in self.naming_conventions.items():
            if category in name_lower:
                return {
                    "category": category,
                    "description": config["description"],
                    "priority": config["priority"],
                    "categories": config["categories"],
                    "confidence_threshold": 0.5
                }
        
        # 未匹配预定义类别，使用默认配置
        return {
            "category": "custom",
            "description": f"自定义模型: {filename}",
            "priority": 10,
            "categories": ["unknown"],
            "confidence_threshold": 0.5
        }
    
    def load_config(self) -> bool:
        """加载模型配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                
                self.model_configs = {}
                for filename, data in config_data.items():
                    self.model_configs[filename] = ModelConfig(
                        name=data.get("name", filename),
                        file_path=os.path.join(self.models_path, filename),
                        priority=data.get("priority", 10),
                        enabled=data.get("enabled", True),
                        description=data.get("description", ""),
                        categories=data.get("categories", []),
                        confidence_threshold=data.get("confidence_threshold", 0.5),
                        model_type=data.get("model_type", "unknown")
                    )
                
                print(f"✅ 加载模型配置文件: {len(self.model_configs)} 个模型")
                return True
            else:
                print(f"📋 配置文件不存在，将创建新配置")
                return False
                
        except Exception as e:
            print(f"⚠️ 加载配置文件失败: {e}")
            return False
    
    def save_config(self) -> bool:
        """保存模型配置文件"""
        try:
            os.makedirs(self.models_path, exist_ok=True)
            
            config_data = {}
            for filename, model_config in self.model_configs.items():
                config_data[filename] = {
                    "name": model_config.name,
                    "priority": model_config.priority,
                    "enabled": model_config.enabled,
                    "description": model_config.description,
                    "categories": model_config.categories,
                    "confidence_threshold": model_config.confidence_threshold,
                    "model_type": model_config.model_type
                }
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            
            print(f"✅ 保存模型配置文件: {len(config_data)} 个模型")
            return True
            
        except Exception as e:
            print(f"⚠️ 保存配置文件失败: {e}")
            return False
    
    def update_models(self) -> List[ModelConfig]:
        """更新模型列表，返回按优先级排序的可用模型"""
        # 扫描文件夹
        model_files = self.scan_models()
        
        # 加载现有配置
        self.load_config()
        
        # 处理新发现的模型
        for filename in model_files:
            if filename not in self.model_configs:
                # 新模型，分析并添加配置
                analysis = self.analyze_model_name(filename)
                
                file_ext = os.path.splitext(filename)[1].lower()
                model_type = self._get_model_type(file_ext)
                
                self.model_configs[filename] = ModelConfig(
                    name=analysis["category"] + "_" + os.path.splitext(filename)[0],
                    file_path=os.path.join(self.models_path, filename),
                    priority=analysis["priority"],
                    enabled=True,
                    description=analysis["description"],
                    categories=analysis["categories"],
                    confidence_threshold=analysis["confidence_threshold"],
                    model_type=model_type
                )
        
        # 移除不存在的模型
        existing_files = set(model_files)
        to_remove = [f for f in self.model_configs.keys() if f not in existing_files]
        for filename in to_remove:
            del self.model_configs[filename]
        
        # 保存更新后的配置
        self.save_config()
        
        # 返回按优先级排序的启用模型
        enabled_models = [config for config in self.model_configs.values() if config.enabled]
        enabled_models.sort(key=lambda x: (x.priority, x.name))
        
        return enabled_models
    
    def _get_model_type(self, file_ext: str) -> str:
        """根据文件扩展名确定模型类型"""
        type_mapping = {
            ".pt": "pytorch",
            ".pth": "pytorch", 
            ".onnx": "onnx",
            ".pb": "tensorflow",
            ".tflite": "tensorflow_lite",
            ".weights": "darknet"
        }
        return type_mapping.get(file_ext, "unknown")
    
    def get_primary_model(self) -> Optional[ModelConfig]:
        """获取主要模型（优先级最高的启用模型）"""
        models = self.update_models()
        return models[0] if models else None
    
    def print_models_status(self):
        """打印模型状态"""
        models = self.update_models()
        
        if not models:
            print(f"📋 未发现可用的新物体检测模型")
            print(f"   请将模型权重放入: {self.models_path}")
            print(f"   支持格式: {', '.join(self.supported_formats)}")
            return
        
        print(f"🔍 发现 {len(models)} 个新物体检测模型:")
        for i, model in enumerate(models, 1):
            status = "✅ 启用" if model.enabled else "❌ 禁用"
            print(f"   {i}. {model.name}")
            print(f"      文件: {os.path.basename(model.file_path)}")
            print(f"      类型: {model.model_type}")
            print(f"      优先级: {model.priority}")
            print(f"      状态: {status}")
            print(f"      描述: {model.description}")
            if model.categories:
                print(f"      类别: {', '.join(model.categories)}")
            print()
        
        primary = models[0]
        print(f"🎯 将使用主要模型: {primary.name}")
        print(f"   文件: {os.path.basename(primary.file_path)}")
        print(f"   类型: {primary.model_type}")

# 全局配置实例
new_object_config = NewObjectDetectionConfig()

def get_naming_guide() -> str:
    """获取命名指南"""
    return new_object_config.get_naming_guide()

def get_primary_model() -> Optional[ModelConfig]:
    """获取主要模型"""
    return new_object_config.get_primary_model()

def print_models_status():
    """打印模型状态"""
    new_object_config.print_models_status()

def update_models() -> List[ModelConfig]:
    """更新模型列表"""
    return new_object_config.update_models()
