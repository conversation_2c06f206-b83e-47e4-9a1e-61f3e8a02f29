{"update_summary": {"total_files_updated": 9, "total_references_updated": 19, "dry_run": false}, "updated_files": ["2/config/config.yaml", "2/modules/config_module.py", "config/human_detection_config.py", "config/new_object_detection_config.py", "core/frame_processor.py", "detection/core/adaptive_fire_smoke_detector.py", "detection/core/integrated_fire_smoke_detector.py", "detection/utils/fire_yolo_utils.py", "detection/utils/yolo_utils.py"], "updated_references": ["2/config/config.yaml: best-1.pt -> models/fire_detection/fire.rknn", "2/modules/config_module.py: best.pt -> models/fire_detection/fire_smoke.rknn", "2/modules/config_module.py: fire_model.pt -> models/fire_detection/fire.rknn", "2/modules/config_module.py: smoke_model.pt -> models/fire_detection/smoke.rknn", "config/human_detection_config.py: yolov8n.pt -> models/human_detection/yolov8n_human.rknn", "config/new_object_detection_config.py: best.pt -> models/fire_detection/fire_smoke.rknn", "detection/core/adaptive_fire_smoke_detector.py: best.pt -> models/fire_detection/fire_smoke.rknn", "detection/core/integrated_fire_smoke_detector.py: best-1.pt -> models/fire_detection/fire.rknn", "detection/core/integrated_fire_smoke_detector.py: best-2.pt -> models/fire_detection/smoke.rknn", "detection/utils/fire_yolo_utils.py: yolov8n.pt -> models/human_detection/yolov8n_human.rknn", "detection/utils/fire_yolo_utils.py: flame_yolo.pt -> models/fire_detection/fire.rknn", "detection/utils/fire_yolo_utils.py: smoke_yolo.pt -> models/fire_detection/smoke.rknn", "detection/utils/fire_yolo_utils.py: fire_combined_yolo.pt -> models/fire_detection/fire_smoke.rknn", "detection/utils/yolo_utils.py: yolov8n.pt -> models/human_detection/yolov8n_human.rknn", "detection/utils/yolo_utils.py: yolov8s.pt -> models/human_detection/yolov8s_human.rknn", "detection/utils/yolo_utils.py: yolov8m.pt -> models/human_detection/yolov8m_human.rknn", "detection/utils/yolo_utils.py: yolov8l.pt -> models/human_detection/yolov8l_human.rknn", "detection/utils/yolo_utils.py: yolov8x.pt -> models/human_detection/yolov8x_human.rknn", "detection/utils/yolo_utils.py: fire_smoke_yolov8.pt -> models/fire_detection/fire_smoke.rknn"], "model_path_mappings": {"yolov8n.pt": "models/human_detection/yolov8n_human.rknn", "yolov8s.pt": "models/human_detection/yolov8s_human.rknn", "yolov8m.pt": "models/human_detection/yolov8m_human.rknn", "yolov8l.pt": "models/human_detection/yolov8l_human.rknn", "yolov8x.pt": "models/human_detection/yolov8x_human.rknn", "best.pt": "models/fire_detection/fire_smoke.rknn", "best-1.pt": "models/fire_detection/fire.rknn", "best-2.pt": "models/fire_detection/smoke.rknn", "fire_model.pt": "models/fire_detection/fire.rknn", "smoke_model.pt": "models/fire_detection/smoke.rknn", "flame_yolo.pt": "models/fire_detection/fire.rknn", "smoke_yolo.pt": "models/fire_detection/smoke.rknn", "fire_combined_yolo.pt": "models/fire_detection/fire_smoke.rknn", "fire_smoke_yolov8.pt": "models/fire_detection/fire_smoke.rknn", "fire_smoke_yolov8n.pt": "models/fire_detection/fire_smoke_n.rknn", "fire_smoke_yolov8s.pt": "models/fire_detection/fire_smoke_s.rknn", "models/best.pt": "models/fire_detection/fire_smoke.rknn", "models/fire_detection/best.pt": "models/fire_detection/fire_smoke.rknn", "models/human_detection/yolov8n.pt": "models/human_detection/yolov8n_human.rknn", "2/models/best-1.pt": "models/fire_detection/fire.rknn", "models/fs/best-1.pt": "models/fire_detection/fire.rknn", "models/fs/best-2.pt": "models/fire_detection/smoke.rknn"}}