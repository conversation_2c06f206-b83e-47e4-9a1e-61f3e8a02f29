{"total_files_checked": 11, "files_with_pytorch_refs": [], "files_with_rknn_refs": ["2/config/config.yaml", "2/modules/config_module.py", "config/human_detection_config.py", "config/new_object_detection_config.py", "core/frame_processor.py", "detection/core/adaptive_fire_smoke_detector.py", "detection/core/integrated_fire_smoke_detector.py", "detection/utils/fire_yolo_utils.py", "detection/utils/yolo_utils.py", "detection/core/human_detector.py", "2/modules/detection_module.py"], "pytorch_references_found": [], "rknn_references_found": [{"file": "2/config/config.yaml", "reference": "models/fire_detection/fire.rknn", "line_context": "Line 7: weights_path: \"models/fire_detection/fire.rknn\"", "position": 95}, {"file": "2/modules/config_module.py", "reference": "models/models/fire_detection/fire_smoke.rknn", "line_context": "Line 44: 'weights_path': 'models/models/fire_detection/fire_smoke.rknn',", "position": 1221}, {"file": "2/modules/config_module.py", "reference": "models/models/fire_detection/fire.rknn", "line_context": "Line 45: 'fire_weights_path': 'models/models/fire_detection/fire.rknn',", "position": 1306}, {"file": "2/modules/config_module.py", "reference": "models/models/fire_detection/smoke.rknn", "line_context": "Line 46: 'smoke_weights_path': 'models/models/fire_detection/smoke.rknn',", "position": 1386}, {"file": "config/human_detection_config.py", "reference": "models/human_detection/yolov8n_human.rknn", "line_context": "Line 16: model_name: str = \"models/human_detection/yolov8n_human.rknn\"  # 默认使用轻量级模型", "position": 206}, {"file": "config/new_object_detection_config.py", "reference": "models/general/.rknn", "line_context": "Line 31: self.supported_formats = [\"models/general/.rknn\", \"models/general/.rknn\", \".weights\", \".onnx\", \".tflite\", \".pb\"]", "position": 846}, {"file": "config/new_object_detection_config.py", "reference": "models/general/.rknn", "line_context": "Line 31: self.supported_formats = [\"models/general/.rknn\", \"models/general/.rknn\", \".weights\", \".onnx\", \".tflite\", \".pb\"]", "position": 870}, {"file": "config/new_object_detection_config.py", "reference": "models/general/vehicle_*.rknn", "line_context": "Line 37: \"pattern\": \"models/general/vehicle_*.rknn\",", "position": 1103}, {"file": "config/new_object_detection_config.py", "reference": "models/general/animal_*.rknn", "line_context": "Line 43: \"pattern\": \"models/general/animal_*.rknn\",", "position": 1353}, {"file": "config/new_object_detection_config.py", "reference": "models/general/tool_*.rknn", "line_context": "Line 49: \"pattern\": \"models/general/tool_*.rknn\",", "position": 1591}, {"file": "config/new_object_detection_config.py", "reference": "models/general/safety_*.rknn", "line_context": "Line 55: \"pattern\": \"models/general/safety_*.rknn\",", "position": 1835}, {"file": "config/new_object_detection_config.py", "reference": "models/general/general_*.rknn", "line_context": "Line 61: \"pattern\": \"models/general/general_*.rknn\",", "position": 2075}, {"file": "config/new_object_detection_config.py", "reference": "models/general/.rknn", "line_context": "Line 258: \"models/general/.rknn\": \"pytorch\",", "position": 8969}, {"file": "config/new_object_detection_config.py", "reference": "models/general/.rknn", "line_context": "Line 259: \"models/general/.rknn\": \"pytorch\",", "position": 9016}, {"file": "core/frame_processor.py", "reference": "models/general/.rknn", "line_context": "Line 1137: if file_ext in ['models/general/.rknn', 'models/general/.rknn']:", "position": 43708}, {"file": "core/frame_processor.py", "reference": "models/general/.rknn", "line_context": "Line 1137: if file_ext in ['models/general/.rknn', 'models/general/.rknn']:", "position": 43732}, {"file": "detection/core/adaptive_fire_smoke_detector.py", "reference": "models/fire_detection/models/fire_detection/fire_smoke.rknn", "line_context": "Line 25: model_path: str = \"models/fire_detection/models/fire_detection/fire_smoke.rknn\",", "position": 444}, {"file": "detection/core/integrated_fire_smoke_detector.py", "reference": "models/fs/models/fire_detection/fire.rknn", "line_context": "Line 163: fire_model_path = \"models/fs/models/fire_detection/fire.rknn\"  # 火焰检测模型", "position": 5066}, {"file": "detection/core/integrated_fire_smoke_detector.py", "reference": "models/fs/models/fire_detection/smoke.rknn", "line_context": "Line 164: smoke_model_path = \"models/fs/models/fire_detection/smoke.rknn\"  # 烟雾检测模型", "position": 5151}, {"file": "detection/utils/fire_yolo_utils.py", "reference": "models/fire_detection/models/fire_detection/fire.rknn", "line_context": "Line 65: model_path=\"models/fire_detection/models/fire_detection/fire.rknn\",", "position": 1503}, {"file": "detection/utils/fire_yolo_utils.py", "reference": "models/fire_detection/models/fire_detection/smoke.rknn", "line_context": "Line 76: model_path=\"models/fire_detection/models/fire_detection/smoke.rknn\",", "position": 1939}, {"file": "detection/utils/fire_yolo_utils.py", "reference": "models/fire_detection/models/fire_detection/fire_smoke.rknn", "line_context": "Line 87: model_path=\"models/fire_detection/models/fire_detection/fire_smoke.rknn\",", "position": 2392}, {"file": "detection/utils/fire_yolo_utils.py", "reference": "models/human_detection/yolov8n_human.rknn", "line_context": "Line 197: model = YOLO(\"models/human_detection/yolov8n_human.rknn\")", "position": 6104}, {"file": "detection/utils/fire_yolo_utils.py", "reference": "models/fire_detection/models/fire_detection/fire.rknn", "line_context": "Line 331: 'model_path': 'models/fire_detection/models/fire_detection/fire.rknn',", "position": 10358}, {"file": "detection/utils/fire_yolo_utils.py", "reference": "models/fire_detection/models/fire_detection/smoke.rknn", "line_context": "Line 337: 'model_path': 'models/fire_detection/models/fire_detection/smoke.rknn',", "position": 10656}, {"file": "detection/utils/yolo_utils.py", "reference": "models/human_detection/yolov8n_human.rknn", "line_context": "Line 16: \"models/human_detection/yolov8n_human.rknn\": \"https://github.com/ultralytics/assets/releases/download/v8.3.0/models/human_detection/yolov8n_human.rknn\",", "position": 240}, {"file": "detection/utils/yolo_utils.py", "reference": "https://github.com/ultralytics/assets/releases/download/v8.3.0/models/human_detection/yolov8n_human.rknn", "line_context": "Line 16: \"models/human_detection/yolov8n_human.rknn\": \"https://github.com/ultralytics/assets/releases/download/v8.3.0/models/human_detection/yolov8n_human.rknn\",", "position": 285}, {"file": "detection/utils/yolo_utils.py", "reference": "models/human_detection/yolov8s_human.rknn", "line_context": "Line 17: \"models/human_detection/yolov8s_human.rknn\": \"https://github.com/ultralytics/assets/releases/download/v8.3.0/models/human_detection/yolov8s_human.rknn\",", "position": 397}, {"file": "detection/utils/yolo_utils.py", "reference": "https://github.com/ultralytics/assets/releases/download/v8.3.0/models/human_detection/yolov8s_human.rknn", "line_context": "Line 17: \"models/human_detection/yolov8s_human.rknn\": \"https://github.com/ultralytics/assets/releases/download/v8.3.0/models/human_detection/yolov8s_human.rknn\",", "position": 442}, {"file": "detection/utils/yolo_utils.py", "reference": "models/human_detection/yolov8m_human.rknn", "line_context": "Line 18: \"models/human_detection/yolov8m_human.rknn\": \"https://github.com/ultralytics/assets/releases/download/v8.3.0/models/human_detection/yolov8m_human.rknn\",", "position": 554}, {"file": "detection/utils/yolo_utils.py", "reference": "https://github.com/ultralytics/assets/releases/download/v8.3.0/models/human_detection/yolov8m_human.rknn", "line_context": "Line 18: \"models/human_detection/yolov8m_human.rknn\": \"https://github.com/ultralytics/assets/releases/download/v8.3.0/models/human_detection/yolov8m_human.rknn\",", "position": 599}, {"file": "detection/utils/yolo_utils.py", "reference": "models/human_detection/yolov8l_human.rknn", "line_context": "Line 19: \"models/human_detection/yolov8l_human.rknn\": \"https://github.com/ultralytics/assets/releases/download/v8.3.0/models/human_detection/yolov8l_human.rknn\",", "position": 711}, {"file": "detection/utils/yolo_utils.py", "reference": "https://github.com/ultralytics/assets/releases/download/v8.3.0/models/human_detection/yolov8l_human.rknn", "line_context": "Line 19: \"models/human_detection/yolov8l_human.rknn\": \"https://github.com/ultralytics/assets/releases/download/v8.3.0/models/human_detection/yolov8l_human.rknn\",", "position": 756}, {"file": "detection/utils/yolo_utils.py", "reference": "models/human_detection/yolov8x_human.rknn", "line_context": "Line 20: \"models/human_detection/yolov8x_human.rknn\": \"https://github.com/ultralytics/assets/releases/download/v8.3.0/models/human_detection/yolov8x_human.rknn\",", "position": 868}, {"file": "detection/utils/yolo_utils.py", "reference": "https://github.com/ultralytics/assets/releases/download/v8.3.0/models/human_detection/yolov8x_human.rknn", "line_context": "Line 20: \"models/human_detection/yolov8x_human.rknn\": \"https://github.com/ultralytics/assets/releases/download/v8.3.0/models/human_detection/yolov8x_human.rknn\",", "position": 913}, {"file": "detection/utils/yolo_utils.py", "reference": "models/fire_detection/fire_smoke.rknn", "line_context": "Line 23: \"models/fire_detection/fire_smoke.rknn\": \"https://github.com/ultralytics/assets/releases/download/v8.3.0/models/human_detection/yolov8n_human.rknn\",  # 使用通用模型作为基础", "position": 1049}, {"file": "detection/utils/yolo_utils.py", "reference": "https://github.com/ultralytics/assets/releases/download/v8.3.0/models/human_detection/yolov8n_human.rknn", "line_context": "Line 23: \"models/fire_detection/fire_smoke.rknn\": \"https://github.com/ultralytics/assets/releases/download/v8.3.0/models/human_detection/yolov8n_human.rknn\",  # 使用通用模型作为基础", "position": 1090}, {"file": "detection/utils/yolo_utils.py", "reference": "fire_smoke_models/human_detection/yolov8n_human.rknn", "line_context": "Line 24: \"fire_smoke_models/human_detection/yolov8n_human.rknn\": \"https://github.com/ultralytics/assets/releases/download/v8.3.0/models/human_detection/yolov8n_human.rknn\",", "position": 1216}, {"file": "detection/utils/yolo_utils.py", "reference": "https://github.com/ultralytics/assets/releases/download/v8.3.0/models/human_detection/yolov8n_human.rknn", "line_context": "Line 24: \"fire_smoke_models/human_detection/yolov8n_human.rknn\": \"https://github.com/ultralytics/assets/releases/download/v8.3.0/models/human_detection/yolov8n_human.rknn\",", "position": 1272}, {"file": "detection/utils/yolo_utils.py", "reference": "fire_smoke_models/human_detection/yolov8s_human.rknn", "line_context": "Line 25: \"fire_smoke_models/human_detection/yolov8s_human.rknn\": \"https://github.com/ultralytics/assets/releases/download/v8.3.0/models/human_detection/yolov8s_human.rknn\"", "position": 1384}, {"file": "detection/utils/yolo_utils.py", "reference": "https://github.com/ultralytics/assets/releases/download/v8.3.0/models/human_detection/yolov8s_human.rknn", "line_context": "Line 25: \"fire_smoke_models/human_detection/yolov8s_human.rknn\": \"https://github.com/ultralytics/assets/releases/download/v8.3.0/models/human_detection/yolov8s_human.rknn\"", "position": 1440}, {"file": "detection/utils/yolo_utils.py", "reference": "models/human_detection/yolov8n_human.rknn", "line_context": "Line 140: model_name: 模型名称 (如 \"models/human_detection/yolov8n_human.rknn\")", "position": 3342}, {"file": "detection/utils/yolo_utils.py", "reference": "models/general/.rknn", "line_context": "Line 205: if file.endswith('models/general/.rknn'):", "position": 5684}, {"file": "detection/utils/yolo_utils.py", "reference": "models/human_detection/yolov8n_human.rknn", "line_context": "Line 238: \"realtime\": \"models/human_detection/yolov8n_human.rknn\",    # 实时检测，速度优先", "position": 6586}, {"file": "detection/utils/yolo_utils.py", "reference": "models/human_detection/yolov8s_human.rknn", "line_context": "Line 239: \"balanced\": \"models/human_detection/yolov8s_human.rknn\",    # 平衡速度和精度", "position": 6670}, {"file": "detection/utils/yolo_utils.py", "reference": "models/human_detection/yolov8m_human.rknn", "line_context": "Line 240: \"accuracy\": \"models/human_detection/yolov8m_human.rknn\"     # 精度优先", "position": 6752}, {"file": "detection/utils/yolo_utils.py", "reference": "models/human_detection/yolov8n_human.rknn", "line_context": "Line 243: return recommendations.get(use_case, \"models/human_detection/yolov8n_human.rknn\")", "position": 6871}, {"file": "detection/utils/yolo_utils.py", "reference": "fire_smoke_models/human_detection/yolov8n_human.rknn", "line_context": "Line 256: \"realtime\": \"fire_smoke_models/human_detection/yolov8n_human.rknn\",    # 实时检测，速度优先", "position": 7212}, {"file": "detection/utils/yolo_utils.py", "reference": "models/fire_detection/fire_smoke.rknn", "line_context": "Line 257: \"balanced\": \"models/fire_detection/fire_smoke.rknn\",     # 平衡速度和精度", "position": 7307}, {"file": "detection/utils/yolo_utils.py", "reference": "fire_smoke_models/human_detection/yolov8s_human.rknn", "line_context": "Line 258: \"accuracy\": \"fire_smoke_models/human_detection/yolov8s_human.rknn\"     # 精度优先", "position": 7386}, {"file": "detection/utils/yolo_utils.py", "reference": "models/fire_detection/fire_smoke.rknn", "line_context": "Line 261: return recommendations.get(use_case, \"models/fire_detection/fire_smoke.rknn\")", "position": 7508}, {"file": "detection/utils/yolo_utils.py", "reference": "fire_smoke_models/human_detection/yolov8n_human.rknn", "line_context": "Line 278: if model_name in [\"fire_smoke_models/human_detection/yolov8n_human.rknn\", \"fire_smoke_models/human_detection/yolov8s_human.rknn\"]:", "position": 7997}, {"file": "detection/utils/yolo_utils.py", "reference": "fire_smoke_models/human_detection/yolov8s_human.rknn", "line_context": "Line 278: if model_name in [\"fire_smoke_models/human_detection/yolov8n_human.rknn\", \"fire_smoke_models/human_detection/yolov8s_human.rknn\"]:", "position": 8053}, {"file": "detection/utils/yolo_utils.py", "reference": "models/human_detection/yolov8n_human.rknn", "line_context": "Line 296: 'model_name': 'models/human_detection/yolov8n_human.rknn',", "position": 8642}, {"file": "detection/core/human_detector.py", "reference": "models/human_detection/yolov8n_human.rknn", "line_context": "Line 37: model_path: str = \"models/human_detection/yolov8n_human.rknn\",", "position": 837}, {"file": "detection/core/human_detector.py", "reference": "models/human_detection/{base_name}.rknn", "line_context": "Line 56: self.model_path = f\"models/human_detection/{base_name}.rknn\"", "position": 1541}, {"file": "2/modules/detection_module.py", "reference": "models/fire_detection/{base_name}.rknn", "line_context": "Line 110: rknn_path = f\"models/fire_detection/{base_name}.rknn\"", "position": 3274}, {"file": "2/modules/detection_module.py", "reference": ".rknn", "line_context": "Line 113: elif model_path.endswith('.rknn'):", "position": 3448}, {"file": "2/modules/detection_module.py", "reference": "{model_path}.rknn", "line_context": "Line 118: return f\"{model_path}.rknn\"", "position": 3573}], "verification_passed": false}