# PyTorch到RKNN模型转换指南

## 转换工具

使用瑞芯微官方的 `rknn-toolkit2` 进行模型转换。

## 安装依赖

```bash
pip install rknn-toolkit2
```

## 转换步骤

### 1. 导出ONNX模型

```python
# 从PyTorch模型导出ONNX
import torch
from ultralytics import YOLO

# 加载PyTorch模型
model = YOLO('yolov8n.pt')

# 导出为ONNX格式
model.export(format='onnx', imgsz=640)
```

### 2. 转换为RKNN

```python
from rknn.api import RKNN

# 创建RKNN对象
rknn = RKNN(verbose=True)

# 配置模型
rknn.config(mean_values=[[0, 0, 0]], std_values=[[255, 255, 255]], target_platform='rk3588')

# 加载ONNX模型
ret = rknn.load_onnx(model='yolov8n.onnx')

# 构建RKNN模型
ret = rknn.build(do_quantization=True, dataset='calibration_data.txt')

# 导出RKNN模型
ret = rknn.export_rknn('./yolov8n.rknn')

# 释放资源
rknn.release()
```

### 3. 量化数据准备

创建校准数据集文件 `calibration_data.txt`：

```
path/to/image1.jpg
path/to/image2.jpg
...
```

## 模型命名规范

- 人体检测: `{model_size}_human.rknn`
- 火焰检测: `fire.rknn`
- 烟雾检测: `smoke.rknn`
- 综合检测: `fire_smoke.rknn`

## 验证转换结果

转换完成后，使用以下代码验证模型：

```python
from rknnlite.api import RKNNLite

# 创建RKNN Lite对象
rknn_lite = RKNNLite()

# 加载RKNN模型
ret = rknn_lite.load_rknn('model.rknn')

# 初始化运行时
ret = rknn_lite.init_runtime()

# 测试推理
import numpy as np
input_data = np.random.randint(0, 255, (1, 3, 640, 640), dtype=np.uint8)
outputs = rknn_lite.inference(inputs=[input_data])

print("模型转换成功，推理正常")
```

## 注意事项

1. 确保输入尺寸与原模型一致
2. 注意数据预处理的差异
3. 验证输出格式是否正确
4. 在目标硬件上测试性能
