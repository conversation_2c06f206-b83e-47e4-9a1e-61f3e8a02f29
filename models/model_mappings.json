{"model_mappings": {"pytorch_to_rknn": {"yolov8n.pt": "models/human_detection/yolov8n_human.rknn", "yolov8s.pt": "models/human_detection/yolov8s_human.rknn", "yolov8m.pt": "models/human_detection/yolov8m_human.rknn", "yolov8l.pt": "models/human_detection/yolov8l_human.rknn", "yolov8x.pt": "models/human_detection/yolov8x_human.rknn", "best.pt": "models/fire_detection/fire_smoke.rknn", "best-1.pt": "models/fire_detection/fire.rknn", "best-2.pt": "models/fire_detection/smoke.rknn", "fire_model.pt": "models/fire_detection/fire.rknn", "smoke_model.pt": "models/fire_detection/smoke.rknn"}}, "model_info": {"human_detection": {"input_size": [640, 640], "classes": ["person"], "format": "YOLO"}, "fire_detection": {"input_size": [640, 640], "classes": ["fire", "smoke"], "format": "YOLO"}}}