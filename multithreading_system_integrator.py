#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多线程系统集成器
将多线程AI处理架构集成到主系统中
"""

import sys
import time
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
import numpy as np
from PyQt5.QtCore import QObject, pyqtSignal, QTimer
from PyQt5.QtWidgets import QApplication

from main_thread_adapter import MainThreadAdapter, AdapterConfig
from ai_processing_migrator import MigrationConfig
from utils.logger import get_logger


@dataclass
class IntegrationConfig:
    """集成配置"""
    enable_performance_monitoring: bool = True
    monitoring_interval: float = 5.0  # 监控间隔（秒）
    enable_auto_optimization: bool = True
    optimization_interval: float = 30.0  # 优化间隔（秒）
    enable_health_check: bool = True
    health_check_interval: float = 10.0  # 健康检查间隔（秒）
    max_memory_usage: float = 1024.0  # 最大内存使用（MB）
    max_queue_size: int = 50
    enable_fallback_mode: bool = True


class MultithreadingSystemIntegrator(QObject):
    """多线程系统集成器"""
    
    # 信号定义
    performance_updated = pyqtSignal(dict)  # 性能统计更新
    health_status_changed = pyqtSignal(str, bool)  # 健康状态变化
    optimization_applied = pyqtSignal(str, dict)  # 优化应用
    system_warning = pyqtSignal(str, str)  # 系统警告
    
    def __init__(self, config: IntegrationConfig = None, parent=None):
        """
        初始化多线程系统集成器
        
        Args:
            config: 集成配置
            parent: 父对象
        """
        super().__init__(parent)
        
        self.config = config or IntegrationConfig()
        self.logger = get_logger("MultithreadingSystemIntegrator")
        
        # 初始化主线程适配器
        adapter_config = AdapterConfig(
            sync_timeout=3.0,
            enable_caching=True,
            cache_ttl=1.0,
            fallback_enabled=self.config.enable_fallback_mode,
            max_concurrent_requests=10
        )
        self.adapter = MainThreadAdapter(adapter_config, self)
        
        # 连接信号
        self._connect_signals()
        
        # 系统状态
        self.system_health = {
            'adapter_status': True,
            'worker_status': True,
            'memory_status': True,
            'queue_status': True,
            'overall_status': True
        }
        
        # 性能监控
        self.performance_history = []
        self.max_history_size = 100
        
        # 定时器
        self._setup_timers()
        
        # 原始检测器引用（用于降级）
        self.original_detectors = {
            'human_detector': None,
            'fire_smoke_detector': None,
            'heat_detector': None
        }
        
        self.logger.info("多线程系统集成器初始化完成")
    
    def _connect_signals(self):
        """连接信号槽"""
        self.adapter.detection_completed.connect(self._on_detection_completed)
        self.adapter.detection_failed.connect(self._on_detection_failed)
    
    def _setup_timers(self):
        """设置定时器"""
        # 性能监控定时器
        if self.config.enable_performance_monitoring:
            self.performance_timer = QTimer()
            self.performance_timer.timeout.connect(self._update_performance_stats)
            self.performance_timer.start(int(self.config.monitoring_interval * 1000))
        
        # 自动优化定时器
        if self.config.enable_auto_optimization:
            self.optimization_timer = QTimer()
            self.optimization_timer.timeout.connect(self._apply_auto_optimization)
            self.optimization_timer.start(int(self.config.optimization_interval * 1000))
        
        # 健康检查定时器
        if self.config.enable_health_check:
            self.health_timer = QTimer()
            self.health_timer.timeout.connect(self._perform_health_check)
            self.health_timer.start(int(self.config.health_check_interval * 1000))
    
    def integrate_with_system(self, 
                            human_detector=None,
                            fire_smoke_detector=None,
                            heat_detector=None,
                            frame_processor=None):
        """
        与主系统集成
        
        Args:
            human_detector: 人体检测器实例
            fire_smoke_detector: 火焰烟雾检测器实例
            heat_detector: 热源检测器实例
            frame_processor: 帧处理器实例
        """
        self.logger.info("开始与主系统集成...")
        
        # 保存原始检测器引用
        self.original_detectors['human_detector'] = human_detector
        self.original_detectors['fire_smoke_detector'] = fire_smoke_detector
        self.original_detectors['heat_detector'] = heat_detector
        
        # 初始化适配器中的检测器
        self.adapter.initialize_detectors(
            human_detector=human_detector,
            fire_smoke_detector=fire_smoke_detector,
            heat_detector=heat_detector
        )
        
        # 如果提供了帧处理器，替换其AI处理方法
        if frame_processor:
            self._patch_frame_processor(frame_processor)
        
        self.logger.info("多线程系统集成完成")
    
    def _patch_frame_processor(self, frame_processor):
        """修补帧处理器的AI处理方法"""
        # 保存原始方法
        if hasattr(frame_processor, '_original_perform_human_detection'):
            return  # 已经修补过了
        
        frame_processor._original_perform_human_detection = frame_processor._perform_human_detection
        frame_processor._original_apply_fire_smoke_detection = frame_processor._apply_fire_smoke_detection
        
        # 替换为多线程版本
        def patched_human_detection(visible_frame):
            """修补的人体检测方法"""
            try:
                # 使用异步检测
                task_id = self.adapter.detect_humans_async(
                    visible_frame,
                    callback=lambda detections, annotated_frame: self._update_human_detection_cache(
                        frame_processor, detections, annotated_frame
                    )
                )
                
                # 返回缓存的结果或原始帧
                if hasattr(frame_processor, 'cached_human_detections'):
                    return visible_frame  # 使用缓存的检测结果
                else:
                    frame_processor.cached_human_detections = []
                    return visible_frame
                    
            except Exception as e:
                self.logger.error(f"多线程人体检测失败，使用原始方法: {e}")
                return frame_processor._original_perform_human_detection(visible_frame)
        
        def patched_fire_smoke_detection(visible_frame):
            """修补的火焰烟雾检测方法"""
            try:
                # 使用异步检测
                task_id = self.adapter.detect_fire_smoke_async(
                    visible_frame,
                    detection_mode='single',
                    callback=lambda detections: self._update_fire_smoke_detection_cache(
                        frame_processor, detections
                    )
                )
                
                # 返回原始帧，使用缓存的检测结果
                return visible_frame
                
            except Exception as e:
                self.logger.error(f"多线程火焰烟雾检测失败，使用原始方法: {e}")
                return frame_processor._original_apply_fire_smoke_detection(visible_frame)
        
        # 应用修补
        frame_processor._perform_human_detection = patched_human_detection
        frame_processor._apply_fire_smoke_detection = patched_fire_smoke_detection
        
        self.logger.info("帧处理器AI方法已修补为多线程版本")
    
    def _update_human_detection_cache(self, frame_processor, detections, annotated_frame):
        """更新人体检测缓存"""
        frame_processor.cached_human_detections = detections
        # 可以在这里添加更多的缓存更新逻辑
    
    def _update_fire_smoke_detection_cache(self, frame_processor, detections):
        """更新火焰烟雾检测缓存"""
        # 转换检测结果格式以匹配原有接口
        frame_processor.last_fire_smoke_detections = {
            'fire_detections': [d for d in detections if d.get('class_name') == 'fire'],
            'smoke_detections': [d for d in detections if d.get('class_name') == 'smoke'],
            'fire_objects': {},
            'smoke_objects': {}
        }
    
    def get_threaded_human_detector(self):
        """获取多线程人体检测器接口"""
        class ThreadedHumanDetector:
            def __init__(self, adapter):
                self.adapter = adapter
            
            def detect_humans(self, frame):
                return self.adapter.detect_humans_sync(frame)
            
            def detect_humans_async(self, frame, callback):
                return self.adapter.detect_humans_async(frame, callback)
        
        return ThreadedHumanDetector(self.adapter)
    
    def get_threaded_fire_smoke_detector(self):
        """获取多线程火焰烟雾检测器接口"""
        class ThreadedFireSmokeDetector:
            def __init__(self, adapter):
                self.adapter = adapter
            
            def detect_single_model(self, frame):
                return self.adapter.detect_fire_smoke_sync(frame, 'single')
            
            def detect_dual_model(self, frame):
                return self.adapter.detect_fire_smoke_sync(frame, 'dual')
            
            def detect_async(self, frame, mode='single', callback=None):
                return self.adapter.detect_fire_smoke_async(frame, mode, callback)
        
        return ThreadedFireSmokeDetector(self.adapter)
    
    def _update_performance_stats(self):
        """更新性能统计"""
        try:
            stats = self.adapter.get_adapter_stats()
            
            # 添加时间戳
            stats['timestamp'] = time.time()
            
            # 添加到历史记录
            self.performance_history.append(stats)
            if len(self.performance_history) > self.max_history_size:
                self.performance_history.pop(0)
            
            # 发送信号
            self.performance_updated.emit(stats)
            
        except Exception as e:
            self.logger.error(f"更新性能统计失败: {e}")
    
    def _apply_auto_optimization(self):
        """应用自动优化"""
        try:
            if len(self.performance_history) < 5:
                return  # 数据不足，跳过优化
            
            # 分析性能趋势
            recent_stats = self.performance_history[-5:]
            avg_queue_size = sum(s.get('queue_status', {}).get('worker_queue_size', 0) 
                               for s in recent_stats) / len(recent_stats)
            
            optimizations = {}
            
            # 队列优化
            if avg_queue_size > self.config.max_queue_size * 0.8:
                optimizations['queue_optimization'] = {
                    'action': 'increase_workers',
                    'reason': 'High queue utilization detected'
                }
            
            # 内存优化
            # 这里可以添加内存使用检查和优化
            
            if optimizations:
                self.optimization_applied.emit('auto_optimization', optimizations)
                self.logger.info(f"应用自动优化: {optimizations}")
            
        except Exception as e:
            self.logger.error(f"自动优化失败: {e}")
    
    def _perform_health_check(self):
        """执行健康检查"""
        try:
            # 检查适配器状态
            adapter_stats = self.adapter.get_adapter_stats()
            queue_status = adapter_stats.get('queue_status', {})
            
            # 检查工作线程状态
            worker_running = queue_status.get('worker_running', False)
            self.system_health['worker_status'] = worker_running
            
            # 检查队列状态
            queue_size = queue_status.get('worker_queue_size', 0)
            self.system_health['queue_status'] = queue_size < self.config.max_queue_size
            
            # 检查适配器状态
            active_requests = adapter_stats.get('active_requests', 0)
            max_requests = adapter_stats.get('max_concurrent_requests', 10)
            self.system_health['adapter_status'] = active_requests < max_requests
            
            # 计算总体健康状态
            overall_status = all(self.system_health.values())
            
            if self.system_health['overall_status'] != overall_status:
                self.system_health['overall_status'] = overall_status
                self.health_status_changed.emit('overall', overall_status)
            
            # 发送警告
            if not overall_status:
                unhealthy_components = [k for k, v in self.system_health.items() if not v]
                self.system_warning.emit(
                    'health_check',
                    f"系统组件不健康: {', '.join(unhealthy_components)}"
                )
            
        except Exception as e:
            self.logger.error(f"健康检查失败: {e}")
            self.system_health['overall_status'] = False
    
    def _on_detection_completed(self, task_id: str, result: Dict):
        """处理检测完成事件"""
        # 这里可以添加额外的处理逻辑
        pass
    
    def _on_detection_failed(self, task_id: str, error: str):
        """处理检测失败事件"""
        self.logger.warning(f"检测任务失败 [{task_id}]: {error}")
        self.system_warning.emit('detection_failed', f"任务 {task_id} 失败: {error}")
    
    def get_system_status(self) -> Dict:
        """获取系统状态"""
        return {
            'health': self.system_health.copy(),
            'performance': self.performance_history[-1] if self.performance_history else {},
            'config': {
                'performance_monitoring': self.config.enable_performance_monitoring,
                'auto_optimization': self.config.enable_auto_optimization,
                'health_check': self.config.enable_health_check,
                'fallback_mode': self.config.enable_fallback_mode
            }
        }
    
    def enable_fallback_mode(self):
        """启用降级模式"""
        self.logger.info("启用降级模式，切换到原始检测器")
        # 这里可以实现切换到原始检测器的逻辑
        self.config.enable_fallback_mode = True
    
    def disable_fallback_mode(self):
        """禁用降级模式"""
        self.logger.info("禁用降级模式，使用多线程检测器")
        self.config.enable_fallback_mode = False
    
    def shutdown(self):
        """关闭集成器"""
        self.logger.info("正在关闭多线程系统集成器...")
        
        # 停止定时器
        if hasattr(self, 'performance_timer'):
            self.performance_timer.stop()
        if hasattr(self, 'optimization_timer'):
            self.optimization_timer.stop()
        if hasattr(self, 'health_timer'):
            self.health_timer.stop()
        
        # 关闭适配器
        self.adapter.shutdown()
        
        # 清理资源
        self.performance_history.clear()
        
        self.logger.info("多线程系统集成器已关闭")
