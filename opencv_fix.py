#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OpenCV线程安全修复模块
解决OpenCV在多线程环境下的兼容性问题
"""

import os
import sys
import threading
from utils.logger import get_logger

logger = get_logger("OpenCVFix")

# 全局锁，用于OpenCV操作
opencv_lock = threading.Lock()

def fix_opencv_threading():
    """修复OpenCV线程安全问题"""
    try:
        # 设置OpenCV线程数为1，避免多线程冲突
        import cv2
        cv2.setNumThreads(1)
        
        # 设置OpenCV使用单线程
        os.environ['OMP_NUM_THREADS'] = '1'
        os.environ['OPENBLAS_NUM_THREADS'] = '1'
        os.environ['MKL_NUM_THREADS'] = '1'
        os.environ['VECLIB_MAXIMUM_THREADS'] = '1'
        os.environ['NUMEXPR_NUM_THREADS'] = '1'
        
        logger.info("✅ OpenCV线程安全修复已应用")
        
    except ImportError:
        logger.warning("⚠️ OpenCV未安装，跳过线程安全修复")
    except Exception as e:
        logger.error(f"❌ OpenCV线程安全修复失败: {e}")

def safe_opencv_operation(func, *args, **kwargs):
    """
    安全的OpenCV操作包装器
    
    Args:
        func: OpenCV函数
        *args: 位置参数
        **kwargs: 关键字参数
        
    Returns:
        函数执行结果
    """
    with opencv_lock:
        return func(*args, **kwargs)

def create_safe_video_capture(source):
    """
    创建线程安全的视频捕获对象

    Args:
        source: 视频源（文件路径或摄像头索引）

    Returns:
        cv2.VideoCapture: 视频捕获对象
    """
    try:
        import cv2

        # 使用线程锁确保线程安全
        with opencv_lock:
            cap = cv2.VideoCapture(source)

            # 设置一些基本的优化参数
            if cap.isOpened():
                # 设置缓冲区大小
                cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)

                # 禁用自动曝光（如果是摄像头）
                if isinstance(source, int):
                    cap.set(cv2.CAP_PROP_AUTO_EXPOSURE, 0.25)

            return cap

    except Exception as e:
        logger.error(f"创建视频捕获对象失败: {e}")
        return None


def apply_video_capture_fix(cap):
    """
    对视频捕获对象应用修复设置

    Args:
        cap: cv2.VideoCapture对象
    """
    try:
        import cv2

        if cap is None or not cap.isOpened():
            return

        # 设置低延迟参数
        cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)

        # 设置读取超时（毫秒）
        cap.set(cv2.CAP_PROP_OPEN_TIMEOUT_MSEC, 5000)
        cap.set(cv2.CAP_PROP_READ_TIMEOUT_MSEC, 5000)

        # 禁用一些可能导致问题的特性
        try:
            cap.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc('M', 'J', 'P', 'G'))
        except:
            pass  # 某些源可能不支持设置编解码器

        logger.debug("视频捕获修复设置已应用")

    except Exception as e:
        logger.warning(f"应用视频捕获修复设置失败: {e}")


def safe_video_read(cap):
    """
    线程安全的视频帧读取

    Args:
        cap: cv2.VideoCapture对象

    Returns:
        tuple: (ret, frame)
    """
    try:
        with opencv_lock:
            return cap.read()
    except Exception as e:
        logger.error(f"安全视频读取失败: {e}")
        return False, None


def safe_video_release(cap):
    """
    线程安全的视频捕获释放

    Args:
        cap: cv2.VideoCapture对象
    """
    try:
        with opencv_lock:
            if cap is not None:
                cap.release()
        logger.debug("视频捕获对象已安全释放")
    except Exception as e:
        logger.warning(f"安全释放视频捕获对象失败: {e}")


# 在模块导入时自动应用修复
fix_opencv_threading()

logger.info("OpenCV修复模块已加载")
