#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
平台检测模块
根据运行环境自动选择真实或模拟的AI Worker
"""

import platform
import os
import sys
from pathlib import Path
from utils.logger import get_logger

logger = get_logger("PlatformDetector")


def detect_target_platform():
    """
    检测是否在目标设备环境中运行
    
    Returns:
        bool: True表示在目标设备上，False表示在PC端
    """
    # 检查操作系统和架构
    system = platform.system()
    machine = platform.machine()
    
    logger.info(f"检测到系统: {system}, 架构: {machine}")
    
    # 目标设备通常是Linux + aarch64 (ARM64)
    is_target_system = system == 'Linux' and machine in ['aarch64', 'arm64']
    
    # 额外检查：是否存在NPU相关的设备文件或库
    npu_indicators = [
        '/dev/davinci0',  # 华为昇腾NPU设备文件
        '/dev/davinci_manager',
        '/usr/local/Ascend',  # 昇腾软件栈安装路径
        '/opt/npu',  # 其他可能的NPU安装路径
    ]
    
    has_npu_hardware = False
    for indicator in npu_indicators:
        if os.path.exists(indicator):
            has_npu_hardware = True
            logger.info(f"检测到NPU硬件指示器: {indicator}")
            break
    
    # 环境变量检查
    force_mock = os.getenv('FORCE_MOCK_AI', '').lower() in ['1', 'true', 'yes']
    force_real = os.getenv('FORCE_REAL_AI', '').lower() in ['1', 'true', 'yes']
    
    if force_mock:
        logger.warning("环境变量FORCE_MOCK_AI已设置，强制使用模拟AI Worker")
        return False
    
    if force_real:
        logger.warning("环境变量FORCE_REAL_AI已设置，强制使用真实AI Worker")
        return True
    
    # 综合判断
    is_target_device = is_target_system and has_npu_hardware
    
    if is_target_device:
        logger.info("✅ 检测到目标设备环境，将使用真实AI Worker")
    else:
        logger.info("🔧 检测到PC端环境，将使用模拟AI Worker")
        if is_target_system and not has_npu_hardware:
            logger.warning("⚠️ 检测到ARM64 Linux系统但未找到NPU硬件")
    
    return is_target_device


def get_ai_worker_class():
    """
    根据平台检测结果返回相应的AI Worker类
    
    Returns:
        class: AI_Worker类（真实或模拟）
    """
    is_target_device = detect_target_platform()
    
    if is_target_device:
        try:
            logger.info("导入真实AI Worker...")
            from ai_worker import AI_Worker
            logger.info("✅ 真实AI Worker导入成功")
            return AI_Worker
        except ImportError as e:
            logger.error(f"❌ 真实AI Worker导入失败: {e}")
            logger.warning("🔄 回退到模拟AI Worker")
            from mock_ai_worker import AI_Worker
            return AI_Worker
    else:
        logger.info("导入模拟AI Worker...")
        from mock_ai_worker import AI_Worker
        logger.info("✅ 模拟AI Worker导入成功")
        return AI_Worker


def get_ai_worker_instance(parent=None):
    """
    获取AI Worker实例
    
    Args:
        parent: 父对象
        
    Returns:
        AI_Worker: AI Worker实例
    """
    AI_Worker = get_ai_worker_class()
    return AI_Worker(parent)


def print_platform_info():
    """打印平台信息"""
    is_target = detect_target_platform()
    
    print("\n" + "="*60)
    print("🔍 平台检测信息")
    print("="*60)
    print(f"操作系统: {platform.system()}")
    print(f"架构: {platform.machine()}")
    print(f"Python版本: {platform.python_version()}")
    print(f"运行模式: {'目标设备' if is_target else 'PC端测试'}")
    print(f"AI Worker: {'真实' if is_target else '模拟'}")
    
    # 环境变量信息
    force_mock = os.getenv('FORCE_MOCK_AI', '')
    force_real = os.getenv('FORCE_REAL_AI', '')
    if force_mock:
        print(f"强制模拟: {force_mock}")
    if force_real:
        print(f"强制真实: {force_real}")
    
    print("="*60)


def setup_mock_environment():
    """设置模拟环境的额外配置"""
    logger.info("设置模拟环境配置...")
    
    # 设置模拟数据路径
    mock_data_dir = Path("mock_data")
    mock_data_dir.mkdir(exist_ok=True)
    
    # 创建模拟视频文件路径（如果不存在）
    mock_video_dir = mock_data_dir / "videos"
    mock_video_dir.mkdir(exist_ok=True)
    
    # 创建模拟图像文件路径
    mock_image_dir = mock_data_dir / "images"
    mock_image_dir.mkdir(exist_ok=True)
    
    logger.info(f"模拟数据目录: {mock_data_dir.absolute()}")
    
    return {
        'mock_data_dir': mock_data_dir,
        'mock_video_dir': mock_video_dir,
        'mock_image_dir': mock_image_dir
    }


def validate_ai_worker_interface(ai_worker):
    """
    验证AI Worker接口完整性
    
    Args:
        ai_worker: AI Worker实例
        
    Returns:
        bool: 接口是否完整
    """
    required_methods = [
        'initialize_detectors',
        'add_task',
        'add_human_detection_task',
        'add_fire_smoke_detection_task',
        'add_heat_detection_task',
        'pause_processing',
        'resume_processing',
        'clear_queue',
        'get_statistics',
        'get_queue_size',
        'stop',
        'cleanup'
    ]
    
    required_signals = [
        'human_detection_finished',
        'fire_smoke_detection_finished',
        'heat_detection_finished',
        'processing_error',
        'statistics_updated'
    ]
    
    missing_methods = []
    missing_signals = []
    
    # 检查方法
    for method in required_methods:
        if not hasattr(ai_worker, method):
            missing_methods.append(method)
    
    # 检查信号
    for signal in required_signals:
        if not hasattr(ai_worker, signal):
            missing_signals.append(signal)
    
    if missing_methods:
        logger.error(f"AI Worker缺少方法: {missing_methods}")
    
    if missing_signals:
        logger.error(f"AI Worker缺少信号: {missing_signals}")
    
    is_valid = len(missing_methods) == 0 and len(missing_signals) == 0
    
    if is_valid:
        logger.info("✅ AI Worker接口验证通过")
    else:
        logger.error("❌ AI Worker接口验证失败")
    
    return is_valid


if __name__ == "__main__":
    # 测试平台检测
    print_platform_info()
    
    # 测试AI Worker导入
    try:
        AI_Worker = get_ai_worker_class()
        worker = AI_Worker()
        
        # 验证接口
        validate_ai_worker_interface(worker)
        
        print(f"\n✅ AI Worker测试成功: {worker.__class__.__module__}.{worker.__class__.__name__}")
        
    except Exception as e:
        print(f"\n❌ AI Worker测试失败: {e}")
