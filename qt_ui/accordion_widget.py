#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手风琴组件 - 用于组织配置与工具界面
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                             QPushButton, QFrame, QScrollArea, QSizePolicy)
from PyQt5.QtCore import Qt, pyqtSignal, QPropertyAnimation, QEasingCurve, QRect
from PyQt5.QtGui import QFont, QIcon
from utils.logger import get_logger
from qt_ui.font_config import get_label_style, get_font_size


class AccordionSection(QWidget):
    """手风琴单个区块"""
    
    # 信号
    toggled = pyqtSignal(bool)  # 展开/折叠状态变化信号
    
    def __init__(self, title: str, content_widget: QWidget = None, parent=None):
        super().__init__(parent)
        self.logger = get_logger("AccordionSection")
        
        self.title = title
        self.content_widget = content_widget
        self.is_expanded = False
        self.animation_duration = 300  # 动画持续时间(毫秒)
        
        self.init_ui()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setStyleSheet("""
            AccordionSection {
                background-color: #323232;
                border: 1px solid #505050;
                border-radius: 5px;
                margin: 2px;
            }
        """)
        
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 创建标题栏
        self.create_header(main_layout)
        
        # 创建内容区域
        self.create_content_area(main_layout)
        
    def create_header(self, layout):
        """创建标题栏"""
        self.header_frame = QFrame()
        self.header_frame.setStyleSheet("""
            QFrame {
                background-color: #404040;
                border: none;
                border-radius: 5px 5px 0 0;
                padding: 5px;
                border-left: 3px solid #00ffff;
                outline: none;
            }
            QFrame:hover {
                background-color: #505050;
                border-left: 3px solid #00aaff;
            }
            QFrame:focus {
                outline: none;
            }
        """)
        self.header_frame.setFixedHeight(40)  # 增加高度以容纳分离的标题
        self.header_frame.setCursor(Qt.PointingHandCursor)
        
        header_layout = QHBoxLayout(self.header_frame)
        header_layout.setContentsMargins(10, 5, 10, 5)
        
        # 展开/折叠图标
        self.toggle_icon = QLabel("▶")
        self.toggle_icon.setStyleSheet("""
            color: #00ffff;
            font-size: 14px;
            font-weight: bold;
            padding: 2px;
        """)
        self.toggle_icon.setFixedWidth(25)
        self.toggle_icon.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(self.toggle_icon)

        # [!!!] --- 开始修复文字重叠问题 --- [!!!]

        # 1. 解析标题字符串，分离出中文和英文
        title_parts = self.title.split('(')
        chinese_title = title_parts[0].strip()
        english_title = ""
        if len(title_parts) > 1:
            # 去掉最后的 ')'
            english_title = f"({title_parts[1].replace(')', '').strip()})"

        # 2. 创建独立的 QLabel 用于中文
        self.chinese_title_label = QLabel(chinese_title)
        self.chinese_title_label.setStyleSheet(get_label_style('accordion_title', 'white', 'bold'))

        # 3. 创建独立的 QLabel 用于英文
        self.english_title_label = QLabel(english_title)
        self.english_title_label.setStyleSheet(get_label_style('accordion_subtitle', '#cccccc', 'normal'))

        # 4. 将它们添加到布局
        header_layout.addWidget(self.chinese_title_label)
        if english_title:  # 只有当有英文标题时才添加间距和英文标签
            header_layout.addSpacing(5)  # 在中英文之间加一点小间距
            header_layout.addWidget(self.english_title_label)

        # [!!!] --- 修复结束 --- [!!!]
        
        # 弹性空间
        header_layout.addStretch()
        
        # 状态指示器（可选）
        self.status_label = QLabel("")
        self.status_label.setStyleSheet(get_label_style('status_small', '#aaaaaa', 'normal'))
        header_layout.addWidget(self.status_label)
        
        layout.addWidget(self.header_frame)
        
        # 绑定点击事件
        self.header_frame.mousePressEvent = self.toggle_section
        
    def create_content_area(self, layout):
        """创建内容区域"""
        # 内容容器
        self.content_frame = QFrame()
        self.content_frame.setStyleSheet("""
            QFrame {
                background-color: #2b2b2b;
                border: none;
                border-radius: 0 0 5px 5px;
            }
        """)
        
        content_layout = QVBoxLayout(self.content_frame)
        content_layout.setContentsMargins(10, 10, 10, 10)
        content_layout.setSpacing(5)
        
        # 添加内容组件
        if self.content_widget:
            content_layout.addWidget(self.content_widget)
        else:
            # 默认占位内容
            placeholder = QLabel("暂无内容")
            placeholder.setStyleSheet("color: #888888; font-style: italic;")
            placeholder.setAlignment(Qt.AlignCenter)
            content_layout.addWidget(placeholder)
        
        layout.addWidget(self.content_frame)
        
        # 初始状态为折叠
        self.content_frame.hide()
        
    def toggle_section(self, event=None):
        """切换展开/折叠状态"""
        self.is_expanded = not self.is_expanded
        
        if self.is_expanded:
            self.expand()
        else:
            self.collapse()
            
        self.toggled.emit(self.is_expanded)
        self.logger.debug(f"手风琴区块 '{self.title}' {'展开' if self.is_expanded else '折叠'}")
        
    def expand(self):
        """展开区块"""
        self.toggle_icon.setText("▼")
        self.content_frame.show()
        self.status_label.setText("已展开")
        
    def collapse(self):
        """折叠区块"""
        self.toggle_icon.setText("▶")
        self.content_frame.hide()
        self.status_label.setText("")
        
    def set_expanded(self, expanded: bool):
        """设置展开状态"""
        if self.is_expanded != expanded:
            self.toggle_section()
            
    def set_content_widget(self, widget: QWidget):
        """设置内容组件"""
        if self.content_widget:
            self.content_frame.layout().removeWidget(self.content_widget)
            
        self.content_widget = widget
        self.content_frame.layout().addWidget(widget)
        
    def set_status_text(self, text: str):
        """设置状态文本"""
        self.status_label.setText(text)


class AccordionWidget(QWidget):
    """手风琴组件 - 管理多个可折叠区块"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger("AccordionWidget")
        
        self.sections = []  # 存储所有区块
        self.allow_multiple_expanded = True  # 是否允许多个区块同时展开
        
        self.init_ui()
        
    def init_ui(self):
        """初始化用户界面"""
        # 主布局
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(3)
        
        # 添加弹性空间，让区块紧凑排列
        self.main_layout.addStretch()
        
    def add_section(self, title: str, content_widget: QWidget = None, expanded: bool = False) -> AccordionSection:
        """添加新的区块"""
        section = AccordionSection(title, content_widget, self)
        section.toggled.connect(self.on_section_toggled)
        
        # 插入到弹性空间之前
        self.main_layout.insertWidget(len(self.sections), section)
        self.sections.append(section)
        
        if expanded:
            section.set_expanded(True)
            
        self.logger.info(f"添加手风琴区块: {title}")
        return section
        
    def on_section_toggled(self, expanded: bool):
        """处理区块展开/折叠事件"""
        if not self.allow_multiple_expanded and expanded:
            # 如果不允许多个展开，则折叠其他区块
            sender = self.sender()
            for section in self.sections:
                if section != sender and section.is_expanded:
                    section.collapse()
                    
    def set_allow_multiple_expanded(self, allow: bool):
        """设置是否允许多个区块同时展开"""
        self.allow_multiple_expanded = allow
        
    def collapse_all(self):
        """折叠所有区块"""
        for section in self.sections:
            if section.is_expanded:
                section.collapse()
                
    def expand_all(self):
        """展开所有区块"""
        for section in self.sections:
            if not section.is_expanded:
                section.expand()
                
    def get_section(self, title: str) -> AccordionSection:
        """根据标题获取区块"""
        for section in self.sections:
            if section.title == title:
                return section
        return None
        
    def remove_section(self, title: str):
        """移除指定区块"""
        section = self.get_section(title)
        if section:
            self.main_layout.removeWidget(section)
            self.sections.remove(section)
            section.deleteLater()
            self.logger.info(f"移除手风琴区块: {title}")
