"""
预警管理器
负责分析火焰烟雾数据并触发相应级别的预警弹窗
"""

from PyQt5.QtCore import QObject, pyqtSignal, QTimer
from qt_ui.alert_popup_widget import AlertPopupWidget
from config.alert_levels import AlertLevel
from utils.logger import get_logger
import time
from typing import Dict, Any, Optional, List


class AlertManager(QObject):
    """预警管理器"""
    
    # 信号定义
    alert_triggered = pyqtSignal(str, dict)  # (alert_level, alert_data)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger(__name__)
        
        # 预警状态跟踪
        self.last_alert_time = {}  # 记录每个级别的最后预警时间

        # 历史数据跟踪（用于判断趋势）
        self.fire_history = []  # 火焰历史数据
        self.smoke_history = []  # 烟雾历史数据
        self.max_history_length = 10  # 保留最近10次数据

        # 当前显示的弹窗
        self.current_popup = None

        # 预警阈值配置
        self.thresholds = {
            # 一级预警：检测到异常热源
            AlertLevel.LEVEL_1: {
                'fire_count_min': 0,
                'smoke_count_min': 1,
                'total_area_min': 100,
                'confidence_min': 0.3
            },
            # 二级预警：热源面积扩散
            AlertLevel.LEVEL_2: {
                'fire_count_min': 0,
                'smoke_count_min': 1,
                'total_area_min': 500,
                'area_growth_rate': 1.5,  # 面积增长1.5倍
                'confidence_min': 0.4
            },
            # 三级预警：检测到火焰
            AlertLevel.LEVEL_3: {
                'fire_count_min': 1,
                'smoke_count_min': 0,
                'total_area_min': 200,
                'confidence_min': 0.5
            },
            # 四级预警：火焰面积增大
            AlertLevel.LEVEL_4: {
                'fire_count_min': 1,
                'smoke_count_min': 0,
                'total_area_min': 800,
                'area_growth_rate': 2.0,  # 火焰面积增长2倍
                'confidence_min': 0.6
            }
        }

        # 冷却时间配置
        self.cooldown_times = {
            AlertLevel.LEVEL_1: 300,  # 5分钟
            AlertLevel.LEVEL_2: 240,  # 4分钟
            AlertLevel.LEVEL_3: 180,  # 3分钟
            AlertLevel.LEVEL_4: 120   # 2分钟
        }
        
    def update_detection_data(self, fire_data: Dict[str, Any], smoke_data: Dict[str, Any], heat_source_data: List[Dict] = None):
        """更新检测数据并评估预警级别"""
        try:
            heat_source_data = heat_source_data or []
            # 简化预警管理器的数据接收输出，保持后台简洁
            # print(f"🔍 预警管理器接收数据: {len(fire_data)}个火焰, {len(smoke_data)}个烟雾, {len(heat_source_data)}个热源")

            # 计算当前统计数据
            current_stats = self._calculate_current_stats(fire_data, smoke_data, heat_source_data)
            # print(f"📊 统计数据: 火焰面积={current_stats['fire_total_area']:.0f}, 烟雾面积={current_stats['smoke_total_area']:.0f}, 热源数量={current_stats['heat_source_count']}, 最高热源温度={current_stats['max_heat_temp']:.1f}°C, 总面积={current_stats['total_area']:.0f}, 最高置信度={current_stats['max_confidence']:.2f}")

            # 更新历史数据
            self._update_history(current_stats)

            # 评估预警级别
            alert_level = self._evaluate_alert_level(current_stats)
            print(f"⚠️ 评估预警级别: {alert_level}")

            # 如果需要预警且满足冷却时间
            if alert_level and self._should_trigger_alert(alert_level):
                print(f"🚨 触发预警: {alert_level}")
                self._trigger_alert(alert_level, current_stats)
            elif alert_level:
                print(f"⏰ 预警{alert_level}在冷却期内，跳过")
            else:
                print(f"✅ 无需预警")

        except Exception as e:
            self.logger.error(f"更新检测数据失败: {e}")
            print(f"❌ 预警管理器更新失败: {e}")
            
    def _calculate_current_stats(self, fire_data: Dict[str, Any], smoke_data: Dict[str, Any], heat_source_data: List[Dict] = None) -> Dict[str, Any]:
        """计算当前统计数据"""
        heat_source_data = heat_source_data or []

        stats = {
            'timestamp': time.time(),
            'fire_count': len(fire_data),
            'smoke_count': len(smoke_data),
            'heat_source_count': len(heat_source_data),
            'fire_total_area': 0,
            'smoke_total_area': 0,
            'heat_source_total_area': 0,
            'total_area': 0,
            'max_fire_confidence': 0,
            'max_smoke_confidence': 0,
            'max_heat_temp': 0,
            'max_confidence': 0
        }
        
        # 计算火焰统计
        for fire_id, fire_info in fire_data.items():
            area = fire_info.get('area', 0)
            confidence = fire_info.get('confidence', 0)
            stats['fire_total_area'] += area
            stats['max_fire_confidence'] = max(stats['max_fire_confidence'], confidence)
            
        # 计算烟雾统计
        for smoke_id, smoke_info in smoke_data.items():
            area = smoke_info.get('area', 0)
            confidence = smoke_info.get('confidence', 0)
            stats['smoke_total_area'] += area
            stats['max_smoke_confidence'] = max(stats['max_smoke_confidence'], confidence)

        # 计算热源统计
        for heat_source in heat_source_data:
            area = heat_source.get('area', 0)
            max_temp = heat_source.get('max_temp', 0)
            stats['heat_source_total_area'] += area
            stats['max_heat_temp'] = max(stats['max_heat_temp'], max_temp)

        # 计算总体统计
        stats['total_area'] = stats['fire_total_area'] + stats['smoke_total_area'] + stats['heat_source_total_area']
        stats['max_confidence'] = max(stats['max_fire_confidence'], stats['max_smoke_confidence'])

        return stats
        
    def _update_history(self, current_stats: Dict[str, Any]):
        """更新历史数据"""
        # 添加到历史记录
        if current_stats['fire_count'] > 0 or current_stats['fire_total_area'] > 0:
            self.fire_history.append({
                'timestamp': current_stats['timestamp'],
                'count': current_stats['fire_count'],
                'area': current_stats['fire_total_area'],
                'confidence': current_stats['max_fire_confidence']
            })
            
        if current_stats['smoke_count'] > 0 or current_stats['smoke_total_area'] > 0:
            self.smoke_history.append({
                'timestamp': current_stats['timestamp'],
                'count': current_stats['smoke_count'],
                'area': current_stats['smoke_total_area'],
                'confidence': current_stats['max_smoke_confidence']
            })
            
        # 限制历史记录长度
        if len(self.fire_history) > self.max_history_length:
            self.fire_history = self.fire_history[-self.max_history_length:]
        if len(self.smoke_history) > self.max_history_length:
            self.smoke_history = self.smoke_history[-self.max_history_length:]
            
    def _evaluate_alert_level(self, current_stats: Dict[str, Any]) -> Optional[str]:
        """评估预警级别"""
        # 按优先级从高到低检查
        alert_levels = [AlertLevel.LEVEL_4, AlertLevel.LEVEL_3, AlertLevel.LEVEL_2, AlertLevel.LEVEL_1]
        
        for level in alert_levels:
            if self._check_alert_conditions(level, current_stats):
                return level
                
        return None
        
    def _check_alert_conditions(self, alert_level: str, current_stats: Dict[str, Any]) -> bool:
        """检查预警条件"""
        threshold = self.thresholds.get(alert_level, {})

        # 一级预警：基于热源检测的独立逻辑
        if alert_level == AlertLevel.LEVEL_1:
            print(f"🔍 检查一级预警条件（基于热源检测）:")
            print(f"   热源数量: {current_stats.get('heat_source_count', 0)} >= {threshold.get('heat_source_count_min', 1)}")
            print(f"   热源温度: {current_stats.get('max_heat_temp', 0):.1f}°C >= {threshold.get('heat_source_temp_min', 35.0)}°C")

            # 一级预警只检查热源条件
            if current_stats.get('heat_source_count', 0) < threshold.get('heat_source_count_min', 1):
                print(f"   ❌ 热源数量不足")
                return False
            if current_stats.get('max_heat_temp', 0) < threshold.get('heat_source_temp_min', 35.0):
                print(f"   ❌ 热源温度不足")
                return False

            print(f"   ✅ 一级预警条件满足！")
            return True

        # 二级预警：基于热源扩散的独立逻辑
        if alert_level == AlertLevel.LEVEL_2:
            print(f"🔍 检查二级预警条件（基于热源扩散）:")
            print(f"   热源数量: {current_stats.get('heat_source_count', 0)} >= {threshold.get('heat_source_count_min', 2)}")
            print(f"   热源总面积: {current_stats.get('heat_source_total_area', 0)} >= {threshold.get('heat_source_area_min', 300)}")

            # 检查热源数量条件
            if current_stats.get('heat_source_count', 0) < threshold.get('heat_source_count_min', 2):
                print(f"   ❌ 热源数量不足")
                return False

            # 检查热源面积条件
            if current_stats.get('heat_source_total_area', 0) < threshold.get('heat_source_area_min', 300):
                print(f"   ❌ 热源总面积不足")
                return False

            # 检查热源面积增长率（如果有历史数据）
            if 'heat_source_growth_rate' in threshold:
                if not self._check_heat_source_area_growth(current_stats, threshold['heat_source_growth_rate']):
                    print(f"   ❌ 热源面积增长率不足")
                    return False

            print(f"   ✅ 二级预警条件满足！")
            return True

        # 三级、四级预警：基于火焰烟雾检测的传统逻辑
        # 添加调试信息
        if alert_level in [AlertLevel.LEVEL_3, AlertLevel.LEVEL_4]:
            print(f"🔍 检查{alert_level}预警条件（基于火焰烟雾检测）:")
            print(f"   火焰数量: {current_stats['fire_count']} >= {threshold.get('fire_count_min', 0)}")
            print(f"   烟雾数量: {current_stats['smoke_count']} >= {threshold.get('smoke_count_min', 0)}")
            print(f"   总面积: {current_stats['total_area']} >= {threshold.get('total_area_min', 0)}")
            print(f"   置信度: {current_stats['max_confidence']:.2f} >= {threshold.get('confidence_min', 0)}")

        # 基础条件检查（火焰烟雾）
        if current_stats['fire_count'] < threshold.get('fire_count_min', 0):
            return False
        if current_stats['smoke_count'] < threshold.get('smoke_count_min', 0):
            return False
        if current_stats['total_area'] < threshold.get('total_area_min', 0):
            return False
        if current_stats['max_confidence'] < threshold.get('confidence_min', 0):
            return False
            
        # 面积增长率检查（二级和四级预警）
        if 'area_growth_rate' in threshold:
            if not self._check_area_growth(alert_level, current_stats, threshold['area_growth_rate']):
                return False
                
        return True
        
    def _check_area_growth(self, alert_level: str, current_stats: Dict[str, Any], required_rate: float) -> bool:
        """检查面积增长率"""
        if alert_level in [AlertLevel.LEVEL_2]:
            # 二级预警：检查烟雾面积增长
            if len(self.smoke_history) < 2:
                return False
            prev_area = self.smoke_history[-2]['area']
            current_area = current_stats['smoke_total_area']
            if prev_area > 0:
                growth_rate = current_area / prev_area
                return growth_rate >= required_rate
                
        elif alert_level in [AlertLevel.LEVEL_4]:
            # 四级预警：检查火焰面积增长
            if len(self.fire_history) < 2:
                return False
            prev_area = self.fire_history[-2]['area']
            current_area = current_stats['fire_total_area']
            if prev_area > 0:
                growth_rate = current_area / prev_area
                return growth_rate >= required_rate
                
        return False

    def _check_heat_source_area_growth(self, current_stats: Dict[str, Any], required_rate: float) -> bool:
        """检查热源面积增长率"""
        # 需要至少2个历史记录来计算增长率
        if not hasattr(self, 'heat_source_history'):
            self.heat_source_history = []

        if len(self.heat_source_history) < 2:
            # 添加当前记录到历史
            self.heat_source_history.append({
                'timestamp': current_stats['timestamp'],
                'area': current_stats.get('heat_source_total_area', 0),
                'count': current_stats.get('heat_source_count', 0)
            })
            return False  # 历史数据不足

        # 获取前一次的面积
        prev_area = self.heat_source_history[-1]['area']
        current_area = current_stats.get('heat_source_total_area', 0)

        # 添加当前记录到历史
        self.heat_source_history.append({
            'timestamp': current_stats['timestamp'],
            'area': current_area,
            'count': current_stats.get('heat_source_count', 0)
        })

        # 限制历史记录长度
        if len(self.heat_source_history) > 10:
            self.heat_source_history = self.heat_source_history[-10:]

        # 计算增长率
        if prev_area > 0:
            growth_rate = current_area / prev_area
            print(f"   热源面积增长率: {growth_rate:.2f} (需要: {required_rate})")
            return growth_rate >= required_rate
        else:
            # 如果之前没有面积，现在有面积，认为是显著增长
            return current_area > 0

    def _should_trigger_alert(self, alert_level: str) -> bool:
        """检查是否应该触发预警（考虑冷却时间）"""
        current_time = time.time()
        last_time = self.last_alert_time.get(alert_level, 0)

        # 获取该级别的冷却时间
        cooldown_time = self.cooldown_times.get(alert_level, 300)

        # 检查冷却时间
        if current_time - last_time < cooldown_time:
            return False

        return True
        
    def _trigger_alert(self, alert_level: str, alert_data: Dict[str, Any]):
        """触发预警"""
        try:
            # 记录预警时间
            self.last_alert_time[alert_level] = time.time()
            
            # 关闭当前弹窗（如果有）
            if self.current_popup:
                self.current_popup.close()
                self.current_popup = None
                
            # 创建新弹窗
            self.current_popup = AlertPopupWidget(alert_level, alert_data)
            
            # 连接信号
            self.current_popup.popup_confirmed.connect(self._on_popup_confirmed)
            self.current_popup.popup_ignored.connect(self._on_popup_ignored)
            self.current_popup.popup_closed.connect(self._on_popup_closed)
            
            # 显示弹窗
            self.current_popup.show()
            self.current_popup.raise_()
            self.current_popup.activateWindow()
            
            # 发送信号
            self.alert_triggered.emit(alert_level, alert_data)
            
            self.logger.info(f"触发{alert_level}预警: 火焰={alert_data['fire_count']}, 烟雾={alert_data['smoke_count']}, 总面积={alert_data['total_area']:.0f}")
            
        except Exception as e:
            self.logger.error(f"触发预警失败: {e}")
            
    def _on_popup_confirmed(self, alert_level: str):
        """弹窗确认处理"""
        self.logger.info(f"预警已确认处理: {alert_level}")
        self.current_popup = None
        
    def _on_popup_ignored(self, alert_level: str):
        """弹窗忽略处理"""
        self.logger.info(f"预警已忽略: {alert_level}")
        self.current_popup = None
        
    def _on_popup_closed(self, alert_level: str):
        """弹窗关闭处理"""
        self.logger.info(f"预警弹窗已关闭: {alert_level}")
        self.current_popup = None
        
    def set_alert_thresholds(self, level: str, thresholds: Dict[str, Any]):
        """设置预警阈值"""
        if level in self.thresholds:
            self.thresholds[level].update(thresholds)
            self.logger.info(f"更新{level}预警阈值: {thresholds}")
            
    def get_alert_history(self) -> Dict[str, Any]:
        """获取预警历史"""
        return {
            'last_alert_times': self.last_alert_time.copy(),
            'fire_history': self.fire_history.copy(),
            'smoke_history': self.smoke_history.copy()
        }
        
    def reset_alert_cooldown(self, alert_level: str = None):
        """重置预警冷却时间"""
        if alert_level:
            if alert_level in self.last_alert_time:
                del self.last_alert_time[alert_level]
                self.logger.info(f"重置{alert_level}预警冷却时间")
        else:
            self.last_alert_time.clear()
            self.logger.info("重置所有预警冷却时间")
            
    def close_current_popup(self):
        """关闭当前弹窗"""
        if self.current_popup:
            self.current_popup.close()
            self.current_popup = None
