"""
四级预警弹窗组件
一级：检测到异常热源
二级：热源面积扩散
三级：检测到火焰
四级：火焰面积增大
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel,
                             QPushButton, QFrame, QProgressBar, QApplication)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QPropertyAnimation, QEasingCurve
from PyQt5.QtGui import QFont, QColor, QPalette, QPixmap, QPainter, QBrush
from config.alert_levels import AlertLevel
from utils.logger import get_logger
import time


class AlertPopupWidget(QDialog):
    """四级预警弹窗组件"""
    
    # 信号定义
    popup_confirmed = pyqtSignal(str)  # 用户确认信号
    popup_ignored = pyqtSignal(str)    # 用户忽略信号
    popup_closed = pyqtSignal(str)     # 弹窗关闭信号
    
    def __init__(self, alert_level, alert_data=None, parent=None):
        super().__init__(parent)
        self.logger = get_logger(__name__)
        
        # 预警数据
        self.alert_level = alert_level
        self.alert_data = alert_data or {}

        # 倒计时相关
        self.countdown_seconds = 30  # 30秒自动关闭
        self.countdown_timer = QTimer()
        self.countdown_timer.timeout.connect(self._update_countdown)

        # 动画效果
        self.animation = None
        
        self.init_ui()
        self.setup_styles()
        self.start_countdown()
        
    def init_ui(self):
        """初始化界面"""
        # 设置窗口属性
        self.setWindowTitle("🚨 火灾预警系统")
        self.setModal(False)  # 设置为非模态，避免阻塞主界面
        self.setWindowFlags(Qt.Dialog | Qt.WindowStaysOnTopHint | Qt.FramelessWindowHint)
        self.setFixedSize(450, 350)
        
        # 居中显示
        self.center_on_screen()
        
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 创建主容器
        container = QFrame()
        container.setObjectName("alertContainer")
        main_layout.addWidget(container)
        
        # 容器布局
        container_layout = QVBoxLayout(container)
        container_layout.setContentsMargins(20, 20, 20, 20)
        container_layout.setSpacing(15)
        
        # 标题区域
        self.create_title_section(container_layout)
        
        # 内容区域
        self.create_content_section(container_layout)
        
        # 按钮区域
        self.create_button_section(container_layout)
        
        # 倒计时区域
        self.create_countdown_section(container_layout)
        
    def create_title_section(self, layout):
        """创建标题区域"""
        title_frame = QFrame()
        title_frame.setObjectName("titleFrame")
        title_layout = QHBoxLayout(title_frame)
        title_layout.setContentsMargins(10, 10, 10, 10)
        
        # 预警图标和标题
        config = self.get_alert_config()
        
        # 图标
        icon_label = QLabel(config['icon'])
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet("font-size: 32px;")
        title_layout.addWidget(icon_label)
        
        # 标题文本
        title_label = QLabel(config['title'])
        title_label.setObjectName("titleLabel")
        title_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        title_layout.addWidget(title_label, 1)
        
        layout.addWidget(title_frame)
        
    def create_content_section(self, layout):
        """创建内容区域"""
        content_frame = QFrame()
        content_frame.setObjectName("contentFrame")
        content_layout = QVBoxLayout(content_frame)
        content_layout.setContentsMargins(15, 15, 15, 15)
        content_layout.setSpacing(10)
        
        config = self.get_alert_config()
        
        # 预警描述
        desc_label = QLabel(config['description'])
        desc_label.setObjectName("descLabel")
        desc_label.setWordWrap(True)
        desc_label.setAlignment(Qt.AlignLeft)
        content_layout.addWidget(desc_label)
        
        # 详细信息
        if self.alert_data:
            self.create_detail_info(content_layout)
        
        # 建议动作
        action_label = QLabel(f"建议动作：{config['action']}")
        action_label.setObjectName("actionLabel")
        action_label.setWordWrap(True)
        content_layout.addWidget(action_label)
        
        layout.addWidget(content_frame)
        
    def create_detail_info(self, layout):
        """创建详细信息"""
        detail_frame = QFrame()
        detail_frame.setObjectName("detailFrame")
        detail_layout = QVBoxLayout(detail_frame)
        detail_layout.setContentsMargins(10, 10, 10, 10)
        detail_layout.setSpacing(5)
        
        # 显示相关数据
        if 'fire_count' in self.alert_data:
            fire_info = QLabel(f"🔥 火焰目标数量：{self.alert_data['fire_count']}")
            fire_info.setObjectName("detailInfo")
            detail_layout.addWidget(fire_info)
            
        if 'smoke_count' in self.alert_data:
            smoke_info = QLabel(f"🌫️ 烟雾目标数量：{self.alert_data['smoke_count']}")
            smoke_info.setObjectName("detailInfo")
            detail_layout.addWidget(smoke_info)
            
        if 'total_area' in self.alert_data:
            area_info = QLabel(f"📏 总面积：{self.alert_data['total_area']:.0f} 像素²")
            area_info.setObjectName("detailInfo")
            detail_layout.addWidget(area_info)
            
        if 'max_confidence' in self.alert_data:
            conf_info = QLabel(f"🎯 最高置信度：{self.alert_data['max_confidence']:.1%}")
            conf_info.setObjectName("detailInfo")
            detail_layout.addWidget(conf_info)
        
        layout.addWidget(detail_frame)
        
    def create_button_section(self, layout):
        """创建按钮区域"""
        button_frame = QFrame()
        button_layout = QHBoxLayout(button_frame)
        button_layout.setContentsMargins(0, 0, 0, 0)
        button_layout.setSpacing(15)
        
        # 确认按钮
        self.confirm_btn = QPushButton("✅ 确认处理")
        self.confirm_btn.setObjectName("confirmBtn")
        self.confirm_btn.clicked.connect(self.on_confirm)
        button_layout.addWidget(self.confirm_btn)
        
        # 忽略按钮
        self.ignore_btn = QPushButton("❌ 暂时忽略")
        self.ignore_btn.setObjectName("ignoreBtn")
        self.ignore_btn.clicked.connect(self.on_ignore)
        button_layout.addWidget(self.ignore_btn)
        
        layout.addWidget(button_frame)
        
    def create_countdown_section(self, layout):
        """创建倒计时区域"""
        countdown_frame = QFrame()
        countdown_layout = QHBoxLayout(countdown_frame)
        countdown_layout.setContentsMargins(0, 5, 0, 0)
        
        # 倒计时标签
        self.countdown_label = QLabel(f"⏰ {self.countdown_seconds}秒后自动关闭")
        self.countdown_label.setObjectName("countdownLabel")
        self.countdown_label.setAlignment(Qt.AlignCenter)
        countdown_layout.addWidget(self.countdown_label)
        
        layout.addWidget(countdown_frame)
        
    def get_alert_config(self):
        """获取预警级别配置"""
        configs = {
            AlertLevel.LEVEL_1: {
                'icon': '🟡',
                'title': '一级预警 - 异常热源',
                'description': '系统检测到异常热源信号，温度超出正常范围。请注意观察相关区域，确认是否存在潜在火灾风险。',
                'action': '记录日志，持续监控',
                'color': '#FFA500',  # 橙色
                'bg_color': '#FFF3E0'
            },
            AlertLevel.LEVEL_2: {
                'icon': '🟠',
                'title': '二级预警 - 热源扩散',
                'description': '检测到热源面积正在扩散，温度异常区域增大。建议立即派人现场确认情况。',
                'action': '现场确认，准备应急措施',
                'color': '#FF8C00',  # 深橙色
                'bg_color': '#FFE0B2'
            },
            AlertLevel.LEVEL_3: {
                'icon': '🔴',
                'title': '三级预警 - 检测到火焰',
                'description': '系统确认检测到火焰！这是严重的火灾警报，请立即采取应急措施。',
                'action': '立即现场处置，启动应急预案',
                'color': '#FF4444',  # 红色
                'bg_color': '#FFEBEE'
            },
            AlertLevel.LEVEL_4: {
                'icon': '🚨',
                'title': '四级预警 - 火势蔓延',
                'description': '火焰面积持续增大，火势正在蔓延！这是最高级别警报，请立即疏散人员并启动灭火程序。',
                'action': '紧急疏散，全面灭火',
                'color': '#CC0000',  # 深红色
                'bg_color': '#FFCDD2'
            }
        }
        return configs.get(self.alert_level, configs[AlertLevel.LEVEL_1])
        
    def setup_styles(self):
        """设置样式"""
        config = self.get_alert_config()
        
        style = f"""
            QDialog {{
                background-color: {config['bg_color']};
                border: 3px solid {config['color']};
                border-radius: 10px;
            }}
            
            #alertContainer {{
                background-color: {config['bg_color']};
                border-radius: 8px;
            }}
            
            #titleFrame {{
                background-color: {config['color']};
                border-radius: 5px;
                margin-bottom: 5px;
            }}
            
            #titleLabel {{
                color: white;
                font-size: 18px;
                font-weight: bold;
            }}
            
            #contentFrame {{
                background-color: rgba(255, 255, 255, 0.9);
                border-radius: 5px;
                border: 1px solid {config['color']};
            }}
            
            #descLabel {{
                color: #333333;
                font-size: 14px;
                line-height: 1.4;
            }}
            
            #actionLabel {{
                color: {config['color']};
                font-size: 13px;
                font-weight: bold;
            }}
            
            #detailFrame {{
                background-color: rgba(240, 240, 240, 0.8);
                border-radius: 3px;
                margin: 5px 0px;
            }}
            
            #detailInfo {{
                color: #555555;
                font-size: 12px;
            }}
            
            #confirmBtn {{
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }}
            
            #confirmBtn:hover {{
                background-color: #45a049;
            }}
            
            #ignoreBtn {{
                background-color: #f44336;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }}
            
            #ignoreBtn:hover {{
                background-color: #da190b;
            }}
            
            #countdownLabel {{
                color: #666666;
                font-size: 12px;
                font-style: italic;
            }}
        """
        
        self.setStyleSheet(style)
        
    def center_on_screen(self):
        """居中显示在屏幕上"""
        screen = QApplication.desktop().screenGeometry()
        size = self.geometry()
        self.move(
            (screen.width() - size.width()) // 2,
            (screen.height() - size.height()) // 2
        )
        
    def start_countdown(self):
        """开始倒计时"""
        self.countdown_timer.start(1000)  # 每秒更新一次
        
    def _update_countdown(self):
        """更新倒计时"""
        self.countdown_seconds -= 1
        self.countdown_label.setText(f"⏰ {self.countdown_seconds}秒后自动关闭")
        
        if self.countdown_seconds <= 0:
            self.countdown_timer.stop()
            self.auto_close()
            
    def auto_close(self):
        """自动关闭"""
        self.logger.info(f"预警弹窗自动关闭: {self.alert_level}")
        self.popup_closed.emit(self.alert_level)
        self.close()
        
    def on_confirm(self):
        """确认处理"""
        self.countdown_timer.stop()
        self.logger.info(f"用户确认处理预警: {self.alert_level}")
        self.popup_confirmed.emit(self.alert_level)
        self.close()
        
    def on_ignore(self):
        """忽略预警"""
        self.countdown_timer.stop()
        self.logger.info(f"用户忽略预警: {self.alert_level}")
        self.popup_ignored.emit(self.alert_level)
        self.close()
        
    def closeEvent(self, event):
        """关闭事件"""
        self.countdown_timer.stop()
        self.popup_closed.emit(self.alert_level)
        super().closeEvent(event)
