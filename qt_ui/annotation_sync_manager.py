#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
标注同步管理器
实现可见光标注区域自动同步到热成像画面
"""

import cv2
import numpy as np
from typing import List, Tuple, Optional, Dict, Any
from dataclasses import dataclass
from datetime import datetime

from utils.logger import get_logger
from config.coordinate_system_manager import get_coordinate_system_manager
from qt_ui.manual_annotation_widget import ManualAnnotation, ManualAnnotationManager


@dataclass
class SyncResult:
    """同步结果"""
    success: bool
    visible_annotation_id: str
    thermal_annotation_id: Optional[str] = None
    error_message: Optional[str] = None


class AnnotationSyncManager:
    """标注同步管理器 - 将可见光标注同步到热成像"""
    
    def __init__(self, annotation_manager: ManualAnnotationManager):
        """
        初始化同步管理器
        
        Args:
            annotation_manager: 手动标注管理器实例
        """
        self.logger = get_logger("AnnotationSyncManager")
        self.annotation_manager = annotation_manager
        self.coord_manager = get_coordinate_system_manager()
        
        # 同步配置
        self.auto_sync_enabled = True  # 是否启用自动同步
        self.sync_color_offset = (0, 0, 50)  # 热成像标注颜色偏移
        self.sync_label_suffix = "_热成像"  # 同步标注的标签后缀
        
        # 同步记录
        self.sync_pairs: Dict[str, str] = {}  # visible_id -> thermal_id
        self.reverse_sync_pairs: Dict[str, str] = {}  # thermal_id -> visible_id

        # 偏移量设置 - 根据用户手动调整的最佳位置设定默认偏移量
        self.manual_offset_x = 20  # 手动X偏移 (向右20像素)
        self.manual_offset_y = 10  # 手动Y偏移 (向下10像素)
        self.manual_scale = 1.0    # 手动缩放比例

        # 累积偏移量记录
        self.original_positions: Dict[str, Tuple[int, int, int, int]] = {}  # thermal_id -> original_bbox
        self.cumulative_offsets: Dict[str, Tuple[int, int, float]] = {}  # thermal_id -> (total_x, total_y, total_scale)

        self.logger.info("标注同步管理器初始化完成")
    
    def enable_auto_sync(self, enabled: bool = True):
        """启用/禁用自动同步"""
        self.auto_sync_enabled = enabled
        status = "启用" if enabled else "禁用"
        self.logger.info(f"自动同步已{status}")
    
    def sync_visible_to_thermal(self, visible_annotation_id: str) -> SyncResult:
        """
        将可见光标注同步到热成像
        
        Args:
            visible_annotation_id: 可见光标注ID
            
        Returns:
            同步结果
        """
        try:
            # 获取可见光标注
            visible_annotation = self.annotation_manager.annotations.get(visible_annotation_id)
            if not visible_annotation:
                return SyncResult(
                    success=False,
                    visible_annotation_id=visible_annotation_id,
                    error_message="找不到指定的可见光标注"
                )
            
            # 检查是否已经是热成像标注
            if getattr(visible_annotation, 'is_thermal', False):
                return SyncResult(
                    success=False,
                    visible_annotation_id=visible_annotation_id,
                    error_message="该标注已经是热成像标注"
                )
            
            # 检查是否已经同步过
            if visible_annotation_id in self.sync_pairs:
                existing_thermal_id = self.sync_pairs[visible_annotation_id]
                # 验证热成像标注是否还存在
                if existing_thermal_id in self.annotation_manager.annotations:
                    self.logger.info(f"标注 {visible_annotation.label} 已经同步过，跳过重复同步")
                    return SyncResult(
                        success=True,
                        visible_annotation_id=visible_annotation_id,
                        thermal_annotation_id=existing_thermal_id,
                        error_message="标注已经同步过"
                    )
                else:
                    # 热成像标注已被删除，清理同步记录
                    self.logger.warning(f"热成像标注 {existing_thermal_id} 已被删除，清理同步记录")
                    del self.sync_pairs[visible_annotation_id]
                    if existing_thermal_id in self.reverse_sync_pairs:
                        del self.reverse_sync_pairs[existing_thermal_id]
            
            # 转换坐标
            thermal_bbox = self._convert_visible_bbox_to_thermal(visible_annotation.bbox)
            if not thermal_bbox:
                return SyncResult(
                    success=False,
                    visible_annotation_id=visible_annotation_id,
                    error_message="坐标转换失败，可能超出热成像范围"
                )

            # 应用手动偏移量（基于用户调整的最佳位置）
            original_thermal_bbox = thermal_bbox  # 保存原始转换位置
            if self.manual_offset_x != 0 or self.manual_offset_y != 0 or self.manual_scale != 1.0:
                thermal_bbox = self._apply_offset_to_bbox(
                    thermal_bbox, self.manual_offset_x, self.manual_offset_y, self.manual_scale
                )
                self.logger.info(f"📐 应用默认偏移量: X={self.manual_offset_x}px, Y={self.manual_offset_y}px, Scale={self.manual_scale:.2f}x")
                self.logger.info(f"📍 位置调整: {original_thermal_bbox} -> {thermal_bbox}")
            
            # 创建热成像标注
            thermal_color = self._adjust_color_for_thermal(visible_annotation.color)
            thermal_label = visible_annotation.label + self.sync_label_suffix
            
            thermal_annotation_id = self.annotation_manager.add_annotation(
                bbox=thermal_bbox,
                label=thermal_label,
                color=thermal_color,
                is_thermal=True
            )
            
            # 记录同步关系
            self.sync_pairs[visible_annotation_id] = thermal_annotation_id
            self.reverse_sync_pairs[thermal_annotation_id] = visible_annotation_id

            # 记录原始位置和初始累积偏移量
            # 使用转换前的位置作为原始基准位置
            self.original_positions[thermal_annotation_id] = original_thermal_bbox
            self.cumulative_offsets[thermal_annotation_id] = (
                self.manual_offset_x,
                self.manual_offset_y,
                self.manual_scale
            )

            self.logger.info(f"✅ 标注同步成功: {visible_annotation.label} -> {thermal_label}")
            self.logger.info(f"📍 记录原始位置: {thermal_bbox}, 初始偏移: ({self.manual_offset_x}, {self.manual_offset_y}, {self.manual_scale})")

            # 打印详细的坐标信息
            print(f"\n🎯 热成像标注坐标信息:")
            print(f"   标注ID: {thermal_annotation_id}")
            print(f"   标注名称: {thermal_label}")
            print(f"   可见光原始坐标: {visible_annotation.bbox}")
            print(f"   热成像转换坐标: {original_thermal_bbox} (转换前)")
            print(f"   热成像最终坐标: {thermal_bbox} (应用偏移后)")
            print(f"   应用的偏移量: X={self.manual_offset_x}px, Y={self.manual_offset_y}px")
            print(f"   坐标格式: (x, y, width, height)")
            print(f"   左上角: ({thermal_bbox[0]}, {thermal_bbox[1]})")
            print(f"   右下角: ({thermal_bbox[0] + thermal_bbox[2]}, {thermal_bbox[1] + thermal_bbox[3]})")
            print(f"   中心点: ({thermal_bbox[0] + thermal_bbox[2]//2}, {thermal_bbox[1] + thermal_bbox[3]//2})")
            print(f"=" * 60)

            return SyncResult(
                success=True,
                visible_annotation_id=visible_annotation_id,
                thermal_annotation_id=thermal_annotation_id
            )
            
        except Exception as e:
            self.logger.error(f"❌ 标注同步失败: {e}")
            return SyncResult(
                success=False,
                visible_annotation_id=visible_annotation_id,
                error_message=str(e)
            )
    
    def sync_all_visible_annotations(self) -> List[SyncResult]:
        """同步所有可见光标注到热成像"""
        results = []

        # 获取所有可见光标注
        all_annotations = self.annotation_manager.annotations
        visible_annotations = [
            ann for ann in all_annotations.values()
            if not getattr(ann, 'is_thermal', False)
        ]

        # 过滤出未同步的标注
        unsynced_annotations = [
            ann for ann in visible_annotations
            if ann.id not in self.sync_pairs
        ]

        self.logger.info(f"开始同步 {len(unsynced_annotations)} 个未同步的可见光标注（总共 {len(visible_annotations)} 个可见光标注）")

        # 对于已同步的标注，添加跳过记录
        for annotation in visible_annotations:
            if annotation.id in self.sync_pairs:
                existing_thermal_id = self.sync_pairs[annotation.id]
                results.append(SyncResult(
                    success=True,
                    visible_annotation_id=annotation.id,
                    thermal_annotation_id=existing_thermal_id,
                    error_message="标注已经同步过，跳过"
                ))

        # 同步未同步的标注
        for annotation in unsynced_annotations:
            result = self.sync_visible_to_thermal(annotation.id)
            results.append(result)

        success_count = sum(1 for r in results if r.success)
        new_sync_count = len(unsynced_annotations)
        self.logger.info(f"同步完成: {success_count}/{len(visible_annotations)} 成功，其中新同步 {new_sync_count} 个")

        return results
    
    def remove_sync_pair(self, visible_annotation_id: str) -> bool:
        """移除同步对"""
        try:
            if visible_annotation_id in self.sync_pairs:
                thermal_annotation_id = self.sync_pairs[visible_annotation_id]

                # 删除热成像标注
                self.annotation_manager.remove_annotation(thermal_annotation_id)

                # 移除同步记录
                del self.sync_pairs[visible_annotation_id]
                if thermal_annotation_id in self.reverse_sync_pairs:
                    del self.reverse_sync_pairs[thermal_annotation_id]

                # 清理累积偏移量记录
                if thermal_annotation_id in self.original_positions:
                    del self.original_positions[thermal_annotation_id]
                if thermal_annotation_id in self.cumulative_offsets:
                    del self.cumulative_offsets[thermal_annotation_id]

                self.logger.info(f"✅ 移除同步对: {visible_annotation_id} -> {thermal_annotation_id}")
                self.logger.info(f"🧹 清理累积偏移量记录")
                return True

            return False

        except Exception as e:
            self.logger.error(f"❌ 移除同步对失败: {e}")
            return False

    def apply_offset_to_thermal_annotations(self, x_offset: int, y_offset: int, scale_factor: float = 1.0) -> int:
        """
        应用偏移量到所有已同步的热成像标注

        Args:
            x_offset: X方向偏移量（像素）
            y_offset: Y方向偏移量（像素）
            scale_factor: 缩放比例

        Returns:
            应用偏移量的标注数量
        """
        try:
            applied_count = 0

            # 更新偏移量设置
            self.manual_offset_x = x_offset
            self.manual_offset_y = y_offset
            self.manual_scale = scale_factor

            # 遍历所有同步的热成像标注
            for thermal_annotation_id in self.sync_pairs.values():
                thermal_annotation = self.annotation_manager.annotations.get(thermal_annotation_id)
                if thermal_annotation:
                    # 获取当前边界框
                    current_bbox = thermal_annotation.bbox

                    # 应用偏移量和缩放
                    adjusted_bbox = self._apply_offset_to_bbox(current_bbox, x_offset, y_offset, scale_factor)

                    # 更新标注
                    thermal_annotation.bbox = adjusted_bbox
                    applied_count += 1

                    # 更新累积偏移量
                    if thermal_annotation_id in self.cumulative_offsets:
                        old_x, old_y, old_scale = self.cumulative_offsets[thermal_annotation_id]
                        # 计算新的累积偏移量
                        new_cumulative_x = old_x + x_offset
                        new_cumulative_y = old_y + y_offset
                        new_cumulative_scale = old_scale * scale_factor
                        self.cumulative_offsets[thermal_annotation_id] = (new_cumulative_x, new_cumulative_y, new_cumulative_scale)

                        # 计算相对于原始位置的总偏移
                        if thermal_annotation_id in self.original_positions:
                            original_bbox = self.original_positions[thermal_annotation_id]
                            total_offset_x = new_cumulative_x
                            total_offset_y = new_cumulative_y

                            self.logger.info(f"✅ 应用偏移量到标注 {thermal_annotation_id}: {current_bbox} -> {adjusted_bbox}")
                            self.logger.info(f"📊 累积偏移量: X={total_offset_x}px, Y={total_offset_y}px, Scale={new_cumulative_scale:.2f}x")

                            # 打印详细的坐标变化信息
                            print(f"\n🔄 热成像标注坐标变化:")
                            print(f"   标注ID: {thermal_annotation_id}")
                            print(f"   标注名称: {thermal_annotation.label}")
                            print(f"   调整前坐标: {current_bbox} (x, y, width, height)")
                            print(f"   调整后坐标: {adjusted_bbox} (x, y, width, height)")
                            print(f"   本次偏移: X={x_offset}px, Y={y_offset}px, Scale={scale_factor:.2f}x")
                            print(f"   累积偏移: X={total_offset_x}px, Y={total_offset_y}px, Scale={new_cumulative_scale:.2f}x")

                            # 计算中心点变化
                            old_center_x = current_bbox[0] + current_bbox[2] // 2
                            old_center_y = current_bbox[1] + current_bbox[3] // 2
                            new_center_x = adjusted_bbox[0] + adjusted_bbox[2] // 2
                            new_center_y = adjusted_bbox[1] + adjusted_bbox[3] // 2

                            print(f"   中心点: ({old_center_x}, {old_center_y}) -> ({new_center_x}, {new_center_y})")
                            print(f"   左上角: ({current_bbox[0]}, {current_bbox[1]}) -> ({adjusted_bbox[0]}, {adjusted_bbox[1]})")
                            print(f"   右下角: ({current_bbox[0]+current_bbox[2]}, {current_bbox[1]+current_bbox[3]}) -> ({adjusted_bbox[0]+adjusted_bbox[2]}, {adjusted_bbox[1]+adjusted_bbox[3]})")
                            print(f"=" * 60)

                            # 打印详细的坐标变化信息
                            print(f"\n🔄 标注坐标变化详情:")
                            print(f"   标注ID: {thermal_annotation_id}")
                            print(f"   标注名称: {thermal_annotation.label}")
                            print(f"   调整前坐标: {current_bbox}")
                            print(f"   调整后坐标: {adjusted_bbox}")
                            print(f"   本次偏移量: X={x_offset}px, Y={y_offset}px, Scale={scale_factor:.2f}x")
                            print(f"   累积偏移量: X={total_offset_x}px, Y={total_offset_y}px, Scale={new_cumulative_scale:.2f}x")

                            # 计算坐标变化
                            delta_x = adjusted_bbox[0] - current_bbox[0]
                            delta_y = adjusted_bbox[1] - current_bbox[1]
                            delta_w = adjusted_bbox[2] - current_bbox[2]
                            delta_h = adjusted_bbox[3] - current_bbox[3]

                            print(f"   坐标变化量: ΔX={delta_x}px, ΔY={delta_y}px, ΔW={delta_w}px, ΔH={delta_h}px")

                            # 显示关键点位置
                            old_center = (current_bbox[0] + current_bbox[2]//2, current_bbox[1] + current_bbox[3]//2)
                            new_center = (adjusted_bbox[0] + adjusted_bbox[2]//2, adjusted_bbox[1] + adjusted_bbox[3]//2)
                            print(f"   中心点移动: ({old_center[0]}, {old_center[1]}) -> ({new_center[0]}, {new_center[1]})")
                            print(f"   左上角移动: ({current_bbox[0]}, {current_bbox[1]}) -> ({adjusted_bbox[0]}, {adjusted_bbox[1]})")
                            print(f"   右下角移动: ({current_bbox[0]+current_bbox[2]}, {current_bbox[1]+current_bbox[3]}) -> ({adjusted_bbox[0]+adjusted_bbox[2]}, {adjusted_bbox[1]+adjusted_bbox[3]})")
                            print(f"-" * 50)
                        else:
                            self.logger.info(f"✅ 应用偏移量到标注 {thermal_annotation_id}: {current_bbox} -> {adjusted_bbox}")

                            # 打印基本坐标变化信息
                            print(f"\n🔄 热成像标注坐标变化:")
                            print(f"   标注ID: {thermal_annotation_id}")
                            print(f"   标注名称: {thermal_annotation.label}")
                            print(f"   调整前坐标: {current_bbox} (x, y, width, height)")
                            print(f"   调整后坐标: {adjusted_bbox} (x, y, width, height)")
                            print(f"   本次偏移: X={x_offset}px, Y={y_offset}px, Scale={scale_factor:.2f}x")
                            print(f"=" * 60)

                    else:
                        # 如果没有累积记录，创建新记录
                        self.cumulative_offsets[thermal_annotation_id] = (x_offset, y_offset, scale_factor)
                        self.logger.info(f"✅ 应用偏移量到标注 {thermal_annotation_id}: {current_bbox} -> {adjusted_bbox}")
                        self.logger.info(f"📊 新建累积偏移量记录: X={x_offset}px, Y={y_offset}px, Scale={scale_factor:.2f}x")

                        # 打印新记录的坐标变化信息
                        print(f"\n🔄 热成像标注坐标变化 (新记录):")
                        print(f"   标注ID: {thermal_annotation_id}")
                        print(f"   标注名称: {thermal_annotation.label}")
                        print(f"   调整前坐标: {current_bbox} (x, y, width, height)")
                        print(f"   调整后坐标: {adjusted_bbox} (x, y, width, height)")
                        print(f"   本次偏移: X={x_offset}px, Y={y_offset}px, Scale={scale_factor:.2f}x")
                        print(f"   累积偏移: X={x_offset}px, Y={y_offset}px, Scale={scale_factor:.2f}x (首次记录)")
                        print(f"=" * 60)

            if applied_count > 0:
                self.logger.info(f"✅ 偏移量应用完成，共调整 {applied_count} 个热成像标注")

                # 打印所有标注的坐标变化总结
                print(f"\n📊 偏移量应用总结:")
                print(f"   应用的偏移量: X={x_offset}px, Y={y_offset}px, Scale={scale_factor:.2f}x")
                print(f"   影响的标注数量: {applied_count} 个")
                print(f"   详细坐标变化见上方日志")
                print(f"=" * 60)

            return applied_count

        except Exception as e:
            self.logger.error(f"❌ 应用偏移量失败: {e}")
            return 0

    def get_offset_preview(self, x_offset: int, y_offset: int, scale_factor: float = 1.0) -> List[Tuple[Tuple[int, int, int, int], Tuple[int, int, int, int]]]:
        """
        获取偏移量预览信息

        Args:
            x_offset: X方向偏移量
            y_offset: Y方向偏移量
            scale_factor: 缩放比例

        Returns:
            (原始边界框, 调整后边界框) 的列表
        """
        try:
            preview_info = []

            # 遍历所有同步的热成像标注
            for thermal_annotation_id in self.sync_pairs.values():
                thermal_annotation = self.annotation_manager.annotations.get(thermal_annotation_id)
                if thermal_annotation:
                    original_bbox = thermal_annotation.bbox
                    adjusted_bbox = self._apply_offset_to_bbox(original_bbox, x_offset, y_offset, scale_factor)
                    preview_info.append((original_bbox, adjusted_bbox))

            return preview_info

        except Exception as e:
            self.logger.error(f"❌ 获取偏移预览失败: {e}")
            return []

    def get_cumulative_offsets_info(self) -> Dict[str, Dict[str, any]]:
        """
        获取所有热成像标注的累积偏移量信息

        Returns:
            包含每个热成像标注累积偏移量信息的字典
        """
        try:
            offset_info = {}

            for thermal_annotation_id in self.sync_pairs.values():
                thermal_annotation = self.annotation_manager.annotations.get(thermal_annotation_id)
                if thermal_annotation:
                    info = {
                        'annotation_id': thermal_annotation_id,
                        'label': thermal_annotation.label,
                        'current_bbox': thermal_annotation.bbox,
                        'original_bbox': self.original_positions.get(thermal_annotation_id),
                        'cumulative_offset': self.cumulative_offsets.get(thermal_annotation_id, (0, 0, 1.0))
                    }

                    # 计算总偏移量
                    if thermal_annotation_id in self.original_positions and thermal_annotation_id in self.cumulative_offsets:
                        original_bbox = self.original_positions[thermal_annotation_id]
                        current_bbox = thermal_annotation.bbox
                        cumulative_x, cumulative_y, cumulative_scale = self.cumulative_offsets[thermal_annotation_id]

                        # 计算实际位置偏移（当前位置 - 原始位置）
                        actual_offset_x = current_bbox[0] - original_bbox[0]
                        actual_offset_y = current_bbox[1] - original_bbox[1]

                        info.update({
                            'actual_offset_x': actual_offset_x,
                            'actual_offset_y': actual_offset_y,
                            'cumulative_offset_x': cumulative_x,
                            'cumulative_offset_y': cumulative_y,
                            'cumulative_scale': cumulative_scale
                        })

                    offset_info[thermal_annotation_id] = info

            return offset_info

        except Exception as e:
            self.logger.error(f"❌ 获取累积偏移量信息失败: {e}")
            return {}

    def _apply_offset_to_bbox(self, bbox: Tuple[int, int, int, int], x_offset: int, y_offset: int, scale_factor: float) -> Tuple[int, int, int, int]:
        """
        应用偏移量和缩放到边界框

        Args:
            bbox: 原始边界框 (x, y, width, height)
            x_offset: X偏移量
            y_offset: Y偏移量
            scale_factor: 缩放比例

        Returns:
            调整后的边界框
        """
        x, y, w, h = bbox

        # 应用缩放（从中心点缩放）
        center_x = x + w // 2
        center_y = y + h // 2

        new_w = int(w * scale_factor)
        new_h = int(h * scale_factor)

        new_x = center_x - new_w // 2
        new_y = center_y - new_h // 2

        # 应用偏移
        new_x += x_offset
        new_y += y_offset

        # 获取热成像传感器尺寸进行边界检查
        thermal_dims, _ = self.coord_manager.get_sensor_dimensions()

        # 边界检查
        new_x = max(0, min(new_x, thermal_dims[0] - new_w))
        new_y = max(0, min(new_y, thermal_dims[1] - new_h))
        new_w = min(new_w, thermal_dims[0] - new_x)
        new_h = min(new_h, thermal_dims[1] - new_y)

        # 确保尺寸有效
        new_w = max(1, new_w)
        new_h = max(1, new_h)

        return (new_x, new_y, new_w, new_h)
    
    def get_sync_status(self) -> Dict[str, Any]:
        """获取同步状态"""
        all_annotations = self.annotation_manager.annotations

        # 只计算可见的标注（与手动标注区域保持一致）
        visible_light_count = sum(1 for ann in all_annotations.values()
                                if ann.visible and not getattr(ann, 'is_thermal', False))
        thermal_count = sum(1 for ann in all_annotations.values()
                          if ann.visible and getattr(ann, 'is_thermal', False))

        # 总的可见标注数
        total_visible_count = visible_light_count + thermal_count

        sync_count = len(self.sync_pairs)

        return {
            'auto_sync_enabled': self.auto_sync_enabled,
            'visible_annotations': visible_light_count,  # 可见光标注数
            'thermal_annotations': thermal_count,        # 热成像标注数
            'total_visible_annotations': total_visible_count,  # 总可见标注数
            'synced_pairs': sync_count,
            'sync_pairs': dict(self.sync_pairs)
        }
    
    def _convert_visible_bbox_to_thermal(self, visible_bbox: Tuple[int, int, int, int]) -> Optional[Tuple[int, int, int, int]]:
        """
        将可见光边界框转换为热成像边界框

        Args:
            visible_bbox: 可见光边界框 (x, y, width, height) - 传感器坐标

        Returns:
            热成像边界框 (x, y, width, height) - 传感器坐标，失败返回None
        """
        try:
            x, y, w, h = visible_bbox

            # 获取传感器尺寸
            thermal_dims, visible_dims = self.coord_manager.get_sensor_dimensions()

            # 方法1: 尝试使用双光配准转换（如果可用）
            thermal_bbox = self._try_dual_camera_conversion(visible_bbox)
            if thermal_bbox:
                return thermal_bbox

            # 方法2: 使用比例缩放转换（备用方案）
            self.logger.info("使用比例缩放进行坐标转换")
            scale_x = thermal_dims[0] / visible_dims[0]
            scale_y = thermal_dims[1] / visible_dims[1]

            # 转换坐标
            thermal_x = int(x * scale_x)
            thermal_y = int(y * scale_y)
            thermal_w = int(w * scale_x)
            thermal_h = int(h * scale_y)

            # 边界检查
            thermal_x = max(0, min(thermal_x, thermal_dims[0] - 1))
            thermal_y = max(0, min(thermal_y, thermal_dims[1] - 1))
            thermal_w = min(thermal_w, thermal_dims[0] - thermal_x)
            thermal_h = min(thermal_h, thermal_dims[1] - thermal_y)

            # 确保尺寸有效
            if thermal_w <= 0 or thermal_h <= 0:
                return None

            return (thermal_x, thermal_y, thermal_w, thermal_h)

        except Exception as e:
            self.logger.error(f"❌ 坐标转换失败: {e}")
            return None

    def _try_dual_camera_conversion(self, visible_bbox: Tuple[int, int, int, int]) -> Optional[Tuple[int, int, int, int]]:
        """
        尝试使用双光配准进行精确坐标转换

        Args:
            visible_bbox: 可见光边界框

        Returns:
            转换后的热成像边界框，失败返回None
        """
        try:
            # 检查是否有双光配准模块可用
            try:
                from tools_complete_backup_20250728_134618.coordinate_converter import DualCameraProjector, BoundingBox

                # 创建投影器实例（这里需要摄像头配置）
                from config.camera_config import CAMERA_CONFIG
                projector = DualCameraProjector(
                    camera_ip=CAMERA_CONFIG.ip,
                    username=CAMERA_CONFIG.username,
                    password=CAMERA_CONFIG.password
                )

                # 获取传感器尺寸
                thermal_dims, visible_dims = self.coord_manager.get_sensor_dimensions()

                # 创建可见光边界框对象
                x, y, w, h = visible_bbox
                visible_bbox_obj = BoundingBox(x=x, y=y, width=w, height=h, label="sync_annotation")

                # 执行转换
                thermal_bboxes = projector.project_visible_to_thermal(
                    [visible_bbox_obj], thermal_dims, visible_dims
                )

                if thermal_bboxes:
                    thermal_bbox_obj = thermal_bboxes[0]
                    return (thermal_bbox_obj.x, thermal_bbox_obj.y,
                           thermal_bbox_obj.width, thermal_bbox_obj.height)

                # 清理资源
                projector.cleanup()

            except ImportError:
                self.logger.debug("双光配准模块不可用，使用备用转换方法")
            except Exception as e:
                self.logger.warning(f"双光配准转换失败: {e}")

            return None

        except Exception as e:
            self.logger.error(f"双光配准转换异常: {e}")
            return None
    
    def _adjust_color_for_thermal(self, visible_color: Tuple[int, int, int]) -> Tuple[int, int, int]:
        """调整颜色用于热成像标注"""
        r, g, b = visible_color
        offset_r, offset_g, offset_b = self.sync_color_offset
        
        # 应用颜色偏移，确保在有效范围内
        thermal_r = max(0, min(255, r + offset_r))
        thermal_g = max(0, min(255, g + offset_g))
        thermal_b = max(0, min(255, b + offset_b))
        
        return (thermal_r, thermal_g, thermal_b)
    
    def on_annotation_created(self, annotation_id: str, is_thermal: bool = False):
        """标注创建事件处理"""
        if self.auto_sync_enabled and not is_thermal:
            # 自动同步可见光标注到热成像
            result = self.sync_visible_to_thermal(annotation_id)
            if result.success:
                print(f"🔄 自动同步标注: {annotation_id} -> {result.thermal_annotation_id}")
            else:
                print(f"⚠️ 自动同步失败: {result.error_message}")
    
    def on_annotation_deleted(self, annotation_id: str):
        """标注删除事件处理"""
        # 如果删除的是可见光标注，同时删除对应的热成像标注
        if annotation_id in self.sync_pairs:
            self.remove_sync_pair(annotation_id)
        
        # 如果删除的是热成像标注，移除同步记录
        elif annotation_id in self.reverse_sync_pairs:
            visible_id = self.reverse_sync_pairs[annotation_id]
            if visible_id in self.sync_pairs:
                del self.sync_pairs[visible_id]
            del self.reverse_sync_pairs[annotation_id]
