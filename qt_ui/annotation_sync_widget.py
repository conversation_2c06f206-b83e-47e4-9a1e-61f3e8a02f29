#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
标注同步控制界面
提供可见光标注同步到热成像的用户界面
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QPushButton, QCheckBox, QGroupBox, QTextEdit,
                            QProgressBar, QMessageBox, QSpinBox, QSlider,
                            QGridLayout, QFrame)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QFont

from utils.logger import get_logger
from qt_ui.annotation_sync_manager import AnnotationSyncManager


class AnnotationSyncWidget(QWidget):
    """标注同步控制界面"""
    
    # 信号
    sync_status_changed = pyqtSignal(dict)  # 同步状态变化信号
    
    def __init__(self, sync_manager: AnnotationSyncManager):
        super().__init__()
        self.logger = get_logger("AnnotationSyncWidget")
        self.sync_manager = sync_manager
        
        self.init_ui()
        self.setup_connections()
        self.update_status()
        
        # 定时更新状态
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_status)
        self.status_timer.start(2000)  # 每2秒更新一次
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout()
        
        # 标题
        title_label = QLabel("🔄 标注同步")
        title_font = QFont()
        title_font.setBold(True)
        title_font.setPointSize(12)
        title_label.setFont(title_font)
        layout.addWidget(title_label)
        
        # 自动同步控制
        auto_sync_group = QGroupBox("自动同步设置")
        auto_sync_layout = QVBoxLayout()
        
        self.auto_sync_checkbox = QCheckBox("启用自动同步")
        self.auto_sync_checkbox.setChecked(True)
        self.auto_sync_checkbox.setToolTip("创建可见光标注时自动同步到热成像")
        auto_sync_layout.addWidget(self.auto_sync_checkbox)
        
        auto_sync_help = QLabel("💡 启用后，在可见光画面创建标注时会自动在热成像画面创建对应标注")
        auto_sync_help.setWordWrap(True)
        auto_sync_help.setStyleSheet("color: #666; font-size: 10px;")
        auto_sync_layout.addWidget(auto_sync_help)
        
        auto_sync_group.setLayout(auto_sync_layout)
        layout.addWidget(auto_sync_group)
        
        # 手动同步控制
        manual_sync_group = QGroupBox("手动同步操作")
        manual_sync_layout = QVBoxLayout()

        # 同步所有按钮
        self.sync_all_btn = QPushButton("🔄 同步所有可见光标注")
        self.sync_all_btn.setToolTip("将所有可见光标注同步到热成像画面")
        manual_sync_layout.addWidget(self.sync_all_btn)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        manual_sync_layout.addWidget(self.progress_bar)

        manual_sync_group.setLayout(manual_sync_layout)
        layout.addWidget(manual_sync_group)

        # 位置调整控制
        position_adjust_group = QGroupBox("位置调整")
        position_adjust_layout = QVBoxLayout()

        # 说明文字
        adjust_help = QLabel("💡 如果同步的标注位置不准确，可以手动调整偏移量")
        adjust_help.setWordWrap(True)
        adjust_help.setStyleSheet("color: #666; font-size: 10px;")
        position_adjust_layout.addWidget(adjust_help)

        # 偏移量控制
        offset_grid = QGridLayout()

        # X偏移
        offset_grid.addWidget(QLabel("X偏移:"), 0, 0)
        self.x_offset_spinbox = QSpinBox()
        self.x_offset_spinbox.setRange(-200, 200)
        self.x_offset_spinbox.setValue(0)
        self.x_offset_spinbox.setSuffix(" px")
        self.x_offset_spinbox.setToolTip("水平方向偏移量（像素）")
        offset_grid.addWidget(self.x_offset_spinbox, 0, 1)

        # Y偏移
        offset_grid.addWidget(QLabel("Y偏移:"), 1, 0)
        self.y_offset_spinbox = QSpinBox()
        self.y_offset_spinbox.setRange(-200, 200)
        self.y_offset_spinbox.setValue(0)
        self.y_offset_spinbox.setSuffix(" px")
        self.y_offset_spinbox.setToolTip("垂直方向偏移量（像素）")
        offset_grid.addWidget(self.y_offset_spinbox, 1, 1)

        # 缩放调整
        offset_grid.addWidget(QLabel("缩放:"), 2, 0)
        self.scale_spinbox = QSpinBox()
        self.scale_spinbox.setRange(50, 200)
        self.scale_spinbox.setValue(100)
        self.scale_spinbox.setSuffix(" %")
        self.scale_spinbox.setToolTip("标注框大小缩放比例")
        offset_grid.addWidget(self.scale_spinbox, 2, 1)

        offset_widget = QWidget()
        offset_widget.setLayout(offset_grid)
        position_adjust_layout.addWidget(offset_widget)

        # 调整按钮
        adjust_buttons_layout = QHBoxLayout()

        self.apply_offset_btn = QPushButton("📐 应用偏移")
        self.apply_offset_btn.setToolTip("将偏移量应用到所有已同步的热成像标注")
        adjust_buttons_layout.addWidget(self.apply_offset_btn)

        self.reset_offset_btn = QPushButton("🔄 重置")
        self.reset_offset_btn.setToolTip("重置偏移量为0")
        adjust_buttons_layout.addWidget(self.reset_offset_btn)

        self.preview_btn = QPushButton("👁️ 预览")
        self.preview_btn.setToolTip("预览调整效果（不保存）")
        adjust_buttons_layout.addWidget(self.preview_btn)

        position_adjust_layout.addLayout(adjust_buttons_layout)

        position_adjust_group.setLayout(position_adjust_layout)
        layout.addWidget(position_adjust_group)
        
        # 状态显示
        status_group = QGroupBox("同步状态")
        status_layout = QVBoxLayout()
        
        # 状态标签
        self.status_labels = {}
        status_items = [
            ("visible_count", "可见光标注"),
            ("thermal_count", "热成像标注"),
            ("synced_count", "已同步对数"),
            ("auto_sync_status", "自动同步状态")
        ]
        
        for key, label_text in status_items:
            label = QLabel(f"{label_text}: --")
            self.status_labels[key] = label
            status_layout.addWidget(label)
        
        status_group.setLayout(status_layout)
        layout.addWidget(status_group)
        
        # 操作日志
        log_group = QGroupBox("操作日志")
        log_layout = QVBoxLayout()
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(100)
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)
        
        # 清空日志按钮
        clear_log_btn = QPushButton("清空日志")
        clear_log_btn.clicked.connect(self.clear_log)
        log_layout.addWidget(clear_log_btn)
        
        log_group.setLayout(log_layout)
        layout.addWidget(log_group)
        
        # 弹簧
        layout.addStretch()
        
        self.setLayout(layout)
    
    def setup_connections(self):
        """设置信号连接"""
        self.auto_sync_checkbox.toggled.connect(self.on_auto_sync_toggled)
        self.sync_all_btn.clicked.connect(self.on_sync_all_clicked)

        # 位置调整信号连接
        self.apply_offset_btn.clicked.connect(self.on_apply_offset_clicked)
        self.reset_offset_btn.clicked.connect(self.on_reset_offset_clicked)
        self.preview_btn.clicked.connect(self.on_preview_clicked)

        # 偏移量变化时的实时预览（可选）
        self.x_offset_spinbox.valueChanged.connect(self.on_offset_changed)
        self.y_offset_spinbox.valueChanged.connect(self.on_offset_changed)
        self.scale_spinbox.valueChanged.connect(self.on_offset_changed)
    
    def on_auto_sync_toggled(self, checked: bool):
        """自动同步开关切换"""
        self.sync_manager.enable_auto_sync(checked)
        status = "启用" if checked else "禁用"
        self.add_log(f"自动同步已{status}")
        self.update_status()
    
    def on_sync_all_clicked(self):
        """同步所有标注按钮点击"""
        try:
            self.sync_all_btn.setEnabled(False)
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)
            
            # 执行同步
            results = self.sync_manager.sync_all_visible_annotations()
            
            # 统计结果
            success_count = sum(1 for r in results if r.success)
            total_count = len(results)
            
            self.progress_bar.setValue(100)
            
            # 显示结果
            if total_count == 0:
                message = "没有可见光标注需要同步"
                self.add_log(message)
                QMessageBox.information(self, "同步完成", message)
            else:
                message = f"同步完成: {success_count}/{total_count} 成功"
                self.add_log(message)
                
                if success_count == total_count:
                    QMessageBox.information(self, "同步完成", message)
                else:
                    # 显示失败的详情
                    failed_results = [r for r in results if not r.success]
                    error_details = "\n".join([f"- {r.error_message}" for r in failed_results[:5]])
                    if len(failed_results) > 5:
                        error_details += f"\n... 还有 {len(failed_results) - 5} 个错误"
                    
                    QMessageBox.warning(self, "同步完成（部分失败)", 
                                      f"{message}\n\n失败原因:\n{error_details}")
            
            self.update_status()
            
        except Exception as e:
            self.logger.error(f"同步操作失败: {e}")
            self.add_log(f"❌ 同步操作失败: {e}")
            QMessageBox.critical(self, "错误", f"同步操作失败:\n{e}")
        
        finally:
            self.sync_all_btn.setEnabled(True)
            self.progress_bar.setVisible(False)
    
    def update_status(self):
        """更新状态显示"""
        try:
            status = self.sync_manager.get_sync_status()

            # 更新状态标签（与手动标注区域保持一致的计数逻辑）
            visible_count = status['visible_annotations']
            thermal_count = status['thermal_annotations']
            total_count = status['total_visible_annotations']
            synced_pairs = status['synced_pairs']

            self.status_labels["visible_count"].setText(
                f"可见光标注: {visible_count}")
            self.status_labels["thermal_count"].setText(
                f"热成像标注: {thermal_count}")
            self.status_labels["synced_count"].setText(
                f"已同步对数: {synced_pairs}")

            auto_status = "启用" if status['auto_sync_enabled'] else "禁用"
            self.status_labels["auto_sync_status"].setText(f"自动同步状态: {auto_status}")

            # 添加总计显示（可选）
            if hasattr(self, 'total_count_label'):
                self.total_count_label.setText(f"总标注数: {total_count}")

            # 发射状态变化信号
            self.sync_status_changed.emit(status)

        except Exception as e:
            self.logger.error(f"更新状态失败: {e}")
            self.add_log(f"❌ 状态更新失败: {e}")
    
    def add_log(self, message: str):
        """添加日志"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        self.log_text.append(log_entry)
        
        # 自动滚动到底部
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())
    
    def clear_log(self):
        """清空日志"""
        self.log_text.clear()
        self.add_log("日志已清空")
    
    def on_annotation_created(self, annotation_id: str, is_thermal: bool = False):
        """标注创建事件处理"""
        try:
            self.sync_manager.on_annotation_created(annotation_id, is_thermal)
            if not is_thermal and self.sync_manager.auto_sync_enabled:
                self.add_log(f"✅ 自动同步标注: {annotation_id}")
            self.update_status()
        except Exception as e:
            self.logger.error(f"标注创建事件处理失败: {e}")
            self.add_log(f"❌ 标注创建事件处理失败: {e}")
    
    def on_annotation_deleted(self, annotation_id: str):
        """标注删除事件处理"""
        try:
            self.sync_manager.on_annotation_deleted(annotation_id)
            self.add_log(f"🗑️ 删除标注: {annotation_id}")
            self.update_status()
        except Exception as e:
            self.logger.error(f"标注删除事件处理失败: {e}")
            self.add_log(f"❌ 标注删除事件处理失败: {e}")
    
    def on_apply_offset_clicked(self):
        """应用偏移量按钮点击"""
        try:
            x_offset = self.x_offset_spinbox.value()
            y_offset = self.y_offset_spinbox.value()
            scale_factor = self.scale_spinbox.value() / 100.0

            # 应用偏移量到所有已同步的热成像标注
            applied_count = self.sync_manager.apply_offset_to_thermal_annotations(
                x_offset, y_offset, scale_factor
            )

            if applied_count > 0:
                self.add_log(f"✅ 已应用偏移量到 {applied_count} 个热成像标注")
                QMessageBox.information(self, "偏移应用成功",
                                      f"已将偏移量应用到 {applied_count} 个热成像标注")
            else:
                self.add_log("⚠️ 没有找到需要调整的热成像标注")
                QMessageBox.information(self, "提示", "没有找到需要调整的热成像标注")

            self.update_status()

        except Exception as e:
            self.logger.error(f"应用偏移量失败: {e}")
            self.add_log(f"❌ 应用偏移量失败: {e}")
            QMessageBox.critical(self, "错误", f"应用偏移量失败:\n{e}")

    def on_reset_offset_clicked(self):
        """重置偏移量按钮点击"""
        self.x_offset_spinbox.setValue(0)
        self.y_offset_spinbox.setValue(0)
        self.scale_spinbox.setValue(100)
        self.add_log("🔄 偏移量已重置")

    def on_preview_clicked(self):
        """预览按钮点击"""
        try:
            x_offset = self.x_offset_spinbox.value()
            y_offset = self.y_offset_spinbox.value()
            scale_factor = self.scale_spinbox.value() / 100.0

            # 获取预览信息
            preview_info = self.sync_manager.get_offset_preview(x_offset, y_offset, scale_factor)

            if preview_info:
                message = f"预览偏移效果:\n\n"
                message += f"X偏移: {x_offset} px\n"
                message += f"Y偏移: {y_offset} px\n"
                message += f"缩放: {scale_factor:.2f}\n\n"
                message += f"将影响 {len(preview_info)} 个热成像标注\n\n"

                # 显示前几个标注的变化
                for i, (old_bbox, new_bbox) in enumerate(preview_info[:3]):
                    message += f"标注 {i+1}: {old_bbox} → {new_bbox}\n"

                if len(preview_info) > 3:
                    message += f"... 还有 {len(preview_info) - 3} 个标注"

                QMessageBox.information(self, "偏移预览", message)
            else:
                QMessageBox.information(self, "预览", "没有找到需要调整的热成像标注")

        except Exception as e:
            self.logger.error(f"预览失败: {e}")
            QMessageBox.critical(self, "错误", f"预览失败:\n{e}")

    def on_offset_changed(self):
        """偏移量变化处理（实时预览提示）"""
        x_offset = self.x_offset_spinbox.value()
        y_offset = self.y_offset_spinbox.value()
        scale_factor = self.scale_spinbox.value() / 100.0

        # 更新按钮提示
        if x_offset != 0 or y_offset != 0 or scale_factor != 1.0:
            self.apply_offset_btn.setText(f"📐 应用偏移 ({x_offset},{y_offset},{scale_factor:.2f})")
        else:
            self.apply_offset_btn.setText("📐 应用偏移")

    def get_sync_manager(self) -> AnnotationSyncManager:
        """获取同步管理器"""
        return self.sync_manager
