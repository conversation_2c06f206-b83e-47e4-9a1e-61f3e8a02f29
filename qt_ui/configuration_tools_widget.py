#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置与工具卡片组件
使用手风琴组件组织手动标注、视频处理、系统设置等功能
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                             QPushButton, QFrame, QGroupBox, QFormLayout,
                             QSpinBox, QDoubleSpinBox, QCheckBox, QComboBox,
                             QLineEdit, QSlider, QTextEdit)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont

from qt_ui.accordion_widget import AccordionWidget
from utils.logger import get_logger
from qt_ui.font_config import (get_groupbox_style, get_button_style, get_input_style,
                              get_label_style, PRESET_STYLES)


class SystemSettingsWidget(QWidget):
    """系统设置组件"""
    
    # 信号
    settings_changed = pyqtSignal(dict)  # 设置变化信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger("SystemSettingsWidget")
        
        self.init_ui()
        
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(10)
        
        # 检测参数设置
        self.create_detection_settings(layout)
        
        # 报警阈值设置
        self.create_alarm_settings(layout)
        
        # 显示设置
        self.create_display_settings(layout)
        
        # 应用按钮
        self.create_action_buttons(layout)
        
    def create_detection_settings(self, layout):
        """创建检测参数设置"""
        group = QGroupBox("检测参数")
        group.setStyleSheet(get_groupbox_style('text_large'))
        
        form_layout = QFormLayout(group)
        
        # 温度阈值
        self.temp_threshold_spin = QDoubleSpinBox()
        self.temp_threshold_spin.setRange(0.0, 200.0)
        self.temp_threshold_spin.setValue(50.0)
        self.temp_threshold_spin.setSuffix("°C")
        self.temp_threshold_spin.setStyleSheet(get_input_style('input_medium'))
        form_layout.addRow("温度阈值:", self.temp_threshold_spin)
        
        # 面积阈值
        self.area_threshold_spin = QSpinBox()
        self.area_threshold_spin.setRange(1, 10000)
        self.area_threshold_spin.setValue(100)
        self.area_threshold_spin.setSuffix(" 像素")
        self.area_threshold_spin.setStyleSheet(get_input_style('input_medium'))
        form_layout.addRow("最小面积:", self.area_threshold_spin)
        
        # 检测灵敏度
        self.sensitivity_slider = QSlider(Qt.Horizontal)
        self.sensitivity_slider.setRange(1, 10)
        self.sensitivity_slider.setValue(5)
        self.sensitivity_slider.setStyleSheet("""
            QSlider::groove:horizontal {
                border: 1px solid #505050;
                height: 8px;
                background: #323232;
                border-radius: 4px;
            }
            QSlider::handle:horizontal {
                background: #00ffff;
                border: 1px solid #00aaaa;
                width: 18px;
                margin: -2px 0;
                border-radius: 9px;
            }
        """)
        form_layout.addRow("检测灵敏度:", self.sensitivity_slider)
        
        layout.addWidget(group)
        
    def create_alarm_settings(self, layout):
        """创建报警阈值设置"""
        group = QGroupBox("报警设置")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 1px solid #505050;
                border-radius: 3px;
                margin-top: 10px;
                padding-top: 5px;
                color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        
        form_layout = QFormLayout(group)
        
        # 高温报警
        self.high_temp_alarm_spin = QDoubleSpinBox()
        self.high_temp_alarm_spin.setRange(0.0, 500.0)
        self.high_temp_alarm_spin.setValue(80.0)
        self.high_temp_alarm_spin.setSuffix("°C")
        self.high_temp_alarm_spin.setStyleSheet("color: white; background-color: #404040;")
        form_layout.addRow("高温报警:", self.high_temp_alarm_spin)
        
        # 面积增长率报警
        self.area_growth_alarm_spin = QDoubleSpinBox()
        self.area_growth_alarm_spin.setRange(0.0, 100.0)
        self.area_growth_alarm_spin.setValue(10.0)
        self.area_growth_alarm_spin.setSuffix("%/秒")
        self.area_growth_alarm_spin.setStyleSheet("color: white; background-color: #404040;")
        form_layout.addRow("面积增长报警:", self.area_growth_alarm_spin)
        
        # 启用声音报警
        self.sound_alarm_check = QCheckBox("启用声音报警")
        self.sound_alarm_check.setChecked(True)
        self.sound_alarm_check.setStyleSheet("color: white;")
        form_layout.addRow("", self.sound_alarm_check)
        
        layout.addWidget(group)
        
    def create_display_settings(self, layout):
        """创建显示设置"""
        group = QGroupBox("显示设置")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 1px solid #505050;
                border-radius: 3px;
                margin-top: 10px;
                padding-top: 5px;
                color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        
        form_layout = QFormLayout(group)
        
        # 热源显示数量
        self.max_sources_spin = QSpinBox()
        self.max_sources_spin.setRange(1, 50)
        self.max_sources_spin.setValue(10)
        self.max_sources_spin.setStyleSheet("color: white; background-color: #404040;")
        form_layout.addRow("最大显示热源:", self.max_sources_spin)
        
        # 更新频率
        self.update_rate_combo = QComboBox()
        self.update_rate_combo.addItems(["实时", "1秒", "2秒", "5秒"])
        self.update_rate_combo.setCurrentText("实时")
        self.update_rate_combo.setStyleSheet("color: white; background-color: #404040;")
        form_layout.addRow("更新频率:", self.update_rate_combo)
        
        # 显示调试信息
        self.debug_info_check = QCheckBox("显示调试信息")
        self.debug_info_check.setChecked(False)
        self.debug_info_check.setStyleSheet("color: white;")
        form_layout.addRow("", self.debug_info_check)
        
        layout.addWidget(group)
        
    def create_action_buttons(self, layout):
        """创建操作按钮"""
        button_layout = QHBoxLayout()
        
        # 应用设置按钮
        self.apply_btn = QPushButton("应用设置")
        self.apply_btn.setStyleSheet(PRESET_STYLES['primary_button'])
        self.apply_btn.clicked.connect(self.apply_settings)
        button_layout.addWidget(self.apply_btn)
        
        # 重置按钮
        self.reset_btn = QPushButton("重置默认")
        self.reset_btn.setStyleSheet(PRESET_STYLES['secondary_button'])
        self.reset_btn.clicked.connect(self.reset_settings)
        button_layout.addWidget(self.reset_btn)
        
        layout.addLayout(button_layout)
        
    def apply_settings(self):
        """应用设置"""
        settings = {
            'temp_threshold': self.temp_threshold_spin.value(),
            'area_threshold': self.area_threshold_spin.value(),
            'sensitivity': self.sensitivity_slider.value(),
            'high_temp_alarm': self.high_temp_alarm_spin.value(),
            'area_growth_alarm': self.area_growth_alarm_spin.value(),
            'sound_alarm': self.sound_alarm_check.isChecked(),
            'max_sources': self.max_sources_spin.value(),
            'update_rate': self.update_rate_combo.currentText(),
            'debug_info': self.debug_info_check.isChecked()
        }
        
        self.settings_changed.emit(settings)
        self.logger.info(f"应用系统设置: {len(settings)} 项")
        
    def reset_settings(self):
        """重置为默认设置"""
        self.temp_threshold_spin.setValue(50.0)
        self.area_threshold_spin.setValue(100)
        self.sensitivity_slider.setValue(5)
        self.high_temp_alarm_spin.setValue(80.0)
        self.area_growth_alarm_spin.setValue(10.0)
        self.sound_alarm_check.setChecked(True)
        self.max_sources_spin.setValue(10)
        self.update_rate_combo.setCurrentText("实时")
        self.debug_info_check.setChecked(False)
        
        self.logger.info("重置系统设置为默认值")


class ConfigurationToolsWidget(QWidget):
    """配置与工具主卡片组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger("ConfigurationToolsWidget")
        
        # 存储原有组件的引用
        self.manual_annotation_widget = None
        self.video_frame_extractor = None
        self.training_dataset_manager = None
        self.annotation_sync_widget = None
        
        self.init_ui()
        
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # 创建卡片标题
        self.create_card_header(layout)
        
        # 创建手风琴组件
        self.accordion = AccordionWidget(self)
        self.accordion.set_allow_multiple_expanded(True)  # 允许多个区块同时展开
        layout.addWidget(self.accordion)
        
        # 初始化手风琴区块
        self.init_accordion_sections()
        
    def create_card_header(self, layout):
        """创建卡片标题"""
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background-color: #404040;
                border: 1px solid #606060;
                border-radius: 5px 5px 0 0;
                padding: 5px;
            }
        """)
        header_frame.setFixedHeight(40)
        
        header_layout = QHBoxLayout(header_frame)
        header_layout.setContentsMargins(10, 5, 10, 5)
        
        
    def init_accordion_sections(self):
        """初始化手风琴区块"""
        # 手动标注区块
        self.manual_annotation_section = self.accordion.add_section(
            "手动标注 (Manual Annotation)", 
            None,  # 稍后设置内容
            False  # 默认折叠
        )
        
        # 视频处理区块
        self.video_processing_section = self.accordion.add_section(
            "视频处理 (Video Processing)", 
            None,  # 稍后设置内容
            False  # 默认折叠
        )
        
        # 标注同步区块
        self.annotation_sync_section = self.accordion.add_section(
            "标注同步 (Annotation Sync)",
            None,  # 稍后设置内容
            False  # 默认折叠
        )

        # 系统设置区块
        self.system_settings_widget = SystemSettingsWidget(self)
        self.system_settings_section = self.accordion.add_section(
            "系统设置 (System Settings)",
            self.system_settings_widget,
            False  # 默认折叠
        )
        
    def set_manual_annotation_widget(self, widget):
        """设置手动标注组件"""
        self.manual_annotation_widget = widget
        self.manual_annotation_section.set_content_widget(widget)
        
    def set_video_processing_widgets(self, video_extractor, dataset_manager):
        """设置视频处理组件"""
        self.video_frame_extractor = video_extractor
        self.training_dataset_manager = dataset_manager
        
        # 创建视频处理容器
        video_container = QWidget()
        video_layout = QVBoxLayout(video_container)
        video_layout.setContentsMargins(0, 0, 0, 0)
        video_layout.setSpacing(5)
        
        video_layout.addWidget(video_extractor)
        video_layout.addWidget(dataset_manager)
        
        self.video_processing_section.set_content_widget(video_container)

    def add_annotation_sync_widget(self, widget):
        """添加标注同步组件"""
        self.annotation_sync_widget = widget
        self.annotation_sync_section.set_content_widget(widget)

    def toggle_all_sections(self):
        """切换所有区块的展开/折叠状态"""
        # 检查当前状态
        expanded_count = sum(1 for section in self.accordion.sections if section.is_expanded)
        total_count = len(self.accordion.sections)
        
        if expanded_count == 0:
            # 全部折叠，则展开所有
            self.accordion.expand_all()
            self.toggle_all_btn.setText("折叠所有")
        elif expanded_count == total_count:
            # 全部展开，则折叠所有
            self.accordion.collapse_all()
            self.toggle_all_btn.setText("展开所有")
        else:
            # 部分展开，则展开所有
            self.accordion.expand_all()
            self.toggle_all_btn.setText("折叠所有")
            
        self.logger.debug(f"切换所有区块状态: {expanded_count}/{total_count} -> {'展开' if expanded_count < total_count else '折叠'}")
