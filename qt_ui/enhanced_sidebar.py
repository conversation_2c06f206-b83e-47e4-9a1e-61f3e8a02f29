#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的Qt侧边栏模块
支持多模式显示（概览/详细/专家）和分层次的监控指标显示
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                             QScrollArea, QFrame, QPushButton, QTextEdit,
                             QSizePolicy, QSpacerItem, QButtonGroup, QGroupBox,
                             QGridLayout, QTabWidget)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QMetaObject, Q_ARG
from PyQt5.QtGui import QFont, QPalette, QColor

from utils.logger import get_logger
from datetime import datetime
from typing import Dict, List, Optional, Any
from qt_ui.font_config import get_button_style, PRESET_STYLES


class DisplayMode:
    """显示模式常量"""
    OVERVIEW = "overview"      # 概览模式
    DETAILED = "detailed"      # 详细模式  
    EXPERT = "expert"          # 专家模式


class GlobalMetricsWidget(QWidget):
    """全局指标显示组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger("GlobalMetricsWidget")
        self.init_ui()
        
        # 全局指标数据
        self.global_metrics = {
            'total_heat_sources': 0,
            'total_heat_sources_change_rate': None,
            'total_heat_area': 0,
            'total_heat_area_change_rate': None,
            'avg_temperature_30days': None
        }
    
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(3)
        
        # 标题
        title_label = QLabel("全局指标")
        title_label.setStyleSheet("font-size: 13px; font-weight: bold; color: #ffff00; padding: 2px;")
        layout.addWidget(title_label)
        
        # 指标网格
        self.metrics_grid = QGridLayout()
        self.metrics_grid.setSpacing(2)
        
        # 创建指标标签
        self.create_metric_labels()
        
        grid_widget = QWidget()
        grid_widget.setLayout(self.metrics_grid)
        layout.addWidget(grid_widget)
        
        # 设置样式
        self.setStyleSheet("""
            QWidget {
                background-color: #323232;
                border-radius: 5px;
                margin: 2px;
                color: white;
                font-size: 11px;
            }
        """)
    
    def create_metric_labels(self):
        """创建指标标签"""
        # 热源总数
        self.total_sources_label = QLabel("热源总数: 0")
        self.total_sources_change_label = QLabel("变化率: --")
        
        # 总面积
        self.total_area_label = QLabel("总面积: 0 像素")
        self.total_area_change_label = QLabel("变化率: --")
        
        # 30天平均温度
        self.avg_temp_30days_label = QLabel("30天平均温度: --")

        # 检测状态指标
        self.human_count_label = QLabel("0")
        self.smoke_info_label = QLabel("0 (0.0/s)")
        self.fire_info_label = QLabel("0 (0.0/s, 0.0/s)")

        # 添加到网格
        self.metrics_grid.addWidget(QLabel("热源数量:"), 0, 0)
        self.metrics_grid.addWidget(self.total_sources_label, 0, 1)
        self.metrics_grid.addWidget(self.total_sources_change_label, 0, 2)

        self.metrics_grid.addWidget(QLabel("总面积:"), 1, 0)
        self.metrics_grid.addWidget(self.total_area_label, 1, 1)
        self.metrics_grid.addWidget(self.total_area_change_label, 1, 2)

        self.metrics_grid.addWidget(QLabel("30天平均:"), 2, 0)
        self.metrics_grid.addWidget(self.avg_temp_30days_label, 2, 1, 1, 2)

        # 添加分隔线
        separator = QLabel("─" * 25)
        separator.setStyleSheet("color: #666666; font-size: 10px;")
        self.metrics_grid.addWidget(separator, 3, 0, 1, 3)

        # 添加检测状态指标
        self.metrics_grid.addWidget(QLabel("人体个数:"), 4, 0)
        self.metrics_grid.addWidget(self.human_count_label, 4, 1, 1, 2)

        self.metrics_grid.addWidget(QLabel("烟雾:"), 5, 0)
        self.metrics_grid.addWidget(self.smoke_info_label, 5, 1, 1, 2)

        self.metrics_grid.addWidget(QLabel("火焰:"), 6, 0)
        self.metrics_grid.addWidget(self.fire_info_label, 6, 1, 1, 2)
        
        # 设置样式
        for i in range(7):  # 减少到7行
            for j in range(3):
                item = self.metrics_grid.itemAtPosition(i, j)
                if item and item.widget():
                    widget = item.widget()
                    if i == 3:  # 分隔线行
                        continue
                    elif j == 0:  # 标签列
                        widget.setStyleSheet("color: #aaaaaa; font-weight: bold; font-size: 10px;")
                    elif j == 2:  # 变化率列
                        widget.setStyleSheet("color: #00ffff; font-size: 10px;")
                    else:  # 数值列
                        if i >= 4:  # 检测状态行
                            widget.setStyleSheet("color: #00ffff; font-size: 10px;")
                        else:
                            widget.setStyleSheet("color: #ffffff; font-size: 10px;")


    
    def update_metrics(self, metrics: Dict):
        """更新全局指标显示"""
        self.global_metrics.update(metrics)
        
        # 更新热源总数
        total_sources = self.global_metrics.get('total_heat_sources', 0)
        self.total_sources_label.setText(f"{total_sources}")
        
        # 更新热源数量变化率
        sources_change = self.global_metrics.get('total_heat_sources_change_rate')
        if sources_change is not None:
            self.total_sources_change_label.setText(f"{sources_change:+.2f}/秒")
            # 根据变化率设置颜色
            color = "#ff6666" if sources_change > 0 else "#66ff66" if sources_change < 0 else "#00ffff"
            self.total_sources_change_label.setStyleSheet(f"color: {color}; font-size: 10px;")
        else:
            self.total_sources_change_label.setText("--")
        
        # 更新总面积
        total_area = self.global_metrics.get('total_heat_area', 0)
        self.total_area_label.setText(f"{total_area} 像素")
        
        # 更新面积变化率
        area_change = self.global_metrics.get('total_heat_area_change_rate')
        if area_change is not None:
            self.total_area_change_label.setText(f"{area_change:+.1f}/秒")
            # 根据变化率设置颜色
            color = "#ff6666" if area_change > 0 else "#66ff66" if area_change < 0 else "#00ffff"
            self.total_area_change_label.setStyleSheet(f"color: {color}; font-size: 10px;")
        else:
            self.total_area_change_label.setText("--")
        
        # 更新30天平均温度
        avg_temp = self.global_metrics.get('avg_temperature_30days')
        if avg_temp is not None:
            self.avg_temp_30days_label.setText(f"{avg_temp:.1f}°C")
        else:
            self.avg_temp_30days_label.setText("--")

    def update_detection_status(self, status_info: Dict):
        """更新检测状态指标"""


        # 更新人体个数
        human_count = status_info.get('human_count', 0)
        self.human_count_label.setText(f"{human_count}")

        # 更新烟雾信息
        smoke_count = status_info.get('smoke_count', 0)
        smoke_area_rate = status_info.get('smoke_area_change_rate', 0.0)
        self.smoke_info_label.setText(f"{smoke_count} (面积:{smoke_area_rate:.1f}px²/s)")

        # 更新火焰信息
        fire_count = status_info.get('fire_count', 0)
        fire_area_rate = status_info.get('fire_area_change_rate', 0.0)
        fire_count_rate = status_info.get('fire_count_change_rate', 0.0)
        self.fire_info_label.setText(f"{fire_count} (面积:{fire_area_rate:.1f}px²/s, 数量:{fire_count_rate:.1f}%/s)")


class HeatSourceDetailWidget(QWidget):
    """热源详情显示组件"""
    
    def __init__(self, display_mode: str = DisplayMode.OVERVIEW, parent=None):
        super().__init__(parent)
        self.logger = get_logger("HeatSourceDetailWidget")
        self.display_mode = display_mode
        self.heat_sources = []
        # 移除滚动相关的变量，改用QTextEdit的内置滚动
        # self.scroll_offset = 0
        # self.max_display_count = 20

        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        # 设置热源详情组件的尺寸策略，允许扩展
        from PyQt5.QtWidgets import QSizePolicy
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)
        
        # 标题区域（移除手动滚动按钮，使用QTextEdit内置滚动）
        header_layout = QHBoxLayout()

        title_label = QLabel("热源详情")
        title_label.setStyleSheet("font-size: 13px; font-weight: bold; color: #ffff00;")
        header_layout.addWidget(title_label)

        header_layout.addStretch()

        # 移除手动滚动按钮，改用QTextEdit的内置滚动条
        # self.scroll_up_btn = QPushButton("↑")
        # self.scroll_down_btn = QPushButton("↓")

        layout.addLayout(header_layout)
        
        # 热源详情文本区域 - 启用内置滚动条显示所有热源
        self.detail_text = QTextEdit()
        self.detail_text.setReadOnly(True)
        # 降低最小高度，让组件更灵活地适应可用空间
        self.detail_text.setMinimumHeight(200)
        # 移除最大高度限制，让它能够充分利用可用空间
        # self.detail_text.setMaximumHeight(600)  # 注释掉最大高度限制
        # 设置尺寸策略，允许垂直扩展
        from PyQt5.QtWidgets import QSizePolicy
        self.detail_text.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        # 确保启用垂直滚动条，以便显示所有热源
        self.detail_text.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.detail_text.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        self.detail_text.setStyleSheet("""
            QTextEdit {
                background-color: #323232;
                border: 1px solid #505050;
                border-radius: 3px;
                color: white;
                font-size: 11px;
                font-family: "Consolas", "Monaco", monospace;
                line-height: 1.2;
            }
            QScrollBar:vertical {
                background-color: #404040;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background-color: #606060;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #707070;
            }
        """)
        layout.addWidget(self.detail_text)
        
        # 设置整体样式
        self.setStyleSheet("""
            QWidget {
                background-color: #323232;
                border-radius: 5px;
                margin: 2px;
            }
            QPushButton {
                background-color: #404040;
                border: 1px solid #606060;
                border-radius: 3px;
                color: white;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #505050;
            }
        """)
    
    def set_display_mode(self, mode: str):
        """设置显示模式"""
        self.display_mode = mode
        self.update_display()
    
    def scroll_sources(self, direction: int):
        """滚动热源列表 - 现在使用QTextEdit的内置滚动"""
        # 不再需要手动滚动逻辑，QTextEdit会自动处理滚动
        # 保留这个方法是为了兼容性，但实际上不做任何操作
        pass
    
    def update_heat_sources(self, heat_sources: List):
        """更新热源数据"""
        print(f"🔥 HeatSourceDetailWidget收到热源数据: {len(heat_sources) if heat_sources else 0}个热源")

        self.heat_sources = heat_sources
        # 移除手动滚动位置重置，QTextEdit会自动处理滚动
        # self.scroll_offset = 0
        self.update_display()
    
    def update_display(self):
        """更新显示内容 - 显示所有热源，使用滚动条"""
        if not self.heat_sources:
            self.detail_text.setText("暂无热源信息")
            # 更新统计区域
            self.update_heat_source_stats(0)
            return

        # 移除动态调整显示数量的限制
        # self._adjust_display_count()

        # 显示所有热源，不再限制数量
        total_sources = len(self.heat_sources)

        # 构建显示文本 - 移除顶部的数量信息，只显示热源详情
        display_text = ""

        for i, source in enumerate(self.heat_sources):
            display_text += self._format_heat_source(source, i + 1)
            display_text += "\n"

        self.detail_text.setText(display_text.strip())

        # 更新统计区域
        self.update_heat_source_stats(total_sources)

    def update_heat_source_stats(self, count: int):
        """更新热源统计信息"""
        # 通过增强侧边栏组件更新统计区域
        enhanced_sidebar = self.parent()
        while enhanced_sidebar and not hasattr(enhanced_sidebar, 'heat_source_count_label'):
            enhanced_sidebar = enhanced_sidebar.parent()

        if enhanced_sidebar and hasattr(enhanced_sidebar, 'heat_source_count_label'):
            enhanced_sidebar.heat_source_count_label.setText(f"共 {count} 个热源")

            # 根据热源数量更新状态
            if hasattr(enhanced_sidebar, 'heat_source_status_label'):
                if count == 0:
                    enhanced_sidebar.heat_source_status_label.setText("无热源")
                    enhanced_sidebar.heat_source_status_label.setStyleSheet("""
                        QLabel {
                            color: #888888;
                            font-size: 12px;
                            font-weight: bold;
                            background: transparent;
                            border: none;
                            margin: 0px;
                            padding: 0px;
                        }
                    """)
                elif count <= 5:
                    enhanced_sidebar.heat_source_status_label.setText("正常")
                    enhanced_sidebar.heat_source_status_label.setStyleSheet("""
                        QLabel {
                            color: #00ff00;
                            font-size: 12px;
                            font-weight: bold;
                            background: transparent;
                            border: none;
                            margin: 0px;
                            padding: 0px;
                        }
                    """)
                elif count <= 10:
                    enhanced_sidebar.heat_source_status_label.setText("注意")
                    enhanced_sidebar.heat_source_status_label.setStyleSheet("""
                        QLabel {
                            color: #ffaa00;
                            font-size: 12px;
                            font-weight: bold;
                            background: transparent;
                            border: none;
                            margin: 0px;
                            padding: 0px;
                        }
                    """)
                else:
                    enhanced_sidebar.heat_source_status_label.setText("警告")
                    enhanced_sidebar.heat_source_status_label.setStyleSheet("""
                        QLabel {
                            color: #ff4444;
                            font-size: 12px;
                            font-weight: bold;
                            background: transparent;
                            border: none;
                            margin: 0px;
                            padding: 0px;
                        }
                    """)

    def _adjust_display_count(self):
        """已移除：不再需要动态调整显示数量，显示所有热源并使用滚动条"""
        # 这个方法已经不再使用，保留是为了兼容性
        # 现在所有热源都会显示，通过QTextEdit的滚动条来浏览
        pass
    
    def _format_heat_source(self, source, index: int) -> str:
        """格式化单个热源信息"""
        # 检查是否为标注区域分析
        is_annotation_analysis = getattr(source, 'is_annotation_analysis', False)

        if is_annotation_analysis:
            # 标注区域分析的特殊显示
            annotation_label = getattr(source, 'annotation_label', '未知标注')
            text = f"--- 📍 标注区域: {annotation_label} ---\n"
        else:
            # 获取跟踪信息
            tracking_info = ""
            if hasattr(source, 'tracking_id') and source.tracking_id is not None:
                tracking_info = f"[ID:{source.tracking_id}] "

            # 获取标注区域信息
            annotation_info = ""
            if hasattr(source, 'annotation_label') and source.annotation_label:
                annotation_info = f"[{source.annotation_label}] "

            text = f"--- {tracking_info}{annotation_info}热源 {index} ---\n"
        
        if is_annotation_analysis:
            # 标注区域分析的特殊显示内容
            text += self._format_annotation_analysis_info(source)
        elif self.display_mode == DisplayMode.OVERVIEW:
            # 概览模式：只显示核心信息，使用紧凑格式
            text += f"位置: ({source.position[0]},{source.position[1]})\n"
            text += f"面积: {getattr(source, 'area', 0)}px\n"
            text += f"最高温: {getattr(source, 'max_temperature', 0):.1f}°C\n"

        elif self.display_mode == DisplayMode.DETAILED:
            # 详细模式：显示完整基础信息，优化换行
            text += f"位置: ({source.position[0]},{source.position[1]})\n"
            text += f"尺寸: {source.size[0]}×{source.size[1]}px\n"
            text += f"面积: {getattr(source, 'area', 0)}px\n"
            min_temp = getattr(source, 'min_temperature', 0)
            max_temp = getattr(source, 'max_temperature', 0)
            text += f"温度: {min_temp:.1f}~{max_temp:.1f}°C\n"

            # 添加基础变化率信息
            if hasattr(source, 'tracking_id') and source.tracking_id is not None:
                change_rates = self._get_basic_change_rates(source.tracking_id)
                if change_rates:
                    text += f"变化率: {change_rates}\n"

            # 添加同步标注框分析信息
            sync_analysis_text = self._get_sync_annotation_analysis_text(source)
            if sync_analysis_text:
                text += sync_analysis_text
                    
        elif self.display_mode == DisplayMode.EXPERT:
            # 专家模式：显示所有14个指标
            text += self._format_expert_metrics(source)
        
        return text
    
    def _get_basic_change_rates(self, tracking_id: int) -> str:
        """获取基础变化率信息（详细模式用）"""
        # 这里需要从父窗口获取分析器实例
        try:
            parent_window = self.parent()
            while parent_window and not hasattr(parent_window, 'heat_source_analyzer'):
                parent_window = parent_window.parent()
            
            if parent_window and parent_window.heat_source_analyzer:
                analyzer = parent_window.heat_source_analyzer
                change_rates = analyzer.get_change_rates(tracking_id, 10.0)
                
                rate_texts = []
                if 'area' in change_rates:
                    area_rate = change_rates['area']
                    if hasattr(area_rate, 'change_rate'):
                        rate_texts.append(f"面积{area_rate.change_rate:+.1f}%/秒")
                
                if 'max_temp' in change_rates:
                    temp_rate = change_rates['max_temp']
                    if hasattr(temp_rate, 'change_rate'):
                        rate_texts.append(f"最高温{temp_rate.change_rate:+.2f}°C/秒")
                
                return " ".join(rate_texts) if rate_texts else ""
        except Exception as e:
            self.logger.warning(f"获取变化率失败: {e}")
        
        return ""
    
    def _format_expert_metrics(self, source) -> str:
        """格式化专家模式的完整指标（14项）"""
        text = ""
        
        # 基础信息
        text += f"位置: ({source.position[0]}, {source.position[1]})\n"
        
        # 尺寸指标（4项）
        text += f"长度: {source.size[0]} 像素\n"
        text += f"宽度: {source.size[1]} 像素\n"
        
        # 面积指标（2项）
        text += f"面积: {getattr(source, 'area', 0)} 像素\n"
        
        # 温度指标（8项）
        min_temp = getattr(source, 'min_temperature', 0)
        max_temp = getattr(source, 'max_temperature', 0)
        avg_temp = (min_temp + max_temp) / 2  # 简单平均，实际应该从源数据获取
        temp_diff = max_temp - min_temp
        
        text += f"最低温: {min_temp:.1f}°C\n"
        text += f"最高温: {max_temp:.1f}°C\n"
        text += f"平均温: {avg_temp:.1f}°C\n"
        text += f"温差: {temp_diff:.1f}°C\n"
        
        # 变化率信息（如果有跟踪ID）
        if hasattr(source, 'tracking_id') and source.tracking_id is not None:
            expert_rates = self._get_expert_change_rates(source.tracking_id)
            if expert_rates:
                text += "--- 变化率(每秒) ---\n"
                text += expert_rates
        
        return text
    
    def _get_expert_change_rates(self, tracking_id: int) -> str:
        """获取专家模式的完整变化率信息"""
        # 这里需要实现完整的14项变化率计算
        # 暂时返回基础变化率，后续可以扩展
        return self._get_basic_change_rates(tracking_id)

    def _get_sync_annotation_analysis_text(self, source) -> str:
        """
        获取同步标注框分析文本

        Args:
            source: 热源信息对象

        Returns:
            格式化的同步标注框分析文本
        """
        try:
            # 检查是否有同步标注框分析数据
            if not hasattr(source, 'sync_annotation_analysis'):
                return ""

            analysis_data = source.sync_annotation_analysis
            if not analysis_data:
                return ""

            text = ""

            # 遍历所有重叠的标注框
            for ann_id, analysis in analysis_data.items():
                label = analysis.get('label', '未知标注')
                overlap_ratio = analysis.get('overlap_ratio', 0.0)
                thermal_data = analysis.get('thermal_analysis', {})

                if not thermal_data:
                    continue

                # 添加标注框信息
                text += f"📍 标注区域: {label}\n"
                text += f"重叠度: {overlap_ratio:.1%}\n"

                # 添加热成像分析数据
                max_temp = thermal_data.get('max_temperature', 0)
                min_temp = thermal_data.get('min_temperature', 0)
                avg_temp = thermal_data.get('avg_temperature', 0)
                temp_range = thermal_data.get('temperature_range', 0)

                text += f"区域温度: {min_temp:.1f}~{max_temp:.1f}°C\n"
                text += f"平均温度: {avg_temp:.1f}°C\n"
                text += f"温度范围: {temp_range:.1f}°C\n"

                # 添加热点信息
                hottest_point = thermal_data.get('hottest_point')
                if hottest_point:
                    text += f"最热点: ({hottest_point[0]}, {hottest_point[1]})\n"

                # 添加像素分布信息
                hot_ratio = thermal_data.get('hot_pixel_ratio', 0)
                cold_ratio = thermal_data.get('cold_pixel_ratio', 0)
                total_pixels = thermal_data.get('total_pixels', 0)

                if total_pixels > 0:
                    text += f"高温像素: {hot_ratio:.1%}\n"
                    text += f"低温像素: {cold_ratio:.1%}\n"
                    text += f"总像素数: {total_pixels}\n"

                text += "\n"  # 标注框之间的分隔

            return text

        except Exception as e:
            print(f"⚠️ 获取同步标注框分析文本失败: {e}")
            return ""

    def _format_annotation_analysis_info(self, source) -> str:
        """
        格式化标注区域分析信息

        Args:
            source: 标注区域分析对象

        Returns:
            格式化的标注区域分析文本
        """
        try:
            text = ""

            # 基本信息
            text += f"区域位置: ({source.position[0]}, {source.position[1]})\n"
            text += f"区域尺寸: {source.size[0]} x {source.size[1]} 像素\n"
            text += f"区域面积: {source.area} 像素\n"

            # 温度信息
            if hasattr(source, 'max_temperature') and hasattr(source, 'min_temperature'):
                max_temp = source.max_temperature
                min_temp = source.min_temperature
                avg_temp = getattr(source, 'avg_temperature', 0)
                temp_range = max_temp - min_temp

                text += f"最高温度: {max_temp:.1f}°C\n"
                text += f"最低温度: {min_temp:.1f}°C\n"
                text += f"平均温度: {avg_temp:.1f}°C\n"
                text += f"温度范围: {temp_range:.1f}°C\n"

            # 详细热成像分析数据
            if hasattr(source, 'thermal_analysis'):
                thermal_data = source.thermal_analysis

                # 最热点信息
                hottest_point = thermal_data.get('hottest_point')
                if hottest_point:
                    text += f"最热点位置: ({hottest_point[0]}, {hottest_point[1]})\n"

                # 像素分布信息
                hot_ratio = thermal_data.get('hot_pixel_ratio', 0)
                cold_ratio = thermal_data.get('cold_pixel_ratio', 0)
                total_pixels = thermal_data.get('total_pixels', 0)

                if total_pixels > 0:
                    text += f"高温像素比例: {hot_ratio:.1%}\n"
                    text += f"低温像素比例: {cold_ratio:.1%}\n"
                    text += f"总像素数: {total_pixels}\n"

                # 温度标准差
                std_temp = thermal_data.get('std_temperature', 0)
                if std_temp > 0:
                    text += f"温度标准差: {std_temp:.1f}°C\n"

            # 标注区域状态
            text += f"数据来源: 标注区域独立分析\n"
            text += f"分析状态: 实时温度监控\n"

            return text

        except Exception as e:
            print(f"⚠️ 格式化标注区域分析信息失败: {e}")
            return ""


class EnhancedThermalSidebar(QWidget):
    """增强的热成像侧边栏 - 支持多模式显示"""

    # 定义信号
    mode_changed = pyqtSignal(str)  # 模式切换信号

    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger("EnhancedThermalSidebar")

        # 侧边栏设置 - 使用自适应宽度
        self.min_width = 300  # 最小宽度
        self.max_width = 500  # 最大宽度
        self.preferred_width = 380  # 首选宽度
        self.sidebar_enabled = True
        self.current_mode = DisplayMode.OVERVIEW

        # 父窗口引用
        self.parent_window = parent

        # 状态信息存储
        self.status_info = {
            'fps': 0.0,
            'timestamp': '',
            'detection_enabled': False,
            'heat_sources_count': 0,
            'heat_sources_detail': [],
            # 检测指标
            'human_count': 0,
            'smoke_count': 0,
            'smoke_area_change_rate': 0.0,
            'fire_count': 0,
            'fire_area_change_rate': 0.0,
            'fire_count_change_rate': 0.0,

        }

        # 全局指标数据
        self.global_metrics = {}

        # 初始化UI
        self.init_ui()

        # 定时器用于更新时间戳
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_timestamp)
        self.update_timer.start(1000)

    def init_ui(self):
        """初始化用户界面"""
        # 设置自适应宽度
        self.setMinimumWidth(self.min_width)
        self.setMaximumWidth(self.max_width)
        self.resize(self.preferred_width, 1000)  # 设置首选大小

        # 设置尺寸策略，允许水平和垂直方向都可以调整
        from PyQt5.QtWidgets import QSizePolicy
        self.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Expanding)

        self.setStyleSheet("""
            QWidget {
                background-color: #282828;
                color: white;
                font-family: "Microsoft YaHei", "SimHei", Arial;
            }
            QLabel {
                color: white;
                padding: 2px;
            }
            QPushButton {
                background-color: #404040;
                border: 1px solid #606060;
                border-radius: 3px;
                padding: 5px 10px;
                font-size: 12px;
                color: white;
            }
            QPushButton:hover {
                background-color: #505050;
            }
            QPushButton:pressed {
                background-color: #303030;
            }
            QPushButton:checked {
                background-color: #0078d4;
                border-color: #106ebe;
            }
        """)

        # 创建主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(8)

        # 创建标题和模式切换区域
        self.create_header_section(main_layout)

        # 创建滚动区域
        self.create_scroll_area(main_layout)

        # 创建底部控制区域
        self.create_bottom_controls(main_layout)

        # 简化方法：直接设置滚动区域的拉伸因子
        # 布局顺序：0=标题标签, 1=模式按钮, 2=状态信息, 3=分割线, 4=滚动区域, 5=底部控制
        main_layout.setStretch(4, 1)  # 只设置滚动区域可以扩展

    def create_header_section(self, layout):
        """创建标题和模式切换区域"""
        # 标题
        title_label = QLabel("热源监控系统")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; color: white; padding: 5px;")
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # 模式切换按钮组
        mode_layout = QHBoxLayout()
        self.mode_button_group = QButtonGroup()

        self.overview_btn = QPushButton("概览")
        self.detailed_btn = QPushButton("详细")
        self.expert_btn = QPushButton("专家")

        # 设置按钮为可选中状态
        for btn in [self.overview_btn, self.detailed_btn, self.expert_btn]:
            btn.setCheckable(True)
            btn.setMaximumHeight(30)
            btn.setStyleSheet(get_button_style('button_small', '#404040', '#606060', '#505050') + """
                QPushButton:checked {
                    background-color: #0078d4;
                    border-color: #106ebe;
                }
            """)
            self.mode_button_group.addButton(btn)
            mode_layout.addWidget(btn)

        # 默认选中概览模式
        self.overview_btn.setChecked(True)

        # 连接信号
        self.overview_btn.clicked.connect(lambda: self.set_display_mode(DisplayMode.OVERVIEW))
        self.detailed_btn.clicked.connect(lambda: self.set_display_mode(DisplayMode.DETAILED))
        self.expert_btn.clicked.connect(lambda: self.set_display_mode(DisplayMode.EXPERT))

        layout.addLayout(mode_layout)

        # 系统状态信息（时间和FPS）
        status_layout = QHBoxLayout()

        self.timestamp_label = QLabel("--:--:--")
        self.timestamp_label.setStyleSheet("font-size: 12px; color: #ffffff;")

        self.fps_label = QLabel("FPS: 0.0")
        self.fps_label.setStyleSheet("font-size: 12px; color: #00ffff;")

        status_layout.addWidget(self.timestamp_label)
        status_layout.addStretch()
        status_layout.addWidget(self.fps_label)

        layout.addLayout(status_layout)

        # 分割线
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setStyleSheet("color: #606060;")
        layout.addWidget(separator)

    def create_scroll_area(self, layout):
        """创建滚动区域"""
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # 设置滚动区域的尺寸策略，确保它能够扩展
        from PyQt5.QtWidgets import QSizePolicy
        self.scroll_area.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        # 设置滚动区域的最小高度，确保它占用足够的空间
        self.scroll_area.setMinimumHeight(400)

        # 创建内容控件
        self.content_widget = QWidget()
        self.content_layout = QVBoxLayout(self.content_widget)
        self.content_layout.setContentsMargins(5, 5, 5, 5)
        self.content_layout.setSpacing(8)

        # 创建组件
        self.create_content_widgets()

        # 设置滚动区域内容
        self.scroll_area.setWidget(self.content_widget)
        layout.addWidget(self.scroll_area)

    def create_content_widgets(self):
        """创建内容组件 - 使用二级选项卡组织"""
        # 导入必要的组件
        from PyQt5.QtWidgets import QSizePolicy

        # 创建二级选项卡组件
        self.content_tab_widget = QTabWidget()
        self.content_tab_widget.setTabPosition(QTabWidget.North)
        self.content_tab_widget.setMovable(False)
        self.content_tab_widget.setTabsClosable(False)

        # 设置二级选项卡的尺寸策略
        self.content_tab_widget.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        # 设置二级选项卡样式
        self.content_tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #505050;
                background-color: #323232;
                border-radius: 3px;
            }
            QTabBar::tab {
                background-color: #404040;
                color: white;
                padding: 8px 12px;
                margin-right: 2px;
                border-top-left-radius: 3px;
                border-top-right-radius: 3px;
                font-size: 11px;
            }
            QTabBar::tab:selected {
                background-color: #0078d4;
                color: white;
            }
            QTabBar::tab:hover {
                background-color: #505050;
            }
        """)

        # 创建三个子页面
        self.create_global_info_tab()
        self.create_flame_analysis_tab()
        self.create_heat_source_detail_tab()

        # 将二级选项卡添加到主内容布局
        self.content_layout.addWidget(self.content_tab_widget)

        # 添加小的弹性空间
        self.content_layout.addItem(QSpacerItem(20, 10, QSizePolicy.Minimum, QSizePolicy.Minimum))

    def create_global_info_tab(self):
        """创建全局信息页面"""
        # 创建页面容器
        global_info_container = QWidget()
        global_info_container.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        global_info_layout = QVBoxLayout(global_info_container)
        global_info_layout.setContentsMargins(5, 5, 5, 5)
        global_info_layout.setSpacing(8)

        # 创建全局指标组件
        self.global_metrics_widget = GlobalMetricsWidget()
        global_info_layout.addWidget(self.global_metrics_widget)

        # 移除弹性空间，让全局指标组件充分利用可用空间
        # global_info_layout.addItem(QSpacerItem(20, 10, QSizePolicy.Minimum, QSizePolicy.Expanding))

        # 添加到选项卡
        tab_index = self.content_tab_widget.addTab(global_info_container, "📊 全局信息")
        self.content_tab_widget.setTabToolTip(tab_index, "系统级监控数据：热源数量、面积统计、温度趋势")

        self.logger.info("创建全局信息子页面")

    def create_flame_analysis_tab(self):
        """创建火焰烟雾页面"""
        # 创建页面容器
        flame_analysis_container = QWidget()
        flame_analysis_container.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        flame_analysis_layout = QVBoxLayout(flame_analysis_container)
        flame_analysis_layout.setContentsMargins(5, 5, 5, 5)
        flame_analysis_layout.setSpacing(8)

        # 创建火焰烟雾分析组件
        try:
            from qt_ui.fire_smoke_analysis_widget import FireSmokeAnalysisWidget
            self.fire_smoke_analysis_widget = FireSmokeAnalysisWidget(self)
            self.fire_smoke_analysis_widget.setMinimumHeight(400)
            flame_analysis_layout.addWidget(self.fire_smoke_analysis_widget)

            # 连接信号
            self.fire_smoke_analysis_widget.target_selected.connect(self.on_fire_smoke_target_selected)

            self.logger.info("火焰烟雾分析组件创建成功")
        except ImportError as e:
            self.logger.warning(f"火焰烟雾分析组件导入失败: {e}")
            self.fire_smoke_analysis_widget = None
            # 添加错误提示
            error_label = QLabel("火焰烟雾分析组件加载失败")
            error_label.setStyleSheet("color: #ff6666; font-style: italic; padding: 20px;")
            error_label.setAlignment(Qt.AlignCenter)
            flame_analysis_layout.addWidget(error_label)

        # 移除弹性空间，让火焰分析组件充分利用可用空间
        # flame_analysis_layout.addItem(QSpacerItem(20, 10, QSizePolicy.Minimum, QSizePolicy.Expanding))

        # 添加到选项卡
        tab_index = self.content_tab_widget.addTab(flame_analysis_container, "🔥 火焰烟雾")
        self.content_tab_widget.setTabToolTip(tab_index, "火焰检测分析：闪烁频率、形状分析、综合评估")

        self.logger.info("创建火焰分析子页面")

    def on_fire_smoke_target_selected(self, target_type, target_id):
        """处理火焰烟雾目标选择事件"""
        self.logger.info(f"选中{target_type}目标: {target_id}")
        # 这里可以添加目标选择后的处理逻辑，比如在主视图中高亮显示

    def update_fire_smoke_data(self, fire_data=None, smoke_data=None):
        """更新火焰烟雾数据"""
        try:
            if hasattr(self, 'fire_smoke_analysis_widget') and self.fire_smoke_analysis_widget:
                print(f"🔥💨 增强侧边栏收到数据: fire_data={len(fire_data) if fire_data else 0}, smoke_data={len(smoke_data) if smoke_data else 0}")
                self.logger.debug(f"收到火焰烟雾数据更新: fire_data={len(fire_data) if fire_data else 0}, smoke_data={len(smoke_data) if smoke_data else 0}")

                # 处理火焰数据
                if fire_data is not None:
                    # 将列表数据转换为字典格式 {id: data}
                    fire_targets = {}
                    for item in fire_data:
                        if isinstance(item, dict):
                            obj_id = item.get('object_id', item.get('id', len(fire_targets)))
                            fire_targets[str(obj_id)] = item
                            self.logger.debug(f"火焰目标 {obj_id}: confidence={item.get('confidence', 'N/A')}, area={item.get('area', 'N/A')}")


                    print(f"🔥 增强侧边栏调用火焰分析组件更新火焰数据: {len(fire_targets)}个")
                    self.fire_smoke_analysis_widget.update_fire_targets(fire_targets)

                # 处理烟雾数据
                if smoke_data is not None:
                    # 将列表数据转换为字典格式 {id: data}
                    smoke_targets = {}
                    for item in smoke_data:
                        if isinstance(item, dict):
                            obj_id = item.get('object_id', item.get('id', len(smoke_targets)))
                            smoke_targets[str(obj_id)] = item
                            self.logger.debug(f"烟雾目标 {obj_id}: confidence={item.get('confidence', 'N/A')}, area={item.get('area', 'N/A')}")

                    print(f"💨 增强侧边栏调用火焰分析组件更新烟雾数据: {len(smoke_targets)}个")
                    self.fire_smoke_analysis_widget.update_smoke_targets(smoke_targets)
            else:
                self.logger.warning("fire_smoke_analysis_widget 不存在，无法更新数据")

        except Exception as e:
            self.logger.error(f"更新火焰烟雾数据失败: {e}")
            import traceback
            self.logger.error(f"详细错误: {traceback.format_exc()}")

    def clear_fire_smoke_selection(self):
        """清除火焰烟雾选择"""
        if hasattr(self, 'fire_smoke_analysis_widget') and self.fire_smoke_analysis_widget:
            self.fire_smoke_analysis_widget.clear_selection()

    def create_heat_source_detail_tab(self):
        """创建热源详情页面"""
        # 创建页面容器
        heat_source_container = QWidget()
        heat_source_container.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        heat_source_layout = QVBoxLayout(heat_source_container)
        heat_source_layout.setContentsMargins(5, 5, 5, 5)
        heat_source_layout.setSpacing(8)

        # 创建热源统计信息区域
        self.create_heat_source_stats_area(heat_source_layout)

        # 创建热源详情组件
        self.heat_source_detail_widget = HeatSourceDetailWidget(self.current_mode, self)
        heat_source_layout.addWidget(self.heat_source_detail_widget)

        # 移除弹性空间，让热源详情组件充分利用可用空间
        # heat_source_layout.addItem(QSpacerItem(20, 10, QSizePolicy.Minimum, QSizePolicy.Expanding))

        # 添加到选项卡
        tab_index = self.content_tab_widget.addTab(heat_source_container, "🌡️ 热源详情")
        self.content_tab_widget.setTabToolTip(tab_index, "热源详细信息：温度数据、位置信息、热源列表")

        self.logger.info("创建热源详情子页面")

    def create_heat_source_stats_area(self, layout):
        """创建热源统计信息区域"""
        # 创建统计信息容器
        stats_container = QWidget()
        stats_container.setStyleSheet("""
            QWidget {
                background-color: #404040;
                border: 2px solid #0078d4;
                border-radius: 8px;
                margin: 2px;
                padding: 8px;
            }
        """)
        stats_layout = QHBoxLayout(stats_container)
        stats_layout.setContentsMargins(10, 8, 10, 8)
        stats_layout.setSpacing(15)

        # 热源数量标签
        self.heat_source_count_label = QLabel("共 0 个热源")
        self.heat_source_count_label.setStyleSheet("""
            QLabel {
                color: #ffff00;
                font-size: 16px;
                font-weight: bold;
                background: transparent;
                border: none;
                margin: 0px;
                padding: 0px;
            }
        """)
        stats_layout.addWidget(self.heat_source_count_label)

        # 添加图标
        icon_label = QLabel("🌡️")
        icon_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                background: transparent;
                border: none;
                margin: 0px;
                padding: 0px;
            }
        """)
        stats_layout.addWidget(icon_label)

        stats_layout.addStretch()

        # 状态指示器
        self.heat_source_status_label = QLabel("正常")
        self.heat_source_status_label.setStyleSheet("""
            QLabel {
                color: #00ff00;
                font-size: 12px;
                font-weight: bold;
                background: transparent;
                border: none;
                margin: 0px;
                padding: 0px;
            }
        """)
        stats_layout.addWidget(self.heat_source_status_label)

        # 设置容器的固定高度
        stats_container.setFixedHeight(50)

        # 添加到主布局
        layout.addWidget(stats_container)

    def get_current_content_tab(self):
        """获取当前选中的内容子选项卡"""
        if hasattr(self, 'content_tab_widget'):
            current_index = self.content_tab_widget.currentIndex()
            tab_text = self.content_tab_widget.tabText(current_index)
            return current_index, tab_text
        return None, None

    def set_current_content_tab(self, index: int):
        """设置当前的内容子选项卡"""
        if hasattr(self, 'content_tab_widget') and 0 <= index < self.content_tab_widget.count():
            self.content_tab_widget.setCurrentIndex(index)
            self.logger.info(f"切换到内容子选项卡: {self.content_tab_widget.tabText(index)}")



    def create_bottom_controls(self, layout):
        """创建底部控制区域"""
        control_layout = QHBoxLayout()

        # 设置按钮
        settings_btn = QPushButton("设置")
        settings_btn.setStyleSheet(PRESET_STYLES['secondary_button'])
        settings_btn.clicked.connect(self.show_settings)
        control_layout.addWidget(settings_btn)

        control_layout.addStretch()

        # 帮助按钮
        help_btn = QPushButton("帮助")
        help_btn.setStyleSheet(PRESET_STYLES['secondary_button'])
        help_btn.clicked.connect(self.show_help)
        control_layout.addWidget(help_btn)

        layout.addLayout(control_layout)

    def set_display_mode(self, mode: str):
        """设置显示模式"""
        if mode == self.current_mode:
            return

        self.current_mode = mode
        self.heat_source_detail_widget.set_display_mode(mode)

        # 更新按钮状态
        if mode == DisplayMode.OVERVIEW:
            self.overview_btn.setChecked(True)
        elif mode == DisplayMode.DETAILED:
            self.detailed_btn.setChecked(True)
        elif mode == DisplayMode.EXPERT:
            self.expert_btn.setChecked(True)

        self.mode_changed.emit(mode)
        self.logger.info(f"显示模式切换到: {mode}")

    def update_timestamp(self):
        """更新时间戳"""
        current_time = datetime.now().strftime("%H:%M:%S")
        self.status_info['timestamp'] = current_time
        self.timestamp_label.setText(current_time)

    def update_status_info(self, **kwargs):
        """更新状态信息"""
        for key, value in kwargs.items():
            if key in self.status_info:
                self.status_info[key] = value

        self.update_status_display()

    def update_status_display(self):
        """更新状态显示"""
        # 更新FPS
        self.fps_label.setText(f"FPS: {self.status_info['fps']:.1f}")

        # 更新全局指标中的检测状态
        self.global_metrics_widget.update_detection_status(self.status_info)

    def update_heat_sources_detail(self, heat_sources_detail: list):
        """更新热源详细信息"""
        print(f"🌡️ EnhancedThermalSidebar收到热源详情更新: {len(heat_sources_detail) if heat_sources_detail else 0}个热源")

        self.status_info['heat_sources_detail'] = heat_sources_detail

        if hasattr(self, 'heat_source_detail_widget') and self.heat_source_detail_widget:
            print(f"🔄 EnhancedThermalSidebar更新heat_source_detail_widget")
            self.heat_source_detail_widget.update_heat_sources(heat_sources_detail)
        else:
            print(f"❌ heat_source_detail_widget不存在")

    def update_global_metrics(self, metrics: Dict):
        """更新全局指标"""
        self.global_metrics.update(metrics)
        self.global_metrics_widget.update_metrics(metrics)

    def update_flame_analysis(self, flame_analysis_results: Dict):
        """更新火焰分析数据"""
        if hasattr(self, 'flame_analysis_widget') and self.flame_analysis_widget:
            self.flame_analysis_widget.update_flame_analysis_data(flame_analysis_results)

    def toggle_sidebar(self):
        """切换侧边栏显示"""
        self.sidebar_enabled = not self.sidebar_enabled
        self.setVisible(self.sidebar_enabled)
        return self.sidebar_enabled

    def set_enabled(self, enabled: bool):
        """设置侧边栏启用状态"""
        self.sidebar_enabled = enabled
        self.setVisible(enabled)

    def show_settings(self):
        """显示设置对话框"""
        try:
            from qt_ui.sidebar_settings_dialog import SidebarSettingsDialog

            dialog = SidebarSettingsDialog(self)
            dialog.settings_changed.connect(self.apply_settings)

            if dialog.exec_() == dialog.Accepted:
                self.logger.info("设置对话框已确认")
            else:
                self.logger.info("设置对话框已取消")

        except Exception as e:
            self.logger.error(f"显示设置对话框失败: {e}")

    def show_help(self):
        """显示帮助信息"""
        from PyQt5.QtWidgets import QMessageBox

        help_text = """
<h3>热源监控系统 - 侧边栏帮助</h3>

<h4>显示模式:</h4>
<ul>
<li><b>概览模式</b>: 显示核心信息（位置、面积、最高温度）</li>
<li><b>详细模式</b>: 显示完整基础信息和变化率</li>
<li><b>专家模式</b>: 显示所有14个监控指标</li>
</ul>

<h4>全局指标:</h4>
<ul>
<li>总热源个数及变化率</li>
<li>整体画面热源面积及变化率</li>
<li>30天平均温度</li>
</ul>

<h4>单个热源指标:</h4>
<ul>
<li>尺寸指标: 长度、宽度及变化率</li>
<li>面积指标: 像素面积及变化率</li>
<li>温度指标: 最低温、最高温、平均温、温差及变化率</li>
</ul>

<h4>快捷操作:</h4>
<ul>
<li>点击模式按钮切换显示详细程度</li>
<li>使用↑↓按钮滚动热源列表</li>
<li>点击设置按钮配置显示选项</li>
</ul>
        """

        msg_box = QMessageBox(self)
        msg_box.setWindowTitle("帮助信息")
        msg_box.setText(help_text)
        msg_box.setTextFormat(Qt.RichText)
        msg_box.setStandardButtons(QMessageBox.Ok)
        msg_box.setModal(False)  # 设置为非模态，避免阻塞主界面
        msg_box.setStyleSheet("""
            QMessageBox {
                background-color: #2b2b2b;
                color: white;
            }
            QMessageBox QLabel {
                color: white;
                min-width: 400px;
            }
            QPushButton {
                background-color: #404040;
                border: 1px solid #606060;
                border-radius: 3px;
                padding: 8px 16px;
                color: white;
            }
            QPushButton:hover {
                background-color: #505050;
            }
        """)
        msg_box.show()  # 使用show()而不是exec_()避免阻塞

    def apply_settings(self, settings: dict):
        """应用设置"""
        try:
            # 更新显示设置
            # 移除max_display_count设置，现在显示所有热源
            # if 'max_heat_sources_display' in settings:
            #     self.heat_source_detail_widget.max_display_count = settings['max_heat_sources_display']

            # 更新其他设置...
            self.logger.info(f"已应用设置: {len(settings)} 项")

        except Exception as e:
            self.logger.error(f"应用设置失败: {e}")

    def scroll_heat_sources(self, direction: int):
        """滚动热源详情"""
        self.heat_source_detail_widget.scroll_sources(direction)

    def adjust_size_to_parent(self):
        """根据父窗口大小调整侧边栏尺寸"""
        if not self.parent_window:
            return

        try:
            # 获取父窗口大小
            parent_size = self.parent_window.size()
            parent_width = parent_size.width()
            parent_height = parent_size.height()

            # 计算合适的侧边栏宽度（父窗口宽度的20-30%）
            target_width = max(self.min_width, min(self.max_width, int(parent_width * 0.25)))

            # 调整宽度
            if hasattr(self.parent_window, 'sidebar_dock'):
                self.parent_window.sidebar_dock.setMinimumWidth(target_width - 20)
                self.parent_window.sidebar_dock.setMaximumWidth(target_width + 50)
                # 移除高度限制，让停靠窗口使用所有可用高度
                # self.parent_window.sidebar_dock.resize(target_width, parent_height - 100)
                # 只设置宽度，让高度自适应
                current_height = self.parent_window.sidebar_dock.height()
                self.parent_window.sidebar_dock.resize(target_width, current_height)

            # 调整自身大小
            self.setMinimumWidth(target_width - 40)
            self.setMaximumWidth(target_width + 30)

            self.logger.debug(f"侧边栏尺寸调整: 宽度={target_width}, 父窗口={parent_width}x{parent_height}")

        except Exception as e:
            self.logger.warning(f"调整侧边栏尺寸失败: {e}")

    def resizeEvent(self, event):
        """处理窗口大小变化事件"""
        super().resizeEvent(event)
        # 当侧边栏大小变化时，可以在这里做一些响应处理
        if hasattr(self, 'heat_source_detail_widget'):
            # 通知子组件更新显示
            self.heat_source_detail_widget.update_display()
