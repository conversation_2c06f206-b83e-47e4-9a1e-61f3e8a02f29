"""
火焰烟雾分析组件
采用双列表 + 单详情的经典布局
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QListWidget,
                             QListWidgetItem, QLabel, QFrame, QScrollArea,
                             QGroupBox, QGridLayout, QProgressBar, QSplitter)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QColor, QPalette
from utils.logger import get_logger
from qt_ui.alert_manager import AlertManager
import time


class FireSmokeAnalysisWidget(QWidget):
    """火焰烟雾分析组件"""
    
    # 信号定义
    target_selected = pyqtSignal(str, str)  # (target_type, target_id)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger(__name__)

        # 数据存储
        self.fire_targets = {}      # 火焰目标数据 {id: data}
        self.smoke_targets = {}     # 烟雾目标数据 {id: data}
        self.selected_target = None # 当前选中的目标

        # 预警管理器
        print(f"🚨 初始化预警管理器...")
        self.alert_manager = AlertManager(self)
        self.alert_manager.alert_triggered.connect(self.on_alert_triggered)
        print(f"✅ 预警管理器初始化完成")

        self.init_ui()
        self.setup_styles()
        
    def init_ui(self):
        """初始化界面"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.setSpacing(8)
        
        # 标题
        title_label = QLabel("🔥 火焰与烟雾分析 🌫️")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #ffffff;
                background-color: #404040;
                border: 1px solid #606060;
                border-radius: 5px;
                padding: 8px;
                margin-bottom: 5px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # 创建分割器
        splitter = QSplitter(Qt.Vertical)
        main_layout.addWidget(splitter)
        
        # 上半部分：双列表
        lists_widget = self.create_lists_widget()
        splitter.addWidget(lists_widget)
        
        # 下半部分：详情区
        self.detail_widget = self.create_detail_widget()
        splitter.addWidget(self.detail_widget)
        
        # 设置分割器比例 (列表:详情 = 1:2，给详情更多空间)
        splitter.setStretchFactor(0, 1)
        splitter.setStretchFactor(1, 2)

        # 设置最小尺寸
        lists_widget.setMinimumHeight(120)
        self.detail_widget.setMinimumHeight(200)
        
    def create_lists_widget(self):
        """创建双列表区域"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(5)
        
        # 火焰列表
        fire_group = QGroupBox("🔥 火焰目标")
        fire_layout = QVBoxLayout(fire_group)
        fire_layout.setContentsMargins(5, 5, 5, 5)
        
        self.fire_list = QListWidget()
        self.fire_list.setMaximumHeight(120)
        self.fire_list.itemClicked.connect(self.on_fire_item_clicked)
        fire_layout.addWidget(self.fire_list)
        
        layout.addWidget(fire_group)
        
        # 烟雾列表
        smoke_group = QGroupBox("🌫️ 烟雾目标")
        smoke_layout = QVBoxLayout(smoke_group)
        smoke_layout.setContentsMargins(5, 5, 5, 5)
        
        self.smoke_list = QListWidget()
        self.smoke_list.setMaximumHeight(120)
        self.smoke_list.itemClicked.connect(self.on_smoke_item_clicked)
        smoke_layout.addWidget(self.smoke_list)
        
        layout.addWidget(smoke_group)
        
        return widget
        
    def create_detail_widget(self):
        """创建详情区域"""
        # 滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setMinimumHeight(300)  # 设置最小高度确保有足够显示空间
        
        # 详情内容容器
        self.detail_content = QWidget()
        self.detail_layout = QVBoxLayout(self.detail_content)
        self.detail_layout.setContentsMargins(5, 5, 5, 5)
        self.detail_layout.setSpacing(8)
        
        # 默认提示
        self.no_selection_label = QLabel("请选择一个火焰或烟雾目标查看详细信息")
        self.no_selection_label.setAlignment(Qt.AlignCenter)
        self.no_selection_label.setStyleSheet("""
            QLabel {
                color: #888888;
                font-style: italic;
                padding: 20px;
            }
        """)
        self.detail_layout.addWidget(self.no_selection_label)
        self.detail_layout.addStretch()
        
        scroll_area.setWidget(self.detail_content)
        return scroll_area
        
    def setup_styles(self):
        """设置样式"""
        # 列表样式
        list_style = """
            QListWidget {
                background-color: #2a2a2a;
                border: 1px solid #404040;
                border-radius: 3px;
                selection-background-color: #0078d4;
                outline: none;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #404040;
                color: #ffffff;
            }
            QListWidget::item:hover {
                background-color: #404040;
            }
            QListWidget::item:selected {
                background-color: #0078d4;
                color: #ffffff;
            }
        """
        
        # 分组框样式
        group_style = """
            QGroupBox {
                font-weight: bold;
                border: 1px solid #404040;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 5px;
                color: #ffffff;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """
        
        self.fire_list.setStyleSheet(list_style)
        self.smoke_list.setStyleSheet(list_style)
        self.setStyleSheet(group_style)
        
    def update_fire_targets(self, targets_data):
        """更新火焰目标数据"""
        self.fire_targets = targets_data or {}
        print(f"🔥 火焰烟雾分析组件收到火焰数据: {len(self.fire_targets)}个目标")
        self.logger.debug(f"更新火焰目标数据: {len(self.fire_targets)}个目标")

        # 为每个目标添加缺失字段的默认值
        for target_id, data in self.fire_targets.items():
            self.logger.debug(f"处理火焰目标 {target_id}: confidence={data.get('confidence', 'N/A')}, area={data.get('area', 'N/A')}")
            self._add_missing_fire_fields(data)
            self.logger.debug(f"火焰目标 {target_id} 默认值补充完成")

        self._refresh_fire_list()

        # 更新预警管理器
        print(f"🚨 火焰数据更新完成，调用预警管理器")
        self._update_alert_manager()
        
    def update_smoke_targets(self, targets_data):
        """更新烟雾目标数据"""
        self.smoke_targets = targets_data or {}
        print(f"💨 火焰烟雾分析组件收到烟雾数据: {len(self.smoke_targets)}个目标")
        self.logger.debug(f"更新烟雾目标数据: {len(self.smoke_targets)}个目标")

        # 为每个目标添加缺失字段的默认值
        for target_id, data in self.smoke_targets.items():
            self.logger.debug(f"处理烟雾目标 {target_id}: confidence={data.get('confidence', 'N/A')}, area={data.get('area', 'N/A')}")
            self._add_missing_smoke_fields(data)
            self.logger.debug(f"烟雾目标 {target_id} 默认值补充完成")

        self._refresh_smoke_list()

        # 更新预警管理器
        print(f"🚨 烟雾数据更新完成，调用预警管理器")
        self._update_alert_manager()
        
    def _refresh_fire_list(self):
        """刷新火焰列表"""
        self.fire_list.clear()
        
        for target_id, data in self.fire_targets.items():
            confidence = data.get('confidence', 0.0)
            area = data.get('area', 0)
            
            # 创建列表项
            item_text = f"🔥 [ID: {target_id}] 置信度: {confidence:.0%} | 面积: {area:.0f}"
            item = QListWidgetItem(item_text)
            item.setData(Qt.UserRole, target_id)
            
            # 根据置信度设置颜色
            if confidence >= 0.8:
                item.setForeground(QColor("#ff6b6b"))  # 高置信度 - 红色
            elif confidence >= 0.6:
                item.setForeground(QColor("#ffa500"))  # 中置信度 - 橙色
            else:
                item.setForeground(QColor("#ffeb3b"))  # 低置信度 - 黄色
                
            self.fire_list.addItem(item)
            
    def _refresh_smoke_list(self):
        """刷新烟雾列表"""
        self.smoke_list.clear()
        
        for target_id, data in self.smoke_targets.items():
            confidence = data.get('confidence', 0.0)
            risk_level = self._calculate_risk_level(data)
            
            # 创建列表项
            item_text = f"🌫️ [ID: {target_id}] 风险: {risk_level} | 置信度: {confidence:.0%}"
            item = QListWidgetItem(item_text)
            item.setData(Qt.UserRole, target_id)
            
            # 根据风险等级设置颜色
            if risk_level == "高":
                item.setForeground(QColor("#ff6b6b"))  # 高风险 - 红色
            elif risk_level == "警告":
                item.setForeground(QColor("#ffa500"))  # 警告 - 橙色
            else:
                item.setForeground(QColor("#4caf50"))  # 低风险 - 绿色
                
            self.smoke_list.addItem(item)
            
    def _calculate_risk_level(self, data):
        """计算烟雾风险等级"""
        confidence = data.get('confidence', 0.0)
        area = data.get('area', 0)
        
        if confidence >= 0.8 and area > 1000:
            return "高"
        elif confidence >= 0.6 or area > 500:
            return "警告"
        else:
            return "低"
            
    def on_fire_item_clicked(self, item):
        """火焰项目点击事件"""
        target_id = item.data(Qt.UserRole)
        self.smoke_list.clearSelection()  # 清除烟雾列表选择
        self.selected_target = ('fire', target_id)
        self._update_detail_view()
        self.target_selected.emit('fire', target_id)
        
    def on_smoke_item_clicked(self, item):
        """烟雾项目点击事件"""
        target_id = item.data(Qt.UserRole)
        self.fire_list.clearSelection()  # 清除火焰列表选择
        self.selected_target = ('smoke', target_id)
        self._update_detail_view()
        self.target_selected.emit('smoke', target_id)
        
    def _update_detail_view(self):
        """更新详情视图"""
        # 清除现有内容
        for i in reversed(range(self.detail_layout.count())):
            child = self.detail_layout.itemAt(i).widget()
            if child:
                child.setParent(None)
                
        if not self.selected_target:
            self.detail_layout.addWidget(self.no_selection_label)
            self.detail_layout.addStretch()
            return
            
        target_type, target_id = self.selected_target
        
        # 获取目标数据
        if target_type == 'fire':
            data = self.fire_targets.get(target_id, {})
            self._create_fire_detail_view(target_id, data)
        else:
            data = self.smoke_targets.get(target_id, {})
            self._create_smoke_detail_view(target_id, data)
            
        self.detail_layout.addStretch()

    def _create_fire_detail_view(self, target_id, data):
        """创建火焰详情视图"""
        # 标题
        title = QLabel(f"🔥 火焰目标 [ID: {target_id}] 详细分析")
        title.setStyleSheet("""
            QLabel {
                font-size: 13px;
                font-weight: bold;
                color: #ff6600;
                padding: 5px;
                border-bottom: 2px solid #ff6600;
                margin-bottom: 10px;
            }
        """)
        self.detail_layout.addWidget(title)

        # 基础信息
        bbox = data.get('bbox', [0, 0, 0, 0])
        centroid = data.get('centroid', (0, 0))
        basic_info = self._create_detail_section("📊 基础信息", [
            ("目标ID", str(target_id)),
            ("置信度", f"{data.get('confidence', 0):.1%}"),
            ("面积", f"{data.get('area', 0):.0f} 像素²"),
            ("边界框", f"[{bbox[0]:.0f}, {bbox[1]:.0f}, {bbox[2]:.0f}, {bbox[3]:.0f}]"),
            ("中心点", f"({centroid[0]:.0f}, {centroid[1]:.0f})"),
            ("宽高比", f"{data.get('aspect_ratio', 0):.2f}"),
            ("持续时间", f"{data.get('duration', 0):.1f} 秒"),
            ("帧数", f"{data.get('frame_count', 0)} 帧")
        ])
        self.detail_layout.addWidget(basic_info)

        # 闪烁频率分析
        flicker_info = self._create_detail_section("⚡ 闪烁频率分析", [
            ("主导频率", f"{data.get('flicker_frequency', 0):.1f} Hz"),
            ("闪烁强度", f"{data.get('flicker_intensity', 0):.2f}"),
            ("频率稳定性", f"{data.get('frequency_stability', 0):.1%}")
        ])
        self.detail_layout.addWidget(flicker_info)

        # 形状分析
        shape_info = self._create_detail_section("🔺 形状分析", [
            ("紧凑度", f"{data.get('compactness', 0):.2f}"),
            ("边缘密度", f"{data.get('edge_density', 0):.2f}"),
            ("形态稳定性", f"{data.get('shape_stability', 0):.1%}")
        ])
        self.detail_layout.addWidget(shape_info)

        # 运动分析
        motion_info = self._create_detail_section("🏃 运动分析", [
            ("移动速度", f"{data.get('movement_speed', 0):.1f} 像素/秒"),
            ("运动方向", f"{data.get('movement_direction', 0):.0f}°"),
            ("轨迹稳定性", f"{data.get('trajectory_stability', 0):.1%}")
        ])
        self.detail_layout.addWidget(motion_info)

        # 时间信息
        import time
        current_time = time.strftime("%Y-%m-%d %H:%M:%S")
        first_seen = data.get('first_seen', time.time())
        last_seen = data.get('last_seen', time.time())

        time_info = self._create_detail_section("⏰ 时间信息", [
            ("首次发现", time.strftime("%H:%M:%S", time.localtime(first_seen))),
            ("最后更新", time.strftime("%H:%M:%S", time.localtime(last_seen))),
            ("当前时间", current_time),
            ("总距离", f"{data.get('total_distance', 0):.1f} 像素")
        ])
        self.detail_layout.addWidget(time_info)

    def _create_smoke_detail_view(self, target_id, data):
        """创建烟雾详情视图"""
        # 标题
        title = QLabel(f"🌫️ 烟雾目标 [ID: {target_id}] 详细分析")
        title.setStyleSheet("""
            QLabel {
                font-size: 13px;
                font-weight: bold;
                color: #888888;
                padding: 5px;
                border-bottom: 2px solid #888888;
                margin-bottom: 10px;
            }
        """)
        self.detail_layout.addWidget(title)

        # 基础信息
        bbox = data.get('bbox', [0, 0, 0, 0])
        centroid = data.get('centroid', (0, 0))
        basic_info = self._create_detail_section("📊 基础信息", [
            ("目标ID", str(target_id)),
            ("置信度", f"{data.get('confidence', 0):.1%}"),
            ("面积", f"{data.get('area', 0):.0f} 像素²"),
            ("边界框", f"[{bbox[0]:.0f}, {bbox[1]:.0f}, {bbox[2]:.0f}, {bbox[3]:.0f}]"),
            ("中心点", f"({centroid[0]:.0f}, {centroid[1]:.0f})"),
            ("密度", f"{data.get('density', 0):.2f}"),
            ("持续时间", f"{data.get('duration', 0):.1f} 秒"),
            ("帧数", f"{data.get('frame_count', 0)} 帧")
        ])
        self.detail_layout.addWidget(basic_info)

        # 扩散分析
        spread_info = self._create_detail_section("💨 扩散分析", [
            ("扩散速度", f"{data.get('spread_speed', 0):.1f} 像素/秒"),
            ("扩散方向", f"{data.get('spread_direction', 0):.0f}°"),
            ("最大扩散面积", f"{data.get('max_spread_area', 0):.0f} 像素²")
        ])
        self.detail_layout.addWidget(spread_info)

        # 风险评估
        risk_level = self._calculate_risk_level(data)
        risk_color = "#ff6b6b" if risk_level == "高" else "#ffa500" if risk_level == "警告" else "#4caf50"

        risk_info = self._create_detail_section("⚠️ 风险评估", [
            ("风险等级", risk_level),
            ("威胁指数", f"{data.get('threat_index', 0):.1f}"),
            ("预警建议", self._get_risk_advice(risk_level))
        ], section_color=risk_color)
        self.detail_layout.addWidget(risk_info)

        # 时间信息
        import time
        current_time = time.strftime("%Y-%m-%d %H:%M:%S")
        first_seen = data.get('first_seen', time.time())
        last_seen = data.get('last_seen', time.time())

        time_info = self._create_detail_section("⏰ 时间信息", [
            ("首次发现", time.strftime("%H:%M:%S", time.localtime(first_seen))),
            ("最后更新", time.strftime("%H:%M:%S", time.localtime(last_seen))),
            ("当前时间", current_time),
            ("总距离", f"{data.get('total_distance', 0):.1f} 像素")
        ])
        self.detail_layout.addWidget(time_info)

    def _create_detail_section(self, title, items, section_color="#0078d4"):
        """创建详情区段"""
        section = QGroupBox(title)
        section.setStyleSheet(f"""
            QGroupBox {{
                font-weight: bold;
                border: 1px solid {section_color};
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 5px;
                color: {section_color};
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }}
        """)

        layout = QGridLayout(section)
        layout.setContentsMargins(10, 15, 10, 10)
        layout.setSpacing(5)

        for i, (label, value) in enumerate(items):
            # 标签
            label_widget = QLabel(f"{label}:")
            label_widget.setStyleSheet("color: #cccccc; font-weight: normal;")
            layout.addWidget(label_widget, i, 0)

            # 数值
            value_widget = QLabel(str(value))
            value_widget.setStyleSheet(f"color: {section_color}; font-weight: bold;")
            layout.addWidget(value_widget, i, 1)

        return section

    def _get_risk_advice(self, risk_level):
        """获取风险建议"""
        advice_map = {
            "高": "立即疏散，启动应急预案",
            "警告": "密切监控，准备应急措施",
            "低": "持续观察，保持警惕"
        }
        return advice_map.get(risk_level, "正常监控")

    def clear_selection(self):
        """清除选择"""
        self.fire_list.clearSelection()
        self.smoke_list.clearSelection()
        self.selected_target = None
        self._update_detail_view()

    def get_selected_target(self):
        """获取当前选中的目标"""
        return self.selected_target

    def _add_missing_fire_fields(self, data):
        """为火焰数据添加缺失字段的默认值"""
        # 闪烁分析字段
        if 'flicker_frequency' not in data:
            data['flicker_frequency'] = 5.0  # 默认5Hz
        if 'flicker_intensity' not in data:
            data['flicker_intensity'] = 0.3  # 默认中等强度
        if 'frequency_stability' not in data:
            data['frequency_stability'] = 0.8  # 默认较稳定

        # 形状分析字段
        if 'shape_stability' not in data:
            data['shape_stability'] = 0.75  # 默认较稳定

        # 基础字段默认值
        if 'duration' not in data:
            data['duration'] = 1.0  # 默认1秒
        if 'aspect_ratio' not in data:
            data['aspect_ratio'] = 1.0  # 默认正方形
        if 'compactness' not in data:
            data['compactness'] = 0.5  # 默认中等紧凑度
        if 'edge_density' not in data:
            data['edge_density'] = 0.2  # 默认边缘密度
        if 'movement_speed' not in data:
            data['movement_speed'] = 0.0  # 默认静止
        if 'movement_direction' not in data:
            data['movement_direction'] = 0.0  # 默认方向
        if 'trajectory_stability' not in data:
            data['trajectory_stability'] = 0.9  # 默认稳定

    def _add_missing_smoke_fields(self, data):
        """为烟雾数据添加缺失字段的默认值"""
        # 扩散分析字段
        if 'spread_speed' not in data:
            data['spread_speed'] = 2.0  # 默认扩散速度
        if 'spread_direction' not in data:
            data['spread_direction'] = 0.0  # 默认扩散方向
        if 'max_spread_area' not in data:
            area = data.get('area', 500)
            data['max_spread_area'] = area * 1.5  # 默认为当前面积的1.5倍

        # 风险评估字段
        if 'threat_index' not in data:
            confidence = data.get('confidence', 0.5)
            area = data.get('area', 500)
            # 基于置信度和面积计算威胁指数
            data['threat_index'] = min(confidence * (area / 1000), 1.0)

        # 基础字段默认值
        if 'density' not in data:
            data['density'] = 0.5  # 默认中等密度
        if 'duration' not in data:
            data['duration'] = 2.0  # 默认2秒

    def _update_alert_manager(self):
        """更新预警管理器（已改为由主窗口统一调用）"""
        try:
            # 注释掉自动调用，改为由主窗口统一传递火焰、烟雾、热源数据
            # print(f"🚨 火焰烟雾分析组件更新预警管理器: {len(self.fire_targets)}个火焰, {len(self.smoke_targets)}个烟雾")
            # self.alert_manager.update_detection_data(self.fire_targets, self.smoke_targets)
            # print(f"✅ 预警管理器数据更新完成")
            pass
        except Exception as e:
            self.logger.error(f"更新预警管理器失败: {e}")
            print(f"❌ 预警管理器更新失败: {e}")

    def on_alert_triggered(self, alert_level: str, alert_data: dict):
        """处理预警触发事件"""
        try:
            self.logger.info(f"预警触发: {alert_level}")
            self.logger.info(f"预警数据: {alert_data}")

            # 可以在这里添加额外的处理逻辑
            # 比如记录日志、发送通知等

        except Exception as e:
            self.logger.error(f"处理预警触发失败: {e}")

    def get_alert_manager(self):
        """获取预警管理器"""
        return self.alert_manager

    def set_alert_thresholds(self, level: str, thresholds: dict):
        """设置预警阈值"""
        if self.alert_manager:
            self.alert_manager.set_alert_thresholds(level, thresholds)

    def reset_alert_cooldown(self, alert_level: str = None):
        """重置预警冷却时间"""
        if self.alert_manager:
            self.alert_manager.reset_alert_cooldown(alert_level)
