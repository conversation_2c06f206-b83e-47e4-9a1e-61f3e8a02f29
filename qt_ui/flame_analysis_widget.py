#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
火焰分析显示组件
在侧边栏中显示火焰闪烁频率和形状分析的详细指标
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                             QFrame, QGroupBox, QGridLayout, QScrollArea,
                             QSizePolicy, QSpacerItem)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QPalette, QColor

from utils.logger import get_logger
from utils.cross_platform_fonts import get_ui_font_family
from qt_ui.accordion_widget import AccordionSection
from typing import Dict, List, Optional, Any


class FlameAnalysisWidget(QWidget):
    """火焰分析显示组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger("FlameAnalysisWidget")
        
        # 火焰分析数据
        self.flame_analysis_data = {}
        self.has_flame_data = False

        # 手风琴区块列表
        self.flame_accordion_sections = {}  # 存储每个火焰的手风琴区块
        
        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(8)
        
        # 标题
        title_label = QLabel("🔥 火焰分析")
        title_label.setStyleSheet("""
            font-size: 14px; 
            font-weight: bold; 
            color: #ff6600; 
            padding: 5px;
            background-color: #2a2a2a;
            border-radius: 3px;
        """)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # 火焰总数显示
        self.flame_count_label = QLabel("🔍 检测到 0 个火焰目标")
        self.flame_count_label.setStyleSheet("""
            font-size: 12px;
            font-weight: bold;
            color: #cccccc;
            padding: 8px;
            background-color: #2a2a2a;
            border-radius: 3px;
            border: 1px solid #505050;
            margin: 2px 0px;
        """)
        self.flame_count_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.flame_count_label)

        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        # 内容容器 - 用于放置手风琴区块
        content_widget = QWidget()
        self.content_layout = QVBoxLayout(content_widget)
        self.content_layout.setContentsMargins(5, 5, 5, 5)
        self.content_layout.setSpacing(5)

        # 无火焰时的提示
        self.no_flame_label = QLabel("🔍 等待火焰检测...")
        self.no_flame_label.setStyleSheet("""
            font-size: 12px;
            color: #888888;
            padding: 20px;
            text-align: center;
        """)
        self.no_flame_label.setAlignment(Qt.AlignCenter)
        self.content_layout.addWidget(self.no_flame_label)

        # 添加弹性空间
        self.content_layout.addItem(QSpacerItem(20, 10, QSizePolicy.Minimum, QSizePolicy.Expanding))
        
        scroll_area.setWidget(content_widget)
        layout.addWidget(scroll_area)
        
        # 设置整体样式
        font_family = get_ui_font_family()
        self.setStyleSheet(f"""
            QWidget {{
                background-color: #323232;
                color: white;
                font-family: {font_family};
            }}
            QGroupBox {{
                font-size: 12px;
                font-weight: bold;
                border: 1px solid #505050;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 5px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }}
            QLabel {{
                color: white;
                font-size: 11px;
            }}
        """)
    
    def create_status_indicator(self, layout):
        """创建状态指示器"""
        status_frame = QFrame()
        status_frame.setFrameStyle(QFrame.Box)
        status_frame.setStyleSheet("""
            QFrame {
                border: 1px solid #505050;
                border-radius: 5px;
                padding: 5px;
                background-color: #2a2a2a;
            }
        """)
        
        status_layout = QHBoxLayout(status_frame)
        status_layout.setContentsMargins(8, 5, 8, 5)
        
        # 状态标签
        status_label = QLabel("分析状态:")
        status_label.setStyleSheet("font-weight: bold; color: #cccccc;")
        status_layout.addWidget(status_label)
        
        # 状态指示
        self.status_indicator = QLabel("无数据")
        self.status_indicator.setStyleSheet("""
            color: #888888;
            font-weight: bold;
            padding: 2px 8px;
            border-radius: 3px;
            background-color: #404040;
        """)
        status_layout.addWidget(self.status_indicator)

        status_layout.addStretch()

        layout.addWidget(status_frame)
    
    def create_flicker_analysis_panel(self, layout):
        """创建火焰闪烁频率分析面板"""
        flicker_group = QGroupBox("闪烁频率分析")
        flicker_group.setStyleSheet("QGroupBox::title { color: #ffaa00; }")
        
        flicker_layout = QGridLayout(flicker_group)
        flicker_layout.setSpacing(5)
        
        # 检测状态
        flicker_layout.addWidget(QLabel("火焰闪烁:"), 0, 0)
        self.flicker_detected_label = QLabel("--")
        flicker_layout.addWidget(self.flicker_detected_label, 0, 1)
        
        # 主导频率
        flicker_layout.addWidget(QLabel("主导频率:"), 1, 0)
        self.dominant_frequency_label = QLabel("-- Hz")
        flicker_layout.addWidget(self.dominant_frequency_label, 1, 1)
        
        # 能量占比
        flicker_layout.addWidget(QLabel("能量占比:"), 2, 0)
        self.energy_ratio_label = QLabel("--%")
        flicker_layout.addWidget(self.energy_ratio_label, 2, 1)
        
        # 分析置信度
        flicker_layout.addWidget(QLabel("置信度:"), 3, 0)
        self.flicker_confidence_label = QLabel("--%")
        flicker_layout.addWidget(self.flicker_confidence_label, 3, 1)
        
        # 数据点数
        flicker_layout.addWidget(QLabel("数据点:"), 4, 0)
        self.data_points_label = QLabel("--")
        flicker_layout.addWidget(self.data_points_label, 4, 1)
        
        layout.addWidget(flicker_group)
    
    def create_morphology_analysis_panel(self, layout):
        """创建火焰形状分析面板"""
        morphology_group = QGroupBox("形状分析")
        morphology_group.setStyleSheet("QGroupBox::title { color: #ff6600; }")
        
        morphology_layout = QGridLayout(morphology_group)
        morphology_layout.setSpacing(5)
        
        # 火焰形状检测
        morphology_layout.addWidget(QLabel("火焰形状:"), 0, 0)
        self.flame_shape_label = QLabel("--")
        morphology_layout.addWidget(self.flame_shape_label, 0, 1)
        
        # 圆度
        morphology_layout.addWidget(QLabel("圆度:"), 1, 0)
        self.circularity_label = QLabel("--")
        morphology_layout.addWidget(self.circularity_label, 1, 1)
        
        # 长宽比
        morphology_layout.addWidget(QLabel("长宽比:"), 2, 0)
        self.aspect_ratio_label = QLabel("--")
        morphology_layout.addWidget(self.aspect_ratio_label, 2, 1)
        
        # 坚实度
        morphology_layout.addWidget(QLabel("坚实度:"), 3, 0)
        self.solidity_label = QLabel("--")
        morphology_layout.addWidget(self.solidity_label, 3, 1)
        
        # 边界粗糙度
        morphology_layout.addWidget(QLabel("粗糙度:"), 4, 0)
        self.roughness_label = QLabel("--")
        morphology_layout.addWidget(self.roughness_label, 4, 1)
        
        # 形状变化率
        morphology_layout.addWidget(QLabel("变化率:"), 5, 0)
        self.shape_change_rate_label = QLabel("--%/s")
        morphology_layout.addWidget(self.shape_change_rate_label, 5, 1)
        
        # 形状置信度
        morphology_layout.addWidget(QLabel("置信度:"), 6, 0)
        self.morphology_confidence_label = QLabel("--%")
        morphology_layout.addWidget(self.morphology_confidence_label, 6, 1)
        
        layout.addWidget(morphology_group)
    
    def create_comprehensive_assessment_panel(self, layout):
        """创建综合评估面板"""
        assessment_group = QGroupBox("综合评估")
        assessment_group.setStyleSheet("QGroupBox::title { color: #ff0000; }")
        
        assessment_layout = QVBoxLayout(assessment_group)
        assessment_layout.setSpacing(8)
        
        # 火焰可能性
        flame_possibility_layout = QHBoxLayout()
        flame_possibility_layout.addWidget(QLabel("火焰可能性:"))
        self.flame_possibility_label = QLabel("--")
        self.flame_possibility_label.setStyleSheet("font-weight: bold; font-size: 12px;")
        flame_possibility_layout.addWidget(self.flame_possibility_label)
        flame_possibility_layout.addStretch()
        assessment_layout.addLayout(flame_possibility_layout)
        
        # 综合置信度
        confidence_layout = QHBoxLayout()
        confidence_layout.addWidget(QLabel("综合置信度:"))
        self.comprehensive_confidence_label = QLabel("--%")
        self.comprehensive_confidence_label.setStyleSheet("font-weight: bold; font-size: 12px;")
        confidence_layout.addWidget(self.comprehensive_confidence_label)
        confidence_layout.addStretch()
        assessment_layout.addLayout(confidence_layout)
        
        # 判断依据
        self.assessment_reasons_label = QLabel("判断依据: --")
        self.assessment_reasons_label.setStyleSheet("""
            color: #cccccc;
            font-size: 10px;
            padding: 5px;
            background-color: #2a2a2a;
            border-radius: 3px;
        """)
        self.assessment_reasons_label.setWordWrap(True)
        assessment_layout.addWidget(self.assessment_reasons_label)
        
        layout.addWidget(assessment_group)

    def update_flame_analysis_data(self, flame_analysis_results: Dict):
        """
        更新火焰分析数据 - 支持集成检测器结果

        Args:
            flame_analysis_results: 从集成火焰烟雾检测器获取的分析结果
        """
        try:
            # 兼容新的集成检测器结果格式
            if self._is_integrated_detector_result(flame_analysis_results):
                self._update_from_integrated_detector(flame_analysis_results)
            else:
                # 保持对原有格式的兼容
                self._update_from_legacy_format(flame_analysis_results)

        except Exception as e:
            self.logger.error(f"更新火焰分析数据失败: {e}")

    def _is_integrated_detector_result(self, results: Dict) -> bool:
        """检查是否为集成检测器结果格式"""
        return ('fire_detections' in results or 'smoke_detections' in results or
                'fire_objects' in results or 'smoke_objects' in results)

    def _update_from_integrated_detector(self, results: Dict):
        """从集成检测器结果更新数据"""
        # 获取火焰和烟雾检测结果
        fire_detections = results.get('fire_detections', [])
        smoke_detections = results.get('smoke_detections', [])
        fire_objects = results.get('fire_objects', {})
        smoke_objects = results.get('smoke_objects', {})

        # 转换为火焰分析数据格式
        flame_analysis_data = {}

        # 处理火焰目标
        for obj_id, obj_data in fire_objects.items():
            flame_analysis_data[f"fire_{obj_id}"] = {
                'flame_id': f"fire_{obj_id}",
                'bbox': obj_data.get('bbox', [0, 0, 0, 0]),
                'confidence': obj_data.get('confidence', 0.0),
                'centroid': obj_data.get('centroid', (0, 0)),
                'frame_count': obj_data.get('frame_count', 1),
                'total_distance': obj_data.get('total_distance', 0.0),
                'class_name': 'fire',
                # 默认分析数据（可以后续从分析引擎获取）
                'flicker_frequency': 0.0,
                'flicker_intensity': 0.0,
                'area': self._calculate_area(obj_data.get('bbox', [0, 0, 0, 0])),
                'aspect_ratio': self._calculate_aspect_ratio(obj_data.get('bbox', [0, 0, 0, 0])),
                'movement_direction': 0.0,
                'movement_speed': 0.0,
                'trajectory_stability': 1.0
            }

        # 处理烟雾目标
        for obj_id, obj_data in smoke_objects.items():
            flame_analysis_data[f"smoke_{obj_id}"] = {
                'flame_id': f"smoke_{obj_id}",
                'bbox': obj_data.get('bbox', [0, 0, 0, 0]),
                'confidence': obj_data.get('confidence', 0.0),
                'centroid': obj_data.get('centroid', (0, 0)),
                'frame_count': obj_data.get('frame_count', 1),
                'total_distance': obj_data.get('total_distance', 0.0),
                'class_name': 'smoke',
                # 默认分析数据
                'flicker_frequency': 0.0,
                'flicker_intensity': 0.0,
                'area': self._calculate_area(obj_data.get('bbox', [0, 0, 0, 0])),
                'aspect_ratio': self._calculate_aspect_ratio(obj_data.get('bbox', [0, 0, 0, 0])),
                'movement_direction': 0.0,
                'movement_speed': 0.0,
                'trajectory_stability': 1.0
            }

        # 更新界面
        self._update_ui_with_data(flame_analysis_data)

    def _calculate_area(self, bbox: List[int]) -> float:
        """计算边界框面积"""
        if len(bbox) >= 4:
            return (bbox[2] - bbox[0]) * (bbox[3] - bbox[1])
        return 0.0

    def _calculate_aspect_ratio(self, bbox: List[int]) -> float:
        """计算边界框宽高比"""
        if len(bbox) >= 4:
            width = bbox[2] - bbox[0]
            height = bbox[3] - bbox[1]
            return width / height if height > 0 else 0.0
        return 0.0

    def _update_ui_with_data(self, flame_analysis_data: Dict):
        """使用分析数据更新UI"""
        self.flame_analysis_data = flame_analysis_data
        self.has_flame_data = len(flame_analysis_data) > 0

        if self.has_flame_data:
            self._update_accordion_sections()
            self._update_summary_stats()
        else:
            self._clear_display()

    def _update_from_legacy_format(self, flame_analysis_results: Dict):
        """从原有格式更新数据（保持兼容性）"""
        
        # 兼容新的数据格式
        if isinstance(flame_analysis_results, dict):
            # 检查是否是新的流水线格式
            if 'tracked_flames' in flame_analysis_results and 'flame_analysis' in flame_analysis_results:
                # 新格式：转换为旧格式兼容
                converted_results = self._convert_pipeline_results(flame_analysis_results)
                self.flame_analysis_data = converted_results
            else:
                # 旧格式：直接使用
                self.flame_analysis_data = flame_analysis_results
        else:
            self.flame_analysis_data = {}

        self.has_flame_data = bool(self.flame_analysis_data)

        # 更新火焰总数显示
        flame_count = len(self.flame_analysis_data) if self.flame_analysis_data else 0
        self._update_flame_count_display(flame_count)

        if not self.has_flame_data:
            self._clear_accordion_display()
            return

        # 更新手风琴区块
        self._update_accordion_sections(self.flame_analysis_data)

    def _convert_pipeline_results(self, pipeline_results: Dict) -> Dict:
        """将新的流水线结果转换为旧格式兼容"""
        try:
            converted = {}

            tracked_flames = pipeline_results.get('tracked_flames', [])
            analysis_results = pipeline_results.get('flame_analysis', [])



            # 创建分析结果映射
            analysis_map = {result.flame_id: result for result in analysis_results}

            for tracked_flame in tracked_flames:
                flame_id = str(tracked_flame.flame_id)
                analysis_result = analysis_map.get(tracked_flame.flame_id)



                # 构建兼容的数据结构
                flame_data = {
                    'tracking_id': tracked_flame.flame_id,
                    'confidence': tracked_flame.current_candidate.confidence,
                    'area': tracked_flame.current_candidate.area,
                    'center': tracked_flame.current_candidate.center,
                    'duration_seconds': tracked_flame.get_duration_seconds(),
                    'total_detections': tracked_flame.total_detections,
                    'state': tracked_flame.state.value,

                    # 闪烁分析（如果有）
                    'flicker_analysis': None,

                    # 形状分析（如果有）
                    'morphology_analysis': None,

                    # 综合评估
                    'is_potential_flame': {
                        'is_flame': False,
                        'confidence': tracked_flame.current_candidate.confidence,
                        'reasons': []
                    }
                }

                # 如果有分析结果，添加详细信息
                if analysis_result:
                    # 闪烁分析
                    if analysis_result.has_flicker:
                        flame_data['flicker_analysis'] = {
                            'is_flame_flicker': True,
                            'dominant_frequency': analysis_result.dominant_frequency,
                            'confidence': analysis_result.flicker_confidence,
                            'energy_ratio': analysis_result.energy_ratio
                        }

                    # 形状分析
                    if analysis_result.has_flame_shape:
                        flame_data['morphology_analysis'] = {
                            'is_flame_shape': True,
                            'confidence': analysis_result.shape_confidence,
                            'shape_change_rate': analysis_result.shape_change_rate,
                            'dynamic_score': analysis_result.dynamic_score,
                            'shape_features': {
                                'circularity': 0.5,  # 默认值
                                'solidity': 0.7,
                                'aspect_ratio': 1.5,
                                'roughness': 0.3
                            }
                        }

                    # 综合评估
                    flame_data['is_potential_flame'] = {
                        'is_flame': analysis_result.is_real_flame,
                        'confidence': analysis_result.flame_probability,
                        'reasons': [
                            f"火焰概率: {analysis_result.flame_probability:.2f}",
                            f"风险级别: {analysis_result.risk_level}",
                            f"分析置信度: {analysis_result.analysis_confidence:.2f}"
                        ]
                    }

                converted[flame_id] = flame_data



            return converted

        except Exception as e:
            self.logger.error(f"转换流水线结果失败: {e}")
            return {}

    def _clear_display(self):
        """清除显示内容"""
        # 更新状态
        self.status_indicator.setText("无火焰分析数据")
        self.status_indicator.setStyleSheet(
            "color: #888888; "
            "font-weight: bold; "
            "padding: 2px 8px; "
            "border-radius: 3px; "
            "background-color: #404040;"
        )

        # 清除闪烁分析
        self.flicker_detected_label.setText("--")
        self.dominant_frequency_label.setText("-- Hz")
        self.energy_ratio_label.setText("--%")
        self.flicker_confidence_label.setText("--%")
        self.data_points_label.setText("--")

        # 清除形状分析
        self.flame_shape_label.setText("--")
        self.circularity_label.setText("--")
        self.aspect_ratio_label.setText("--")
        self.solidity_label.setText("--")
        self.roughness_label.setText("--")
        self.shape_change_rate_label.setText("--%/s")
        self.morphology_confidence_label.setText("--%")

        # 清除综合评估
        self.flame_possibility_label.setText("--")
        self.comprehensive_confidence_label.setText("--%")
        self.assessment_reasons_label.setText("判断依据: --")

    def _update_status_indicator(self):
        """更新状态指示器"""
        # 检查是否有疑似火焰
        has_potential_flame = any(
            region_data.get('is_potential_flame', {}).get('is_flame', False)
            for region_data in self.flame_analysis_data.values()
        )

        if has_potential_flame:
            self.status_indicator.setText("检测到疑似火焰")
            self.status_indicator.setStyleSheet(
                "color: #ffffff; "
                "font-weight: bold; "
                "padding: 2px 8px; "
                "border-radius: 3px; "
                "background-color: #ff4444;"
            )
        else:
            self.status_indicator.setText("正在分析")
            self.status_indicator.setStyleSheet(
                "color: #ffffff; "
                "font-weight: bold; "
                "padding: 2px 8px; "
                "border-radius: 3px; "
                "background-color: #0078d4;"
            )

    def _update_flicker_display(self, flicker_result):
        """更新闪烁分析显示"""
        if not flicker_result:
            return

        # 火焰闪烁检测
        if flicker_result.is_flame_flicker:
            self.flicker_detected_label.setText("是")
            self.flicker_detected_label.setStyleSheet("color: #ff4444; font-weight: bold;")
        else:
            self.flicker_detected_label.setText("否")
            self.flicker_detected_label.setStyleSheet("color: #888888;")

        # 主导频率
        freq = flicker_result.dominant_frequency
        self.dominant_frequency_label.setText(f"{freq:.1f} Hz")
        if freq > 0:
            self.dominant_frequency_label.setStyleSheet("color: #00ffff;")
        else:
            self.dominant_frequency_label.setStyleSheet("color: #888888;")

        # 能量占比
        energy_ratio = flicker_result.flicker_band_energy_ratio * 100
        self.energy_ratio_label.setText(f"{energy_ratio:.1f}%")
        if energy_ratio > 30:  # 阈值通常是30%
            self.energy_ratio_label.setStyleSheet("color: #ffaa00;")
        else:
            self.energy_ratio_label.setStyleSheet("color: #888888;")

        # 置信度
        confidence = flicker_result.confidence * 100
        self.flicker_confidence_label.setText(f"{confidence:.1f}%")
        if confidence > 50:
            self.flicker_confidence_label.setStyleSheet("color: #00ff00;")
        else:
            self.flicker_confidence_label.setStyleSheet("color: #888888;")

        # 数据点数
        self.data_points_label.setText(str(flicker_result.data_points_count))
        if flicker_result.data_points_count >= 32:  # 足够的数据点
            self.data_points_label.setStyleSheet("color: #00ff00;")
        else:
            self.data_points_label.setStyleSheet("color: #ffaa00;")

    def _update_morphology_display(self, morphology_result):
        """更新形状分析显示"""
        if not morphology_result:
            return

        # 火焰形状检测
        if morphology_result.is_flame_shape:
            self.flame_shape_label.setText("是")
            self.flame_shape_label.setStyleSheet("color: #ff4444; font-weight: bold;")
        else:
            self.flame_shape_label.setText("否")
            self.flame_shape_label.setStyleSheet("color: #888888;")

        # 形状特征
        features = morphology_result.shape_features

        # 圆度
        circularity = features.circularity
        self.circularity_label.setText(f"{circularity:.3f}")
        if circularity < 0.6:  # 火焰通常圆度较低
            self.circularity_label.setStyleSheet("color: #ffaa00;")
        else:
            self.circularity_label.setStyleSheet("color: #888888;")

        # 长宽比
        aspect_ratio = features.aspect_ratio
        self.aspect_ratio_label.setText(f"{aspect_ratio:.2f}")
        if 0.1 <= aspect_ratio <= 1.0:  # 火焰的典型长宽比范围
            self.aspect_ratio_label.setStyleSheet("color: #00ffff;")
        else:
            self.aspect_ratio_label.setStyleSheet("color: #888888;")

        # 坚实度
        solidity = features.solidity
        self.solidity_label.setText(f"{solidity:.3f}")
        if 0.3 <= solidity <= 0.9:  # 火焰的典型坚实度范围
            self.solidity_label.setStyleSheet("color: #00ffff;")
        else:
            self.solidity_label.setStyleSheet("color: #888888;")

        # 边界粗糙度
        roughness = features.roughness
        self.roughness_label.setText(f"{roughness:.3f}")
        if roughness > 0.1:  # 火焰边界通常较粗糙
            self.roughness_label.setStyleSheet("color: #ffaa00;")
        else:
            self.roughness_label.setStyleSheet("color: #888888;")

        # 形状变化率
        change_rate = morphology_result.shape_change_rate * 100
        self.shape_change_rate_label.setText(f"{change_rate:.2f}%/s")
        if change_rate > 5:  # 火焰通常有较高的变化率
            self.shape_change_rate_label.setStyleSheet("color: #ffaa00;")
        else:
            self.shape_change_rate_label.setStyleSheet("color: #888888;")

        # 形状置信度
        confidence = morphology_result.confidence * 100
        self.morphology_confidence_label.setText(f"{confidence:.1f}%")
        if confidence > 50:
            self.morphology_confidence_label.setStyleSheet("color: #00ff00;")
        else:
            self.morphology_confidence_label.setStyleSheet("color: #888888;")

    def _update_comprehensive_assessment(self, flame_potential):
        """更新综合评估显示"""
        if not flame_potential:
            return

        # 火焰可能性
        if flame_potential['is_flame']:
            self.flame_possibility_label.setText("高")
            self.flame_possibility_label.setStyleSheet("color: #ff4444; font-weight: bold;")
        else:
            self.flame_possibility_label.setText("低")
            self.flame_possibility_label.setStyleSheet("color: #888888; font-weight: bold;")

        # 综合置信度
        confidence = flame_potential['confidence'] * 100
        self.comprehensive_confidence_label.setText(f"{confidence:.1f}%")
        if confidence > 70:
            self.comprehensive_confidence_label.setStyleSheet("color: #ff4444; font-weight: bold;")
        elif confidence > 40:
            self.comprehensive_confidence_label.setStyleSheet("color: #ffaa00; font-weight: bold;")
        else:
            self.comprehensive_confidence_label.setStyleSheet("color: #888888; font-weight: bold;")

        # 判断依据
        reasons = flame_potential.get('reasons', [])
        if reasons:
            reasons_text = "判断依据: " + "; ".join(reasons)
        else:
            reasons_text = "判断依据: 无足够数据"

        self.assessment_reasons_label.setText(reasons_text)

    def _update_flame_count_display(self, count: int):
        """更新火焰总数显示"""
        if count == 0:
            self.flame_count_label.setText("🔍 检测到 0 个火焰目标")
            self.flame_count_label.setStyleSheet(
                "font-size: 12px; "
                "font-weight: bold; "
                "color: #888888; "
                "padding: 8px; "
                "background-color: #2a2a2a; "
                "border-radius: 3px; "
                "border: 1px solid #505050; "
                "margin: 2px 0px;"
            )
        else:
            self.flame_count_label.setText(f"🔥 检测到 {count} 个火焰目标")
            self.flame_count_label.setStyleSheet(
                "font-size: 12px; "
                "font-weight: bold; "
                "color: #ff6600; "
                "padding: 8px; "
                "background-color: #2a2a2a; "
                "border-radius: 3px; "
                "border: 1px solid #ff6600; "
                "margin: 2px 0px;"
            )

    def _clear_accordion_display(self):
        """清空手风琴显示"""
        # 移除所有现有的手风琴区块
        for flame_id, section in self.flame_accordion_sections.items():
            self.content_layout.removeWidget(section)
            section.deleteLater()

        self.flame_accordion_sections.clear()

        # 显示无火焰提示
        self.no_flame_label.show()

    def _update_accordion_sections(self, flame_analysis_results: Dict):
        """更新手风琴区块"""


        # 隐藏无火焰提示
        self.no_flame_label.hide()

        # 获取当前存在的火焰ID
        current_flame_ids = set(self.flame_accordion_sections.keys())
        new_flame_ids = set(flame_analysis_results.keys())



        # 移除不再存在的火焰区块
        removed_ids = current_flame_ids - new_flame_ids
        for flame_id in removed_ids:
            section = self.flame_accordion_sections[flame_id]
            self.content_layout.removeWidget(section)
            section.deleteLater()
            del self.flame_accordion_sections[flame_id]

        # 添加或更新火焰区块
        for flame_id, flame_data in flame_analysis_results.items():
            if flame_id in self.flame_accordion_sections:
                # 更新现有区块
                self._update_flame_section(flame_id, flame_data)
            else:
                # 创建新区块
                self._create_flame_section(flame_id, flame_data)

    def _create_flame_section(self, flame_id: str, flame_data: Dict):
        """创建单个火焰的手风琴区块"""
        try:
            # 获取火焰基本信息
            flame_potential = flame_data.get('is_potential_flame', {})
            confidence = flame_potential.get('confidence', 0.0) * 100

            # 根据置信度确定标题样式
            if confidence >= 70:
                icon = "🔥"
                title_color = "#ff4444"
                desc = "火焰"
            elif confidence >= 40:
                icon = "🟡"
                title_color = "#ffaa00"
                desc = "疑似火焰"
            else:
                icon = "⚠️"
                title_color = "#888888"
                desc = "疑似目标"

            # 创建标题
            title = f"{icon} {desc} {flame_id} (置信度: {confidence:.0f}%)"

            # 创建内容组件
            content_widget = self._create_flame_content_widget(flame_id, flame_data)

            # 创建手风琴区块
            section = AccordionSection(title, content_widget)

            # 设置区块样式
            section.setStyleSheet(f"""
                AccordionSection {{
                    background-color: #323232;
                    border: 1px solid #505050;
                    border-radius: 5px;
                    margin: 2px 0px;
                }}
                AccordionSection QPushButton {{
                    background-color: #404040;
                    border: none;
                    color: {title_color};
                    font-weight: bold;
                    font-size: 11px;
                    padding: 8px;
                    text-align: left;
                }}
                AccordionSection QPushButton:hover {{
                    background-color: #4a4a4a;
                }}
            """)

            # 添加到布局
            insert_index = len(self.flame_accordion_sections)
            self.content_layout.insertWidget(insert_index, section)

            # 保存引用
            self.flame_accordion_sections[flame_id] = section

        except Exception as e:
            self.logger.error(f"创建火焰区块失败 {flame_id}: {e}")

    def _update_flame_section(self, flame_id: str, flame_data: Dict):
        """更新现有火焰区块的数据"""
        try:
            if flame_id not in self.flame_accordion_sections:
                return

            section = self.flame_accordion_sections[flame_id]

            # 更新标题
            flame_potential = flame_data.get('is_potential_flame', {})
            confidence = flame_potential.get('confidence', 0.0) * 100

            if confidence >= 70:
                icon = "🔥"
                desc = "火焰"
            elif confidence >= 40:
                icon = "🟡"
                desc = "疑似火焰"
            else:
                icon = "⚠️"
                desc = "疑似目标"

            new_title = f"{icon} {desc} {flame_id} (置信度: {confidence:.0f}%)"
            # 更新标题标签
            if hasattr(section, 'chinese_title_label'):
                section.chinese_title_label.setText(new_title)

            # 更新内容
            if section.content_widget:
                self._update_flame_content_widget(section.content_widget, flame_id, flame_data)

        except Exception as e:
            self.logger.error(f"更新火焰区块失败 {flame_id}: {e}")

    def _create_flame_content_widget(self, flame_id: str, flame_data: Dict) -> QWidget:
        """创建火焰详细分析内容组件"""
        content_widget = QWidget()
        layout = QVBoxLayout(content_widget)
        layout.setContentsMargins(10, 5, 10, 5)
        layout.setSpacing(8)

        # 闪烁频率分析
        flicker_group = self._create_flicker_analysis_group(flame_data.get('flicker_analysis'))
        layout.addWidget(flicker_group)

        # 形状分析
        morphology_group = self._create_morphology_analysis_group(flame_data.get('morphology_analysis'))
        layout.addWidget(morphology_group)

        # 综合评估
        assessment_group = self._create_comprehensive_assessment_group(flame_data.get('is_potential_flame'))
        layout.addWidget(assessment_group)

        return content_widget

    def _update_flame_content_widget(self, content_widget: QWidget, flame_id: str, flame_data: Dict):
        """更新火焰内容组件的数据"""
        try:
            # 这里可以实现更细粒度的数据更新
            # 为了简化，我们重新创建内容
            layout = content_widget.layout()

            # 清空现有内容
            while layout.count():
                child = layout.takeAt(0)
                if child.widget():
                    child.widget().deleteLater()

            # 重新添加内容
            flicker_group = self._create_flicker_analysis_group(flame_data.get('flicker_analysis'))
            layout.addWidget(flicker_group)

            morphology_group = self._create_morphology_analysis_group(flame_data.get('morphology_analysis'))
            layout.addWidget(morphology_group)

            assessment_group = self._create_comprehensive_assessment_group(flame_data.get('is_potential_flame'))
            layout.addWidget(assessment_group)

        except Exception as e:
            self.logger.error(f"更新火焰内容失败 {flame_id}: {e}")

    def _create_flicker_analysis_group(self, flicker_analysis) -> QGroupBox:
        """创建闪烁频率分析组"""
        group = QGroupBox("📊 闪烁频率分析")
        group.setStyleSheet("""
            QGroupBox {
                font-size: 11px;
                font-weight: bold;
                border: 1px solid #505050;
                border-radius: 3px;
                margin-top: 8px;
                padding-top: 5px;
                color: #00ffff;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 8px;
                padding: 0 3px 0 3px;
            }
        """)

        layout = QGridLayout(group)
        layout.setContentsMargins(8, 15, 8, 8)
        layout.setSpacing(5)

        if flicker_analysis and hasattr(flicker_analysis, 'dominant_frequency'):
            # 主导频率
            layout.addWidget(QLabel("主导频率:"), 0, 0)
            freq_label = QLabel(f"{flicker_analysis.dominant_frequency:.1f} Hz")
            freq_label.setStyleSheet("color: #00ffff; font-weight: bold;")
            layout.addWidget(freq_label, 0, 1)

            # 置信度
            layout.addWidget(QLabel("置信度:"), 1, 0)
            conf_label = QLabel(f"{flicker_analysis.confidence * 100:.1f}%")
            conf_label.setStyleSheet("color: #00ff00; font-weight: bold;")
            layout.addWidget(conf_label, 1, 1)

            # 能量占比
            layout.addWidget(QLabel("能量占比:"), 2, 0)
            energy_label = QLabel(f"{flicker_analysis.flicker_band_energy_ratio * 100:.1f}%")
            energy_label.setStyleSheet("color: #ffaa00; font-weight: bold;")
            layout.addWidget(energy_label, 2, 1)
        else:
            no_data_label = QLabel("暂无闪烁分析数据")
            no_data_label.setStyleSheet("color: #888888; font-style: italic;")
            layout.addWidget(no_data_label, 0, 0, 1, 2)

        return group

    def _create_morphology_analysis_group(self, morphology_analysis) -> QGroupBox:
        """创建形状分析组"""
        group = QGroupBox("🔍 形状分析")
        group.setStyleSheet("""
            QGroupBox {
                font-size: 11px;
                font-weight: bold;
                border: 1px solid #505050;
                border-radius: 3px;
                margin-top: 8px;
                padding-top: 5px;
                color: #ffaa00;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 8px;
                padding: 0 3px 0 3px;
            }
        """)

        layout = QGridLayout(group)
        layout.setContentsMargins(8, 15, 8, 8)
        layout.setSpacing(5)

        if morphology_analysis and hasattr(morphology_analysis, 'shape_features'):
            features = morphology_analysis.shape_features

            # 火焰形状
            layout.addWidget(QLabel("火焰形状:"), 0, 0)
            shape_label = QLabel("是" if morphology_analysis.is_flame_shape else "否")
            shape_label.setStyleSheet("color: #ff4444; font-weight: bold;" if morphology_analysis.is_flame_shape else "color: #888888;")
            layout.addWidget(shape_label, 0, 1)

            # 圆度
            layout.addWidget(QLabel("圆度:"), 1, 0)
            circ_label = QLabel(f"{features.circularity:.2f}")
            layout.addWidget(circ_label, 1, 1)

            # 长宽比
            layout.addWidget(QLabel("长宽比:"), 2, 0)
            aspect_label = QLabel(f"{features.aspect_ratio:.2f}")
            layout.addWidget(aspect_label, 2, 1)

            # 面积
            layout.addWidget(QLabel("面积:"), 3, 0)
            area_label = QLabel(f"{features.area:.0f} px²")
            layout.addWidget(area_label, 3, 1)
        else:
            no_data_label = QLabel("暂无形状分析数据")
            no_data_label.setStyleSheet("color: #888888; font-style: italic;")
            layout.addWidget(no_data_label, 0, 0, 1, 2)

        return group

    def _create_comprehensive_assessment_group(self, flame_potential) -> QGroupBox:
        """创建综合评估组"""
        group = QGroupBox("🎯 综合评估")
        group.setStyleSheet("""
            QGroupBox {
                font-size: 11px;
                font-weight: bold;
                border: 1px solid #505050;
                border-radius: 3px;
                margin-top: 8px;
                padding-top: 5px;
                color: #ff6600;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 8px;
                padding: 0 3px 0 3px;
            }
        """)

        layout = QGridLayout(group)
        layout.setContentsMargins(8, 15, 8, 8)
        layout.setSpacing(5)

        if flame_potential:
            # 火焰可能性
            layout.addWidget(QLabel("火焰可能性:"), 0, 0)
            possibility = "高" if flame_potential.get('is_potential_flame', False) else "低"
            poss_label = QLabel(possibility)
            poss_label.setStyleSheet("color: #ff4444; font-weight: bold;" if possibility == "高" else "color: #888888;")
            layout.addWidget(poss_label, 0, 1)

            # 综合置信度
            layout.addWidget(QLabel("综合置信度:"), 1, 0)
            confidence = flame_potential.get('confidence', 0.0) * 100
            conf_label = QLabel(f"{confidence:.1f}%")
            if confidence > 70:
                conf_label.setStyleSheet("color: #ff4444; font-weight: bold;")
            elif confidence > 40:
                conf_label.setStyleSheet("color: #ffaa00; font-weight: bold;")
            else:
                conf_label.setStyleSheet("color: #888888;")
            layout.addWidget(conf_label, 1, 1)

            # 判断依据
            reasons = flame_potential.get('reasons', [])
            if reasons:
                layout.addWidget(QLabel("判断依据:"), 2, 0)
                reasons_text = "; ".join(reasons)
                reasons_label = QLabel(reasons_text)
                reasons_label.setWordWrap(True)
                reasons_label.setStyleSheet("color: #cccccc; font-size: 10px;")
                layout.addWidget(reasons_label, 2, 1)
        else:
            no_data_label = QLabel("暂无综合评估数据")
            no_data_label.setStyleSheet("color: #888888; font-style: italic;")
            layout.addWidget(no_data_label, 0, 0, 1, 2)

        return group
