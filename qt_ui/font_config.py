#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字体配置模块
统一管理界面字体大小，确保文字清晰易读
"""

# 字体大小配置
FONT_SIZES = {
    # 标题字体
    'title_large': 20,      # 主标题
    'title_medium': 18,     # 中等标题
    'title_small': 16,      # 小标题
    
    # 正文字体
    'text_large': 14,       # 大正文
    'text_medium': 13,      # 中等正文
    'text_small': 12,       # 小正文
    
    # 控件字体
    'button_large': 14,     # 大按钮
    'button_medium': 13,    # 中等按钮
    'button_small': 12,     # 小按钮
    
    'input_large': 14,      # 大输入框
    'input_medium': 13,     # 中等输入框
    'input_small': 12,      # 小输入框
    
    'label_large': 14,      # 大标签
    'label_medium': 13,     # 中等标签
    'label_small': 12,      # 小标签
    
    # 选项卡字体
    'tab_title': 13,        # 选项卡标题
    'tab_content': 13,      # 选项卡内容
    
    # 手风琴字体
    'accordion_title': 16,  # 手风琴标题
    'accordion_subtitle': 12, # 手风琴副标题（英文部分）
    'accordion_content': 13, # 手风琴内容
    
    # 状态字体
    'status_large': 14,     # 大状态文字
    'status_medium': 13,    # 中等状态文字
    'status_small': 12,     # 小状态文字
}

# 字体族配置
FONT_FAMILIES = {
    'primary': '"Microsoft YaHei", "SimHei", "PingFang SC", "Helvetica Neue", Arial, sans-serif',
    'monospace': '"Consolas", "Monaco", "Courier New", monospace',
    'system': 'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
}

def get_font_size(size_key: str) -> int:
    """获取字体大小"""
    return FONT_SIZES.get(size_key, 13)  # 默认13px

def get_font_family(family_key: str = 'primary') -> str:
    """获取字体族"""
    return FONT_FAMILIES.get(family_key, FONT_FAMILIES['primary'])

def create_font_style(size_key: str, family_key: str = 'primary', 
                     weight: str = 'normal', color: str = 'white') -> str:
    """创建字体样式字符串"""
    size = get_font_size(size_key)
    family = get_font_family(family_key)
    
    return f"""
        font-size: {size}px;
        font-family: {family};
        font-weight: {weight};
        color: {color};
    """

def get_button_style(size_key: str = 'button_medium',
                    bg_color: str = '#404040',
                    border_color: str = '#606060',
                    hover_color: str = '#505050') -> str:
    """获取按钮样式"""
    font_size = get_font_size(size_key)
    font_family = get_font_family('primary')

    return f"""
        QPushButton {{
            background-color: {bg_color};
            border: 1px solid {border_color};
            border-radius: 3px;
            padding: 3px 12px;
            color: white;
            font-size: {font_size}px;
            font-family: {font_family};
            font-weight: normal;
            outline: none;
        }}
        QPushButton:hover {{
            background-color: {hover_color};
        }}
        QPushButton:pressed {{
            background-color: #303030;
        }}
        QPushButton:focus {{
            outline: none;
            border: 1px solid #00aaff;
        }}
    """

def get_input_style(size_key: str = 'input_medium',
                   bg_color: str = '#404040',
                   border_color: str = '#606060') -> str:
    """获取输入框样式"""
    font_size = get_font_size(size_key)
    font_family = get_font_family('primary')

    return f"""
        color: white;
        background-color: {bg_color};
        border: 1px solid {border_color};
        border-radius: 3px;
        padding: 4px 8px;
        font-size: {font_size}px;
        font-family: {font_family};
        outline: none;
    """

def get_label_style(size_key: str = 'label_medium',
                   color: str = 'white',
                   weight: str = 'normal') -> str:
    """获取标签样式"""
    font_size = get_font_size(size_key)
    font_family = get_font_family('primary')
    
    return f"""
        color: {color};
        font-size: {font_size}px;
        font-family: {font_family};
        font-weight: {weight};
        padding: 2px;
    """

def get_groupbox_style(size_key: str = 'text_large') -> str:
    """获取分组框样式"""
    font_size = get_font_size(size_key)
    font_family = get_font_family('primary')
    
    return f"""
        QGroupBox {{
            font-weight: bold;
            font-size: {font_size}px;
            font-family: {font_family};
            border: 1px solid #505050;
            border-radius: 3px;
            margin-top: 12px;
            padding-top: 8px;
            color: white;
        }}
        QGroupBox::title {{
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
            font-size: {font_size}px;
            font-family: {font_family};
        }}
    """

def get_tab_style() -> str:
    """获取选项卡样式"""
    tab_font_size = get_font_size('tab_title')
    font_family = get_font_family('primary')
    
    return f"""
        QTabWidget::pane {{
            border: 1px solid #505050;
            background-color: #2b2b2b;
            border-radius: 0 0 5px 5px;
        }}
        
        QTabWidget::tab-bar {{
            alignment: center;
        }}
        
        QTabBar::tab {{
            background-color: #404040;
            border: 1px solid #505050;
            border-bottom: none;
            border-radius: 5px 5px 0 0;
            padding: 8px 16px;
            margin: 0 2px;
            color: white;
            font-size: {tab_font_size}px;
            font-family: {font_family};
            font-weight: bold;
            min-width: 70px;
            outline: none;
        }}
        
        QTabBar::tab:selected {{
            background-color: #0078d4;
            border-color: #106ebe;
            color: white;
        }}
        
        QTabBar::tab:hover:!selected {{
            background-color: #505050;
            border-color: #606060;
        }}
        
        QTabBar::tab:first {{
            margin-left: 0;
        }}
        
        QTabBar::tab:last {{
            margin-right: 0;
        }}
    """

def get_accordion_style() -> str:
    """获取手风琴样式"""
    title_font_size = get_font_size('accordion_title')
    content_font_size = get_font_size('accordion_content')
    font_family = get_font_family('primary')
    
    return f"""
        /* 手风琴标题样式 */
        .accordion-title {{
            color: white;
            font-size: {title_font_size}px;
            font-family: {font_family};
            font-weight: bold;
        }}
        
        /* 手风琴内容样式 */
        .accordion-content {{
            color: white;
            font-size: {content_font_size}px;
            font-family: {font_family};
        }}
        
        /* 手风琴状态样式 */
        .accordion-status {{
            color: #aaaaaa;
            font-size: {get_font_size('status_small')}px;
            font-family: {font_family};
        }}
    """

# 预设样式组合
PRESET_STYLES = {
    'primary_button': get_button_style('button_medium', '#0078d4', '#106ebe', '#106ebe'),
    'secondary_button': get_button_style('button_medium', '#404040', '#606060', '#505050'),
    'danger_button': get_button_style('button_medium', '#dc3545', '#c82333', '#c82333'),
    'success_button': get_button_style('button_medium', '#28a745', '#1e7e34', '#1e7e34'),
    
    'primary_input': get_input_style('input_medium', '#404040', '#606060'),
    'large_input': get_input_style('input_large', '#404040', '#606060'),
    
    'title_label': get_label_style('title_medium', 'white', 'bold'),
    'subtitle_label': get_label_style('title_small', 'white', 'bold'),
    'normal_label': get_label_style('label_medium', 'white', 'normal'),
    'small_label': get_label_style('label_small', '#cccccc', 'normal'),
}

def apply_font_config():
    """应用字体配置到应用程序"""
    from PyQt5.QtWidgets import QApplication
    from PyQt5.QtGui import QFont
    
    app = QApplication.instance()
    if app:
        # 设置应用程序默认字体
        default_font = QFont()
        default_font.setFamily("Microsoft YaHei")
        default_font.setPointSize(10)  # 基础字体大小
        app.setFont(default_font)
        
        print("✅ 字体配置已应用")
    else:
        print("⚠️ 未找到QApplication实例，无法应用字体配置")
