#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Qt主窗口模块
替换OpenCV窗口，实现完整的Qt界面
"""

import sys
import cv2
import numpy as np
import time
from typing import Optional, Tuple, Dict
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                             QLabel, QToolBar, QDockWidget, QApplication,
                             QSizePolicy, QFrame)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QSize
from PyQt5.QtGui import QPixmap, QImage, QPainter, QPen, QColor

# 导入配置模块
from config.camera_config import UI_CONFIG, VIDEO_CONFIG
from config.coordinate_system_manager import get_coordinate_system_manager


class VideoDisplayWidget(QLabel):
    """视频显示控件 - 支持双摄像头画面显示和鼠标事件"""

    # 鼠标事件信号
    mouse_moved = pyqtSignal(int, int)  # x, y
    mouse_clicked = pyqtSignal(int, int, int)  # x, y, button
    mouse_released = pyqtSignal(int, int, int)  # x, y, button

    def __init__(self, parent=None):
        super().__init__(parent)

        # 设置基本属性
        self.setMinimumSize(800, 600)
        self.setScaledContents(False)
        self.setAlignment(Qt.AlignCenter)
        self.setStyleSheet("border: 1px solid gray; background-color: black;")

        # 鼠标跟踪
        self.setMouseTracking(True)

        # 当前显示的图像
        self.current_frame = None
        self.display_width = 640
        self.display_height = 480

        # 工具栏高度（用于坐标调整）
        self.toolbar_height = 50

        # 十字线显示（禁用Qt原生十字线，使用OpenCV叠加）
        self.show_crosshair = False
        self.crosshair_pos = (0, 0)

        # 图像缩放信息
        self.image_scale_x = 1.0
        self.image_scale_y = 1.0
        self.image_offset_x = 0
        self.image_offset_y = 0

    def set_frame(self, frame: np.ndarray):
        """设置要显示的帧"""
        if frame is None:
            return

        self.current_frame = frame.copy()

        # 转换为Qt图像格式
        qt_image = self.convert_cv_to_qt(frame)
        if qt_image:
            pixmap = QPixmap.fromImage(qt_image)
            # 保持宽高比缩放
            scaled_pixmap = pixmap.scaled(self.size(), Qt.KeepAspectRatio, Qt.SmoothTransformation)

            # 计算缩放比例和偏移
            self._calculate_image_transform(frame, scaled_pixmap)

            self.setPixmap(scaled_pixmap)

    def _calculate_image_transform(self, original_frame: np.ndarray, scaled_pixmap: QPixmap):
        """计算图像变换参数"""
        # 原始图像尺寸
        original_height, original_width = original_frame.shape[:2]

        # 缩放后图像尺寸
        scaled_width = scaled_pixmap.width()
        scaled_height = scaled_pixmap.height()

        # 控件尺寸
        widget_width = self.width()
        widget_height = self.height()

        # 计算缩放比例
        self.image_scale_x = scaled_width / original_width
        self.image_scale_y = scaled_height / original_height

        # 计算偏移（图像在控件中的位置）
        self.image_offset_x = (widget_width - scaled_width) // 2
        self.image_offset_y = (widget_height - scaled_height) // 2

    def convert_widget_to_image_coords(self, widget_x: int, widget_y: int) -> tuple:
        """将控件坐标转换为图像坐标"""
        # 减去偏移
        image_x = widget_x - self.image_offset_x
        image_y = widget_y - self.image_offset_y

        # 应用逆缩放
        if self.image_scale_x > 0 and self.image_scale_y > 0:
            original_x = int(image_x / self.image_scale_x)
            original_y = int(image_y / self.image_scale_y)
        else:
            original_x = image_x
            original_y = image_y

        return original_x, original_y

    def convert_cv_to_qt(self, cv_image: np.ndarray) -> Optional[QImage]:
        """将OpenCV图像转换为Qt图像"""
        try:
            if cv_image is None:
                return None

            height, width = cv_image.shape[:2]

            if len(cv_image.shape) == 3:  # BGR图像
                # 转换BGR到RGB
                rgb_image = cv2.cvtColor(cv_image, cv2.COLOR_BGR2RGB)
                qt_image = QImage(rgb_image.data, width, height,
                                width * 3, QImage.Format_RGB888)
            else:  # 灰度图像
                qt_image = QImage(cv_image.data, width, height,
                                width, QImage.Format_Grayscale8)

            return qt_image

        except Exception as e:
            print(f"图像转换失败: {e}")
            return None

    def paintEvent(self, event):
        """重写绘制事件，添加十字线"""
        super().paintEvent(event)

        if self.show_crosshair and self.current_frame is not None:
            painter = QPainter(self)
            painter.setPen(QPen(QColor(255, 255, 255), 1))

            # 绘制十字线
            x, y = self.crosshair_pos
            painter.drawLine(x - 10, y, x + 10, y)
            painter.drawLine(x, y - 10, x, y + 10)

    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        widget_x, widget_y = event.x(), event.y()

        # 转换为图像坐标
        image_x, image_y = self.convert_widget_to_image_coords(widget_x, widget_y)

        # 发射鼠标移动信号（使用图像坐标）
        self.mouse_moved.emit(image_x, image_y)

    def mousePressEvent(self, event):
        """鼠标按下事件"""
        widget_x, widget_y = event.x(), event.y()
        button = event.button()

        # 转换为图像坐标
        image_x, image_y = self.convert_widget_to_image_coords(widget_x, widget_y)

        # 发射鼠标点击信号
        self.mouse_clicked.emit(image_x, image_y, button)

    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        widget_x, widget_y = event.x(), event.y()
        button = event.button()

        # 转换为图像坐标
        image_x, image_y = self.convert_widget_to_image_coords(widget_x, widget_y)

        # 发射鼠标释放信号
        self.mouse_released.emit(image_x, image_y, button)

    def set_crosshair_visible(self, visible: bool):
        """设置十字线可见性"""
        self.show_crosshair = visible
        self.update()


class ThermalCameraMainWindow(QMainWindow):
    """热成像摄像头系统Qt主窗口"""

    def __init__(self):
        super().__init__()

        # 窗口基本设置
        self.setWindowTitle('海康威视双光谱热成像监控系统 - Qt版本')
        self.setMinimumSize(1200, 800)

        # 系统实例引用（稍后设置）
        self.system_instance = None

        # 全局热源分析器实例（确保数据一致性）
        self.heat_source_analyzer = None

        # 监控指标管理器
        self.metrics_manager = None

        # 初始化UI组件
        self.init_ui()

        # 定时器用于更新显示
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_display)

    def init_ui(self):
        """初始化用户界面"""
        # 创建中央控件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # 创建视频显示区域
        self.video_widget = VideoDisplayWidget()
        main_layout.addWidget(self.video_widget)

        # 创建Qt事件处理器
        self._create_event_handlers()

        # 连接鼠标事件
        self.video_widget.mouse_moved.connect(self.on_mouse_moved)
        self.video_widget.mouse_clicked.connect(self.on_mouse_clicked)

        # 创建工具栏（稍后实现）
        self.create_toolbar()

        # 创建侧边栏（稍后实现）
        self.create_sidebar()

        # 设置状态栏
        self.statusBar().showMessage('系统就绪')

    def _create_event_handlers(self):
        """创建事件处理器"""
        from qt_ui.qt_mouse_handler import QtMouseHandler
        from qt_ui.qt_event_handler import QtKeyboardHandler
        from qt_ui.qt_overlay_renderer import QtOverlayRenderer

        # 创建鼠标处理器（基于原有实现）
        self.qt_mouse_handler = QtMouseHandler(self)

        # 创建键盘处理器
        self.qt_keyboard_handler = QtKeyboardHandler(self)

        # 创建叠加渲染器
        self.qt_overlay_renderer = QtOverlayRenderer()

        # 连接事件处理器信号
        self._connect_event_handler_signals()

    def _connect_event_handler_signals(self):
        """连接事件处理器信号"""
        # 连接鼠标处理器信号
        self.qt_mouse_handler.temperature_updated.connect(self.on_temperature_updated)
        self.qt_mouse_handler.position_updated.connect(self.on_position_updated)

        # 连接键盘处理器信号
        self.qt_keyboard_handler.detection_toggle_requested.connect(self.on_detection_toggle)
        self.qt_keyboard_handler.human_detection_toggle_requested.connect(self.on_human_detection_toggle)
        self.qt_keyboard_handler.thermal_human_toggle_requested.connect(self.on_thermal_human_toggle)
        self.qt_keyboard_handler.adaptive_mode_toggle_requested.connect(self.on_adaptive_mode_toggle)
        self.qt_keyboard_handler.threshold_adjust_requested.connect(self.on_threshold_adjust)
        self.qt_keyboard_handler.save_image_requested.connect(self.on_save_image)
        self.qt_keyboard_handler.save_debug_requested.connect(self.on_save_debug)
        self.qt_keyboard_handler.help_toggle_requested.connect(self.on_help_toggle)

        self.qt_keyboard_handler.sidebar_scroll_requested.connect(self.on_sidebar_scroll)
        self.qt_keyboard_handler.quit_requested.connect(self.on_quit_requested)

        # 手动标注鼠标事件将在侧边栏创建后连接

    def create_toolbar(self):
        """创建工具栏"""
        from qt_ui.qt_toolbar import QtThermalToolbar

        # 创建Qt工具栏
        self.qt_toolbar = QtThermalToolbar(self)
        toolbar = self.qt_toolbar.create_toolbar()

        # 添加到主窗口
        self.addToolBar(Qt.TopToolBarArea, toolbar)

        # 连接工具栏信号到系统回调
        self._connect_toolbar_signals()

    def _connect_toolbar_signals(self):
        """连接工具栏信号到系统回调"""
        if not hasattr(self, 'qt_toolbar') or not self.system_instance:
            return

        # 导入回调处理器
        from .toolbar_callbacks import ToolbarCallbackHandler
        self.toolbar_callback_handler = ToolbarCallbackHandler(self.system_instance)

        # 连接各种信号
        self.qt_toolbar.detection_mode_changed.connect(self.toolbar_callback_handler.change_detection_mode)  # 新增检测模式信号
        self.qt_toolbar.video_file_selected.connect(self.toolbar_callback_handler.select_video_file)  # 新增视频文件选择信号
        self.qt_toolbar.detection_toggled.connect(self.toolbar_callback_handler.toggle_detection)
        self.qt_toolbar.human_detection_toggled.connect(self.toolbar_callback_handler.toggle_human_detection)
        self.qt_toolbar.thermal_human_toggled.connect(self.toolbar_callback_handler.toggle_thermal_human_detection)
        self.qt_toolbar.fire_smoke_detection_toggled.connect(self.toolbar_callback_handler.toggle_fire_smoke_detection)

        self.qt_toolbar.threshold_changed.connect(self.toolbar_callback_handler.change_threshold)
        self.qt_toolbar.adaptive_mode_changed.connect(self.toolbar_callback_handler.change_adaptive_mode)
        self.qt_toolbar.save_image_clicked.connect(self.toolbar_callback_handler.save_image)
        self.qt_toolbar.save_debug_clicked.connect(self.toolbar_callback_handler.save_debug_info)
        self.qt_toolbar.export_excel_clicked.connect(self.toolbar_callback_handler.export_excel_data)
        self.qt_toolbar.help_clicked.connect(self.toolbar_callback_handler.show_help)
        self.qt_toolbar.panel_toggled.connect(self.on_panel_toggle_from_toolbar)
        self.qt_toolbar.quit_clicked.connect(self.toolbar_callback_handler.quit_application)

    def on_panel_toggle_from_toolbar(self, checked: bool):
        """处理工具栏面板切换"""
        if hasattr(self, 'sidebar_dock'):
            self.sidebar_dock.setVisible(checked)
            if hasattr(self, 'qt_sidebar'):
                self.qt_sidebar.set_enabled(checked)

        # 初始化工具栏状态
        self._initialize_toolbar_status()

    def _initialize_toolbar_status(self):
        """初始化工具栏状态"""
        if not self.system_instance or not hasattr(self, 'qt_toolbar'):
            return

        # 设置初始状态
        self.qt_toolbar.update_detection_status(getattr(self.system_instance, 'detection_enabled', False))
        self.qt_toolbar.update_human_detection_status(getattr(self.system_instance, 'human_detection_enabled', True))
        self.qt_toolbar.update_thermal_human_status(False)  # 默认关闭


        # 设置阈值相关状态
        if hasattr(self.system_instance, 'heat_detector') and self.system_instance.heat_detector:
            # 获取当前阈值
            threshold = self.system_instance.get_current_threshold()
            self.qt_toolbar.update_threshold_value(threshold)

            # 获取当前模式
            adaptive_mode = self.system_instance.is_adaptive_mode()
            self.qt_toolbar.update_adaptive_mode(adaptive_mode)

    def create_sidebar(self):
        """创建增强侧边栏"""
        from qt_ui.enhanced_sidebar import EnhancedThermalSidebar
        from qt_ui.metrics_manager import MetricsManager
        from qt_ui.manual_annotation_widget import ManualAnnotationWidget
        from qt_ui.manual_annotation_handler import ManualAnnotationHandler, ManualAnnotationRenderer
        from PyQt5.QtWidgets import QScrollArea

        # 创建停靠窗口
        self.sidebar_dock = QDockWidget("热源监控面板", self)
        # 使用自适应宽度，不再固定宽度
        self.sidebar_dock.setMinimumWidth(320)  # 设置最小宽度
        self.sidebar_dock.setMaximumWidth(550)  # 设置最大宽度
        self.sidebar_dock.resize(420, 1000)  # 设置首选大小
        self.sidebar_dock.setFeatures(QDockWidget.DockWidgetMovable | QDockWidget.DockWidgetClosable | QDockWidget.DockWidgetFloatable)

        # 设置停靠窗口的尺寸策略，确保它能够充分利用可用空间
        from PyQt5.QtWidgets import QSizePolicy
        self.sidebar_dock.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Expanding)

        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setFrameShape(QScrollArea.NoFrame)  # 移除边框以获得更好的外观

        # 设置滚动区域的尺寸策略，确保它能够扩展
        scroll_area.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        # 设置滚动区域样式
        scroll_area.setStyleSheet("""
            QScrollArea {
                background-color: #2b2b2b;
                border: none;
            }
            QScrollBar:vertical {
                background-color: #3c3c3c;
                width: 12px;
                border-radius: 6px;
                margin: 0px;
            }
            QScrollBar::handle:vertical {
                background-color: #606060;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #707070;
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                border: none;
                background: none;
            }
        """)

        # 创建选项卡式侧边栏
        from qt_ui.tabbed_sidebar import TabbedSidebar
        self.tabbed_sidebar = TabbedSidebar(self)

        # 创建增强Qt侧边栏（热源监控面板）
        self.qt_sidebar = EnhancedThermalSidebar(self)

        # 创建配置与工具卡片
        from qt_ui.configuration_tools_widget import ConfigurationToolsWidget
        self.configuration_tools_widget = ConfigurationToolsWidget(self)

        # 火焰烟雾数据已集成到侧边栏中

        # 创建原有组件（现在将被整合到配置与工具卡片中）
        self.manual_annotation_widget = ManualAnnotationWidget(self)

        from qt_ui.video_frame_extractor import VideoFrameExtractor
        self.video_frame_extractor = VideoFrameExtractor(self.manual_annotation_widget, self)

        from qt_ui.training_dataset_manager import TrainingDatasetManager
        self.training_dataset_manager = TrainingDatasetManager(self.video_frame_extractor, self)

        # 创建标注同步管理器和界面（在配置工具使用之前）
        from qt_ui.annotation_sync_manager import AnnotationSyncManager
        from qt_ui.annotation_sync_widget import AnnotationSyncWidget
        self.annotation_sync_manager = AnnotationSyncManager(self.manual_annotation_widget.manager)
        self.annotation_sync_widget = AnnotationSyncWidget(self.annotation_sync_manager)

        # 将组件整合到配置与工具卡片中
        self.configuration_tools_widget.set_manual_annotation_widget(self.manual_annotation_widget)
        self.configuration_tools_widget.set_video_processing_widgets(
            self.video_frame_extractor,
            self.training_dataset_manager
        )

        # 将标注同步界面添加到配置与工具卡片中
        if hasattr(self.configuration_tools_widget, 'add_annotation_sync_widget'):
            self.configuration_tools_widget.add_annotation_sync_widget(self.annotation_sync_widget)

        # 添加选项卡页面
        self.tabbed_sidebar.add_monitoring_tab(self.qt_sidebar)
        self.tabbed_sidebar.add_configuration_tab(self.configuration_tools_widget)

        # 默认显示监控页面
        self.tabbed_sidebar.set_current_tab(0)

        # 创建监控指标管理器
        self.metrics_manager = MetricsManager()

        # 创建手动标注处理器和渲染器
        self.manual_annotation_handler = ManualAnnotationHandler(self.manual_annotation_widget)
        self.manual_annotation_renderer = ManualAnnotationRenderer()

        # 设置选项卡式侧边栏到滚动区域
        scroll_area.setWidget(self.tabbed_sidebar)

        # 设置滚动区域到停靠窗口
        self.sidebar_dock.setWidget(scroll_area)
        self.addDockWidget(Qt.RightDockWidgetArea, self.sidebar_dock)

        # 连接侧边栏信号
        self._connect_sidebar_signals()

        # 连接手动标注信号
        self._connect_manual_annotation_signals()

        # 连接标注同步信号
        self._connect_annotation_sync_signals()

        # 连接配置与工具信号
        self._connect_configuration_tools_signals()

    def _connect_sidebar_signals(self):
        """连接侧边栏信号"""
        if hasattr(self, 'tabbed_sidebar'):
            # 连接选项卡切换信号
            self.tabbed_sidebar.tab_changed.connect(self.on_tab_changed)

            # 连接停靠窗口的可见性变化信号
            self.sidebar_dock.visibilityChanged.connect(self.on_sidebar_visibility_changed)

        if hasattr(self, 'qt_sidebar'):
            # 连接模式切换信号
            self.qt_sidebar.mode_changed.connect(self.on_sidebar_mode_changed)

    def on_tab_changed(self, index: int, tab_name: str):
        """处理选项卡切换"""
        print(f"📋 切换到选项卡: {tab_name} (索引: {index})")

        # 更新停靠窗口标题
        if index == 0:  # 监控页面
            self.sidebar_dock.setWindowTitle("热源监控面板")
        elif index == 1:  # 配置页面
            self.sidebar_dock.setWindowTitle("配置与工具面板")

    def on_sidebar_mode_changed(self, mode: str):
        """处理侧边栏模式切换"""
        mode_names = {
            'overview': '概览模式',
            'detailed': '详细模式',
            'expert': '专家模式'
        }
        mode_text = mode_names.get(mode, mode)
        # 只在监控选项卡时更新标题
        if hasattr(self, 'tabbed_sidebar') and self.tabbed_sidebar.get_current_tab_index() == 0:
            self.sidebar_dock.setWindowTitle(f"热源监控面板 - {mode_text}")
        print(f"📋 侧边栏模式切换到: {mode_text}")

    def on_sidebar_visibility_changed(self, visible: bool):
        """处理侧边栏可见性变化"""
        if hasattr(self, 'qt_toolbar'):
            # 更新工具栏面板按钮状态
            self.qt_toolbar.update_panel_status(visible)
        print(f"📋 侧边栏: {'显示' if visible else '隐藏'}")

    def set_system_instance(self, system_instance):
        """设置系统实例引用"""
        self.system_instance = system_instance

        # 配置事件处理器
        self._configure_event_handlers()

        # 如果工具栏已创建，重新连接信号
        if hasattr(self, 'qt_toolbar'):
            self._connect_toolbar_signals()

        # 设置火焰烟雾检测器的界面更新回调
        self._setup_fire_smoke_callback()

        # 同步工具栏状态
        self._sync_toolbar_status()

    def _configure_event_handlers(self):
        """配置事件处理器"""
        if not self.system_instance:
            return

        # 配置鼠标处理器
        if hasattr(self, 'qt_mouse_handler'):
            # 设置显示配置
            self.qt_mouse_handler.set_display_config(640, 640, 480)

            # 设置侧边栏配置
            if hasattr(self, 'qt_sidebar'):
                self.qt_mouse_handler.set_sidebar_config(300, True)

            # 设置温度读取器
            if (hasattr(self.system_instance, 'temperature_manager') and
                self.system_instance.temperature_manager):
                reader = self.system_instance.temperature_manager.get_primary_reader()
                if reader:
                    self.qt_mouse_handler.set_temperature_reader(reader)
                    print(f"✅ Qt鼠标处理器温度读取器已设置: {type(reader).__name__}")
                else:
                    print("❌ 未找到主要温度读取器")
            else:
                print("❌ 温度管理器未找到")

            # 设置温度计算器
            if (hasattr(self.system_instance, 'temperature_calculator') and
                self.system_instance.temperature_calculator):
                self.qt_mouse_handler.set_temperature_calculator(
                    self.system_instance.temperature_calculator.calculate_temperature
                )

            # 设置性能模式
            self.qt_mouse_handler.set_performance_mode("balanced")

        # 配置键盘处理器
        if hasattr(self, 'qt_keyboard_handler'):
            self.qt_keyboard_handler.set_system_instance(self.system_instance)

        # 设置热源信息自动更新定时器
        self.heat_sources_update_timer = QTimer()
        self.heat_sources_update_timer.timeout.connect(self.update_heat_sources_info)
        self.heat_sources_update_timer.start(5000)  # 每5秒更新一次热源信息（降低频率）
        print("✅ 热源信息自动更新已启动")

        # 设置检测状态更新定时器
        self.detection_status_timer = QTimer()
        self.detection_status_timer.timeout.connect(self.update_detection_status)
        self.detection_status_timer.start(2000)  # 每2秒更新一次检测状态
        print("✅ 检测状态自动更新已启动")

        # 启动主显示更新定时器
        self.update_timer.start(33)  # 约30FPS的更新频率
        print("✅ 主显示更新定时器已启动 (30FPS)")

    def update_display(self):
        """更新显示 - 主循环调用"""
        try:
            if not self.system_instance:
                return

            # 获取摄像头帧
            if (hasattr(self.system_instance, 'camera_capture') and
                self.system_instance.camera_capture and
                self.system_instance.camera_capture.is_running):

                ret_visible, ret_thermal, visible_frame, thermal_frame = self.system_instance.camera_capture.read_frames()

                # 处理可见光帧（包括火焰烟雾检测）
                if ret_visible and visible_frame is not None:
                    if (hasattr(self.system_instance, 'frame_processor') and
                        self.system_instance.frame_processor):

                        # 使用帧处理器处理可见光帧（包含火焰烟雾检测）
                        try:
                            # 使用新添加的方法
                            if hasattr(self.system_instance.frame_processor, 'process_visible_frame'):
                                processed_visible_frame = self.system_instance.frame_processor.process_visible_frame(visible_frame)
                            else:
                                # 兼容旧版本
                                processed_visible_frame = self.system_instance.frame_processor._apply_visible_light_detection(visible_frame)

                            # 更新Qt显示
                            if processed_visible_frame is not None:
                                self.video_widget.set_frame(processed_visible_frame)
                        except Exception as e:
                            print(f"⚠️ 处理可见光帧失败: {e}")
                            # 出错时直接显示原始帧
                            self.video_widget.set_frame(visible_frame)
                    else:
                        # 如果没有帧处理器，直接显示
                        self.video_widget.set_frame(visible_frame)

                # 处理红外帧（后台处理，不在Qt界面显示）
                if ret_thermal and thermal_frame is not None:
                    if (hasattr(self.system_instance, 'frame_processor') and
                        self.system_instance.frame_processor):

                        try:
                            # 使用新添加的方法处理红外帧（用于热源检测等）
                            if hasattr(self.system_instance.frame_processor, 'process_thermal_frame'):
                                processed_thermal_frame = self.system_instance.frame_processor.process_thermal_frame(thermal_frame)
                            else:
                                # 兼容旧版本
                                processed_thermal_frame = self.system_instance.frame_processor._apply_thermal_detection_with_priority(thermal_frame)

                            # 注意：当前Qt界面主要显示可见光画面，红外帧在后台处理
                            # 如果需要显示红外画面，需要创建额外的显示组件

                        except Exception as e:
                            print(f"⚠️ 处理红外帧失败: {e}")
                    # 红外帧处理完成，不需要在Qt界面显示

        except Exception as e:
            print(f"⚠️ 更新显示失败: {e}")
            # 静默处理异常，避免影响主程序运行

    def update_heat_sources_info(self):
        """更新热源信息 - 定时器调用"""
        if not self.system_instance:
            print("⚠️ 自动更新跳过: 系统实例未设置")
            return

        try:
            # 获取或创建全局热源分析器实例（确保数据一致性）
            if self.heat_source_analyzer is None:
                from detection.analysis.heat_source_analyzer import HeatSourceAnalyzer
                self.heat_source_analyzer = HeatSourceAnalyzer("detection/debug_test", enable_tracking=True)
                print("✅ 创建全局热源分析器实例")

                # 设置到指标管理器
                if self.metrics_manager:
                    self.metrics_manager.set_heat_source_analyzer(self.heat_source_analyzer)

                # 连接标注管理器
                if hasattr(self, 'manual_annotation_widget') and self.manual_annotation_widget:
                    self.heat_source_analyzer.set_annotation_manager(self.manual_annotation_widget.manager)
                    print("🏷️ 热源分析器已连接标注管理器")

            analyzer = self.heat_source_analyzer

            # 获取温度管理器
            temperature_manager = None
            if hasattr(self.system_instance, 'temperature_manager'):
                temperature_manager = self.system_instance.temperature_manager

            # 获取温度矩阵
            temp_matrix = None
            if temperature_manager:
                reader = temperature_manager.get_primary_reader()
                if (reader and hasattr(reader, 'current_temp_matrix') and
                    reader.current_temp_matrix is not None):
                    temp_matrix = reader.current_temp_matrix
                    print(f"🌡️ MainWindow获取到温度矩阵: {temp_matrix.shape if temp_matrix is not None else 'None'}")
                else:
                    print(f"❌ MainWindow无法获取温度矩阵: reader={reader is not None}")
            else:
                print(f"❌ MainWindow温度管理器不存在")

            # 检查最新分析文件
            latest_file = analyzer.get_latest_analysis_file()
            print(f"🔍 MainWindow最新分析文件: {latest_file}")

            # 分析最新的检测结果
            print(f"🔍 MainWindow调用热源分析器...")
            heat_sources_detail = analyzer.analyze_latest_detection(temp_matrix)
            print(f"🔍 MainWindow热源分析结果: {len(heat_sources_detail) if heat_sources_detail else 0}个热源")

            # 🏭 工位四：打包发货 - 使用完整流水线处理后的数据
            # heat_sources_detail 已经经过：检测 → 跟踪 → 匹配 的完整流水线
            if heat_sources_detail:
                try:
                    from utils.excel_data_exporter import get_excel_exporter
                    excel_exporter = get_excel_exporter()

                    # 确保Excel导出器有最新的标注数据
                    excel_exporter.sync_manual_annotations_to_assets(force_update=False)

                    # 直接使用热源分析器完整流水线处理后的数据
                    # 每个 heat_source 已经包含：tracking_id, annotation_label, asset_id
                    for heat_source in heat_sources_detail:
                        # 使用热源分析器统一设置的asset_id（避免重复处理）
                        asset_id = getattr(heat_source, 'asset_id', None)
                        annotation_label = getattr(heat_source, 'annotation_label', None)

                        # 直接导出，不再重新匹配（避免双重处理）
                        excel_exporter.add_heat_source_event(heat_source, "CAM_001", asset_id)

                        # 调试信息
                        if annotation_label and asset_id:
                            print(f"🔗 热源{heat_source.id}关联到资产: {annotation_label} (ID: {asset_id})")
                        elif annotation_label:
                            print(f"⚠️ 热源{heat_source.id}有标注'{annotation_label}'但asset_id为空")

                except Exception as e:
                    pass  # 静默处理Excel导出器错误，不影响主程序



            # 计算监控指标
            all_metrics = {}
            if self.metrics_manager and heat_sources_detail:
                all_metrics = self.metrics_manager.calculate_all_metrics(heat_sources_detail)

            # 更新侧边栏显示
            if hasattr(self, 'tabbed_sidebar'):
                print(f"🔍 MainWindow检查热源详情: {len(heat_sources_detail) if heat_sources_detail else 0}个热源")
                if heat_sources_detail:
                    print(f"🏠 MainWindow更新侧边栏热源详情: {len(heat_sources_detail)}个热源")
                    self.tabbed_sidebar.update_heat_sources_detail(heat_sources_detail)
                    self.tabbed_sidebar.update_monitoring_data(
                        heat_sources_count=len(heat_sources_detail),
                        detection_enabled=True
                    )

                    # 更新全局指标
                    if 'global_metrics' in all_metrics and hasattr(self, 'qt_sidebar'):
                        self.qt_sidebar.update_global_metrics(all_metrics['global_metrics'])

                    # 将热源数据传递给预警管理器（用于一级预警）
                    self._update_alert_manager_with_heat_sources(heat_sources_detail)

                    # 只在热源数量变化时输出日志
                    if not hasattr(self, '_last_heat_sources_count') or self._last_heat_sources_count != len(heat_sources_detail):
                        self._last_heat_sources_count = len(heat_sources_detail)

                        # 输出跟踪统计信息
                        if analyzer.tracker:
                            stats = analyzer.tracker.get_tracking_statistics()
                            print(f"📊 跟踪统计: 活跃热源{stats.get('active_sources', 0)}, 总创建{stats.get('sources_created', 0)}, 总丢失{stats.get('sources_lost', 0)}")

                        # 输出综合指标信息（每10次更新输出一次，避免过于频繁）
                        if not hasattr(self, '_metrics_output_counter'):
                            self._metrics_output_counter = 0
                        self._metrics_output_counter += 1

                        if self._metrics_output_counter >= 10:  # 每10次输出一次
                            self._metrics_output_counter = 0
                            try:
                                metrics_report = analyzer.get_comprehensive_metrics_report()
                                if metrics_report:
                                    global_metrics = metrics_report.get('global_metrics', {}).get('latest', {})
                                    if global_metrics:
                                        print(f"🌍 全局指标: 热源数{global_metrics.get('total_heat_sources', 0)}, "
                                              f"总面积{global_metrics.get('total_heat_area', 0)}像素, "
                                              f"30天平均温度{global_metrics.get('avg_temperature_30days', 'N/A')}℃")
                            except Exception as e:
                                pass  # 静默处理指标获取异常
                else:
                    print(f"❌ MainWindow热源详情为空，跳过侧边栏更新")
                    self.tabbed_sidebar.update_heat_sources_detail([])
                    self.tabbed_sidebar.update_monitoring_data(
                        heat_sources_count=0,
                        detection_enabled=False
                    )



        except Exception as e:
            # 静默处理异常，避免影响主程序运行
            pass

    def _update_alert_manager_with_heat_sources(self, heat_sources_detail):
        """将热源数据传递给预警管理器"""
        try:
            # 获取火焰烟雾分析组件的预警管理器
            alert_manager = None

            # 从tabbed_sidebar获取
            if hasattr(self, 'tabbed_sidebar') and self.tabbed_sidebar:
                if hasattr(self.tabbed_sidebar, 'monitoring_tab') and self.tabbed_sidebar.monitoring_tab:
                    if hasattr(self.tabbed_sidebar.monitoring_tab, 'fire_smoke_analysis_widget'):
                        fire_smoke_widget = self.tabbed_sidebar.monitoring_tab.fire_smoke_analysis_widget
                        if fire_smoke_widget and hasattr(fire_smoke_widget, 'get_alert_manager'):
                            alert_manager = fire_smoke_widget.get_alert_manager()

            if alert_manager:
                # 转换热源数据格式
                heat_source_data = []
                for heat_source in heat_sources_detail:
                    heat_data = {
                        'id': getattr(heat_source, 'id', 0),
                        'area': getattr(heat_source, 'area_pixels', 0),
                        'max_temp': getattr(heat_source, 'max_temperature', 0),
                        'avg_temp': getattr(heat_source, 'temperature', 0),
                        'position': getattr(heat_source, 'position', (0, 0)),
                        'bbox': getattr(heat_source, 'bbox', (0, 0, 0, 0))
                    }
                    heat_source_data.append(heat_data)

                # 简化热源数据传递的输出信息，保持后台简洁
                # print(f"🌡️ 主窗口向预警管理器传递热源数据: {len(heat_source_data)}个热源")
                # for i, heat_data in enumerate(heat_source_data):
                #     print(f"   热源{i+1}: 面积={heat_data['area']}, 温度={heat_data['max_temp']:.1f}°C")

                # 获取当前的火焰烟雾数据（从火焰烟雾分析组件）
                fire_data = getattr(fire_smoke_widget, 'fire_targets', {})
                smoke_data = getattr(fire_smoke_widget, 'smoke_targets', {})

                # 更新预警管理器（包含热源数据）
                alert_manager.update_detection_data(fire_data, smoke_data, heat_source_data)
            else:
                print("⚠️ 未找到预警管理器，无法传递热源数据")

        except Exception as e:
            print(f"❌ 传递热源数据到预警管理器失败: {e}")
            import traceback
            traceback.print_exc()

    def display_frame(self, frame: np.ndarray):
        """显示帧到视频控件"""
        if frame is not None:
            # 设置当前帧到抽帧器（用于抽帧功能）
            if hasattr(self, 'video_frame_extractor'):
                # 提取可见光部分（假设左半部分是可见光）
                h, w = frame.shape[:2]
                visible_frame = frame[:, :w//2] if w > 100 else frame
                self.video_frame_extractor.set_current_frame(visible_frame)

            # 添加鼠标叠加信息（基于原有实现）
            frame_with_overlay = self._add_mouse_overlay(frame)

            self.video_widget.set_frame(frame_with_overlay)

            # 更新鼠标处理器的帧引用
            if hasattr(self, 'qt_mouse_handler'):
                # 从帧处理器获取最新的原始帧
                thermal_frame = None
                visible_frame = None

                if (self.system_instance and
                    hasattr(self.system_instance, 'frame_processor') and
                    self.system_instance.frame_processor):

                    # 获取最新的原始帧
                    processor = self.system_instance.frame_processor
                    if hasattr(processor, 'current_thermal_frame'):
                        thermal_frame = processor.current_thermal_frame
                    if hasattr(processor, 'current_visible_frame'):
                        visible_frame = processor.current_visible_frame

                # 设置帧引用
                self.qt_mouse_handler.set_frames(thermal_frame, visible_frame, frame)

    def _add_mouse_overlay(self, frame: np.ndarray) -> np.ndarray:
        """添加鼠标叠加信息（基于原有实现）"""
        result_frame = frame.copy()

        # 添加鼠标十字线
        if hasattr(self, 'qt_mouse_handler') and hasattr(self, 'qt_overlay_renderer'):
            # 获取鼠标信息（使用调整后的坐标，不包含工具栏）
            mouse_x, mouse_y = self.qt_mouse_handler.get_mouse_position()

            # 直接使用调整后的坐标绘制十字线（不需要再加工具栏高度）
            if mouse_x > 0 and mouse_y > 0:
                # 只显示十字线，不显示温度
                result_frame = self.qt_overlay_renderer.add_mouse_overlay(
                    result_frame, mouse_x, mouse_y, 0.0, True
                )

        # 添加手动标注渲染
        if hasattr(self, 'manual_annotation_widget') and hasattr(self, 'manual_annotation_renderer'):
            # 渲染已保存的标注
            annotations = self.manual_annotation_widget.get_visible_annotations()
            if annotations:
                # 分别渲染可见光和热成像标注
                result_frame = self.manual_annotation_renderer.render_annotations(
                    result_frame, annotations, is_thermal=False, thermal_offset_x=0
                )
                # 获取可见光显示宽度
                visible_width = getattr(self.qt_mouse_handler, 'visible_display_width', 640) if hasattr(self, 'qt_mouse_handler') else 640
                result_frame = self.manual_annotation_renderer.render_annotations(
                    result_frame, annotations, is_thermal=True, thermal_offset_x=visible_width
                )

            # 渲染临时标注（拖拽过程中）
            if hasattr(self, 'manual_annotation_handler'):
                temp_bbox = self.manual_annotation_handler.get_temp_bbox()
                if temp_bbox and self.manual_annotation_handler.is_drawing():
                    is_thermal = self.manual_annotation_handler.is_thermal_drawing()
                    # 如果是热成像标注，需要调整坐标
                    if is_thermal:
                        # 获取可见光显示宽度
                        visible_width = getattr(self.qt_mouse_handler, 'visible_display_width', 640) if hasattr(self, 'qt_mouse_handler') else 640
                        adjusted_bbox = (
                            temp_bbox[0] + visible_width,  # 加上热成像区域偏移
                            temp_bbox[1],
                            temp_bbox[2],
                            temp_bbox[3]
                        )
                        result_frame = self.manual_annotation_renderer.render_temp_bbox(
                            result_frame, adjusted_bbox, is_thermal=is_thermal
                        )
                    else:
                        result_frame = self.manual_annotation_renderer.render_temp_bbox(
                            result_frame, temp_bbox, is_thermal=is_thermal
                        )

        return result_frame

    def on_mouse_moved(self, x: int, y: int):
        """处理鼠标移动事件"""
        # 使用Qt鼠标处理器处理（基于原有实现）
        if hasattr(self, 'qt_mouse_handler'):
            self.qt_mouse_handler.handle_mouse_move(x, y)

    def on_mouse_clicked(self, x: int, y: int, button: int):
        """处理鼠标点击事件"""
        # 记录点击事件（用于调试）
        print(f"🖱️ 鼠标点击: ({x}, {y}), 按钮={button}")

        # 判断点击区域
        if hasattr(self, 'qt_mouse_handler') and self.qt_mouse_handler.is_in_thermal_area(x):
            print("点击在热成像区域")
        else:
            print("点击在可见光区域")

    def keyPressEvent(self, event):
        """处理键盘事件"""
        # 使用Qt键盘处理器处理
        if hasattr(self, 'qt_keyboard_handler'):
            if self.qt_keyboard_handler.handle_key_press(event.key(), int(event.modifiers())):
                return  # 事件已被处理

        # 如果事件未被处理，调用父类方法
        super().keyPressEvent(event)

    # 事件处理器回调方法
    def on_temperature_updated(self, temperature: float):
        """处理温度更新 - 已禁用鼠标温度显示"""
        pass

    def on_position_updated(self, x: int, y: int):
        """处理位置更新"""
        if hasattr(self, 'qt_sidebar'):
            self.qt_sidebar.update_status_info(mouse_pos=(x, y))

    def on_detection_toggle(self):
        """处理热源检测切换"""
        if self.system_instance and hasattr(self.system_instance, 'ui_control_handler'):
            self.system_instance.ui_control_handler.toggle_detection()

    def on_human_detection_toggle(self):
        """处理人体检测切换"""
        if self.system_instance and hasattr(self.system_instance, 'ui_control_handler'):
            self.system_instance.ui_control_handler.toggle_human_detection()

    def on_thermal_human_toggle(self):
        """处理热成像人体检测切换"""
        if self.system_instance and hasattr(self.system_instance, 'ui_control_handler'):
            self.system_instance.ui_control_handler.toggle_thermal_human_detection()

    def on_adaptive_mode_toggle(self, is_adaptive: bool):
        """处理自适应模式切换"""
        if self.system_instance:
            success = self.system_instance.set_adaptive_threshold(is_adaptive)
            if success:
                # 更新工具栏状态
                self.qt_toolbar.update_adaptive_mode(is_adaptive)
                mode_name = "自适应" if is_adaptive else "手动"

    def on_threshold_adjust(self, threshold: float):
        """处理阈值调节"""
        if self.system_instance:
            # 只在手动模式下允许调整阈值
            if not self.system_instance.is_adaptive_mode():
                success = self.system_instance.set_temperature_threshold(threshold)
                if success:
                    print(f"🌡️ 手动阈值设置为: {threshold:.1f}°C")
                else:
                    print("⚠️ 阈值设置失败")
            else:
                print("⚠️ 自适应模式下无法手动调整阈值")

    def on_save_image(self):
        """处理保存图像"""
        if hasattr(self, 'toolbar_callback_handler'):
            self.toolbar_callback_handler.save_image()

    def on_save_debug(self):
        """处理保存调试"""
        if hasattr(self, 'toolbar_callback_handler'):
            self.toolbar_callback_handler.save_debug()

    def on_help_toggle(self):
        """处理帮助切换"""
        if hasattr(self, 'qt_sidebar'):
            self.qt_sidebar.toggle_mode()

    def on_sidebar_scroll(self, direction: int):
        """处理侧边栏滚动"""
        if hasattr(self, 'tabbed_sidebar'):
            self.tabbed_sidebar.scroll_heat_sources(direction)



    def on_quit_requested(self):
        """处理退出请求 - 使用非模态对话框避免界面卡住"""
        from PyQt5.QtWidgets import QMessageBox

        # 创建非模态确认对话框
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle('确认退出')
        msg_box.setText('确定要退出热成像监控系统吗？')
        msg_box.setStandardButtons(QMessageBox.Yes | QMessageBox.No)
        msg_box.setDefaultButton(QMessageBox.No)
        msg_box.setModal(False)  # 设置为非模态

        # 连接信号处理用户选择
        msg_box.buttonClicked.connect(self._handle_quit_response)
        msg_box.show()  # 使用show()而不是exec_()

        # 保存对话框引用，防止被垃圾回收
        self._quit_dialog = msg_box

    def _handle_quit_response(self, button):
        """处理退出确认响应"""
        from PyQt5.QtWidgets import QMessageBox

        if hasattr(self, '_quit_dialog'):
            if self._quit_dialog.standardButton(button) == QMessageBox.Yes:
                if self.system_instance:
                    self.system_instance.is_running = False
                self.close()

            # 清理对话框引用
            self._quit_dialog.close()
            delattr(self, '_quit_dialog')

    def on_system_settings_changed(self, settings: dict):
        """处理系统设置变化"""
        try:
            self.logger.info(f"收到系统设置变化: {len(settings)} 项")

            # 更新侧边栏显示设置
            if 'max_sources' in settings and hasattr(self, 'qt_sidebar'):
                max_sources = settings['max_sources']
                if hasattr(self.qt_sidebar, 'heat_source_detail_widget'):
                    self.qt_sidebar.heat_source_detail_widget.max_display_count = max_sources
                    self.logger.info(f"更新最大显示热源数量: {max_sources}")

            # 更新调试信息显示
            if 'debug_info' in settings:
                debug_enabled = settings['debug_info']
                # 这里可以连接到实际的调试信息控制
                self.logger.info(f"调试信息显示: {'开启' if debug_enabled else '关闭'}")

            # 更新检测参数（如果系统实例存在）
            if self.system_instance:
                if 'temp_threshold' in settings:
                    # 这里可以更新实际的温度阈值
                    temp_threshold = settings['temp_threshold']
                    self.logger.info(f"更新温度阈值: {temp_threshold}°C")

                if 'area_threshold' in settings:
                    # 这里可以更新实际的面积阈值
                    area_threshold = settings['area_threshold']
                    self.logger.info(f"更新面积阈值: {area_threshold} 像素")

                if 'sensitivity' in settings:
                    # 这里可以更新检测灵敏度
                    sensitivity = settings['sensitivity']
                    self.logger.info(f"更新检测灵敏度: {sensitivity}/10")

            # 更新报警设置
            if 'high_temp_alarm' in settings:
                high_temp_alarm = settings['high_temp_alarm']
                self.logger.info(f"更新高温报警阈值: {high_temp_alarm}°C")

            if 'sound_alarm' in settings:
                sound_alarm = settings['sound_alarm']
                self.logger.info(f"声音报警: {'开启' if sound_alarm else '关闭'}")

            print(f"✅ 系统设置已更新: {list(settings.keys())}")

        except Exception as e:
            self.logger.error(f"处理系统设置变化失败: {e}")
            print(f"❌ 系统设置更新失败: {e}")

    def resizeEvent(self, event):
        """处理窗口大小变化事件"""
        super().resizeEvent(event)

        # 调整侧边栏大小以适应新的窗口尺寸
        if hasattr(self, 'qt_sidebar'):
            # 延迟调整，避免频繁调整
            QTimer.singleShot(100, self.qt_sidebar.adjust_size_to_parent)

    def showEvent(self, event):
        """处理窗口显示事件"""
        super().showEvent(event)

        # 窗口首次显示时调整侧边栏大小
        if hasattr(self, 'qt_sidebar'):
            QTimer.singleShot(200, self.qt_sidebar.adjust_size_to_parent)

    def update_detection_status(self):
        """更新检测状态信息到侧边栏"""
        try:
            if not self.system_instance or not hasattr(self, 'qt_sidebar'):
                return

            # 获取基础检测状态
            detection_status = self.system_instance.get_detection_status()



            # 获取检测计数信息
            human_count = 0
            fire_count = 0
            smoke_count = 0
            fire_area_change_rate = 0.0
            smoke_area_change_rate = 0.0
            fire_count_change_rate = 0.0
            smoke_count_change_rate = 0.0

            # 从帧处理器获取检测计数管理器
            if (hasattr(self.system_instance, 'frame_processor') and
                self.system_instance.frame_processor and
                hasattr(self.system_instance.frame_processor, 'detection_count_manager')):

                count_manager = self.system_instance.frame_processor.detection_count_manager
                if count_manager:
                    # 获取最新计数
                    current_counts = count_manager.get_current_counts()
                    if current_counts:
                        human_count = current_counts.human_count
                        fire_count = current_counts.fire_count
                        smoke_count = current_counts.smoke_count

                    # 获取变化率
                    change_rates = count_manager.get_change_rates()
                    fire_area_change_rate = change_rates.get('fire_area_change_rate', 0.0)
                    smoke_area_change_rate = change_rates.get('smoke_area_change_rate', 0.0)
                    fire_count_change_rate = change_rates.get('fire_count_change_rate', 0.0)
                    smoke_count_change_rate = change_rates.get('smoke_count_change_rate', 0.0)

            # 如果检测计数管理器没有数据，尝试从火焰烟雾检测器获取
            if fire_count == 0 and smoke_count == 0:
                fire_count, smoke_count = self._get_current_fire_smoke_counts()

            # 更新侧边栏状态
            detection_status.update({
                'human_count': human_count,
                'fire_count': fire_count,
                'smoke_count': smoke_count,
                'fire_area_change_rate': fire_area_change_rate,
                'smoke_area_change_rate': smoke_area_change_rate,
                'fire_count_change_rate': fire_count_change_rate,
                'smoke_count_change_rate': smoke_count_change_rate
            })

            # 直接更新侧边栏
            self.qt_sidebar.update_status_info(
                human_count=human_count,
                fire_count=fire_count,
                smoke_count=smoke_count,
                fire_area_change_rate=fire_area_change_rate,
                smoke_area_change_rate=smoke_area_change_rate,
                fire_count_change_rate=fire_count_change_rate,
                smoke_count_change_rate=smoke_count_change_rate
            )



        except Exception as e:
            print(f"⚠️ 更新检测状态失败: {e}")
            # 静默处理异常，避免影响主程序运行

    def _get_current_fire_smoke_counts(self):
        """获取当前火焰和烟雾计数"""
        fire_count = 0
        smoke_count = 0

        try:
            # 1. 从集成火焰烟雾检测器获取数据
            if (self.system_instance and
                hasattr(self.system_instance, 'fire_smoke_detector') and
                self.system_instance.fire_smoke_detector):

                detector = self.system_instance.fire_smoke_detector

                # 从数据引擎获取当前对象数量
                if hasattr(detector, 'data_engine') and detector.data_engine:
                    data_engine = detector.data_engine

                    # 获取当前活跃的对象ID
                    fire_objects = set()
                    smoke_objects = set()

                    # 从最近的记录中提取活跃对象
                    current_time = time.time()
                    time_threshold = 5.0  # 5秒内的检测认为是活跃的

                    for record in getattr(data_engine, 'fire_data', []):
                        if current_time - record.get('timestamp', 0) <= time_threshold:
                            fire_objects.add(record.get('object_id'))

                    for record in getattr(data_engine, 'smoke_data', []):
                        if current_time - record.get('timestamp', 0) <= time_threshold:
                            smoke_objects.add(record.get('object_id'))

                    fire_count = len(fire_objects)
                    smoke_count = len(smoke_objects)

                # 如果数据引擎没有数据，尝试从最新检测结果获取
                if fire_count == 0 and smoke_count == 0:
                    if hasattr(detector, 'get_last_results'):
                        last_results = detector.get_last_results()
                        if last_results:
                            fire_objects = last_results.get('fire_objects', {})
                            smoke_objects = last_results.get('smoke_objects', {})
                            fire_count = len(fire_objects)
                            smoke_count = len(smoke_objects)

        except Exception as e:
            print(f"⚠️ 获取火焰烟雾计数失败: {e}")

        return fire_count, smoke_count

    def _update_detection_status_immediate(self):
        """立即更新检测状态（用于火焰烟雾检测回调）"""
        try:
            if not self.system_instance:
                return

            # 获取最新的检测计数和变化率
            fire_count = 0
            smoke_count = 0
            fire_area_change_rate = 0.0
            smoke_area_change_rate = 0.0
            fire_count_change_rate = 0.0
            smoke_count_change_rate = 0.0

            # 从检测计数管理器获取最新数据
            if (hasattr(self.system_instance, 'frame_processor') and
                self.system_instance.frame_processor and
                hasattr(self.system_instance.frame_processor, 'detection_count_manager')):

                count_manager = self.system_instance.frame_processor.detection_count_manager
                if count_manager:
                    # 获取当前计数
                    current_counts = count_manager.get_current_counts()
                    if current_counts:
                        fire_count = current_counts.fire_count
                        smoke_count = current_counts.smoke_count

                    # 获取最新变化率
                    change_rates = count_manager.get_change_rates()
                    fire_area_change_rate = change_rates.get('fire_area_change_rate', 0.0)
                    smoke_area_change_rate = change_rates.get('smoke_area_change_rate', 0.0)
                    fire_count_change_rate = change_rates.get('fire_count_change_rate', 0.0)
                    smoke_count_change_rate = change_rates.get('smoke_count_change_rate', 0.0)

            # 立即更新侧边栏显示
            if hasattr(self, 'qt_sidebar'):
                self.qt_sidebar.update_status_info(
                    fire_count=fire_count,
                    smoke_count=smoke_count,
                    fire_area_change_rate=fire_area_change_rate,
                    smoke_area_change_rate=smoke_area_change_rate,
                    fire_count_change_rate=fire_count_change_rate,
                    smoke_count_change_rate=smoke_count_change_rate
                )


        except Exception as e:
            print(f"⚠️ 立即更新检测状态失败: {e}")

    def _update_fire_smoke_data_display(self):
        """更新火焰烟雾数据显示"""
        try:
            fire_data = []
            smoke_data = []
            data_source = "无数据"

            # 1. 从集成火焰烟雾检测器获取数据
            if (self.system_instance and
                hasattr(self.system_instance, 'fire_smoke_detector') and
                self.system_instance.fire_smoke_detector):

                detector = self.system_instance.fire_smoke_detector

                # 获取数据引擎中的数据
                if hasattr(detector, 'data_engine') and detector.data_engine:
                    data_engine = detector.data_engine
                    raw_fire_data = getattr(data_engine, 'fire_data', [])
                    raw_smoke_data = getattr(data_engine, 'smoke_data', [])

                    if raw_fire_data or raw_smoke_data:
                        data_source = "数据引擎"
                        # 转换数据格式：从记录列表转换为按对象ID分组的最新数据
                        fire_data = self._convert_records_to_objects(raw_fire_data)
                        smoke_data = self._convert_records_to_objects(raw_smoke_data)

                # 如果数据引擎没有数据，尝试获取最新检测结果
                if not fire_data and not smoke_data:
                    if hasattr(detector, 'get_last_results'):
                        last_results = detector.get_last_results()
                        if last_results:
                            fire_data = last_results.get('fire_detections', [])
                            smoke_data = last_results.get('smoke_detections', [])
                            if fire_data or smoke_data:
                                data_source = "最新检测结果"

            # 2. 从帧处理器获取数据（备用）
            if not fire_data and not smoke_data and (
                self.system_instance and
                hasattr(self.system_instance, 'frame_processor') and
                self.system_instance.frame_processor):

                frame_processor = self.system_instance.frame_processor
                if hasattr(frame_processor, 'last_fire_smoke_detections'):
                    last_detections = getattr(frame_processor, 'last_fire_smoke_detections', {})
                    if last_detections:
                        fire_data = last_detections.get('fire_detections', [])
                        smoke_data = last_detections.get('smoke_detections', [])
                        if fire_data or smoke_data:
                            data_source = "帧处理器"

            # 记录数据获取情况
            if fire_data or smoke_data:
                print(f"🔥💨 获取火焰烟雾数据: {len(fire_data)}个火焰, {len(smoke_data)}个烟雾 (来源: {data_source})")

            # 更新侧边栏显示
            if hasattr(self, 'qt_sidebar') and hasattr(self.qt_sidebar, 'update_fire_smoke_data'):
                self.qt_sidebar.update_fire_smoke_data(fire_data, smoke_data)
            else:
                print("⚠️ 侧边栏或update_fire_smoke_data方法不存在")

        except Exception as e:
            print(f"❌ 更新火焰烟雾数据显示失败: {e}")
            import traceback
            print(f"详细错误: {traceback.format_exc()}")

    def _convert_records_to_objects(self, records_list):
        """
        将记录列表转换为按对象ID分组的最新数据

        Args:
            records_list: 记录列表，每个记录包含完整的检测信息

        Returns:
            list: 转换后的对象列表，每个对象包含最新的检测信息
        """
        if not records_list:
            return []

        # 按object_id分组，保留每个对象的最新记录
        objects_dict = {}
        for record in records_list:
            obj_id = record.get('object_id')
            if obj_id is not None:
                # 如果已存在该对象，比较时间戳保留最新的
                if obj_id not in objects_dict or record.get('timestamp', 0) > objects_dict[obj_id].get('timestamp', 0):
                    # 转换记录格式为侧边栏期望的格式
                    # 只做必要的格式转换，默认值由侧边栏组件处理
                    converted_record = {
                        'object_id': obj_id,
                        'id': obj_id,  # 兼容性字段
                        'confidence': record.get('confidence', 0.0),
                        'bbox': [
                            record.get('bbox_x1', 0),
                            record.get('bbox_y1', 0),
                            record.get('bbox_x2', 0),
                            record.get('bbox_y2', 0)
                        ],
                        'centroid': (record.get('centroid_x', 0), record.get('centroid_y', 0)),
                        'area': record.get('area', 0),
                        'duration': record.get('duration', 0),
                        'frame_count': record.get('frame_count', 1),
                        'timestamp': record.get('timestamp', 0),
                        'first_seen': record.get('first_seen', record.get('timestamp', 0)),
                        'last_seen': record.get('last_seen', record.get('timestamp', 0)),
                        'total_distance': record.get('total_distance', 0.0)
                    }

                    # 添加记录中存在的其他字段（避免丢失数据）
                    for key, value in record.items():
                        if key not in converted_record and key not in ['bbox_x1', 'bbox_y1', 'bbox_x2', 'bbox_y2', 'centroid_x', 'centroid_y']:
                            converted_record[key] = value
                    objects_dict[obj_id] = converted_record

        # 转换为列表并按ID排序
        result = list(objects_dict.values())
        result.sort(key=lambda x: x.get('object_id', 0))

        print(f"📊 数据转换完成: {len(records_list)}条记录 → {len(result)}个对象")
        return result

    def _setup_fire_smoke_callback(self):
        """设置火焰烟雾检测器的界面更新回调"""
        try:
            if (self.system_instance and
                hasattr(self.system_instance, 'fire_smoke_detector') and
                self.system_instance.fire_smoke_detector):

                detector = self.system_instance.fire_smoke_detector
                if hasattr(detector, 'set_ui_update_callback'):
                    detector.set_ui_update_callback(self._on_fire_smoke_detection)
                    print("🔥💨 火焰烟雾检测器界面回调已设置")

                    # 验证回调是否正确设置
                    if hasattr(detector, 'ui_update_callback') and detector.ui_update_callback:
                        print("✅ 回调验证成功 - 回调函数已存储")
                    else:
                        print("❌ 回调验证失败 - 回调函数未存储")

                    # 强制重新设置回调（防止被覆盖）
                    self._force_callback_setup()
                else:
                    print("⚠️ 火焰烟雾检测器不支持界面回调")
            else:
                print("⚠️ 火焰烟雾检测器未找到")

        except Exception as e:
            print(f"❌ 设置火焰烟雾回调失败: {e}")

    def _force_callback_setup(self):
        """强制重新设置回调（定时器方式）"""
        try:
            from PyQt5.QtCore import QTimer

            # 创建定时器，延迟重新设置回调
            self.callback_timer = QTimer()
            self.callback_timer.timeout.connect(self._retry_callback_setup)
            self.callback_timer.setSingleShot(True)
            self.callback_timer.start(1000)  # 1秒后重试



        except Exception as e:
            print(f"❌ 强制回调设置失败: {e}")

    def _retry_callback_setup(self):
        """重试回调设置"""
        try:
            if (self.system_instance and
                hasattr(self.system_instance, 'fire_smoke_detector') and
                self.system_instance.fire_smoke_detector):

                detector = self.system_instance.fire_smoke_detector
                if hasattr(detector, 'set_ui_update_callback'):
                    detector.set_ui_update_callback(self._on_fire_smoke_detection)


        except Exception as e:
            print(f"❌ 重试回调设置失败: {e}")

    def _sync_toolbar_status(self):
        """同步工具栏状态与系统状态"""
        try:
            if not self.system_instance or not hasattr(self, 'qt_toolbar'):
                return

            # 同步火焰烟雾检测状态
            if (hasattr(self.system_instance, 'frame_processor') and
                self.system_instance.frame_processor and
                hasattr(self.system_instance.frame_processor, 'fire_smoke_detection_enabled')):

                fire_smoke_enabled = self.system_instance.frame_processor.fire_smoke_detection_enabled
                self.qt_toolbar.update_fire_smoke_detection_status(fire_smoke_enabled)

        except Exception as e:
            pass  # 静默处理同步失败

    def _on_fire_smoke_detection(self, result):
        """处理火焰烟雾检测结果的回调"""
        try:
            print(f"🔥💨 主窗口回调被调用! 结果类型: {type(result)}")

            # 提取火焰和烟雾数据
            fire_objects = result.get('fire_objects', {})
            smoke_objects = result.get('smoke_objects', {})

            print(f"   - 原始火焰检测: {len(result.get('fire_detections', []))}")
            print(f"   - 原始烟雾检测: {len(result.get('smoke_detections', []))}")
            print(f"   - 火焰对象: {len(fire_objects)}")
            print(f"   - 烟雾对象: {len(smoke_objects)}")

            # 打印检测详情
            for i, detection in enumerate(result.get('fire_detections', []), 1):
                print(f"     火焰检测 {i}: 置信度={detection.get('confidence', 0):.3f}, bbox={detection.get('bbox', [])}")
            for i, detection in enumerate(result.get('smoke_detections', []), 1):
                print(f"     烟雾检测 {i}: 置信度={detection.get('confidence', 0):.3f}, bbox={detection.get('bbox', [])}")

            # 打印对象详情
            for obj_id, obj_data in fire_objects.items():
                print(f"   火焰对象 {obj_id}: 置信度={obj_data.get('confidence', 0):.3f}, 帧数={obj_data.get('frame_count', 0)}, bbox={obj_data.get('bbox', [])}")
            for obj_id, obj_data in smoke_objects.items():
                print(f"   烟雾对象 {obj_id}: 置信度={obj_data.get('confidence', 0):.3f}, 帧数={obj_data.get('frame_count', 0)}, bbox={obj_data.get('bbox', [])}")

            # 转换为列表格式
            fire_data = []
            for obj_id, obj_data in fire_objects.items():
                # 检查分析数据
                geometric_features = obj_data.get('geometric_features', {})
                flicker_analysis = obj_data.get('flicker_analysis', {})

                # 修正字段名映射
                fire_record = {
                    'object_id': obj_id,
                    'confidence': obj_data.get('confidence', 0.0),
                    'area': self._calculate_bbox_area(obj_data.get('bbox', [0, 0, 0, 0])),
                    'bbox': obj_data.get('bbox', [0, 0, 0, 0]),
                    'centroid': obj_data.get('centroid', (0, 0)),
                    'duration': obj_data.get('frame_count', 1) / 30.0,  # 假设30fps
                    'frame_count': obj_data.get('frame_count', 1),
                    'total_distance': obj_data.get('total_distance', 0.0),
                    'first_seen': obj_data.get('first_seen', 0),
                    'last_seen': obj_data.get('last_seen', 0),
                    **geometric_features
                }

                # 映射闪烁分析字段名
                if flicker_analysis:
                    fire_record['flicker_frequency'] = flicker_analysis.get('frequency', 0.0)
                    fire_record['flicker_intensity'] = flicker_analysis.get('intensity', 0.0)
                    # 添加频率稳定性（基于闪烁强度计算）
                    intensity = flicker_analysis.get('intensity', 0.0)
                    fire_record['frequency_stability'] = max(0.0, 1.0 - intensity) if intensity > 0 else 0.8

                # 计算运动分析数据
                total_distance = obj_data.get('total_distance', 0.0)
                frame_count = obj_data.get('frame_count', 1)
                duration = fire_record['duration']

                # 计算运动速度（像素/秒）
                fire_record['movement_speed'] = total_distance / duration if duration > 0 else 0.0

                # 计算轨迹稳定性（基于距离和帧数的关系）
                if frame_count > 1:
                    expected_distance = frame_count * 2  # 假设每帧移动2像素为正常
                    stability = 1.0 - min(1.0, abs(total_distance - expected_distance) / expected_distance) if expected_distance > 0 else 1.0
                    fire_record['trajectory_stability'] = max(0.0, stability)
                else:
                    fire_record['trajectory_stability'] = 1.0

                # 运动方向（暂时设为0，需要轨迹数据才能准确计算）
                fire_record['movement_direction'] = 0.0
                fire_data.append(fire_record)

            smoke_data = []
            for obj_id, obj_data in smoke_objects.items():
                # 检查分析数据
                geometric_features = obj_data.get('geometric_features', {})
                flicker_analysis = obj_data.get('flicker_analysis', {})

                # 修正字段名映射
                smoke_record = {
                    'object_id': obj_id,
                    'confidence': obj_data.get('confidence', 0.0),
                    'area': self._calculate_bbox_area(obj_data.get('bbox', [0, 0, 0, 0])),
                    'bbox': obj_data.get('bbox', [0, 0, 0, 0]),
                    'centroid': obj_data.get('centroid', (0, 0)),
                    'duration': obj_data.get('frame_count', 1) / 30.0,  # 假设30fps
                    'frame_count': obj_data.get('frame_count', 1),
                    'total_distance': obj_data.get('total_distance', 0.0),
                    'first_seen': obj_data.get('first_seen', 0),
                    'last_seen': obj_data.get('last_seen', 0),
                    **geometric_features
                }

                # 映射闪烁分析字段名（烟雾也可能有闪烁特征）
                if flicker_analysis:
                    smoke_record['flicker_frequency'] = flicker_analysis.get('frequency', 0.0)
                    smoke_record['flicker_intensity'] = flicker_analysis.get('intensity', 0.0)

                # 计算烟雾扩散分析数据
                total_distance = obj_data.get('total_distance', 0.0)
                frame_count = obj_data.get('frame_count', 1)
                duration = smoke_record['duration']
                area = smoke_record['area']

                # 扩散速度（像素/秒）
                smoke_record['spread_speed'] = total_distance / duration if duration > 0 else 0.0

                # 扩散方向（暂时设为0，需要轨迹数据才能准确计算）
                smoke_record['spread_direction'] = 0.0

                # 最大扩散面积（基于当前面积估算）
                smoke_record['max_spread_area'] = area * (1.0 + duration * 0.1)  # 假设每秒扩散10%

                # 威胁指数（基于置信度、面积和扩散速度）
                confidence = smoke_record['confidence']
                spread_speed = smoke_record['spread_speed']
                threat_index = (confidence * 0.5 + (area / 1000) * 0.3 + (spread_speed / 100) * 0.2)
                smoke_record['threat_index'] = min(1.0, threat_index)

                # 密度（基于面积和几何特征估算）
                compactness = geometric_features.get('compactness', 0.5)
                smoke_record['density'] = compactness * confidence
                smoke_data.append(smoke_record)



            print(f"   - 转换后火焰数据: {len(fire_data)}")
            print(f"   - 转换后烟雾数据: {len(smoke_data)}")

            # 计算总面积
            total_fire_area = sum(item.get('area', 0) for item in fire_data)
            total_smoke_area = sum(item.get('area', 0) for item in smoke_data)

            # 更新检测计数管理器
            if (self.system_instance and
                hasattr(self.system_instance, 'frame_processor') and
                self.system_instance.frame_processor and
                hasattr(self.system_instance.frame_processor, 'detection_count_manager')):

                count_manager = self.system_instance.frame_processor.detection_count_manager
                if count_manager:
                    # 更新计数和面积
                    count_manager.update_fire_smoke_counts(len(fire_data), len(smoke_data))
                    # 如果检测计数管理器支持面积更新，也更新面积信息
                    if hasattr(count_manager, 'update_fire_smoke_areas'):
                        count_manager.update_fire_smoke_areas(total_fire_area, total_smoke_area)

            # 直接更新侧边栏显示
            if hasattr(self, 'qt_sidebar') and hasattr(self.qt_sidebar, 'update_fire_smoke_data'):
                print(f"🔥💨 主窗口更新侧边栏: {len(fire_data)}个火焰, {len(smoke_data)}个烟雾")
                self.qt_sidebar.update_fire_smoke_data(fire_data, smoke_data)
                print(f"✅ 侧边栏数据更新完成")

            # 立即更新全局详情中的计数显示（包含最新的变化率）
            self._update_detection_status_immediate()

        except Exception as e:
            print(f"❌ 处理火焰烟雾检测结果失败: {e}")
            import traceback
            print(f"详细错误: {traceback.format_exc()}")

    def _calculate_bbox_area(self, bbox):
        """计算边界框面积"""
        if len(bbox) >= 4:
            x1, y1, x2, y2 = bbox[:4]
            return abs((x2 - x1) * (y2 - y1))
        return 0







    def _connect_manual_annotation_signals(self):
        """连接手动标注信号"""
        if hasattr(self, 'manual_annotation_widget'):
            # 连接标注模式切换信号
            self.manual_annotation_widget.annotation_mode_changed.connect(
                self.on_annotation_mode_changed
            )



        # 连接手动标注鼠标事件
        if hasattr(self, 'manual_annotation_handler') and hasattr(self, 'video_widget'):
            self.video_widget.mouse_clicked.connect(self.on_manual_annotation_mouse_press)
            self.video_widget.mouse_moved.connect(self.on_manual_annotation_mouse_move)
            self.video_widget.mouse_released.connect(self.on_manual_annotation_mouse_release)

    def _connect_annotation_sync_signals(self):
        """连接标注同步信号"""
        if hasattr(self, 'manual_annotation_widget') and hasattr(self, 'annotation_sync_widget'):
            # 连接标注创建信号 - 使用正确的信号
            self.manual_annotation_widget.annotation_added.connect(
                self.on_annotation_created_for_sync
            )
            # 连接标注删除信号
            self.manual_annotation_widget.annotation_removed.connect(
                self.annotation_sync_widget.on_annotation_deleted
            )

    def _connect_configuration_tools_signals(self):
        """连接配置与工具信号"""
        if hasattr(self, 'configuration_tools_widget'):
            # 连接系统设置变化信号
            if hasattr(self.configuration_tools_widget, 'system_settings_widget'):
                self.configuration_tools_widget.system_settings_widget.settings_changed.connect(
                    self.on_system_settings_changed
                )

            print("🛠️ 配置与工具信号连接完成")

    def on_annotation_mode_changed(self, enabled: bool):
        """处理标注模式切换"""
        if hasattr(self, 'manual_annotation_handler'):
            # 更新坐标系统管理器的传感器尺寸
            coord_manager = get_coordinate_system_manager()

            # 尝试从系统中获取温度管理器和热源检测器
            temperature_manager = getattr(self, 'temperature_manager', None)
            heat_detector = getattr(self, 'heat_detector', None)

            # 更新传感器尺寸
            coord_manager.update_sensor_dimensions(temperature_manager, heat_detector)

            # 使用与qt_mouse_handler相同的显示配置
            if hasattr(self, 'qt_mouse_handler'):
                visible_width = self.qt_mouse_handler.visible_display_width
                thermal_width = self.qt_mouse_handler.thermal_display_width
                display_height = self.qt_mouse_handler.display_height

                # 总显示宽度
                display_width = visible_width + thermal_width

                print(f"🖱️ 标注模式尺寸配置: 可见光={visible_width}, 热成像={thermal_width}, 高度={display_height}, 总宽度={display_width}")

                self.manual_annotation_handler.set_display_size(
                    visible_width, display_height, thermal_width, display_height, display_width, display_height
                )
            else:
                # 回退到默认配置
                visible_width = 640
                thermal_width = 640
                display_height = 480
                display_width = visible_width + thermal_width

                print(f"⚠️ 使用默认标注模式尺寸配置: 可见光={visible_width}, 热成像={thermal_width}, 高度={display_height}")

                self.manual_annotation_handler.set_display_size(
                    visible_width, display_height, thermal_width, display_height, display_width, display_height
                )

        if enabled:
            print("🖱️ 手动标注模式已启用 - 在可见光或热成像画面上拖拽鼠标创建标注")
        else:
            print("🖱️ 手动标注模式已禁用")

    def on_manual_annotation_mouse_press(self, x: int, y: int, button: int):
        """处理手动标注鼠标按下事件"""
        if hasattr(self, 'manual_annotation_handler') and button == 1:  # 左键
            self.manual_annotation_handler.handle_mouse_press(x, y)

    def on_manual_annotation_mouse_move(self, x: int, y: int):
        """处理手动标注鼠标移动事件"""
        if hasattr(self, 'manual_annotation_handler'):
            self.manual_annotation_handler.handle_mouse_move(x, y)

    def on_manual_annotation_mouse_release(self, x: int, y: int, button: int):
        """处理手动标注鼠标释放事件"""
        if hasattr(self, 'manual_annotation_handler') and button == 1:  # 左键
            self.manual_annotation_handler.handle_mouse_release(x, y)

    def on_annotation_created_for_sync(self, annotation_id: str):
        """处理标注创建事件（用于同步）"""
        try:
            if hasattr(self, 'annotation_sync_widget') and hasattr(self, 'manual_annotation_widget'):
                # 检查是否为热成像标注
                annotation = self.manual_annotation_widget.manager.annotations.get(annotation_id)
                is_thermal = getattr(annotation, 'is_thermal', False) if annotation else False

                # 通知同步管理器
                self.annotation_sync_widget.on_annotation_created(annotation_id, is_thermal)

        except Exception as e:
            # 记录错误但不让程序崩溃
            if hasattr(self, 'logger'):
                self.logger.error(f"标注同步处理失败: {e}")
            else:
                print(f"❌ 标注同步处理失败: {e}")

    def closeEvent(self, event):
        """窗口关闭事件 - 避免模态对话框导致界面卡住"""
        # 如果已经有退出确认对话框在显示，直接忽略关闭事件
        if hasattr(self, '_quit_dialog'):
            event.ignore()
            return

        # 先忽略关闭事件，等待用户确认
        event.ignore()

        # 调用非模态退出确认
        self.on_quit_requested()



    def _update_real_time_detection_data(self):
        """更新实时检测数据"""
        try:
            fire_detections = []
            smoke_detections = []

            # 1. 从集成火焰烟雾检测器获取结果
            if (self.system_instance and
                hasattr(self.system_instance, 'fire_smoke_detector') and
                self.system_instance.fire_smoke_detector):

                integrated_detector = self.system_instance.fire_smoke_detector
                if hasattr(integrated_detector, 'get_last_results'):
                    last_results = integrated_detector.get_last_results()
                    if last_results:
                        fire_detections = last_results.get('fire_detections', [])
                        smoke_detections = last_results.get('smoke_detections', [])

            # 2. 从帧处理器获取结果（备用）
            if not fire_detections and not smoke_detections and (
                self.system_instance and
                hasattr(self.system_instance, 'frame_processor') and
                self.system_instance.frame_processor):

                frame_processor = self.system_instance.frame_processor
                if hasattr(frame_processor, 'last_fire_smoke_detections'):
                    last_detections = getattr(frame_processor, 'last_fire_smoke_detections', {})
                    if last_detections:
                        fire_detections = last_detections.get('fire_detections', [])
                        smoke_detections = last_detections.get('smoke_detections', [])

            # 更新侧边栏的实时检测数据
            if hasattr(self, 'qt_sidebar') and hasattr(self.qt_sidebar, 'update_real_time_detections'):
                self.qt_sidebar.update_real_time_detections(
                    fire_detections=fire_detections,
                    smoke_detections=smoke_detections
                )

        except Exception as e:
            # 静默处理异常，避免影响主程序运行
            pass


def create_qt_application():
    """创建Qt应用程序实例"""
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    return app


if __name__ == "__main__":
    # 测试代码
    app = create_qt_application()
    window = ThermalCameraMainWindow()
    window.show()
    sys.exit(app.exec_())
