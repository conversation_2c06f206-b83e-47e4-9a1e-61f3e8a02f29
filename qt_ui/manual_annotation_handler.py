#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手动标注鼠标处理器
处理在可见光和热成像画面上的鼠标拖拽标注操作
"""

import cv2
import numpy as np
from typing import Tuple, Optional, List
from PyQt5.QtCore import QObject, pyqtSignal
from PyQt5.QtWidgets import QInputDialog, QMessageBox
from .manual_annotation_widget import ManualAnnotation
from config.coordinate_system_manager import get_coordinate_system_manager
from utils.chinese_text_renderer import draw_chinese_text


class ManualAnnotationHandler(QObject):
    """手动标注鼠标处理器"""

    # 信号
    annotation_created = pyqtSignal(tuple, str, bool)  # (bbox, label, is_thermal)

    def __init__(self, annotation_widget):
        super().__init__()
        self.annotation_widget = annotation_widget

        # 坐标系统管理器
        self.coord_manager = get_coordinate_system_manager()

        # 鼠标状态
        self.drawing = False
        self.start_point = None  # 传感器坐标
        self.current_point = None  # 传感器坐标
        self.temp_bbox = None  # 传感器坐标系的边界框
        self.is_thermal_annotation = False  # 是否在热成像区域标注

        # 画面尺寸（保持兼容性，但主要使用坐标管理器）
        self.visible_width = 0
        self.visible_height = 0
        self.thermal_width = 0
        self.thermal_height = 0
        self.display_width = 0
        self.display_height = 0

        # 热成像区域偏移（保持兼容性）
        self.thermal_offset_x = 0

        # 连接信号
        self.annotation_created.connect(self.on_annotation_created)

    def set_display_size(self, visible_width: int, visible_height: int,
                        thermal_width: int, thermal_height: int,
                        display_width: int, display_height: int):
        """设置显示尺寸并更新坐标管理器"""
        # 保持兼容性
        self.visible_width = visible_width
        self.visible_height = visible_height
        self.thermal_width = thermal_width
        self.thermal_height = thermal_height
        self.display_width = display_width
        self.display_height = display_height
        self.thermal_offset_x = visible_width  # 热成像区域从可见光区域右侧开始

        # 更新坐标管理器的显示尺寸
        self.coord_manager.update_display_dimensions(
            visible_width, visible_height,
            thermal_width, thermal_height,
            thermal_offset_x=visible_width
        )

        print(f"🖱️ 手动标注尺寸配置:")
        print(f"   可见光区域: [0-{self.visible_width}, 0-{self.display_height}]")
        print(f"   热成像区域: [{self.thermal_offset_x}-{self.thermal_offset_x + self.thermal_width}, 0-{self.display_height}]")
        print(f"   总显示尺寸: {self.display_width} x {self.display_height}")

        # 显示传感器坐标系统信息
        thermal_dims, visible_dims = self.coord_manager.get_sensor_dimensions()
        print(f"📏 传感器坐标系统:")
        print(f"   热成像传感器: {thermal_dims[0]}x{thermal_dims[1]}")
        print(f"   可见光传感器: {visible_dims[0]}x{visible_dims[1]}")

    def handle_mouse_press(self, x: int, y: int) -> bool:
        """
        处理鼠标按下事件

        Args:
            x, y: 鼠标坐标（相对于整个显示窗口）

        Returns:
            是否处理了该事件
        """
        # 检查是否启用标注模式
        if not self.annotation_widget.is_annotation_mode_enabled():
            return False

        # 使用坐标管理器转换为传感器坐标
        sensor_coords, is_thermal = self.coord_manager.widget_to_sensor_coords(x, y)

        if sensor_coords is None:
            return False  # 坐标不在有效区域内

        # 记录是否在热成像区域标注
        self.is_thermal_annotation = is_thermal

        # 保存传感器坐标作为起始点
        self.start_point = sensor_coords
        self.current_point = sensor_coords

        # 开始绘制
        self.drawing = True
        self.temp_bbox = None

        return True

    def handle_mouse_move(self, x: int, y: int) -> bool:
        """
        处理鼠标移动事件

        Args:
            x, y: 鼠标坐标

        Returns:
            是否处理了该事件
        """
        if not self.drawing or not self.start_point:
            return False

        # 使用坐标管理器转换为传感器坐标
        sensor_coords, is_thermal = self.coord_manager.widget_to_sensor_coords(x, y)

        if sensor_coords is None:
            return False  # 坐标不在有效区域内

        # 检查是否与开始标注的区域类型一致
        if is_thermal != self.is_thermal_annotation:
            return False

        # 更新当前点（传感器坐标）
        self.current_point = sensor_coords

        # 计算临时边界框（传感器坐标系）
        x1, y1 = self.start_point
        x2, y2 = self.current_point

        # 确保坐标顺序正确
        min_x, max_x = min(x1, x2), max(x1, x2)
        min_y, max_y = min(y1, y2), max(y1, y2)

        # 获取传感器尺寸进行边界检查
        thermal_dims, visible_dims = self.coord_manager.get_sensor_dimensions()

        if self.is_thermal_annotation:
            # 限制在热成像传感器区域内
            min_x = max(0, min_x)
            min_y = max(0, min_y)
            max_x = min(thermal_dims[0], max_x)
            max_y = min(thermal_dims[1], max_y)
        else:
            # 限制在可见光传感器区域内
            min_x = max(0, min_x)
            min_y = max(0, min_y)
            max_x = min(visible_dims[0], max_x)
            max_y = min(visible_dims[1], max_y)

        # 保存传感器坐标系的边界框
        self.temp_bbox = (min_x, min_y, max_x - min_x, max_y - min_y)

        return True

    def handle_mouse_release(self, x: int, y: int) -> bool:
        """
        处理鼠标释放事件

        Args:
            x, y: 鼠标坐标

        Returns:
            是否处理了该事件
        """
        if not self.drawing or not self.start_point:
            return False

        self.drawing = False

        # 检查边界框是否有效
        if self.temp_bbox and self.temp_bbox[2] > 10 and self.temp_bbox[3] > 10:
            # 弹出对话框输入物体名称
            self.prompt_for_label(self.temp_bbox)

        # 清理状态
        self.start_point = None
        self.current_point = None
        self.temp_bbox = None

        return True

    def prompt_for_label(self, bbox: Tuple[int, int, int, int]):
        """弹出对话框输入物体标签"""
        default_label = self.annotation_widget.default_label_input.text() or "物体"

        label, ok = QInputDialog.getText(
            None, "标注物体", "请输入物体名称:", text=default_label
        )

        if ok and label.strip():
            # 发送标注创建信号，包含是否为热成像标注的信息
            self.annotation_created.emit(bbox, label.strip(), self.is_thermal_annotation)
        else:
            print("❌ 标注已取消")

    def on_annotation_created(self, bbox: Tuple[int, int, int, int], label: str, is_thermal: bool):
        """处理标注创建"""
        self.annotation_widget.add_annotation_from_bbox(bbox, label, is_thermal)

    def is_in_visible_area(self, x: int, y: int) -> bool:
        """检查坐标是否在可见光区域内"""
        return 0 <= x < self.visible_width and 0 <= y < self.display_height

    def is_in_thermal_area(self, x: int, y: int) -> bool:
        """检查坐标是否在热成像区域内"""
        return (self.thermal_offset_x <= x < self.thermal_offset_x + self.thermal_width and
                0 <= y < self.display_height)

    def get_temp_bbox(self) -> Optional[Tuple[int, int, int, int]]:
        """获取临时边界框（用于实时绘制），返回显示坐标系的边界框"""
        if self.temp_bbox is None:
            return None

        # 将传感器坐标系的边界框转换为显示坐标系
        display_bbox = self.coord_manager.convert_bbox_sensor_to_display(
            self.temp_bbox, self.is_thermal_annotation
        )
        return display_bbox

    def is_drawing(self) -> bool:
        """检查是否正在绘制"""
        return self.drawing

    def is_thermal_drawing(self) -> bool:
        """检查是否在热成像区域绘制"""
        return self.drawing and self.is_thermal_annotation


class ManualAnnotationRenderer:
    """手动标注渲染器"""

    def __init__(self):
        """初始化渲染器"""
        self.font_scale = 0.6
        self.font_thickness = 1
        self.bbox_thickness = 2
        self.label_padding = 5
        self.coord_manager = get_coordinate_system_manager()

    def render_annotations(self, frame: np.ndarray,
                          annotations: List[ManualAnnotation],
                          is_thermal: bool = False,
                          thermal_offset_x: int = 0) -> np.ndarray:
        """
        在画面上渲染手动标注

        Args:
            frame: 输入画面
            annotations: 标注列表
            is_thermal: 是否为热成像画面
            thermal_offset_x: 热成像区域的X偏移量

        Returns:
            渲染后的画面
        """
        result_frame = frame.copy()

        for annotation in annotations:
            if not annotation.visible:
                continue

            # 根据标注类型决定是否渲染
            if hasattr(annotation, 'is_thermal'):
                if annotation.is_thermal != is_thermal:
                    continue
            else:
                # 兼容旧版本，默认为可见光标注
                if is_thermal:
                    continue

            self.render_single_annotation(result_frame, annotation, is_thermal, thermal_offset_x)

        return result_frame

    def render_single_annotation(self, frame: np.ndarray,
                               annotation: ManualAnnotation,
                               is_thermal: bool = False,
                               thermal_offset_x: int = 0):
        """渲染单个标注（标注存储为传感器坐标，渲染时转换为显示坐标）"""
        # 标注的bbox是传感器坐标系
        sensor_bbox = annotation.bbox
        color = annotation.color
        label = annotation.label

        # 检查标注类型是否匹配当前渲染模式
        annotation_is_thermal = getattr(annotation, 'is_thermal', False)
        if annotation_is_thermal != is_thermal:
            return  # 跳过不匹配的标注类型

        # 将传感器坐标转换为显示坐标
        display_bbox = self.coord_manager.convert_bbox_sensor_to_display(
            sensor_bbox, annotation_is_thermal
        )
        x, y, w, h = display_bbox

        # 如果是热成像标注，需要调整坐标到热成像区域
        if is_thermal and annotation_is_thermal:
            # 热成像标注需要加上偏移量
            x += thermal_offset_x

        # 绘制边界框
        cv2.rectangle(frame, (x, y), (x + w, y + h), color, self.bbox_thickness)

        # 准备标签文本
        label_text = f"{label}"
        if is_thermal and annotation_is_thermal:
            label_text += " [热成像]"

        # 使用中文文字渲染器绘制标签
        # 计算字体大小（将font_scale转换为像素大小）
        font_size = int(self.font_scale * 20)  # 将OpenCV的font_scale转换为像素大小

        # 标签位置
        label_x = x
        label_y = y - 10 if y > 30 else y + h + 10

        # 确保标签在画面内
        if label_y < 0:
            label_y = y + h + 30

        # 使用中文文字渲染器绘制标签（包含背景）
        try:
            draw_chinese_text(
                frame, label_text,
                (label_x, label_y),
                font_size=font_size,
                color=(255, 255, 255),  # 白色文字
                background_color=color,  # 使用标注颜色作为背景
                padding=self.label_padding
            )
        except Exception as e:
            print(f"⚠️ 中文标签渲染失败，使用备用方案: {e}")
            # 备用方案：使用OpenCV绘制（可能显示为问号）
            (text_width, text_height), baseline = cv2.getTextSize(
                label_text, cv2.FONT_HERSHEY_SIMPLEX, self.font_scale, self.font_thickness
            )

            # 绘制标签背景
            cv2.rectangle(frame,
                         (label_x, label_y - text_height - self.label_padding),
                         (label_x + text_width + self.label_padding * 2, label_y + baseline),
                         color, -1)

            # 绘制标签文字
            cv2.putText(frame, label_text,
                       (label_x + self.label_padding, label_y - self.label_padding),
                       cv2.FONT_HERSHEY_SIMPLEX, self.font_scale,
                       (255, 255, 255), self.font_thickness)
    
    def render_temp_bbox(self, frame: np.ndarray,
                        bbox: Tuple[int, int, int, int],
                        color: Tuple[int, int, int] = (0, 255, 255),
                        is_thermal: bool = False) -> np.ndarray:
        """
        渲染临时边界框（拖拽过程中）

        Args:
            frame: 输入画面
            bbox: 边界框 (x, y, width, height)
            color: 边界框颜色
            is_thermal: 是否为热成像区域

        Returns:
            渲染后的画面
        """
        result_frame = frame.copy()

        x, y, w, h = bbox

        # 绘制虚线边界框
        self.draw_dashed_rectangle(result_frame, (x, y), (x + w, y + h), color, 2)

        # 绘制提示文字
        hint_text = "热成像标注..." if is_thermal else "拖拽选择区域..."
        hint_y = y - 10 if y > 20 else y + h + 20

        try:
            # 使用中文文字渲染器绘制提示文字
            draw_chinese_text(
                result_frame, hint_text,
                (x, hint_y),
                font_size=16,
                color=(255, 255, 255),  # 白色文字
                background_color=color,  # 使用边界框颜色作为背景
                padding=3
            )
        except Exception as e:
            print(f"⚠️ 提示文字渲染失败，使用备用方案: {e}")
            # 备用方案：使用OpenCV绘制
            cv2.putText(result_frame, hint_text,
                       (x, hint_y),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)

        return result_frame
    
    def draw_dashed_rectangle(self, frame: np.ndarray, pt1: Tuple[int, int], 
                            pt2: Tuple[int, int], color: Tuple[int, int, int], 
                            thickness: int = 1, dash_length: int = 10):
        """绘制虚线矩形"""
        x1, y1 = pt1
        x2, y2 = pt2
        
        # 上边
        self.draw_dashed_line(frame, (x1, y1), (x2, y1), color, thickness, dash_length)
        # 下边
        self.draw_dashed_line(frame, (x1, y2), (x2, y2), color, thickness, dash_length)
        # 左边
        self.draw_dashed_line(frame, (x1, y1), (x1, y2), color, thickness, dash_length)
        # 右边
        self.draw_dashed_line(frame, (x2, y1), (x2, y2), color, thickness, dash_length)
    
    def draw_dashed_line(self, frame: np.ndarray, pt1: Tuple[int, int], 
                        pt2: Tuple[int, int], color: Tuple[int, int, int], 
                        thickness: int = 1, dash_length: int = 10):
        """绘制虚线"""
        x1, y1 = pt1
        x2, y2 = pt2
        
        # 计算线段长度和方向
        length = int(np.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2))
        if length == 0:
            return
        
        dx = (x2 - x1) / length
        dy = (y2 - y1) / length
        
        # 绘制虚线段
        for i in range(0, length, dash_length * 2):
            start_x = int(x1 + i * dx)
            start_y = int(y1 + i * dy)
            end_x = int(x1 + min(i + dash_length, length) * dx)
            end_y = int(y1 + min(i + dash_length, length) * dy)
            
            cv2.line(frame, (start_x, start_y), (end_x, end_y), color, thickness)
