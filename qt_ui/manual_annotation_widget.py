#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手动标注组件
允许用户在可见光画面上手动标注物体区域和名称
"""

import cv2
import json
import time
from typing import Dict, List, Tuple, Optional
from datetime import datetime
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton, 
                           QLineEdit, QLabel, QListWidget, QListWidgetItem,
                           QInputDialog, QMessageBox, QCheckBox, QSpinBox,
                           QColorDialog, QComboBox)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QColor, QFont
from dataclasses import dataclass
from pathlib import Path


@dataclass
class ManualAnnotation:
    """手动标注数据类（使用传感器原始坐标系统）"""
    id: str
    bbox: Tuple[int, int, int, int]  # (x, y, width, height) - 传感器坐标系
    label: str
    confidence: float
    color: Tuple[int, int, int]  # BGR颜色
    timestamp: datetime
    visible: bool = True
    is_thermal: bool = False  # 是否为热成像标注


class ManualAnnotationManager:
    """手动标注管理器"""
    
    def __init__(self):
        """初始化标注管理器"""
        self.annotations: Dict[str, ManualAnnotation] = {}
        self.next_id = 1
        self.save_file = Path("manual_annotations.json")
        self.load_annotations()
    
    def add_annotation(self, bbox: Tuple[int, int, int, int], label: str,
                      color: Tuple[int, int, int] = (0, 255, 0),
                      is_thermal: bool = False) -> str:
        """
        添加新标注

        Args:
            bbox: 边界框 (x, y, width, height)
            label: 物体标签
            color: 边界框颜色 (BGR)
            is_thermal: 是否为热成像标注

        Returns:
            标注ID
        """
        annotation_id = f"manual_{self.next_id:04d}"
        self.next_id += 1

        annotation = ManualAnnotation(
            id=annotation_id,
            bbox=bbox,
            label=label,
            confidence=1.0,  # 手动标注置信度为1.0
            color=color,
            timestamp=datetime.now(),
            is_thermal=is_thermal
        )

        self.annotations[annotation_id] = annotation
        self.save_annotations()
        return annotation_id
    
    def remove_annotation(self, annotation_id: str) -> bool:
        """删除标注"""
        if annotation_id in self.annotations:
            del self.annotations[annotation_id]
            self.save_annotations()
            return True
        return False
    
    def update_annotation(self, annotation_id: str, **kwargs) -> bool:
        """更新标注属性"""
        if annotation_id in self.annotations:
            annotation = self.annotations[annotation_id]
            for key, value in kwargs.items():
                if hasattr(annotation, key):
                    setattr(annotation, key, value)
            self.save_annotations()
            return True
        return False
    
    def get_visible_annotations(self) -> List[ManualAnnotation]:
        """获取可见的标注列表"""
        return [ann for ann in self.annotations.values() if ann.visible]
    
    def save_annotations(self):
        """保存标注到文件"""
        try:
            data = {}
            for ann_id, annotation in self.annotations.items():
                data[ann_id] = {
                    'bbox': annotation.bbox,
                    'label': annotation.label,
                    'confidence': annotation.confidence,
                    'color': annotation.color,
                    'timestamp': annotation.timestamp.isoformat(),
                    'visible': annotation.visible,
                    'is_thermal': getattr(annotation, 'is_thermal', False)
                }
            
            with open(self.save_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"❌ 保存标注失败: {e}")
    
    def load_annotations(self):
        """从文件加载标注"""
        try:
            if self.save_file.exists():
                with open(self.save_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                for ann_id, ann_data in data.items():
                    annotation = ManualAnnotation(
                        id=ann_id,
                        bbox=tuple(ann_data['bbox']),
                        label=ann_data['label'],
                        confidence=ann_data['confidence'],
                        color=tuple(ann_data['color']),
                        timestamp=datetime.fromisoformat(ann_data['timestamp']),
                        visible=ann_data.get('visible', True),
                        is_thermal=ann_data.get('is_thermal', False)
                    )
                    self.annotations[ann_id] = annotation
                
                # 更新下一个ID
                if self.annotations:
                    max_id = max([int(ann_id.split('_')[1]) for ann_id in self.annotations.keys()])
                    self.next_id = max_id + 1
                    
        except Exception as e:
            print(f"⚠️ 加载标注失败: {e}")


class ManualAnnotationWidget(QWidget):
    """手动标注控制面板"""
    
    # 信号
    annotation_mode_changed = pyqtSignal(bool)  # 标注模式切换
    annotation_added = pyqtSignal(str)  # 添加标注
    annotation_removed = pyqtSignal(str)  # 删除标注
    annotation_updated = pyqtSignal(str)  # 更新标注
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.manager = ManualAnnotationManager()
        self.annotation_mode = False
        self.current_color = (0, 255, 0)  # 默认绿色
        self.init_ui()
        self.update_annotation_list()
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)
        
        # 标题
        title_label = QLabel("📝 手动标注")
        title_label.setStyleSheet("""
            font-size: 14px;
            font-weight: bold;
            color: #4CAF50;
            background-color: rgba(76, 175, 80, 0.15);
            padding: 8px;
            border-radius: 5px;
            border: 1px solid rgba(76, 175, 80, 0.3);
            margin-bottom: 5px;
        """)
        layout.addWidget(title_label)
        
        # 标注模式开关
        mode_layout = QHBoxLayout()
        self.mode_checkbox = QCheckBox("启用标注模式")
        self.mode_checkbox.setStyleSheet("color: #ffffff;")
        self.mode_checkbox.toggled.connect(self.toggle_annotation_mode)
        mode_layout.addWidget(self.mode_checkbox)
        layout.addLayout(mode_layout)
        
        # 默认标签设置
        label_layout = QHBoxLayout()
        label_layout.addWidget(QLabel("默认标签:"))
        self.default_label_input = QLineEdit("物体")
        self.default_label_input.setStyleSheet("""
            QLineEdit {
                background-color: #404040;
                border: 1px solid #606060;
                border-radius: 3px;
                padding: 3px;
                color: white;
            }
        """)
        label_layout.addWidget(self.default_label_input)
        layout.addLayout(label_layout)
        
        # 颜色选择
        color_layout = QHBoxLayout()
        color_layout.addWidget(QLabel("边界框颜色:"))
        self.color_button = QPushButton()
        self.color_button.setFixedSize(30, 25)
        self.color_button.setStyleSheet(f"background-color: rgb{self.current_color[::-1]};")  # RGB顺序
        self.color_button.clicked.connect(self.choose_color)
        color_layout.addWidget(self.color_button)
        layout.addLayout(color_layout)
        
        # 标注列表
        list_label = QLabel("标注列表:")
        list_label.setStyleSheet("color: #ffffff; margin-top: 10px;")
        layout.addWidget(list_label)
        
        self.annotation_list = QListWidget()
        self.annotation_list.setStyleSheet("""
            QListWidget {
                background-color: #323232;
                border: 1px solid #505050;
                border-radius: 3px;
                color: white;
                font-size: 11px;
            }
            QListWidget::item {
                padding: 3px;
                border-bottom: 1px solid #404040;
            }
            QListWidget::item:selected {
                background-color: #0078d4;
            }
        """)
        self.annotation_list.setMaximumHeight(150)
        self.annotation_list.itemDoubleClicked.connect(self.edit_annotation)
        layout.addWidget(self.annotation_list)
        
        # 操作按钮
        button_layout = QHBoxLayout()
        
        self.edit_button = QPushButton("编辑")
        self.edit_button.clicked.connect(self.edit_selected_annotation)
        button_layout.addWidget(self.edit_button)
        
        self.delete_button = QPushButton("删除")
        self.delete_button.clicked.connect(self.delete_selected_annotation)
        button_layout.addWidget(self.delete_button)
        
        self.clear_button = QPushButton("清空")
        self.clear_button.clicked.connect(self.clear_all_annotations)
        button_layout.addWidget(self.clear_button)
        
        # 设置按钮样式
        for button in [self.edit_button, self.delete_button, self.clear_button]:
            button.setStyleSheet("""
                QPushButton {
                    background-color: #404040;
                    border: 1px solid #606060;
                    border-radius: 3px;
                    padding: 5px 10px;
                    font-size: 10px;
                    color: white;
                }
                QPushButton:hover {
                    background-color: #505050;
                }
                QPushButton:pressed {
                    background-color: #303030;
                }
            """)
        
        layout.addLayout(button_layout)
        
        # 统计信息
        self.stats_label = QLabel("标注数量: 0")
        self.stats_label.setStyleSheet("color: #aaaaaa; font-size: 10px;")
        layout.addWidget(self.stats_label)
        
        # 添加弹性空间
        layout.addStretch()
    
    def toggle_annotation_mode(self, enabled: bool):
        """切换标注模式"""
        self.annotation_mode = enabled
        self.annotation_mode_changed.emit(enabled)
        
        if enabled:
            print("🖱️ 标注模式已启用 - 在可见光画面上拖拽鼠标创建标注")
        else:
            print("🖱️ 标注模式已禁用")
    
    def choose_color(self):
        """选择边界框颜色"""
        color = QColorDialog.getColor(QColor(*self.current_color[::-1]), self)  # RGB顺序
        if color.isValid():
            # 转换为BGR格式
            self.current_color = (color.blue(), color.green(), color.red())
            self.color_button.setStyleSheet(f"background-color: {color.name()};")
    
    def add_annotation_from_bbox(self, bbox: Tuple[int, int, int, int],
                                label: str = None, is_thermal: bool = False) -> str:
        """从边界框添加标注（bbox为传感器坐标系）"""
        if label is None:
            label = self.default_label_input.text() or "物体"

        annotation_id = self.manager.add_annotation(bbox, label, self.current_color, is_thermal)
        self.update_annotation_list()
        self.annotation_added.emit(annotation_id)

        area_type = "热成像" if is_thermal else "可见光"
        print(f"✅ 添加{area_type}标注: {label} at {bbox}")
        return annotation_id
    
    def update_annotation_list(self):
        """更新标注列表显示"""
        self.annotation_list.clear()
        
        for annotation in self.manager.annotations.values():
            area_type = "[热成像]" if getattr(annotation, 'is_thermal', False) else "[可见光]"
            item_text = f"{area_type} [{annotation.id}] {annotation.label}"
            if not annotation.visible:
                item_text += " (隐藏)"

            item = QListWidgetItem(item_text)
            item.setData(Qt.UserRole, annotation.id)

            # 设置颜色指示
            color = QColor(*annotation.color[::-1])  # BGR转RGB
            item.setBackground(color.lighter(180))

            self.annotation_list.addItem(item)
        
        # 更新统计信息
        total_count = len(self.manager.annotations)
        visible_count = len(self.manager.get_visible_annotations())
        self.stats_label.setText(f"标注数量: {visible_count}/{total_count}")
    
    def edit_annotation(self, item: QListWidgetItem):
        """编辑标注"""
        annotation_id = item.data(Qt.UserRole)
        self.edit_annotation_by_id(annotation_id)
    
    def edit_selected_annotation(self):
        """编辑选中的标注"""
        current_item = self.annotation_list.currentItem()
        if current_item:
            self.edit_annotation(current_item)
    
    def edit_annotation_by_id(self, annotation_id: str):
        """根据ID编辑标注"""
        if annotation_id not in self.manager.annotations:
            return
        
        annotation = self.manager.annotations[annotation_id]
        
        # 编辑标签
        new_label, ok = QInputDialog.getText(
            self, "编辑标注", "物体名称:", text=annotation.label
        )
        
        if ok and new_label.strip():
            self.manager.update_annotation(annotation_id, label=new_label.strip())
            self.update_annotation_list()
            self.annotation_updated.emit(annotation_id)
            print(f"✏️ 更新标注: {annotation_id} -> {new_label}")

    def delete_selected_annotation(self):
        """删除选中的标注"""
        current_item = self.annotation_list.currentItem()
        if current_item:
            annotation_id = current_item.data(Qt.UserRole)

            reply = QMessageBox.question(
                self, "确认删除", f"确定要删除标注 {annotation_id} 吗？",
                QMessageBox.Yes | QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                self.manager.remove_annotation(annotation_id)
                self.update_annotation_list()
                self.annotation_removed.emit(annotation_id)
                print(f"🗑️ 删除标注: {annotation_id}")

    def clear_all_annotations(self):
        """清空所有标注"""
        if not self.manager.annotations:
            return

        reply = QMessageBox.question(
            self, "确认清空", "确定要清空所有标注吗？",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.manager.annotations.clear()
            self.manager.save_annotations()
            self.update_annotation_list()
            print("🗑️ 已清空所有标注")

    def get_visible_annotations(self) -> List[ManualAnnotation]:
        """获取可见的标注列表"""
        return self.manager.get_visible_annotations()

    def is_annotation_mode_enabled(self) -> bool:
        """检查是否启用标注模式"""
        return self.annotation_mode


# 全局标注管理器实例
_global_annotation_manager = None


def get_global_annotation_manager() -> ManualAnnotationManager:
    """
    获取全局标注管理器实例

    Returns:
        ManualAnnotationManager: 全局标注管理器实例
    """
    global _global_annotation_manager
    if _global_annotation_manager is None:
        _global_annotation_manager = ManualAnnotationManager()
    return _global_annotation_manager


def set_global_annotation_manager(manager: ManualAnnotationManager):
    """
    设置全局标注管理器实例

    Args:
        manager: 标注管理器实例
    """
    global _global_annotation_manager
    _global_annotation_manager = manager
