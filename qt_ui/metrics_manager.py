#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
监控指标管理器
整合全局指标计算和单个热源详细指标计算
"""

import numpy as np
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from dataclasses import dataclass

from utils.logger import get_logger
from detection.analysis.global_metrics_calculator import GlobalMetricsCalculator, GlobalMetrics
from detection.analysis.heat_source_analyzer import HeatSourceAnalyzer


@dataclass
class DetailedHeatSourceMetrics:
    """单个热源的详细指标（14项）"""
    tracking_id: int
    timestamp: datetime
    
    # 尺寸指标（4项）
    length: int                           # 长度（像素）
    length_change_rate: Optional[float]   # 长度变化率（像素/秒）
    width: int                            # 宽度（像素）
    width_change_rate: Optional[float]    # 宽度变化率（像素/秒）
    
    # 面积指标（2项）
    area: int                             # 像素级面积（像素）
    area_change_rate: Optional[float]     # 面积变化率（像素/秒）
    
    # 温度指标（8项）
    min_temperature: float                # 最低温度（℃）
    min_temperature_change_rate: Optional[float]  # 最低温度变化率（℃/秒）
    max_temperature: float                # 最高温度（℃）
    max_temperature_change_rate: Optional[float]  # 最高温度变化率（℃/秒）
    avg_temperature: float                # 平均温度（℃）
    avg_temperature_change_rate: Optional[float]  # 平均温度变化率（℃/秒）
    temperature_diff: float               # 温差（最高温-最低温）（℃）
    temperature_diff_change_rate: Optional[float]  # 温差变化率（℃/秒）


class MetricsManager:
    """监控指标管理器"""
    
    def __init__(self, heat_source_analyzer: Optional[HeatSourceAnalyzer] = None):
        self.logger = get_logger("MetricsManager")
        
        # 热源分析器引用
        self.heat_source_analyzer = heat_source_analyzer
        
        # 全局指标计算器
        self.global_calculator = GlobalMetricsCalculator()
        
        # 缓存最新的指标数据
        self.latest_global_metrics: Optional[GlobalMetrics] = None
        self.latest_detailed_metrics: Dict[int, DetailedHeatSourceMetrics] = {}
        
        self.logger.info("监控指标管理器初始化完成")
    
    def set_heat_source_analyzer(self, analyzer: HeatSourceAnalyzer):
        """设置热源分析器"""
        self.heat_source_analyzer = analyzer
        self.logger.info("热源分析器已设置")
    
    def calculate_all_metrics(self, heat_sources_detail: List) -> Dict[str, Any]:
        """
        计算所有监控指标
        
        Args:
            heat_sources_detail: 热源详细信息列表
            
        Returns:
            包含全局指标和详细指标的字典
        """
        try:
            # 转换为快照格式
            snapshots = self._convert_to_snapshots(heat_sources_detail)
            
            # 计算全局指标
            global_metrics = self.global_calculator.calculate_global_metrics(snapshots)
            self.latest_global_metrics = global_metrics
            
            # 计算详细指标
            detailed_metrics = self._calculate_detailed_metrics(heat_sources_detail)
            self.latest_detailed_metrics = detailed_metrics
            
            return {
                'global_metrics': global_metrics.to_dict(),
                'detailed_metrics': detailed_metrics,
                'timestamp': datetime.now().strftime('%H:%M:%S')
            }
            
        except Exception as e:
            self.logger.error(f"计算监控指标失败: {e}")
            return {
                'global_metrics': self._get_empty_global_metrics(),
                'detailed_metrics': {},
                'timestamp': datetime.now().strftime('%H:%M:%S')
            }
    
    def get_global_metrics_dict(self) -> Dict:
        """获取全局指标字典格式"""
        if self.latest_global_metrics:
            return self.latest_global_metrics.to_dict()
        return self._get_empty_global_metrics()
    
    def get_detailed_metrics_for_display(self, display_mode: str) -> Dict[int, Dict]:
        """
        根据显示模式获取详细指标
        
        Args:
            display_mode: 显示模式 ('overview', 'detailed', 'expert')
            
        Returns:
            格式化后的详细指标字典
        """
        formatted_metrics = {}
        
        for tracking_id, metrics in self.latest_detailed_metrics.items():
            if display_mode == 'overview':
                formatted_metrics[tracking_id] = self._format_overview_metrics(metrics)
            elif display_mode == 'detailed':
                formatted_metrics[tracking_id] = self._format_detailed_metrics(metrics)
            elif display_mode == 'expert':
                formatted_metrics[tracking_id] = self._format_expert_metrics(metrics)
        
        return formatted_metrics
    
    def _convert_to_snapshots(self, heat_sources_detail: List) -> List:
        """将热源详细信息转换为快照格式"""
        from detection.analysis.heat_source_history import HeatSourceSnapshot
        
        snapshots = []
        timestamp = datetime.now()
        
        for source in heat_sources_detail:
            try:
                snapshot = HeatSourceSnapshot(
                    timestamp=timestamp,
                    tracking_id=getattr(source, 'tracking_id', 0),
                    position=getattr(source, 'position', (0, 0)),
                    size=getattr(source, 'size', (0, 0)),
                    area=getattr(source, 'area', 0),
                    max_temperature=getattr(source, 'max_temperature', 0.0),
                    min_temperature=getattr(source, 'min_temperature', 0.0),
                    avg_temperature=(getattr(source, 'max_temperature', 0.0) + 
                                   getattr(source, 'min_temperature', 0.0)) / 2,
                    bbox=getattr(source, 'bbox', (0, 0, 0, 0)),
                    confidence=getattr(source, 'confidence', 1.0)
                )
                snapshots.append(snapshot)
            except Exception as e:
                self.logger.warning(f"转换热源快照失败: {e}")
                continue
        
        return snapshots
    
    def _calculate_detailed_metrics(self, heat_sources_detail: List) -> Dict[int, DetailedHeatSourceMetrics]:
        """计算详细指标"""
        detailed_metrics = {}
        timestamp = datetime.now()
        
        for source in heat_sources_detail:
            try:
                tracking_id = getattr(source, 'tracking_id', None)
                if tracking_id is None:
                    continue
                
                # 基础指标
                size = getattr(source, 'size', (0, 0))
                length = size[0]
                width = size[1]
                area = getattr(source, 'area', 0)
                min_temp = getattr(source, 'min_temperature', 0.0)
                max_temp = getattr(source, 'max_temperature', 0.0)
                avg_temp = (min_temp + max_temp) / 2
                temp_diff = max_temp - min_temp
                
                # 变化率计算
                change_rates = self._get_change_rates(tracking_id)
                
                metrics = DetailedHeatSourceMetrics(
                    tracking_id=tracking_id,
                    timestamp=timestamp,
                    length=length,
                    length_change_rate=change_rates.get('length_change_rate'),
                    width=width,
                    width_change_rate=change_rates.get('width_change_rate'),
                    area=area,
                    area_change_rate=change_rates.get('area_change_rate'),
                    min_temperature=min_temp,
                    min_temperature_change_rate=change_rates.get('min_temp_change_rate'),
                    max_temperature=max_temp,
                    max_temperature_change_rate=change_rates.get('max_temp_change_rate'),
                    avg_temperature=avg_temp,
                    avg_temperature_change_rate=change_rates.get('avg_temp_change_rate'),
                    temperature_diff=temp_diff,
                    temperature_diff_change_rate=change_rates.get('temp_diff_change_rate')
                )
                
                detailed_metrics[tracking_id] = metrics
                
            except Exception as e:
                self.logger.warning(f"计算详细指标失败: {e}")
                continue
        
        return detailed_metrics
    
    def _get_change_rates(self, tracking_id: int) -> Dict[str, Optional[float]]:
        """获取变化率数据"""
        if not self.heat_source_analyzer:
            return {}
        
        try:
            # 获取基础变化率
            change_rates = self.heat_source_analyzer.get_change_rates(tracking_id, 10.0)
            
            result = {}
            
            # 面积变化率
            if 'area' in change_rates:
                area_rate = change_rates['area']
                if hasattr(area_rate, 'change_rate'):
                    result['area_change_rate'] = area_rate.change_rate
            
            # 温度变化率
            for temp_type in ['min_temp', 'max_temp', 'avg_temp']:
                if temp_type in change_rates:
                    temp_rate = change_rates[temp_type]
                    if hasattr(temp_rate, 'change_rate'):
                        result[f'{temp_type}_change_rate'] = temp_rate.change_rate
            
            # 尺寸变化率（需要从历史数据计算）
            # 这里暂时返回None，后续可以扩展
            result['length_change_rate'] = None
            result['width_change_rate'] = None
            result['temp_diff_change_rate'] = None
            
            return result
            
        except Exception as e:
            self.logger.warning(f"获取变化率失败: {e}")
            return {}
    
    def _get_empty_global_metrics(self) -> Dict:
        """获取空的全局指标"""
        return {
            'timestamp': datetime.now().strftime('%H:%M:%S'),
            'total_heat_sources': 0,
            'total_heat_sources_change_rate': None,
            'total_heat_area': 0,
            'total_heat_area_change_rate': None,
            'avg_temperature_30days': None
        }
    
    def _format_overview_metrics(self, metrics: DetailedHeatSourceMetrics) -> Dict:
        """格式化概览模式指标"""
        return {
            'area': metrics.area,
            'max_temperature': metrics.max_temperature,
            'basic_info': f"面积: {metrics.area} 像素, 最高温: {metrics.max_temperature:.1f}°C"
        }
    
    def _format_detailed_metrics(self, metrics: DetailedHeatSourceMetrics) -> Dict:
        """格式化详细模式指标"""
        change_info = []
        if metrics.area_change_rate is not None:
            change_info.append(f"面积{metrics.area_change_rate:+.1f}/秒")
        if metrics.max_temperature_change_rate is not None:
            change_info.append(f"最高温{metrics.max_temperature_change_rate:+.2f}°C/秒")
        
        return {
            'length': metrics.length,
            'width': metrics.width,
            'area': metrics.area,
            'min_temperature': metrics.min_temperature,
            'max_temperature': metrics.max_temperature,
            'change_rates': " ".join(change_info) if change_info else "无变化率数据"
        }
    
    def _format_expert_metrics(self, metrics: DetailedHeatSourceMetrics) -> Dict:
        """格式化专家模式指标"""
        return {
            'length': metrics.length,
            'length_change_rate': metrics.length_change_rate,
            'width': metrics.width,
            'width_change_rate': metrics.width_change_rate,
            'area': metrics.area,
            'area_change_rate': metrics.area_change_rate,
            'min_temperature': metrics.min_temperature,
            'min_temperature_change_rate': metrics.min_temperature_change_rate,
            'max_temperature': metrics.max_temperature,
            'max_temperature_change_rate': metrics.max_temperature_change_rate,
            'avg_temperature': metrics.avg_temperature,
            'avg_temperature_change_rate': metrics.avg_temperature_change_rate,
            'temperature_diff': metrics.temperature_diff,
            'temperature_diff_change_rate': metrics.temperature_diff_change_rate
        }
