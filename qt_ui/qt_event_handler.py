#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Qt事件处理模块
实现完整的Qt事件处理，包括鼠标事件、键盘快捷键、温度测量等
"""

import cv2
from typing import Optional, Callable, Tuple
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QObject
from PyQt5.QtGui import QKeySequence
from PyQt5.QtWidgets import QShortcut

from utils.logger import get_logger


class QtMouseHandler(QObject):
    """Qt鼠标处理器"""
    
    # 定义信号
    temperature_updated = pyqtSignal(float)  # 温度更新信号
    position_updated = pyqtSignal(int, int)  # 位置更新信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger("QtMouseHandler")
        
        # 鼠标状态
        self.mouse_x = 0
        self.mouse_y = 0
        self.mouse_temp = 0.0
        
        # 显示区域配置
        self.visible_display_width = 640
        self.thermal_display_width = 640
        self.display_height = 480
        self.sidebar_width = 300
        self.sidebar_enabled = True
        self.toolbar_height = 50
        
        # 当前帧引用
        self.thermal_frame = None
        self.visible_frame = None
        self.combined_frame = None
        
        # 温度计算相关
        self.temperature_reader = None
        self.temperature_calculator: Optional[Callable] = None
        
        # 性能优化
        self.temp_update_counter = 0
        self.temp_update_interval = 3  # 每3次鼠标移动更新一次温度
        self.temp_cache = {}
        self.cache_max_size = 100
        self.last_temp_position = (-1, -1)
        
        # 定时器用于温度更新
        self.temp_update_timer = QTimer()
        self.temp_update_timer.timeout.connect(self._update_temperature_delayed)
        self.temp_update_timer.setSingleShot(True)
        
        # 待更新的位置
        self.pending_temp_position = None
    
    def set_display_config(self, visible_width: int, thermal_width: int, height: int):
        """设置显示配置"""
        self.visible_display_width = visible_width
        self.thermal_display_width = thermal_width
        self.display_height = height
        self.logger.info(f"显示配置更新: 可见光={visible_width}, 热成像={thermal_width}, 高度={height}")
    
    def set_sidebar_config(self, width: int, enabled: bool):
        """设置侧边栏配置"""
        self.sidebar_width = width
        self.sidebar_enabled = enabled
        self.logger.info(f"侧边栏配置更新: 宽度={width}, 启用={enabled}")
    
    def set_frames(self, thermal_frame, visible_frame, combined_frame):
        """设置当前帧"""
        self.thermal_frame = thermal_frame
        self.visible_frame = visible_frame
        self.combined_frame = combined_frame
    
    def set_temperature_reader(self, reader):
        """设置温度读取器"""
        self.temperature_reader = reader
    
    def set_temperature_calculator(self, calculator: Callable):
        """设置温度计算函数"""
        self.temperature_calculator = calculator
    
    def handle_mouse_move(self, x: int, y: int):
        """处理鼠标移动事件"""
        # 调整坐标（减去工具栏高度）
        adjusted_y = y - self.toolbar_height
        if adjusted_y < 0:
            return

        # 更新鼠标位置
        self.mouse_x, self.mouse_y = x, adjusted_y



        # 发射位置更新信号
        self.position_updated.emit(x, adjusted_y)

        # 温度计算采用延迟策略减少计算频率
        self._schedule_temperature_update(x, adjusted_y)
    
    def handle_mouse_click(self, x: int, y: int, button: int):
        """处理鼠标点击事件"""
        # 调整坐标
        adjusted_y = y - self.toolbar_height
        if adjusted_y < 0:
            return
        
        # 转换Qt按钮到OpenCV事件
        if button == Qt.LeftButton:
            cv_event = cv2.EVENT_LBUTTONDOWN
        elif button == Qt.RightButton:
            cv_event = cv2.EVENT_RBUTTONDOWN
        else:
            return
        
        self.logger.info(f"鼠标点击: ({x}, {adjusted_y}), 按钮={button}")
        
        # 这里可以添加点击处理逻辑
        # 例如：在热成像区域点击时进行特殊处理
        if self.is_in_thermal_area(x):
            self.logger.info("点击在热成像区域")
    
    def _schedule_temperature_update(self, x: int, y: int):
        """安排温度更新"""
        self.pending_temp_position = (x, y)
        
        # 使用定时器延迟更新，避免频繁计算
        if not self.temp_update_timer.isActive():
            self.temp_update_timer.start(50)  # 50ms延迟
    
    def _update_temperature_delayed(self):
        """延迟温度更新"""
        if self.pending_temp_position is None:
            print("❌ 延迟温度更新: 没有待处理位置")
            return

        x, y = self.pending_temp_position
        self.pending_temp_position = None
        print(f"🔄 延迟温度更新: 处理位置 ({x}, {y})")

        # 降频策略：不是每次都计算温度
        self.temp_update_counter += 1
        if self.temp_update_counter < self.temp_update_interval:
            print(f"⏳ 降频跳过: {self.temp_update_counter}/{self.temp_update_interval}")
            return

        self.temp_update_counter = 0
        print(f"✅ 降频通过，开始温度计算")

        # 检查位置是否变化足够大
        last_x, last_y = self.last_temp_position
        if abs(x - last_x) < 5 and abs(y - last_y) < 5:
            print(f"📍 位置变化太小: ({last_x}, {last_y}) -> ({x}, {y})")
            return  # 位置变化太小，不更新温度

        self.last_temp_position = (x, y)
        print(f"📍 位置变化足够大，继续温度计算")
        
        # 检查缓存
        cache_key = (x // 10, y // 10)  # 10像素精度的缓存
        if cache_key in self.temp_cache:
            self.mouse_temp = self.temp_cache[cache_key]
            self.temperature_updated.emit(self.mouse_temp)
            return
        
        # 计算新温度
        new_temp = self._calculate_temperature_fast(x, y)
        print(f"🔄 快速温度计算结果: {new_temp:.1f}°C")

        # 更新缓存
        if len(self.temp_cache) >= self.cache_max_size:
            # 清理一半缓存
            keys_to_remove = list(self.temp_cache.keys())[:self.cache_max_size // 2]
            for key in keys_to_remove:
                del self.temp_cache[key]

        self.temp_cache[cache_key] = new_temp
        self.mouse_temp = new_temp

        # 发射温度更新信号
        print(f"📡 发射温度更新信号: {new_temp:.1f}°C")
        self.temperature_updated.emit(new_temp)
    
    def _calculate_temperature_fast(self, x: int, y: int) -> float:
        """快速温度计算方法"""
        if self.combined_frame is None:
            return 0.0
        
        # 计算视频区域的总宽度
        video_total_width = self.visible_display_width + self.thermal_display_width
        
        # 判断鼠标位置
        if x < self.visible_display_width:
            # 鼠标在可见光区域
            return 0.0  # 可见光区域不显示温度
        elif x < video_total_width:
            # 鼠标在红外区域
            thermal_x = x - self.visible_display_width
            thermal_y = y
            
            if self.thermal_frame is not None and self.thermal_display_width > 0:
                # 计算在原始红外图像中的坐标
                original_x = int(thermal_x * self.thermal_frame.shape[1] / self.thermal_display_width)
                original_y = int(thermal_y * self.thermal_frame.shape[0] / self.display_height)
                
                # 边界检查
                original_x = max(0, min(original_x, self.thermal_frame.shape[1] - 1))
                original_y = max(0, min(original_y, self.thermal_frame.shape[0] - 1))
                
                # 快速温度计算
                return self._calculate_temperature(original_x, original_y)
        else:
            # 鼠标在侧边栏区域
            return 0.0  # 侧边栏区域不显示温度
        
        return 0.0
    
    def _calculate_temperature(self, x: int, y: int) -> float:
        """计算指定位置的温度"""
        try:
            # 优先使用真实温度读取器（更准确但可能较慢）
            if self.temperature_reader:
                # 检查温度矩阵是否存在
                if (hasattr(self.temperature_reader, 'current_temp_matrix') and
                    self.temperature_reader.current_temp_matrix is not None):

                    # 将显示坐标转换为温度矩阵坐标
                    matrix_height, matrix_width = self.temperature_reader.current_temp_matrix.shape

                    if self.thermal_frame is not None:
                        matrix_x = int(x * matrix_width / self.thermal_frame.shape[1])
                        matrix_y = int(y * matrix_height / self.thermal_frame.shape[0])
                    else:
                        # 如果没有thermal_frame，假设320x240的热成像分辨率
                        matrix_x = int(x * matrix_width / 320)
                        matrix_y = int(y * matrix_height / 240)

                    # 边界检查
                    matrix_x = max(0, min(matrix_x, matrix_width - 1))
                    matrix_y = max(0, min(matrix_y, matrix_height - 1))

                    temp = self.temperature_reader.get_temperature_at_point(matrix_x, matrix_y)
                    if temp is not None:
                        print(f"🌡️ 温度读取成功: 显示坐标({x}, {y}) -> 矩阵坐标({matrix_x}, {matrix_y}) = {temp:.1f}°C")
                        return temp
                    else:
                        print(f"❌ 温度读取失败: 显示坐标({x}, {y}) -> 矩阵坐标({matrix_x}, {matrix_y}), 矩阵大小: {matrix_width}x{matrix_height}")
                else:
                    print(f"❌ 温度矩阵不存在或为空")
                    # 尝试强制获取最新温度数据
                    if hasattr(self.temperature_reader, 'get_real_temperature_data'):
                        result = self.temperature_reader.get_real_temperature_data()
                        if result and result.get('temp_data'):
                            self.temperature_reader.current_temp_matrix = result['temp_data']['matrix']
                            print(f"✅ 强制更新温度矩阵成功")
                        else:
                            print(f"❌ 强制更新温度矩阵失败")

            # 备用：使用图像分析计算温度
            if self.temperature_calculator and self.thermal_frame is not None:
                temp = self.temperature_calculator(self.thermal_frame, x, y)
                print(f"🔧 图像分析温度: ({x}, {y}) = {temp:.1f}°C")
                return temp

        except Exception as e:
            print(f"❌ 温度计算异常: {e}")

        return 0.0
    
    def get_mouse_position(self) -> Tuple[int, int]:
        """获取鼠标位置（调整后的坐标，不包含工具栏）"""
        return self.mouse_x, self.mouse_y
    
    def get_window_mouse_position(self) -> Tuple[int, int]:
        """获取窗口鼠标位置（包含工具栏的完整窗口坐标）"""
        return self.mouse_x, self.mouse_y + self.toolbar_height
    
    def get_mouse_temperature(self) -> float:
        """获取鼠标位置的温度"""
        return self.mouse_temp
    
    def is_in_thermal_area(self, x: int = None) -> bool:
        """判断鼠标是否在红外区域"""
        if x is None:
            x = self.mouse_x
        video_total_width = self.visible_display_width + self.thermal_display_width
        return self.visible_display_width <= x < video_total_width
    
    def set_performance_mode(self, mode: str):
        """设置性能模式"""
        if mode == "fast":
            # 快速模式：降低温度计算频率
            self.temp_update_interval = 5
            self.cache_max_size = 50
        elif mode == "balanced":
            # 平衡模式：默认设置
            self.temp_update_interval = 3
            self.cache_max_size = 100
        elif mode == "accurate":
            # 精确模式：每次都计算
            self.temp_update_interval = 1
            self.cache_max_size = 200
        else:
            self.logger.warning(f"未知的性能模式: {mode}")
        
        self.logger.info(f"性能模式设置为: {mode}")
    
    def clear_cache(self):
        """清理温度缓存"""
        self.temp_cache.clear()
        self.last_temp_position = (-1, -1)
        self.logger.info("温度缓存已清理")


class QtKeyboardHandler(QObject):
    """Qt键盘处理器"""

    # 定义信号
    detection_toggle_requested = pyqtSignal()
    human_detection_toggle_requested = pyqtSignal()
    thermal_human_toggle_requested = pyqtSignal()
    adaptive_mode_toggle_requested = pyqtSignal()
    threshold_adjust_requested = pyqtSignal(float)  # 阈值调节
    save_image_requested = pyqtSignal()
    save_debug_requested = pyqtSignal()
    help_toggle_requested = pyqtSignal()
    sidebar_scroll_requested = pyqtSignal(int)  # 滚动方向

    quit_requested = pyqtSignal()

    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger("QtKeyboardHandler")

        # 系统实例引用
        self.system_instance = None

        # 快捷键映射
        self.shortcuts = {}

        # 初始化快捷键
        self._setup_shortcuts()

    def set_system_instance(self, system_instance):
        """设置系统实例引用"""
        self.system_instance = system_instance

    def _setup_shortcuts(self):
        """设置快捷键"""
        # 基础快捷键 - 修改为Ctrl+Q避免误触
        self.shortcuts['quit'] = Qt.Key_Q  # 需要配合Ctrl使用
        self.shortcuts['help'] = Qt.Key_H

        # 检测控制快捷键
        self.shortcuts['detection'] = Qt.Key_D
        self.shortcuts['human_detection'] = Qt.Key_T
        self.shortcuts['thermal_human'] = Qt.Key_Y
        self.shortcuts['adaptive_mode'] = Qt.Key_A

        # 阈值调节快捷键
        self.shortcuts['threshold_up'] = Qt.Key_Plus
        self.shortcuts['threshold_down'] = Qt.Key_Minus
        self.shortcuts['threshold_up_alt'] = Qt.Key_Equal  # = 键作为 + 的替代

        # 功能快捷键
        self.shortcuts['save_image'] = Qt.Key_S
        self.shortcuts['save_debug'] = Qt.Key_R

        # 滚动快捷键
        self.shortcuts['scroll_up'] = Qt.Key_Up
        self.shortcuts['scroll_down'] = Qt.Key_Down



        self.logger.info("快捷键设置完成")

    def handle_key_press(self, key: int, modifiers: int = 0) -> bool:
        """处理按键事件"""
        try:
            # 基础快捷键 - 需要Ctrl+Q才能退出
            if key == self.shortcuts['quit'] and (modifiers & Qt.ControlModifier):
                self.quit_requested.emit()
                return True

            elif key == self.shortcuts['help']:
                self.help_toggle_requested.emit()
                return True

            # 检测控制快捷键
            elif key == self.shortcuts['detection']:
                self.detection_toggle_requested.emit()
                self.logger.info("热源检测切换快捷键触发")
                return True

            elif key == self.shortcuts['human_detection']:
                self.human_detection_toggle_requested.emit()
                self.logger.info("人体检测切换快捷键触发")
                return True

            elif key == self.shortcuts['thermal_human']:
                self.thermal_human_toggle_requested.emit()
                self.logger.info("热成像人体检测切换快捷键触发")
                return True

            elif key == self.shortcuts['adaptive_mode']:
                self.adaptive_mode_toggle_requested.emit()
                self.logger.info("自适应模式切换快捷键触发")
                return True

            # 阈值调节快捷键
            elif key == self.shortcuts['threshold_up'] or key == self.shortcuts['threshold_up_alt']:
                self.threshold_adjust_requested.emit(2.0)  # 增加2度
                self.logger.info("阈值增加快捷键触发")
                return True

            elif key == self.shortcuts['threshold_down']:
                self.threshold_adjust_requested.emit(-2.0)  # 减少2度
                self.logger.info("阈值减少快捷键触发")
                return True

            # 功能快捷键
            elif key == self.shortcuts['save_image']:
                self.save_image_requested.emit()
                self.logger.info("保存图像快捷键触发")
                return True

            elif key == self.shortcuts['save_debug']:
                self.save_debug_requested.emit()
                self.logger.info("保存调试快捷键触发")
                return True

            # 滚动快捷键
            elif key == self.shortcuts['scroll_up']:
                self.sidebar_scroll_requested.emit(-1)
                return True

            elif key == self.shortcuts['scroll_down']:
                self.sidebar_scroll_requested.emit(1)
                return True



            return False

        except Exception as e:
            self.logger.exception(f"处理按键事件失败: {e}")
            return False

    def get_shortcuts_help(self) -> str:
        """获取快捷键帮助信息"""
        help_text = """快捷键说明：

基础操作：
• Q: 退出程序
• H: 切换帮助/状态模式

检测控制：
• D: 切换热源检测
• T: 切换人体检测
• Y: 切换热成像人体检测
• A: 切换自适应/手动模式

阈值调节：
• +/=: 增加阈值 (+2°C)
• -: 减少阈值 (-2°C)

功能操作：
• S: 保存当前图像
• R: 保存调试信息

界面控制：
• ↑: 向上滚动热源详情
• ↓: 向下滚动热源详情"""

        return help_text

    def update_shortcut(self, action: str, key: int):
        """更新快捷键映射"""
        if action in self.shortcuts:
            old_key = self.shortcuts[action]
            self.shortcuts[action] = key
            self.logger.info(f"快捷键更新: {action} {old_key} -> {key}")
        else:
            self.logger.warning(f"未知的快捷键动作: {action}")
