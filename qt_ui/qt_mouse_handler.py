#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Qt鼠标事件处理器
基于原有的ui/mouse_handler.py重写，完全按照原有逻辑实现
"""

import numpy as np
from typing import Optional, Callable, Tuple
from PyQt5.QtCore import QObject, pyqtSignal

from utils.logger import get_logger
from temperature.readers.base_reader import BaseTemperatureReader


class QtMouseHandler(QObject):
    """Qt鼠标事件处理器 - 基于原有实现"""
    
    # 定义信号
    position_updated = pyqtSignal(int, int)  # 鼠标位置更新
    temperature_updated = pyqtSignal(float)  # 温度更新
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger("QtMouseHandler")
        
        # 温度读取器
        self.temperature_reader: Optional[BaseTemperatureReader] = None
        
        # 鼠标状态
        self.mouse_x = 0
        self.mouse_y = 0
        self.mouse_temp = 0.0
        
        # 显示区域配置
        self.visible_display_width = 0
        self.thermal_display_width = 0
        self.display_height = 0
        self.sidebar_width = 0
        self.sidebar_enabled = False
        self.toolbar_height = 50
        
        # 当前帧引用
        self.thermal_frame = None
        self.visible_frame = None
        self.combined_frame = None
        
        # 回调函数
        self.temperature_calculator: Optional[Callable] = None
        
        # 性能优化设置（与原有实现一致）
        self.temp_update_interval = 3  # 每3次鼠标移动才更新一次温度
        self.temp_update_counter = 0
        self.last_temp_position = (-1, -1)
        self.temp_cache = {}  # 温度缓存
        self.cache_max_size = 100  # 缓存最大大小
    
    def set_temperature_reader(self, reader: BaseTemperatureReader):
        """设置温度读取器"""
        self.temperature_reader = reader
        self.logger.info(f"温度读取器已设置: {type(reader).__name__}")
    
    def set_display_config(self, visible_width: int, thermal_width: int, height: int):
        """设置显示区域配置"""
        self.visible_display_width = visible_width
        self.thermal_display_width = thermal_width
        self.display_height = height
        self.logger.debug(f"显示配置: 可见光={visible_width}, 热成像={thermal_width}, 高度={height}")
    
    def set_sidebar_config(self, sidebar_width: int, sidebar_enabled: bool):
        """设置侧边栏配置"""
        self.sidebar_width = sidebar_width
        self.sidebar_enabled = sidebar_enabled
        self.logger.debug(f"侧边栏配置: 宽度={sidebar_width}, 启用={sidebar_enabled}")
    
    def set_frames(self, thermal_frame, visible_frame, combined_frame):
        """设置当前帧"""
        self.thermal_frame = thermal_frame
        self.visible_frame = visible_frame
        self.combined_frame = combined_frame
    
    def set_temperature_calculator(self, calculator: Callable):
        """设置温度计算函数"""
        self.temperature_calculator = calculator
    
    def handle_mouse_move(self, x: int, y: int):
        """处理鼠标移动事件"""
        # 直接使用传入的坐标（已经在VideoDisplayWidget中转换过了）
        self.mouse_x, self.mouse_y = x, y



        # 发射位置更新信号
        self.position_updated.emit(x, y)

        # 温度计算采用降频策略减少延迟
        self._update_temperature_optimized(x, y)
    
    def _update_temperature_optimized(self, x: int, y: int):
        """优化的温度更新方法 - 减少计算频率（与原有实现一致）"""
        # 降频策略：不是每次鼠标移动都计算温度
        self.temp_update_counter += 1
        if self.temp_update_counter < self.temp_update_interval:
            return
        
        self.temp_update_counter = 0
        
        # 检查位置是否变化足够大
        last_x, last_y = self.last_temp_position
        if abs(x - last_x) < 5 and abs(y - last_y) < 5:
            return  # 位置变化太小，不更新温度
        
        self.last_temp_position = (x, y)
        
        # 检查缓存
        cache_key = (x // 10, y // 10)  # 10像素精度的缓存
        if cache_key in self.temp_cache:
            self.mouse_temp = self.temp_cache[cache_key]
            # 温度信号发射已禁用
            # self.temperature_updated.emit(self.mouse_temp)
            return
        
        # 计算新温度
        new_temp = self._calculate_temperature_fast(x, y)
        
        # 更新缓存
        if len(self.temp_cache) >= self.cache_max_size:
            # 清理一半缓存
            keys_to_remove = list(self.temp_cache.keys())[:self.cache_max_size // 2]
            for key in keys_to_remove:
                del self.temp_cache[key]
        
        self.temp_cache[cache_key] = new_temp
        self.mouse_temp = new_temp
        
        # 温度信号发射已禁用
        # self.temperature_updated.emit(new_temp)
    
    def _calculate_temperature_fast(self, x: int, y: int) -> float:
        """快速温度计算方法（与原有实现一致）"""
        if self.combined_frame is None:
            return 0.0
        
        # 计算视频区域的总宽度
        video_total_width = self.visible_display_width + self.thermal_display_width
        
        # 判断鼠标位置
        if x < self.visible_display_width:
            # 鼠标在可见光区域
            return 0.0  # 可见光区域不显示温度
        elif x < video_total_width:
            # 鼠标在红外区域
            thermal_x = x - self.visible_display_width
            thermal_y = y
            
            if self.thermal_frame is not None and self.thermal_display_width > 0:
                # 计算在原始红外图像中的坐标
                original_x = int(thermal_x * self.thermal_frame.shape[1] / self.thermal_display_width)
                original_y = int(thermal_y * self.thermal_frame.shape[0] / self.display_height)
                
                # 边界检查
                original_x = max(0, min(original_x, self.thermal_frame.shape[1] - 1))
                original_y = max(0, min(original_y, self.thermal_frame.shape[0] - 1))
                
                # 快速温度计算
                return self._calculate_temperature(original_x, original_y)
        else:
            # 鼠标在侧边栏区域
            return 0.0  # 侧边栏区域不显示温度
        
        return 0.0
    
    def _calculate_temperature(self, x: int, y: int) -> float:
        """计算指定位置的温度（与原有实现一致）"""
        try:
            # 优先使用真实温度读取器（更准确但可能较慢）
            if (self.temperature_reader and
                hasattr(self.temperature_reader, 'current_temp_matrix') and
                self.temperature_reader.current_temp_matrix is not None):

                # 将显示坐标转换为温度矩阵坐标
                matrix_height, matrix_width = self.temperature_reader.current_temp_matrix.shape

                # 使用热成像显示区域的尺寸进行转换
                # 热成像显示区域的坐标范围是 (0, 0) 到 (thermal_display_width, display_height)
                matrix_x = int(x * matrix_width / self.thermal_display_width)
                matrix_y = int(y * matrix_height / self.display_height)

                # 边界检查
                matrix_x = max(0, min(matrix_x, matrix_width - 1))
                matrix_y = max(0, min(matrix_y, matrix_height - 1))

                temp = self.temperature_reader.get_temperature_at_point(matrix_x, matrix_y)
                if temp is not None:
                    return temp
            
            # 备用：使用图像分析计算温度（更快但精度较低）
            if self.temperature_calculator and self.thermal_frame is not None:
                return self.temperature_calculator(self.thermal_frame, x, y)
        
        except Exception:
            # 发生异常时返回默认值，避免影响鼠标响应
            pass
        
        return 0.0
    
    def get_mouse_position(self) -> Tuple[int, int]:
        """获取鼠标位置（调整后的坐标，不包含工具栏）"""
        return self.mouse_x, self.mouse_y
    
    def get_window_mouse_position(self) -> Tuple[int, int]:
        """获取窗口鼠标位置（包含工具栏的完整窗口坐标）"""
        return self.mouse_x, self.mouse_y + self.toolbar_height
    
    def get_mouse_temperature(self) -> float:
        """获取鼠标位置的温度"""
        return self.mouse_temp
    
    def is_in_thermal_area(self, x: int = None) -> bool:
        """判断鼠标是否在红外区域"""
        if x is None:
            x = self.mouse_x
        video_total_width = self.visible_display_width + self.thermal_display_width
        return self.visible_display_width <= x < video_total_width
    
    def set_performance_mode(self, mode: str):
        """设置性能模式"""
        if mode == "fast":
            # 快速模式：降低温度计算频率
            self.temp_update_interval = 5
            self.cache_max_size = 50
        elif mode == "balanced":
            # 平衡模式：默认设置
            self.temp_update_interval = 3
            self.cache_max_size = 100
        elif mode == "accurate":
            # 精确模式：每次都计算
            self.temp_update_interval = 1
            self.cache_max_size = 200
        else:
            self.logger.warning(f"未知的性能模式: {mode}")
        
        self.logger.info(f"性能模式设置为: {mode}")
    
    def clear_cache(self):
        """清理温度缓存"""
        self.temp_cache.clear()
        self.last_temp_position = (-1, -1)
        self.logger.info("温度缓存已清理")
