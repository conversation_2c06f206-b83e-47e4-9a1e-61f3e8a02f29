#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Qt叠加渲染器
负责在画面上绘制鼠标十字线等叠加信息
基于原有的core/display_manager.py的叠加功能重写
"""

import cv2
import numpy as np
from typing import Tuple, Optional

from utils.logger import get_logger
from utils.chinese_text_renderer import draw_chinese_text
from config.camera_config import UI_CONFIG
from config.chinese_text_config import get_chinese_text, get_font_size


class QtOverlayRenderer:
    """Qt叠加渲染器"""
    
    def __init__(self):
        self.logger = get_logger("QtOverlayRenderer")
        self.ui_config = UI_CONFIG
        
        # 十字线配置
        self.crosshair_size = 10  # 十字线大小
        
        self.logger.info("Qt叠加渲染器初始化完成")
    
    def add_mouse_overlay(self, frame: np.ndarray, mouse_x: int, mouse_y: int,
                         temperature: float = 0.0, show_crosshair: bool = True) -> np.ndarray:
        """添加鼠标叠加信息（只显示十字线）"""
        if frame is None:
            return frame

        result_frame = frame.copy()

        if mouse_x > 0 and mouse_y > 0:
            if show_crosshair:
                # 绘制十字线
                result_frame = self._draw_crosshair(result_frame, mouse_x, mouse_y)

        return result_frame
    
    def _draw_crosshair(self, frame: np.ndarray, x: int, y: int) -> np.ndarray:
        """绘制十字线"""
        try:
            # 绘制水平线
            cv2.line(frame, (x - self.crosshair_size, y), (x + self.crosshair_size, y), 
                    self.ui_config.crosshair_color, 1)
            # 绘制垂直线
            cv2.line(frame, (x, y - self.crosshair_size), (x, y + self.crosshair_size), 
                    self.ui_config.crosshair_color, 1)
        except Exception as e:
            self.logger.debug(f"绘制十字线失败: {e}")
        
        return frame
    
    # 温度文字绘制功能已删除
    
    def add_detection_overlay(self, frame: np.ndarray, detections: list,
                            detection_type: str = "heat") -> np.ndarray:
        """添加检测结果叠加（热源检测、人体检测等）"""
        if frame is None or not detections:
            return frame

        result_frame = frame.copy()

        try:
            for detection in detections:
                if detection_type == "heat":
                    result_frame = self._draw_heat_detection(result_frame, detection)
                elif detection_type == "human":
                    result_frame = self._draw_human_detection(result_frame, detection)
        except Exception as e:
            self.logger.debug(f"绘制检测叠加失败: {e}")

        return result_frame
    
    def _draw_heat_detection(self, frame: np.ndarray, detection: dict) -> np.ndarray:
        """绘制热源检测结果"""
        try:
            # 获取边界框坐标
            x, y, w, h = detection.get('bbox', (0, 0, 0, 0))
            
            # 绘制红色边界框
            cv2.rectangle(frame, (x, y), (x + w, y + h), (0, 0, 255), 2)
            
            # 绘制标签
            label = f"Heat {detection.get('id', '')}"
            cv2.putText(frame, label, (x, y - 5),
                       self.ui_config.font_face, self.ui_config.font_scale,
                       (0, 0, 255), self.ui_config.font_thickness)
        except Exception as e:
            self.logger.debug(f"绘制热源检测失败: {e}")
        
        return frame
    
    def _draw_human_detection(self, frame: np.ndarray, detection: dict) -> np.ndarray:
        """绘制人体检测结果"""
        try:
            # 获取边界框坐标
            x, y, w, h = detection.get('bbox', (0, 0, 0, 0))

            # 绘制蓝色边界框
            cv2.rectangle(frame, (x, y), (x + w, y + h), (255, 0, 0), 2)

            # 绘制标签
            label = f"Human {detection.get('confidence', 0.0):.2f}"
            cv2.putText(frame, label, (x, y - 5),
                       self.ui_config.font_face, self.ui_config.font_scale,
                       (255, 0, 0), self.ui_config.font_thickness)
        except Exception as e:
            self.logger.debug(f"绘制人体检测失败: {e}")

        return frame
    
    def add_status_overlay(self, frame: np.ndarray, status_text: str, 
                          position: Tuple[int, int] = None) -> np.ndarray:
        """添加状态信息叠加"""
        if frame is None or not status_text:
            return frame
        
        result_frame = frame.copy()
        
        try:
            if position is None:
                # 默认位置：左上角
                position = (10, 30)
            
            # 使用中文文字渲染
            result_frame = draw_chinese_text(
                result_frame, status_text, position,
                font_size=get_font_size("status"),
                color=self.ui_config.thermal_label_color,
                background_color=(0, 0, 0),  # 黑色背景
                padding=5
            )
        except Exception as e:
            self.logger.debug(f"绘制状态叠加失败: {e}")
            # 备用：使用OpenCV绘制
            cv2.putText(result_frame, status_text, position,
                       self.ui_config.font_face, self.ui_config.font_scale,
                       self.ui_config.thermal_label_color, self.ui_config.font_thickness)
        
        return result_frame
    
    def set_crosshair_size(self, size: int):
        """设置十字线大小"""
        self.crosshair_size = max(5, min(size, 20))
        self.logger.debug(f"十字线大小设置为: {self.crosshair_size}")
    
    # 温度文字偏移设置功能已删除
