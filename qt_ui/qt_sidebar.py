#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Qt侧边栏模块
实现完整的Qt侧边栏，替换OpenCV绘制的侧边栏
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                             QScrollArea, QFrame, QPushButton, QTextEdit,
                             QSizePolicy, QSpacerItem, QGroupBox, QGridLayout)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QMetaObject, Q_ARG
from PyQt5.QtGui import QFont, QPalette, QColor

from utils.logger import get_logger
from datetime import datetime
from .fire_smoke_analysis_widget import FireSmokeAnalysisWidget



class QtThermalSidebar(QWidget):
    """Qt热成像侧边栏类"""

    # 定义信号
    mode_changed = pyqtSignal(str)  # 模式切换信号

    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger("QtThermalSidebar")

        # 侧边栏设置
        self.sidebar_width = 300
        self.sidebar_enabled = True
        self.sidebar_mode = "status"  # "status" 或 "help"
        self.view_level = "overview"  # "overview", "detail", "expert"

        # 父窗口引用（用于获取全局分析器实例）
        self.parent_window = parent

        # 热源详细信息滚动控制
        self.heat_sources_scroll_offset = 0
        self.max_heat_sources_display = 20  # 增加显示数量，支持显示更多热源

        # 状态信息存储
        self.status_info = {
            'fps': 0.0,
            'timestamp': '',
            'scale_info': '',
            'mouse_pos': (0, 0),
            'heat_sources_detail': [],
            # MQTT状态
            'mqtt_enabled': False,
            'mqtt_connected': False,
            # 检测指标
            'human_count': 0
        }



        # 实时检测数据（用于概览视图）
        self.current_detections = {
            'max_confidence': 0.0,
            'risk_level': 'safe',
            'justification': []
        }

        # 选中的目标（用于详细视图和专家视图）
        self.selected_target = None

        # 初始化UI
        self.init_ui()

        # 定时器用于更新时间戳
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_timestamp)
        self.update_timer.start(1000)  # 每秒更新一次

    def init_ui(self):
        """初始化用户界面"""
        self.setFixedWidth(self.sidebar_width)
        self.setStyleSheet("""
            QWidget {
                background-color: #282828;
                color: white;
                font-family: "Microsoft YaHei", "SimHei", Arial;
            }
            QLabel {
                color: white;
                padding: 2px;
            }
            QPushButton {
                background-color: #404040;
                border: 1px solid #606060;
                border-radius: 3px;
                padding: 8px 10px;
                font-size: 12px;
                color: white;
            }
            QPushButton:hover {
                background-color: #505050;
            }
            QPushButton:pressed {
                background-color: #303030;
            }
            QScrollArea {
                border: none;
                background-color: #282828;
            }
            QTextEdit {
                background-color: #323232;
                border: 1px solid #505050;
                border-radius: 3px;
                color: white;
                font-size: 11px;
            }
        """)

        # 创建主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(5)

        # 创建标题区域
        self.create_title_section(main_layout)

        # 创建内容区域
        self.create_content_area(main_layout)

        # 创建底部按钮
        self.create_bottom_buttons(main_layout)

    def create_title_section(self, layout):
        """创建标题区域"""
        # 标题标签
        self.title_label = QLabel("系统状态")
        self.title_label.setStyleSheet("font-size: 16px; font-weight: bold; color: white; padding: 5px;")
        self.title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.title_label)

        # 模式切换提示
        self.mode_hint_label = QLabel("按 'H' 键切换帮助模式")
        self.mode_hint_label.setStyleSheet("font-size: 10px; color: #aaaaaa; padding: 2px;")
        self.mode_hint_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.mode_hint_label)

        # 分割线
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setStyleSheet("color: #606060;")
        layout.addWidget(separator)

    def create_content_area(self, layout):
        """创建内容区域"""
        # 创建滚动区域
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # 创建内容控件
        self.content_widget = QWidget()
        self.content_layout = QVBoxLayout(self.content_widget)
        self.content_layout.setContentsMargins(5, 5, 5, 5)
        self.content_layout.setSpacing(8)

        # 设置滚动区域内容
        self.scroll_area.setWidget(self.content_widget)
        layout.addWidget(self.scroll_area)

        # 初始化状态显示
        self.create_status_content()

    def create_bottom_buttons(self, layout):
        """创建底部按钮"""
        button_layout = QHBoxLayout()

        # 模式切换按钮
        self.mode_button = QPushButton("切换到帮助")
        self.mode_button.clicked.connect(self.toggle_mode)
        button_layout.addWidget(self.mode_button)

        # 滚动按钮（仅在状态模式下显示）
        self.scroll_up_button = QPushButton("↑")
        self.scroll_up_button.setMaximumWidth(30)
        self.scroll_up_button.clicked.connect(lambda: self.scroll_heat_sources(-1))
        button_layout.addWidget(self.scroll_up_button)

        self.scroll_down_button = QPushButton("↓")
        self.scroll_down_button.setMaximumWidth(30)
        self.scroll_down_button.clicked.connect(lambda: self.scroll_heat_sources(1))
        button_layout.addWidget(self.scroll_down_button)

        layout.addLayout(button_layout)

    def create_status_content(self):
        """创建状态显示内容"""
        # 清空现有内容
        self.clear_content()

        # 系统状态信息（时间和性能合并显示）
        self.system_status_section = self.create_info_section("系统状态")

        # 创建水平布局来放置时间和FPS
        status_widget = QWidget()
        status_layout = QHBoxLayout(status_widget)
        status_layout.setContentsMargins(0, 0, 0, 0)
        status_layout.setSpacing(10)

        # 时间标签
        self.timestamp_label = QLabel("--:--:--")
        self.timestamp_label.setStyleSheet("font-size: 12px; color: #ffffff;")

        # FPS标签
        self.fps_label = QLabel("FPS: 0.0")
        self.fps_label.setStyleSheet("font-size: 12px; color: #00ffff;")

        # 添加到水平布局
        status_layout.addWidget(self.timestamp_label)
        status_layout.addStretch()  # 添加弹性空间，让时间和FPS分别靠左右对齐
        status_layout.addWidget(self.fps_label)

        # 添加到区块
        self.system_status_section.layout().addWidget(status_widget)
        self.content_layout.addWidget(self.system_status_section)

        # 鼠标信息区域已删除



        # 热源详细信息
        self.heat_sources_section = self.create_info_section("热源详情")
        self.heat_sources_text = QTextEdit()
        self.heat_sources_text.setMaximumHeight(400)  # 进一步增加高度
        self.heat_sources_text.setMinimumHeight(300)  # 增加最小高度，确保显示更多内容
        self.heat_sources_text.setReadOnly(True)
        self.heat_sources_text.setText("暂无热源信息")
        # 设置尺寸策略，让热源详情优先获得空间
        self.heat_sources_text.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.heat_sources_section.layout().addWidget(self.heat_sources_text)
        self.content_layout.addWidget(self.heat_sources_section)

        # 火焰烟雾分析部分
        self.create_fire_smoke_analysis_section()
        self.content_layout.addWidget(self.fire_smoke_analysis_section)




        # 减少弹性空间，让热源详情获得更多空间
        self.content_layout.addItem(QSpacerItem(20, 10, QSizePolicy.Minimum, QSizePolicy.Minimum))


    def create_fire_smoke_analysis_section(self):
        """创建火焰烟雾分析部分"""
        self.fire_smoke_analysis_section = self.create_info_section("🔥💨 火焰烟雾分析")

        # 创建火焰烟雾分析组件
        self.fire_smoke_widget = FireSmokeAnalysisWidget()
        self.fire_smoke_widget.setMinimumHeight(400)
        self.fire_smoke_widget.setMaximumHeight(600)

        # 连接信号
        self.fire_smoke_widget.target_selected.connect(self.on_fire_smoke_target_selected)

        self.fire_smoke_analysis_section.layout().addWidget(self.fire_smoke_widget)

    def on_fire_smoke_target_selected(self, target_type, target_id):
        """处理火焰烟雾目标选择事件"""
        self.logger.info(f"选中{target_type}目标: {target_id}")
        # 这里可以添加目标选择后的处理逻辑，比如在主视图中高亮显示

    def update_fire_smoke_data(self, fire_data=None, smoke_data=None):
        """更新火焰烟雾数据"""
        try:
            if hasattr(self, 'fire_smoke_widget'):
                self.logger.debug(f"收到火焰烟雾数据更新: fire_data={len(fire_data) if fire_data else 0}, smoke_data={len(smoke_data) if smoke_data else 0}")

                # 处理火焰数据
                if fire_data is not None:
                    # 将列表数据转换为字典格式 {id: data}
                    fire_targets = {}
                    for item in fire_data:
                        if isinstance(item, dict):
                            obj_id = item.get('object_id', item.get('id', len(fire_targets)))
                            fire_targets[str(obj_id)] = item
                            self.logger.debug(f"火焰目标 {obj_id}: confidence={item.get('confidence', 'N/A')}, area={item.get('area', 'N/A')}")


                    self.fire_smoke_widget.update_fire_targets(fire_targets)

                # 处理烟雾数据
                if smoke_data is not None:
                    # 将列表数据转换为字典格式 {id: data}
                    smoke_targets = {}
                    for item in smoke_data:
                        if isinstance(item, dict):
                            obj_id = item.get('object_id', item.get('id', len(smoke_targets)))
                            smoke_targets[str(obj_id)] = item
                            self.logger.debug(f"烟雾目标 {obj_id}: confidence={item.get('confidence', 'N/A')}, area={item.get('area', 'N/A')}")


                    self.fire_smoke_widget.update_smoke_targets(smoke_targets)
            else:
                self.logger.warning("fire_smoke_widget 不存在，无法更新数据")

        except Exception as e:
            self.logger.error(f"更新火焰烟雾数据失败: {e}")
            import traceback
            self.logger.error(f"详细错误: {traceback.format_exc()}")

    def clear_fire_smoke_selection(self):
        """清除火焰烟雾选择"""
        if hasattr(self, 'fire_smoke_widget'):
            self.fire_smoke_widget.clear_selection()
    def create_help_content(self):
        """创建帮助显示内容"""
        # 清空现有内容
        self.clear_content()

        # 系统信息
        system_section = self.create_info_section("系统信息")
        system_info = QLabel("海康威视双光谱热成像监控系统\n版本: Qt 1.0\n支持: 实时温度检测")
        system_info.setStyleSheet("font-size: 11px; color: #cccccc; line-height: 1.4;")
        system_info.setWordWrap(True)
        system_section.layout().addWidget(system_info)
        self.content_layout.addWidget(system_section)

        # 鼠标功能
        mouse_section = self.create_info_section("鼠标功能")
        mouse_help = QTextEdit()
        mouse_help.setMaximumHeight(100)
        mouse_help.setReadOnly(True)
        mouse_help.setText("• 在热成像区域移动鼠标显示实时温度\n• 通过ISAPI接口获取精确温度数据\n• 支持实时温度监控")
        mouse_section.layout().addWidget(mouse_help)
        self.content_layout.addWidget(mouse_section)

        # 检测功能
        detection_section = self.create_info_section("检测功能")
        detection_help = QTextEdit()
        detection_help.setMaximumHeight(120)
        detection_help.setReadOnly(True)
        detection_help.setText("• 使用ISAPI原始数据进行热源检测\n• 无UI干扰的纯数据分析\n• 自适应阈值算法\n• 实时分析和调试图像保存")
        detection_section.layout().addWidget(detection_help)
        self.content_layout.addWidget(detection_section)

        # 快捷键
        hotkey_section = self.create_info_section("快捷键")
        hotkey_help = QTextEdit()
        hotkey_help.setMaximumHeight(100)
        hotkey_help.setReadOnly(True)
        hotkey_help.setText("• H: 切换帮助/状态模式\n• Q: 退出程序\n• ↑/↓: 滚动热源详情")
        hotkey_section.layout().addWidget(hotkey_help)
        self.content_layout.addWidget(hotkey_section)

        # 添加弹性空间
        self.content_layout.addItem(QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding))

    def create_info_section(self, title: str) -> QWidget:
        """创建信息区块"""
        section = QWidget()
        section_layout = QVBoxLayout(section)
        section_layout.setContentsMargins(5, 5, 5, 5)
        section_layout.setSpacing(3)

        # 标题
        title_label = QLabel(title)
        title_label.setStyleSheet("font-size: 13px; font-weight: bold; color: #ffff00; padding: 2px;")
        section_layout.addWidget(title_label)

        # 设置背景
        section.setStyleSheet("QWidget { background-color: #323232; border-radius: 5px; margin: 2px; }")

        return section

    def clear_content(self):
        """清空内容区域"""
        while self.content_layout.count():
            child = self.content_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

    def toggle_mode(self):
        """切换显示模式"""
        if self.sidebar_mode == "status":
            self.sidebar_mode = "help"
            self.title_label.setText("帮助信息")
            self.mode_hint_label.setText("按 'H' 键切换状态模式")
            self.mode_button.setText("切换到状态")
            self.create_help_content()
            # 隐藏滚动按钮
            self.scroll_up_button.hide()
            self.scroll_down_button.hide()
        else:
            self.sidebar_mode = "status"
            self.title_label.setText("系统状态")
            self.mode_hint_label.setText("按 'H' 键切换帮助模式")
            self.mode_button.setText("切换到帮助")
            self.create_status_content()
            # 显示滚动按钮
            self.scroll_up_button.show()
            self.scroll_down_button.show()

        self.mode_changed.emit(self.sidebar_mode)
        self.logger.info(f"侧边栏模式切换到: {self.sidebar_mode}")

    def update_timestamp(self):
        """更新时间戳"""
        current_time = datetime.now().strftime("%H:%M:%S")
        self.status_info['timestamp'] = current_time

        if hasattr(self, 'timestamp_label') and self.sidebar_mode == "status":
            self.timestamp_label.setText(current_time)

    def scroll_heat_sources(self, direction: int):
        """滚动热源详细信息"""
        if not self.status_info['heat_sources_detail']:
            return

        total_sources = len(self.status_info['heat_sources_detail'])
        max_offset = max(0, total_sources - self.max_heat_sources_display)

        if direction > 0:  # 向下滚动
            self.heat_sources_scroll_offset = min(self.heat_sources_scroll_offset + 1, max_offset)
        else:  # 向上滚动
            self.heat_sources_scroll_offset = max(self.heat_sources_scroll_offset - 1, 0)

        # 更新热源详情显示
        self.update_heat_sources_display()
        self.logger.info(f"热源详情滚动: 偏移量 {self.heat_sources_scroll_offset}")

    def update_status_info(self, **kwargs):
        """更新状态信息"""
        for key, value in kwargs.items():
            if key in self.status_info:
                self.status_info[key] = value

        # 更新UI显示
        if self.sidebar_mode == "status":
            self.update_status_display()

    def update_status_display(self):
        """更新状态显示"""
        if not hasattr(self, 'fps_label') or not hasattr(self, 'timestamp_label'):
            return

        # 更新FPS
        self.fps_label.setText(f"FPS: {self.status_info['fps']:.1f}")

        # 更新时间戳（如果需要手动更新的话，通常由定时器自动更新）
        # self.timestamp_label.setText(self.status_info['timestamp'])

        # 鼠标温度显示已删除



        # 检测状态已移至全局指标中

        # 更新热源详情
        self.update_heat_sources_display()

    def update_heat_sources_display(self):
        """更新热源详细信息显示"""
        if not hasattr(self, 'heat_sources_text'):
            return

        heat_sources = self.status_info['heat_sources_detail']
        if not heat_sources:
            # 使用线程安全的方式更新UI
            QMetaObject.invokeMethod(self.heat_sources_text, "setText",
                                   Qt.QueuedConnection, Q_ARG(str, "暂无热源信息"))
            return

        # 计算显示范围
        total_sources = len(heat_sources)
        start_idx = self.heat_sources_scroll_offset
        end_idx = min(start_idx + self.max_heat_sources_display, total_sources)

        # 构建显示文本
        display_text = ""
        if total_sources > self.max_heat_sources_display:
            display_text += f"显示 {start_idx + 1}-{end_idx}/{total_sources} 个热源\n"
            display_text += "使用 ↑/↓ 按钮滚动\n\n"

        for i in range(start_idx, end_idx):
            source = heat_sources[i]

            # 检查是否为标注区域分析
            is_annotation_analysis = getattr(source, 'is_annotation_analysis', False)

            if is_annotation_analysis:
                # 标注区域分析的特殊显示
                annotation_label = getattr(source, 'annotation_label', '未知标注')
                display_text += f"--- 📍 标注区域: {annotation_label} ---\n"
            else:
                # 热源标题行
                tracking_info = ""
                if hasattr(source, 'tracking_id') and source.tracking_id is not None:
                    tracking_info = f"[ID:{source.tracking_id}] "

                # 添加标注区域信息
                annotation_info = ""
                if hasattr(source, 'annotation_label') and source.annotation_label:
                    annotation_info = f"[{source.annotation_label}] "

                display_text += f"--- {tracking_info}{annotation_info}热源 {source.id} ---\n"

            if is_annotation_analysis:
                # 标注区域分析的特殊显示内容
                display_text += self._format_annotation_analysis_info(source)
            else:
                # 热源的常规显示内容
                # 位置和尺寸信息
                display_text += f"中心位置: ({source.position[0]}, {source.position[1]})\n"
                display_text += f"尺寸: {source.size[0]} x {source.size[1]} 像素\n"

                # 面积信息（增强显示）
                if hasattr(source, 'area') and source.area:
                    area_text = self._get_area_analysis_text(source)
                    display_text += f"面积: {source.area} 像素{area_text}\n"

                # 温度信息 - 显示最高温和最低温
                temp_info = self._format_temperature_info(source)
                if temp_info:
                    display_text += f"温度: {temp_info}\n"

                # 添加时序变化率信息（如果有跟踪ID）
                if hasattr(source, 'tracking_id') and source.tracking_id is not None:
                    change_rates_text = self._get_change_rates_text(source.tracking_id)
                    if change_rates_text:
                        display_text += f"{change_rates_text}"

                # 添加同步标注框分析信息
                sync_analysis_text = self._get_sync_annotation_analysis_text(source)
                if sync_analysis_text:
                    display_text += sync_analysis_text

            display_text += "\n"  # 热源之间的空行

        # 使用线程安全的方式更新UI
        QMetaObject.invokeMethod(self.heat_sources_text, "setText",
                               Qt.QueuedConnection, Q_ARG(str, display_text.strip()))

    def update_heat_sources_detail(self, heat_sources_detail: list):
        """更新热源详细信息"""
        self.status_info['heat_sources_detail'] = heat_sources_detail
        # 重置滚动位置
        self.heat_sources_scroll_offset = 0
        # 更新显示
        if self.sidebar_mode == "status":
            self.update_heat_sources_display()

    def toggle_sidebar(self):
        """切换侧边栏显示"""
        self.sidebar_enabled = not self.sidebar_enabled
        self.setVisible(self.sidebar_enabled)
        return self.sidebar_enabled

    def set_enabled(self, enabled: bool):
        """设置侧边栏启用状态"""
        self.sidebar_enabled = enabled
        self.setVisible(enabled)

    def _get_change_rates_text(self, tracking_id: int) -> str:
        """获取变化率显示文本"""
        try:
            # 尝试从父窗口获取全局分析器实例
            analyzer = None
            if (self.parent_window and
                hasattr(self.parent_window, 'heat_source_analyzer') and
                self.parent_window.heat_source_analyzer is not None):
                analyzer = self.parent_window.heat_source_analyzer
            else:
                # 如果无法获取全局实例，创建新的（但会导致数据不一致）
                from detection.analysis.heat_source_analyzer import HeatSourceAnalyzer
                analyzer = HeatSourceAnalyzer("detection/debug_test", enable_tracking=True)
                print("⚠️ 侧边栏创建了新的分析器实例，可能导致数据不一致")

            # 获取变化率数据
            change_rates = analyzer.get_change_rates(tracking_id, time_window_seconds=10.0)

                        # 添加调试信息
            print(f"🔍 热源 {tracking_id} 变化率计算:")
            print(f"   时间窗口: 10.0秒")
            if analyzer.history:
                for data_type in ['area', 'max_temp', 'min_temp', 'avg_temp']:
                    data_points = analyzer.history.get_time_series(tracking_id, data_type, 10.0)
                    print(f"   {data_type}: {len(data_points)} 个数据点")

            if not change_rates:
                # 添加调试信息
                if analyzer.history:
                    snapshots = analyzer.history.get_snapshots(tracking_id, count=10)
                    print(f"🔍 跟踪ID {tracking_id} 变化率为空，历史快照数量: {len(snapshots)}")
                    if snapshots:
                        latest = snapshots[-1]
                        print(f"   最新快照时间: {latest.timestamp}, 面积: {latest.area}, 最高温: {latest.max_temperature}")
                return ""

            change_text = "变化率(每秒):\n"
            has_significant_change = False

            # 面积变化率
            if 'area' in change_rates:
                area_rate = change_rates['area']
                if abs(area_rate.change_rate) > 0.5:  # 只显示显著变化
                    change_text += f"  面积: {area_rate.get_formatted_rate()}\n"
                    has_significant_change = True

            # 最高温度变化率
            if 'max_temp' in change_rates:
                temp_rate = change_rates['max_temp']
                if abs(temp_rate.change_rate) > 0.1:  # 只显示显著变化
                    change_text += f"  最高温: {temp_rate.get_formatted_rate()}\n"
                    has_significant_change = True

            if has_significant_change:
                return change_text
            else:
                return "变化率: 稳定\n"

        except Exception:
            # 静默处理异常，避免影响主界面
            return ""

    def _get_area_analysis_text(self, source) -> str:
        """获取面积分析显示文本"""
        try:
            # 尝试从父窗口获取全局分析器实例
            analyzer = None
            if (self.parent_window and
                hasattr(self.parent_window, 'heat_source_analyzer') and
                self.parent_window.heat_source_analyzer is not None):
                analyzer = self.parent_window.heat_source_analyzer

            if not analyzer or not analyzer.area_calculator:
                return ""

            # 从热源信息创建简单轮廓进行面积分析
            import cv2
            import numpy as np

            x, y, w, h = source.bbox
            rect_contour = np.array([
                [x, y], [x + w, y], [x + w, y + h], [x, y + h]
            ], dtype=np.int32)

            # 分析轮廓面积
            area_result = analyzer.area_calculator.analyze_contour_area(rect_contour)

            area_text = ""

            # 添加物理面积信息
            if area_result.area_m2 > 0:
                if area_result.area_m2 >= 0.001:  # 大于1平方厘米
                    area_text += f"({area_result.area_m2:.3f}m²)"
                else:
                    area_text += f"({area_result.area_m2*10000:.1f}cm²)"

            # 添加警报信息
            if area_result.is_critical:
                area_text += "[严重]"
            elif area_result.is_warning:
                area_text += "[警告]"

            return area_text

        except Exception as e:
            # 静默处理异常，避免影响主界面
            return ""

    def _format_temperature_info(self, source) -> str:
        """格式化温度信息显示"""
        try:
            # 优先显示最高温和最低温
            if hasattr(source, 'max_temperature') and hasattr(source, 'min_temperature'):
                max_temp = source.max_temperature
                min_temp = source.min_temperature

                if max_temp > 0 or min_temp > 0:  # 有有效温度数据
                    if abs(max_temp - min_temp) > 0.5:  # 温差较大时显示范围
                        return f"{min_temp:.1f}°C ~ {max_temp:.1f}°C (温差: {max_temp - min_temp:.1f}°C)"
                    else:  # 温差很小时只显示最高温
                        return f"{max_temp:.1f}°C"

            # 向后兼容：如果仍有avg_temperature字段
            elif hasattr(source, 'max_temperature') and hasattr(source, 'avg_temperature'):
                max_temp = source.max_temperature
                avg_temp = source.avg_temperature
                if max_temp > 0 or avg_temp > 0:
                    if abs(max_temp - avg_temp) > 1.0:
                        return f"最高: {max_temp:.1f}°C, 平均: {avg_temp:.1f}°C"
                    else:
                        return f"{max_temp:.1f}°C"

            # 兼容旧的temperature属性
            elif hasattr(source, 'temperature') and source.temperature:
                return f"{source.temperature:.1f}°C"

            return ""

        except Exception:
            return ""

    def _get_sync_annotation_analysis_text(self, source) -> str:
        """
        获取同步标注框分析文本

        Args:
            source: 热源信息对象

        Returns:
            格式化的同步标注框分析文本
        """
        try:
            # 检查是否有同步标注框分析数据
            if not hasattr(source, 'sync_annotation_analysis'):
                return ""

            analysis_data = source.sync_annotation_analysis
            if not analysis_data:
                return ""

            text = ""

            # 遍历所有重叠的标注框
            for ann_id, analysis in analysis_data.items():
                label = analysis.get('label', '未知标注')
                overlap_ratio = analysis.get('overlap_ratio', 0.0)
                thermal_data = analysis.get('thermal_analysis', {})

                if not thermal_data:
                    continue

                # 添加标注框信息
                text += f"📍 标注区域: {label}\n"
                text += f"重叠度: {overlap_ratio:.1%}\n"

                # 添加热成像分析数据
                max_temp = thermal_data.get('max_temperature', 0)
                min_temp = thermal_data.get('min_temperature', 0)
                avg_temp = thermal_data.get('avg_temperature', 0)
                temp_range = thermal_data.get('temperature_range', 0)

                text += f"区域温度: {min_temp:.1f}~{max_temp:.1f}°C\n"
                text += f"平均温度: {avg_temp:.1f}°C\n"
                text += f"温度范围: {temp_range:.1f}°C\n"

                # 添加热点信息
                hottest_point = thermal_data.get('hottest_point')
                if hottest_point:
                    text += f"最热点: ({hottest_point[0]}, {hottest_point[1]})\n"

                # 添加像素分布信息
                hot_ratio = thermal_data.get('hot_pixel_ratio', 0)
                cold_ratio = thermal_data.get('cold_pixel_ratio', 0)
                total_pixels = thermal_data.get('total_pixels', 0)

                if total_pixels > 0:
                    text += f"高温像素: {hot_ratio:.1%}\n"
                    text += f"低温像素: {cold_ratio:.1%}\n"
                    text += f"总像素数: {total_pixels}\n"

                text += "\n"  # 标注框之间的分隔

            return text

        except Exception as e:
            print(f"⚠️ 获取同步标注框分析文本失败: {e}")
            return ""

    def _format_annotation_analysis_info(self, source) -> str:
        """
        格式化标注区域分析信息

        Args:
            source: 标注区域分析对象

        Returns:
            格式化的标注区域分析文本
        """
        try:
            text = ""

            # 基本信息
            text += f"区域位置: ({source.position[0]}, {source.position[1]})\n"
            text += f"区域尺寸: {source.size[0]} x {source.size[1]} 像素\n"
            text += f"区域面积: {source.area} 像素\n"

            # 温度信息
            if hasattr(source, 'max_temperature') and hasattr(source, 'min_temperature'):
                max_temp = source.max_temperature
                min_temp = source.min_temperature
                avg_temp = getattr(source, 'avg_temperature', 0)
                temp_range = max_temp - min_temp

                text += f"最高温度: {max_temp:.1f}°C\n"
                text += f"最低温度: {min_temp:.1f}°C\n"
                text += f"平均温度: {avg_temp:.1f}°C\n"
                text += f"温度范围: {temp_range:.1f}°C\n"

            # 详细热成像分析数据
            if hasattr(source, 'thermal_analysis'):
                thermal_data = source.thermal_analysis

                # 最热点信息
                hottest_point = thermal_data.get('hottest_point')
                if hottest_point:
                    text += f"最热点位置: ({hottest_point[0]}, {hottest_point[1]})\n"

                # 像素分布信息
                hot_ratio = thermal_data.get('hot_pixel_ratio', 0)
                cold_ratio = thermal_data.get('cold_pixel_ratio', 0)
                total_pixels = thermal_data.get('total_pixels', 0)

                if total_pixels > 0:
                    text += f"高温像素比例: {hot_ratio:.1%}\n"
                    text += f"低温像素比例: {cold_ratio:.1%}\n"
                    text += f"总像素数: {total_pixels}\n"

                # 温度标准差
                std_temp = thermal_data.get('std_temperature', 0)
                if std_temp > 0:
                    text += f"温度标准差: {std_temp:.1f}°C\n"

            # 标注区域状态
            text += f"数据来源: 标注区域独立分析\n"
            text += f"分析状态: 实时温度监控\n"

            return text

        except Exception as e:
            print(f"⚠️ 格式化标注区域分析信息失败: {e}")
            return ""





















    def _refresh_overview_display(self):
        """刷新概览视图显示"""
        # 这个方法将在后面实现概览视图UI时调用
        pass
