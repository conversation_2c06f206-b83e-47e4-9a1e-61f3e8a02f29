#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Qt系统适配器
将现有的OpenCV系统适配到Qt界面
"""

import cv2
import numpy as np
import time
from typing import Optional
from PyQt5.QtCore import QTimer, QThread, pyqtSignal
from PyQt5.QtWidgets import QApplication

from qt_ui.main_window import ThermalCameraMainWindow, create_qt_application
from utils.logger import get_logger


class FrameUpdateThread(QThread):
    """帧更新线程 - 在后台处理视频帧"""

    frame_ready = pyqtSignal(np.ndarray)  # 帧准备好的信号
    fps_updated = pyqtSignal(float)  # FPS更新信号

    def __init__(self, system_instance):
        super().__init__()
        self.system_instance = system_instance
        self.running = False
        self.logger = get_logger("FrameUpdateThread")

        # 连接失败计数器和重连机制
        self.connection_fail_count = 0
        self.max_fail_count = 10  # 连续失败10次才重置摄像头
        self.last_successful_frame_time = time.time()
        self.connection_timeout = 30.0  # 30秒无帧才认为连接断开
    
    def run(self):
        """线程主循环"""
        self.running = True
        self.logger.info("帧更新线程启动")

        while self.running and self.system_instance.is_running:
            try:
                # 检查是否有摄像头初始化
                if not self.system_instance.is_camera_ready():
                    # 没有摄像头，等待用户选择模式
                    self.msleep(100)  # 等待100ms
                    continue

                # 读取双摄像头帧
                ret_visible, ret_thermal, frame_visible, frame_thermal = self.system_instance.camera_capture.read_frames()

                # 更新性能监控
                current_fps = self.system_instance.performance_monitor.update()

                # 更新温度范围
                if self.system_instance.temperature_manager:
                    self.system_instance.temperature_manager.update_all_ranges()

                # 根据检测模式处理帧
                if self.system_instance.detection_mode == "video":
                    # 视频模式：只处理可见光，单画面显示
                    if ret_visible:
                        processed_frame = self._process_video_frame(frame_visible, current_fps)
                        if processed_frame is not None:
                            self.frame_ready.emit(processed_frame)
                            # 发射FPS更新信号
                            self.fps_updated.emit(current_fps)
                            # 重置失败计数器
                            self.connection_fail_count = 0
                            self.last_successful_frame_time = time.time()
                    else:
                        # 视频模式下可见光读取失败
                        self.connection_fail_count += 1
                        if self.connection_fail_count % 5 == 0:
                            self.logger.warning(f"视频文件读取失败 ({self.connection_fail_count}次)")
                        self.msleep(100)
                        continue

                elif self.system_instance.detection_mode == "camera":
                    # 摄像头模式：期望双画面显示
                    if ret_visible and ret_thermal:
                        # 正常双摄像头画面
                        combined_frame = self._process_dual_frames(frame_visible, frame_thermal, current_fps)
                        if combined_frame is not None:
                            self.frame_ready.emit(combined_frame)
                            # 发射FPS更新信号
                            self.fps_updated.emit(current_fps)
                            # 重置失败计数器
                            self.connection_fail_count = 0
                            self.last_successful_frame_time = time.time()

                    elif ret_visible:
                        # 摄像头模式但红外断开 - 这是故障情况
                        self.logger.warning("红外摄像头连接中断，仅显示可见光")
                        processed_frame = self._process_single_frame(frame_visible, "Visible Light (IR Disconnected)", current_fps)
                        if processed_frame is not None:
                            self.frame_ready.emit(processed_frame)
                            # 重置失败计数器
                            self.connection_fail_count = 0
                            self.last_successful_frame_time = time.time()

                    elif ret_thermal:
                        # 摄像头模式但可见光断开 - 这也是故障情况
                        self.logger.warning("可见光摄像头连接中断，仅显示红外")
                        processed_frame = self._process_single_frame(frame_thermal, "Thermal Infrared (Visible Disconnected)", current_fps)
                        if processed_frame is not None:
                            self.frame_ready.emit(processed_frame)
                            # 重置失败计数器
                            self.connection_fail_count = 0
                            self.last_successful_frame_time = time.time()
                    else:
                        # 摄像头模式下双摄像头都失败
                        self.connection_fail_count += 1
                        current_time = time.time()

                        if self.connection_fail_count >= self.max_fail_count or \
                           (current_time - self.last_successful_frame_time) > self.connection_timeout:
                            self.logger.error(f"双摄像头连接持续失败 (失败次数: {self.connection_fail_count})")
                            # 重置摄像头状态
                            self.system_instance.camera_initialized = False
                            if self.system_instance.camera_capture:
                                self.system_instance.camera_capture.release()
                                self.system_instance.camera_capture = None
                            self.connection_fail_count = 0
                            self.last_successful_frame_time = time.time()
                            continue
                        else:
                            if self.connection_fail_count % 5 == 0:
                                self.logger.warning(f"双摄像头连接临时失败 ({self.connection_fail_count}/{self.max_fail_count})")
                            self.msleep(100)
                            continue

                else:
                    # 未知检测模式
                    self.logger.warning(f"未知检测模式: {self.system_instance.detection_mode}")
                    self.msleep(100)
                    continue

                # 控制帧率
                self.msleep(33)  # 约30fps

            except Exception as e:
                self.logger.exception(f"帧处理异常: {e}")
                # 异常处理 - 增加容错性，不立即重置摄像头
                self.connection_fail_count += 1

                if self.connection_fail_count >= self.max_fail_count:
                    self.logger.error(f"帧处理持续异常 ({self.connection_fail_count}次)，重置摄像头")
                    # 发生持续异常时才重置摄像头状态
                    self.system_instance.camera_initialized = False
                    if self.system_instance.camera_capture:
                        self.system_instance.camera_capture.release()
                        self.system_instance.camera_capture = None
                    self.connection_fail_count = 0
                    self.msleep(1000)  # 等待1秒后重试
                else:
                    # 偶发异常，短暂等待后继续
                    self.logger.warning(f"帧处理异常 ({self.connection_fail_count}/{self.max_fail_count})，继续尝试")
                    self.msleep(200)  # 等待200ms后重试

        self.logger.info("帧更新线程结束")

    def _process_video_frame(self, visible_frame, fps: float) -> Optional[np.ndarray]:
        """处理视频文件帧（仅可见光，无热源检测）"""
        if self.system_instance.frame_processor:
            # 更新当前帧引用
            self.system_instance.frame_processor.current_visible_frame = visible_frame
            self.system_instance.frame_processor.current_thermal_frame = None  # 视频模式无红外帧

            # 只应用人体检测和火焰烟雾检测，不进行热源检测
            processed_visible_frame = self.system_instance.frame_processor._apply_human_detection(visible_frame)
            processed_visible_frame = self.system_instance.frame_processor._apply_fire_smoke_detection(processed_visible_frame)

            # 直接返回处理后的可见光帧
            return processed_visible_frame
        else:
            self.logger.error("帧处理器未初始化")
            return None

    def _process_dual_frames(self, visible_frame, thermal_frame, fps: float) -> Optional[np.ndarray]:
        """处理双摄像头画面"""
        if self.system_instance.frame_processor:
            # 更新当前帧引用
            self.system_instance.frame_processor.current_visible_frame = visible_frame
            self.system_instance.frame_processor.current_thermal_frame = thermal_frame
            
            # 更新鼠标处理器的帧引用
            if self.system_instance.mouse_handler:
                self.system_instance.mouse_handler.set_frames(thermal_frame, visible_frame, None)
            
            # 应用检测和处理
            processed_thermal_frame = self.system_instance.frame_processor._apply_thermal_detection_with_priority(thermal_frame)
            processed_visible_frame = self.system_instance.frame_processor._apply_human_detection(visible_frame)

            # 应用火焰烟雾检测
            processed_visible_frame = self.system_instance.frame_processor._apply_fire_smoke_detection(processed_visible_frame)
            
            # 拼接画面
            if self.system_instance.display_manager:
                combined_frame = self.system_instance.display_manager.combine_frames(
                    processed_visible_frame, processed_thermal_frame, fps
                )
                return combined_frame
        
        return None
    
    def _process_single_frame(self, frame, title: str, fps: float) -> Optional[np.ndarray]:
        """处理单个画面"""
        if self.system_instance.frame_processor:
            # 使用帧处理器处理单个画面
            self.system_instance.frame_processor.process_single_frame(frame, title, fps)
            # 返回处理后的帧（这里简化处理）
            return frame
        return frame
    
    def stop(self):
        """停止线程"""
        self.running = False
        self.wait()


class QtSystemAdapter:
    """Qt系统适配器 - 将现有系统适配到Qt界面"""
    
    def __init__(self, thermal_system):
        """
        初始化Qt适配器
        
        Args:
            thermal_system: ThermalCameraSystem实例
        """
        self.system = thermal_system
        self.logger = get_logger("QtSystemAdapter")
        
        # Qt应用和窗口
        self.qt_app = None
        self.main_window = None
        
        # 帧更新线程
        self.frame_thread = None
        
        # 初始化Qt界面
        self._init_qt_interface()
    
    def _init_qt_interface(self):
        """初始化Qt界面"""
        try:
            # 创建Qt应用
            self.qt_app = create_qt_application()
            
            # 创建主窗口
            self.main_window = ThermalCameraMainWindow()
            self.main_window.set_system_instance(self.system)
            
            # 创建帧更新线程
            self.frame_thread = FrameUpdateThread(self.system)
            self.frame_thread.frame_ready.connect(self.main_window.display_frame)
            self.frame_thread.fps_updated.connect(self._on_fps_updated)
            
            self.logger.info("Qt界面初始化成功")

        except Exception as e:
            self.logger.exception(f"Qt界面初始化失败: {e}")
            raise

    def _on_fps_updated(self, fps: float):
        """处理FPS更新信号"""
        if self.main_window and hasattr(self.main_window, 'qt_sidebar'):
            self.main_window.qt_sidebar.update_status_info(fps=fps)
    
    def show_window(self):
        """显示主窗口"""
        if self.main_window:
            self.main_window.show()
            self.logger.info("Qt主窗口已显示")
    
    def start_frame_processing(self):
        """开始帧处理"""
        if self.frame_thread and not self.frame_thread.isRunning():
            self.frame_thread.start()
            self.logger.info("帧处理线程已启动")
    
    def stop_frame_processing(self):
        """停止帧处理"""
        if self.frame_thread and self.frame_thread.isRunning():
            self.frame_thread.stop()
            self.logger.info("帧处理线程已停止")
    
    def run_qt_event_loop(self):
        """运行Qt事件循环"""
        if self.qt_app:
            return self.qt_app.exec_()
        return 0
    
    def cleanup(self):
        """清理资源"""
        self.logger.info("清理Qt适配器资源")
        
        # 停止帧处理线程
        self.stop_frame_processing()
        
        # 关闭主窗口
        if self.main_window:
            self.main_window.close()
        
        # 退出Qt应用
        if self.qt_app:
            self.qt_app.quit()


class QtSidebarManager:
    """Qt侧边栏管理器 - 兼容原有接口"""

    def __init__(self):
        self.sidebar_width = 300
        self.sidebar_enabled = True
        self.sidebar_mode = "status"

        # 状态信息存储
        self.status_info = {
            'fps': 0.0,
            'timestamp': '',
            'detection_enabled': False,
            'detection_status': '',
            'heat_sources_count': 0,
            'scale_info': '',
            'mouse_temp': 0.0,
            'mouse_pos': (0, 0),
            'heat_sources_detail': []
        }

    def toggle_sidebar(self):
        """切换侧边栏显示"""
        self.sidebar_enabled = not self.sidebar_enabled
        return self.sidebar_enabled

    def update_status_info(self, **kwargs):
        """更新状态信息"""
        for key, value in kwargs.items():
            if key in self.status_info:
                self.status_info[key] = value


class QtDisplayManager:
    """Qt显示管理器 - 替换原有的OpenCV显示管理器"""

    def __init__(self, qt_adapter: QtSystemAdapter):
        self.qt_adapter = qt_adapter
        self.logger = get_logger("QtDisplayManager")

        # 保持与原有接口的兼容性
        self.window_created = True
        self.window_name = "Qt Main Window"

        # 创建兼容的sidebar_manager
        self.sidebar_manager = QtSidebarManager()
    
    def create_window(self):
        """创建窗口 - Qt版本"""
        self.qt_adapter.show_window()
        self.logger.info("Qt窗口已创建")
    
    def display_combined_frame(self, combined_frame: np.ndarray):
        """显示拼接画面 - Qt版本"""
        if combined_frame is not None and self.qt_adapter.main_window:
            self.qt_adapter.main_window.display_frame(combined_frame)
    
    def display_single_frame(self, frame: np.ndarray, title: str = ""):
        """显示单个画面 - Qt版本"""
        if frame is not None and self.qt_adapter.main_window:
            # 如果有标题，可以在这里添加文字
            if title:
                frame_with_title = frame.copy()
                cv2.putText(frame_with_title, title, (10, 30),
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
                self.qt_adapter.main_window.display_frame(frame_with_title)
            else:
                self.qt_adapter.main_window.display_frame(frame)
    
    def destroy_window(self):
        """销毁窗口 - Qt版本"""
        if self.qt_adapter.main_window:
            self.qt_adapter.main_window.close()
        self.logger.info("Qt窗口已销毁")
    
    def handle_toolbar_mouse_event(self, event: int, x: int, y: int) -> bool:
        """处理工具栏鼠标事件 - 占位实现"""
        # 这个方法将在实现Qt工具栏时完善
        return False
    
    def set_callback(self, callback_name: str, callback):
        """设置回调函数 - 占位实现"""
        # 这个方法将在实现Qt工具栏时完善
        pass
    
    def update_detection_status(self, enabled: bool, status_text: str = "",
                               sources_count: int = 0, scale_info: str = ""):
        """更新检测状态信息"""
        # 更新工具栏状态
        if hasattr(self.qt_adapter.main_window, 'qt_toolbar'):
            self.qt_adapter.main_window.qt_toolbar.update_detection_status(enabled)

        # 更新侧边栏状态
        if hasattr(self.qt_adapter.main_window, 'qt_sidebar'):
            self.qt_adapter.main_window.qt_sidebar.update_status_info(
                scale_info=scale_info
            )

    def update_toolbar_detection_status(self, enabled: bool):
        """更新工具栏热源检测状态"""
        if hasattr(self.qt_adapter.main_window, 'qt_toolbar'):
            self.qt_adapter.main_window.qt_toolbar.update_detection_status(enabled)

    def update_toolbar_human_detection_status(self, enabled: bool):
        """更新工具栏人体检测状态"""
        if hasattr(self.qt_adapter.main_window, 'qt_toolbar'):
            self.qt_adapter.main_window.qt_toolbar.update_human_detection_status(enabled)

    def update_toolbar_thermal_human_detection_status(self, enabled: bool):
        """更新工具栏热成像人体检测状态"""
        if hasattr(self.qt_adapter.main_window, 'qt_toolbar'):
            self.qt_adapter.main_window.qt_toolbar.update_thermal_human_status(enabled)

    def update_toolbar_fire_smoke_detection_status(self, enabled: bool):
        """更新工具栏火焰烟雾检测状态"""
        if hasattr(self.qt_adapter.main_window, 'qt_toolbar'):
            self.qt_adapter.main_window.qt_toolbar.update_fire_smoke_detection_status(enabled)

    def update_toolbar_panel_status(self, enabled: bool):
        """更新工具栏面板状态"""
        if hasattr(self.qt_adapter.main_window, 'qt_toolbar'):
            self.qt_adapter.main_window.qt_toolbar.update_panel_status(enabled)

    def update_toolbar_colormap_status(self, mode_name: str):
        """更新工具栏颜色映射状态"""
        if hasattr(self.qt_adapter.main_window, 'qt_toolbar'):
            self.qt_adapter.main_window.qt_toolbar.update_colormap_mode(mode_name)

    def update_heat_sources_detail(self, heat_sources_detail: list):
        """更新热源详细信息"""
        if hasattr(self.qt_adapter.main_window, 'qt_sidebar'):
            self.qt_adapter.main_window.qt_sidebar.update_heat_sources_detail(heat_sources_detail)

    def update_sidebar_status(self, **kwargs):
        """更新侧边栏状态信息"""
        if hasattr(self.qt_adapter.main_window, 'qt_sidebar'):
            self.qt_adapter.main_window.qt_sidebar.update_status_info(**kwargs)

    def update_all_detection_status(self, detection_status: dict):
        """更新所有检测状态信息"""
        if hasattr(self.qt_adapter.main_window, 'qt_sidebar'):
            self.qt_adapter.main_window.qt_sidebar.update_status_info(
                mqtt_enabled=detection_status.get('mqtt_enabled', False),
                human_count=detection_status.get('human_count', 0),
                smoke_count=detection_status.get('smoke_count', 0),
                smoke_area_change_rate=detection_status.get('smoke_area_change_rate', 0.0),
                fire_count=detection_status.get('fire_count', 0),
                fire_area_change_rate=detection_status.get('fire_area_change_rate', 0.0),
                fire_count_change_rate=detection_status.get('fire_count_change_rate', 0.0)
            )

    def update_fire_smoke_analysis_data(self, fire_smoke_results: dict):
        """更新火焰烟雾分析数据"""
        if hasattr(self.qt_adapter.main_window, 'qt_sidebar'):
            # 获取火焰分析组件
            sidebar = self.qt_adapter.main_window.qt_sidebar
            if hasattr(sidebar, 'flame_analysis_widget') and sidebar.flame_analysis_widget:
                sidebar.flame_analysis_widget.update_flame_analysis_data(fire_smoke_results)

    def toggle_sidebar_mode(self):
        """切换侧边栏模式"""
        if hasattr(self.qt_adapter.main_window, 'qt_sidebar'):
            self.qt_adapter.main_window.qt_sidebar.toggle_mode()
            return self.qt_adapter.main_window.qt_sidebar.sidebar_mode
        return "status"

    def toggle_sidebar(self):
        """切换侧边栏显示"""
        if hasattr(self.qt_adapter.main_window, 'qt_sidebar'):
            return self.qt_adapter.main_window.qt_sidebar.toggle_sidebar()
        return True
    
    def combine_frames(self, visible_frame, thermal_frame, fps: float = 0.0):
        """拼接画面 - 使用原有逻辑"""
        # 这里可以复用原有的combine_frames逻辑
        # 暂时简化实现
        if visible_frame is None or thermal_frame is None:
            return None
        
        # 调整画面尺寸
        display_width, display_height = 640, 480
        visible_resized = cv2.resize(visible_frame, (display_width, display_height))
        thermal_resized = cv2.resize(thermal_frame, (display_width, display_height))
        
        # 水平拼接
        combined_frame = np.hstack([visible_resized, thermal_resized])
        
        # 添加分割线
        cv2.line(combined_frame, (display_width, 0),
                (display_width, combined_frame.shape[0]),
                (255, 255, 255), 2)
        
        return combined_frame
