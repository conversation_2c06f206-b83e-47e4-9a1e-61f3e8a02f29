#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Qt工具栏模块
实现完整的Qt工具栏，替换OpenCV绘制的工具栏
"""

from PyQt5.QtWidgets import (QToolBar, QPushButton, QSlider, QLabel, 
                             QWidget, QHBoxLayout, QVBoxLayout, QFrame,
                             QSizePolicy, QSpacerItem)
from PyQt5.QtCore import Qt, pyqtSignal, QSize
from PyQt5.QtGui import QFont, QPalette, QColor

from utils.logger import get_logger


class QtThermalToolbar(QWidget):
    """Qt热成像工具栏类"""
    
    # 定义信号
    detection_mode_changed = pyqtSignal(str)  # 新增检测模式信号
    video_file_selected = pyqtSignal(str)  # 新增视频文件选择信号
    detection_toggled = pyqtSignal(bool)
    human_detection_toggled = pyqtSignal(bool)
    thermal_human_toggled = pyqtSignal(bool)
    fire_smoke_detection_toggled = pyqtSignal(bool)  # 新增火焰烟雾检测信号
    threshold_changed = pyqtSignal(float)
    adaptive_mode_changed = pyqtSignal(bool)
    save_image_clicked = pyqtSignal()
    save_debug_clicked = pyqtSignal()
    export_excel_clicked = pyqtSignal()  # 新增Excel导出信号
    help_clicked = pyqtSignal()
    panel_toggled = pyqtSignal(bool)
    quit_clicked = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger("QtThermalToolbar")
        
        # 工具栏状态
        # 检测模式状态
        self.detection_mode = "none"  # "none", "video", "camera"

        # 检测状态 - 所有检测功能默认关闭，等待用户手动启用
        self.detection_enabled = False  # 热源检测默认关闭
        self.human_detection_enabled = False  # 人体检测默认关闭
        self.thermal_human_enabled = False  # 热成像人体检测默认关闭
        self.fire_smoke_detection_enabled = False  # 火焰烟雾检测默认关闭
        self.panel_enabled = True
        self.adaptive_mode = True  # 默认自适应模式

        # 当前阈值
        self.current_threshold = 35.0

        # 初始化UI
        self.init_ui()
    
    def init_ui(self):
        """初始化用户界面"""
        # 设置工具栏样式
        self.setStyleSheet("""
            QWidget {
                background-color: #2d2d2d;
                color: white;
                border: none;
            }
            QPushButton {
                background-color: #464646;
                border: 1px solid #787878;
                border-radius: 3px;
                padding: 8px 10px;
                font-size: 12px;
                min-width: 70px;
                min-height: 30px;
            }
            QPushButton:hover {
                background-color: #5a5a5a;
            }
            QPushButton:pressed {
                background-color: #3c3c3c;
            }
            QPushButton:checked {
                background-color: #0078d4;
                border-color: #106ebe;
            }
            QSlider::groove:horizontal {
                border: 1px solid #787878;
                height: 8px;
                background: #464646;
                border-radius: 4px;
            }
            QSlider::handle:horizontal {
                background: #0078d4;
                border: 1px solid #106ebe;
                width: 18px;
                margin: -5px 0;
                border-radius: 9px;
            }
            QLabel {
                color: white;
                font-size: 11px;
            }
        """)
    
    def create_toolbar(self) -> QToolBar:
        """创建并返回QToolBar"""
        toolbar = QToolBar("热成像工具栏")
        toolbar.setFixedHeight(50)
        toolbar.setMovable(False)
        toolbar.setStyleSheet("QToolBar { border: none; background-color: #2d2d2d; }")
        
        # 创建主容器
        main_widget = QWidget()
        main_layout = QHBoxLayout(main_widget)
        main_layout.setContentsMargins(10, 5, 10, 5)
        main_layout.setSpacing(10)

        # 0. 检测模式选择组
        mode_group = self._create_mode_group()
        main_layout.addWidget(mode_group)

        # 添加分隔符
        main_layout.addWidget(self._create_separator())

        # 1. 检测按钮组
        detection_group = self._create_detection_group()
        main_layout.addWidget(detection_group)

        # 添加分隔符
        main_layout.addWidget(self._create_separator())

        # 2. 阈值控制组
        threshold_group = self._create_threshold_group()
        main_layout.addWidget(threshold_group)
        
        # 添加分隔符
        main_layout.addWidget(self._create_separator())
        
        # 3. 功能按钮组
        function_group = self._create_function_group()
        main_layout.addWidget(function_group)
        
        # 添加弹性空间
        main_layout.addItem(QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum))
        
        # 4. 系统按钮组
        system_group = self._create_system_group()
        main_layout.addWidget(system_group)
        
        # 将主容器添加到工具栏
        toolbar.addWidget(main_widget)
        
        return toolbar

    def _create_mode_group(self) -> QWidget:
        """创建检测模式选择组"""
        group = QWidget()
        layout = QHBoxLayout(group)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(5)

        # 模式标签
        mode_label = QLabel("检测模式:")
        mode_label.setStyleSheet("color: white; font-weight: bold;")
        layout.addWidget(mode_label)

        # 视频检测按钮
        self.video_mode_btn = QPushButton("📹 视频检测")
        self.video_mode_btn.setCheckable(True)
        self.video_mode_btn.setChecked(self.detection_mode == "video")
        self.video_mode_btn.clicked.connect(lambda: self._on_mode_changed("video"))
        self.video_mode_btn.setStyleSheet("""
            QPushButton {
                background-color: #464646;
                border: 1px solid #787878;
                border-radius: 3px;
                padding: 8px 10px;
                font-size: 12px;
                min-width: 100px;
                min-height: 30px;
            }
            QPushButton:checked {
                background-color: #28a745;
                border-color: #1e7e34;
                color: white;
            }
            QPushButton:hover {
                background-color: #5a5a5a;
            }
        """)
        layout.addWidget(self.video_mode_btn)

        # 摄像头检测按钮
        self.camera_mode_btn = QPushButton("📡 摄像头检测")
        self.camera_mode_btn.setCheckable(True)
        self.camera_mode_btn.setChecked(self.detection_mode == "camera")
        self.camera_mode_btn.clicked.connect(lambda: self._on_mode_changed("camera"))
        self.camera_mode_btn.setStyleSheet("""
            QPushButton {
                background-color: #464646;
                border: 1px solid #787878;
                border-radius: 3px;
                padding: 8px 10px;
                font-size: 12px;
                min-width: 120px;
                min-height: 30px;
            }
            QPushButton:checked {
                background-color: #007bff;
                border-color: #0056b3;
                color: white;
            }
            QPushButton:hover {
                background-color: #5a5a5a;
            }
        """)
        layout.addWidget(self.camera_mode_btn)

        # 视频文件选择按钮
        self.select_video_btn = QPushButton("📁 选择视频")
        self.select_video_btn.clicked.connect(self._on_select_video_file)
        self.select_video_btn.setEnabled(False)  # 默认禁用，只有在视频模式下才启用
        self.select_video_btn.setStyleSheet("""
            QPushButton {
                background-color: #464646;
                border: 1px solid #787878;
                border-radius: 3px;
                padding: 8px 10px;
                font-size: 12px;
                min-width: 100px;
                min-height: 30px;
            }
            QPushButton:enabled {
                background-color: #6c757d;
                border-color: #5a6268;
                color: white;
            }
            QPushButton:enabled:hover {
                background-color: #5a6268;
            }
            QPushButton:disabled {
                background-color: #343a40;
                border-color: #495057;
                color: #6c757d;
            }
        """)
        layout.addWidget(self.select_video_btn)

        # 状态标签
        self.mode_status_label = QLabel("未选择")
        self.mode_status_label.setStyleSheet("color: #ffc107; font-size: 11px;")
        layout.addWidget(self.mode_status_label)

        return group

    def _create_detection_group(self) -> QWidget:
        """创建检测按钮组"""
        group = QWidget()
        layout = QHBoxLayout(group)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(5)
        
        # 热源检测按钮
        self.heat_btn = QPushButton("热源检测")
        self.heat_btn.setCheckable(True)
        self.heat_btn.setChecked(self.detection_enabled)
        self.heat_btn.toggled.connect(self._on_detection_toggled)
        layout.addWidget(self.heat_btn)
        
        # 人体检测按钮
        self.human_btn = QPushButton("人体检测")
        self.human_btn.setCheckable(True)
        self.human_btn.setChecked(self.human_detection_enabled)
        self.human_btn.toggled.connect(self._on_human_detection_toggled)
        layout.addWidget(self.human_btn)
        
        # 热成像人体检测按钮
        self.thermal_human_btn = QPushButton("热成像人体检测")
        self.thermal_human_btn.setCheckable(True)
        self.thermal_human_btn.setChecked(self.thermal_human_enabled)
        self.thermal_human_btn.toggled.connect(self._on_thermal_human_toggled)
        layout.addWidget(self.thermal_human_btn)

        # 火焰烟雾检测按钮
        self.fire_smoke_btn = QPushButton("火焰烟雾检测")
        self.fire_smoke_btn.setCheckable(True)
        self.fire_smoke_btn.setChecked(self.fire_smoke_detection_enabled)
        self.fire_smoke_btn.toggled.connect(self._on_fire_smoke_detection_toggled)
        self.fire_smoke_btn.setStyleSheet("""
            QPushButton {
                background-color: #464646;
                border: 1px solid #787878;
                border-radius: 3px;
                padding: 8px 10px;
                font-size: 12px;
                min-width: 120px;
                min-height: 30px;
            }
            QPushButton:checked {
                background-color: #ff6600;
                border-color: #cc5200;
                color: white;
            }
            QPushButton:hover {
                background-color: #5a5a5a;
            }
        """)
        layout.addWidget(self.fire_smoke_btn)

        return group
    
    def _create_threshold_group(self) -> QWidget:
        """创建阈值控制组"""
        group = QWidget()
        layout = QVBoxLayout(group)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(2)
        
        # 模式按钮行
        mode_layout = QHBoxLayout()
        mode_layout.setSpacing(3)
        
        # 自适应模式按钮
        self.adaptive_btn = QPushButton("自适应")
        self.adaptive_btn.setCheckable(True)
        self.adaptive_btn.setChecked(self.adaptive_mode)
        self.adaptive_btn.toggled.connect(self._on_adaptive_mode_toggled)
        mode_layout.addWidget(self.adaptive_btn)
        
        # 手动模式按钮
        self.manual_btn = QPushButton("手动")
        self.manual_btn.setCheckable(True)
        self.manual_btn.setChecked(not self.adaptive_mode)
        self.manual_btn.toggled.connect(self._on_manual_mode_toggled)
        mode_layout.addWidget(self.manual_btn)
        
        layout.addLayout(mode_layout)
        
        # 阈值滑块行
        slider_layout = QHBoxLayout()
        slider_layout.setSpacing(5)
        
        # 阈值标签
        self.threshold_label = QLabel(f"{self.current_threshold:.1f}°C")
        self.threshold_label.setMinimumWidth(50)
        slider_layout.addWidget(self.threshold_label)
        
        # 阈值滑块
        self.threshold_slider = QSlider(Qt.Horizontal)
        self.threshold_slider.setMinimum(200)  # 20.0°C
        self.threshold_slider.setMaximum(800)  # 80.0°C
        self.threshold_slider.setValue(int(self.current_threshold * 10))
        self.threshold_slider.setMinimumWidth(120)
        self.threshold_slider.valueChanged.connect(self._on_threshold_changed)
        slider_layout.addWidget(self.threshold_slider)
        
        layout.addLayout(slider_layout)
        
        return group
    
    def _create_function_group(self) -> QWidget:
        """创建功能按钮组"""
        group = QWidget()
        layout = QHBoxLayout(group)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(5)

        # 面板切换按钮
        self.panel_btn = QPushButton("面板")
        self.panel_btn.setCheckable(True)
        self.panel_btn.setChecked(self.panel_enabled)
        self.panel_btn.toggled.connect(self._on_panel_toggled)
        layout.addWidget(self.panel_btn)
        
        return group
    
    def _create_system_group(self) -> QWidget:
        """创建系统按钮组"""
        group = QWidget()
        layout = QHBoxLayout(group)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(5)
        
        # 保存图像按钮
        save_img_btn = QPushButton("保存图像")
        save_img_btn.clicked.connect(self.save_image_clicked.emit)
        layout.addWidget(save_img_btn)
        
        # 保存调试按钮
        save_debug_btn = QPushButton("保存调试")
        save_debug_btn.clicked.connect(self.save_debug_clicked.emit)
        layout.addWidget(save_debug_btn)

        # Excel导出按钮
        export_excel_btn = QPushButton("导出数据")
        export_excel_btn.clicked.connect(self.export_excel_clicked.emit)
        export_excel_btn.setToolTip("导出热源数据到Excel文件")
        layout.addWidget(export_excel_btn)

        # 帮助按钮
        help_btn = QPushButton("帮助")
        help_btn.clicked.connect(self.help_clicked.emit)
        layout.addWidget(help_btn)
        
        # 退出按钮
        quit_btn = QPushButton("退出")
        quit_btn.clicked.connect(self.quit_clicked.emit)
        layout.addWidget(quit_btn)
        
        return group
    
    def _create_separator(self) -> QFrame:
        """创建分隔符"""
        separator = QFrame()
        separator.setFrameShape(QFrame.VLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setStyleSheet("QFrame { color: #787878; }")
        return separator

    # 事件处理方法
    def _on_mode_changed(self, mode: str):
        """检测模式切换"""
        # 更新按钮状态
        self.video_mode_btn.setChecked(mode == "video")
        self.camera_mode_btn.setChecked(mode == "camera")

        # 更新状态
        self.detection_mode = mode

        # 更新视频选择按钮状态
        self.select_video_btn.setEnabled(mode == "video")

        # 更新状态标签
        if mode == "video":
            self.mode_status_label.setText("视频检测模式 - 请选择视频文件")
            self.mode_status_label.setStyleSheet("color: #28a745; font-size: 11px;")
        elif mode == "camera":
            self.mode_status_label.setText("摄像头检测模式")
            self.mode_status_label.setStyleSheet("color: #007bff; font-size: 11px;")
        else:
            self.mode_status_label.setText("未选择")
            self.mode_status_label.setStyleSheet("color: #ffc107; font-size: 11px;")

        # 发射信号
        self.detection_mode_changed.emit(mode)
        self.logger.info(f"检测模式切换为: {mode}")

    def _on_select_video_file(self):
        """选择视频文件"""
        try:
            from PyQt5.QtWidgets import QFileDialog

            # 打开文件选择对话框
            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "选择视频文件",
                "",
                "视频文件 (*.mp4 *.avi *.mov *.mkv *.wmv *.flv);;所有文件 (*)"
            )

            if file_path:
                # 更新状态标签
                import os
                filename = os.path.basename(file_path)
                self.mode_status_label.setText(f"已选择: {filename}")
                self.mode_status_label.setStyleSheet("color: #28a745; font-size: 11px;")

                # 发射视频文件选择信号
                self.video_file_selected.emit(file_path)
                self.logger.info(f"选择视频文件: {file_path}")

        except Exception as e:
            self.logger.error(f"选择视频文件失败: {e}")

    def _on_detection_toggled(self, checked: bool):
        """热源检测切换"""
        self.detection_enabled = checked
        self.detection_toggled.emit(checked)
        self.logger.info(f"热源检测: {'开启' if checked else '关闭'}")

    def _on_human_detection_toggled(self, checked: bool):
        """人体检测切换"""
        self.human_detection_enabled = checked
        self.human_detection_toggled.emit(checked)
        self.logger.info(f"人体检测: {'开启' if checked else '关闭'}")

    def _on_thermal_human_toggled(self, checked: bool):
        """热成像人体检测切换"""
        self.thermal_human_enabled = checked
        self.thermal_human_toggled.emit(checked)
        self.logger.info(f"热成像人体检测: {'开启' if checked else '关闭'}")

    def _on_fire_smoke_detection_toggled(self, checked: bool):
        """火焰烟雾检测切换"""
        self.fire_smoke_detection_enabled = checked
        self.fire_smoke_detection_toggled.emit(checked)
        self.logger.info(f"火焰烟雾检测: {'开启' if checked else '关闭'}")

        if checked:
            self.logger.info("✅ 火焰烟雾检测已启用，将在可见光画面上检测火焰和烟雾")
        else:
            self.logger.info("❌ 火焰烟雾检测已禁用")

    def _on_adaptive_mode_toggled(self, checked: bool):
        """自适应模式切换"""
        if checked:
            self.adaptive_mode = True
            self.manual_btn.setChecked(False)
            # 自适应模式下禁用滑块
            self.threshold_slider.setEnabled(False)
            self.adaptive_mode_changed.emit(True)
            self.logger.info("切换到自适应阈值模式")

    def _on_manual_mode_toggled(self, checked: bool):
        """手动模式切换"""
        if checked:
            self.adaptive_mode = False
            self.adaptive_btn.setChecked(False)
            # 手动模式下启用滑块
            self.threshold_slider.setEnabled(True)
            self.adaptive_mode_changed.emit(False)
            self.logger.info("切换到手动阈值模式")

    def _on_threshold_changed(self, value: int):
        """阈值滑块变化"""
        self.current_threshold = value / 10.0
        self.threshold_label.setText(f"{self.current_threshold:.1f}°C")
        self.threshold_changed.emit(self.current_threshold)

    def _on_panel_toggled(self, checked: bool):
        """面板切换"""
        self.panel_enabled = checked
        self.panel_toggled.emit(checked)
        self.logger.info(f"侧边栏: {'显示' if checked else '隐藏'}")

    # 公共方法用于外部状态更新
    def update_detection_status(self, enabled: bool):
        """更新热源检测状态"""
        self.detection_enabled = enabled
        self.heat_btn.setChecked(enabled)

    def update_human_detection_status(self, enabled: bool):
        """更新人体检测状态"""
        self.human_detection_enabled = enabled
        self.human_btn.setChecked(enabled)

    def update_thermal_human_status(self, enabled: bool):
        """更新热成像人体检测状态"""
        self.thermal_human_enabled = enabled
        self.thermal_human_btn.setChecked(enabled)

    def update_panel_status(self, enabled: bool):
        """更新面板状态"""
        self.panel_enabled = enabled
        self.panel_btn.setChecked(enabled)

    def update_threshold_value(self, value: float):
        """更新阈值显示"""
        self.current_threshold = value
        self.threshold_label.setText(f"{value:.1f}°C")
        self.threshold_slider.setValue(int(value * 10))

    def update_adaptive_mode(self, is_adaptive: bool):
        """更新自适应模式状态"""
        self.adaptive_mode = is_adaptive
        if is_adaptive:
            self.adaptive_btn.setChecked(True)
            self.manual_btn.setChecked(False)
            # 自适应模式下禁用滑块
            self.threshold_slider.setEnabled(False)
            self.logger.info("界面切换到自适应阈值模式")
        else:
            self.adaptive_btn.setChecked(False)
            self.manual_btn.setChecked(True)
            # 手动模式下启用滑块
            self.threshold_slider.setEnabled(True)
            self.logger.info("界面切换到手动阈值模式")

    def enable_threshold_slider(self, enabled: bool):
        """启用或禁用阈值滑块"""
        self.threshold_slider.setEnabled(enabled)
        if enabled:
            self.logger.info("阈值滑块已启用")
        else:
            self.logger.info("阈值滑块已禁用")

    def update_fire_smoke_detection_status(self, enabled: bool):
        """更新火焰烟雾检测状态"""
        self.fire_smoke_detection_enabled = enabled
        self.fire_smoke_btn.setChecked(enabled)


