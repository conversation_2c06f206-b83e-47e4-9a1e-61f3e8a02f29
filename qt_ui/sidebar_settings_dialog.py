#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
侧边栏设置对话框
配置显示选项和监控参数
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel,
                             QSpinBox, QDoubleSpinBox, QCheckBox, QPushButton,
                             QGroupBox, QGridLayout, QComboBox, QSlider,
                             QTabWidget, QWidget)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont

from utils.logger import get_logger


class SidebarSettingsDialog(QDialog):
    """侧边栏设置对话框"""
    
    # 定义信号
    settings_changed = pyqtSignal(dict)  # 设置变更信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger("SidebarSettingsDialog")
        
        # 当前设置
        self.current_settings = {
            # 显示设置
            'max_heat_sources_display': 10,
            'update_interval_ms': 5000,
            'auto_scroll_enabled': False,
            'show_tracking_ids': True,
            'show_change_rates': True,
            
            # 全局指标设置
            'enable_global_metrics': True,
            'metrics_update_interval': 10.0,
            'temperature_history_days': 30,
            
            # 变化率设置
            'change_rate_window_seconds': 60.0,
            'min_change_rate_threshold': 0.01,
            'show_percentage_rates': True,
            
            # 性能设置
            'enable_real_time_calculation': True,
            'max_history_snapshots': 1000,
            'cleanup_interval_hours': 24.0
        }
        
        self.init_ui()
        self.load_current_settings()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle('侧边栏设置')
        self.setFixedSize(500, 600)
        self.setModal(True)
        
        # 创建主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # 创建选项卡控件
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)
        
        # 创建各个选项卡
        self.create_display_tab()
        self.create_metrics_tab()
        self.create_performance_tab()
        
        # 创建按钮区域
        self.create_button_area(main_layout)
        
        # 设置样式
        self.setStyleSheet("""
            QDialog {
                background-color: #2b2b2b;
                color: white;
                font-family: "Microsoft YaHei", "SimHei", Arial;
            }
            QTabWidget::pane {
                border: 1px solid #555555;
                background-color: #2b2b2b;
            }
            QTabBar::tab {
                background-color: #404040;
                color: white;
                padding: 8px 16px;
                margin-right: 2px;
            }
            QTabBar::tab:selected {
                background-color: #0078d4;
            }
            QGroupBox {
                font-weight: bold;
                color: #ffff00;
                border: 1px solid #555555;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QLabel {
                color: white;
            }
            QSpinBox, QDoubleSpinBox, QComboBox {
                background-color: #404040;
                border: 1px solid #606060;
                border-radius: 3px;
                padding: 5px;
                color: white;
            }
            QCheckBox {
                color: white;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
            QCheckBox::indicator:unchecked {
                background-color: #404040;
                border: 1px solid #606060;
            }
            QCheckBox::indicator:checked {
                background-color: #0078d4;
                border: 1px solid #106ebe;
            }
            QPushButton {
                background-color: #404040;
                border: 1px solid #606060;
                border-radius: 3px;
                padding: 8px 16px;
                color: white;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #505050;
            }
            QPushButton:pressed {
                background-color: #303030;
            }
        """)
    
    def create_display_tab(self):
        """创建显示设置选项卡"""
        display_widget = QWidget()
        layout = QVBoxLayout(display_widget)
        layout.setSpacing(15)
        
        # 显示控制组
        display_group = QGroupBox("显示控制")
        display_layout = QGridLayout(display_group)
        
        # 最大显示热源数量
        display_layout.addWidget(QLabel("最大显示热源数:"), 0, 0)
        self.max_sources_spin = QSpinBox()
        self.max_sources_spin.setRange(5, 50)
        self.max_sources_spin.setValue(10)
        display_layout.addWidget(self.max_sources_spin, 0, 1)
        
        # 更新间隔
        display_layout.addWidget(QLabel("更新间隔(毫秒):"), 1, 0)
        self.update_interval_spin = QSpinBox()
        self.update_interval_spin.setRange(1000, 30000)
        self.update_interval_spin.setSingleStep(1000)
        self.update_interval_spin.setValue(5000)
        display_layout.addWidget(self.update_interval_spin, 1, 1)
        
        # 显示选项
        self.auto_scroll_check = QCheckBox("启用自动滚动")
        self.show_tracking_check = QCheckBox("显示跟踪ID")
        self.show_rates_check = QCheckBox("显示变化率")
        
        display_layout.addWidget(self.auto_scroll_check, 2, 0, 1, 2)
        display_layout.addWidget(self.show_tracking_check, 3, 0, 1, 2)
        display_layout.addWidget(self.show_rates_check, 4, 0, 1, 2)
        
        layout.addWidget(display_group)
        layout.addStretch()
        
        self.tab_widget.addTab(display_widget, "显示设置")
    
    def create_metrics_tab(self):
        """创建指标设置选项卡"""
        metrics_widget = QWidget()
        layout = QVBoxLayout(metrics_widget)
        layout.setSpacing(15)
        
        # 全局指标组
        global_group = QGroupBox("全局指标")
        global_layout = QGridLayout(global_group)
        
        self.enable_global_check = QCheckBox("启用全局指标计算")
        global_layout.addWidget(self.enable_global_check, 0, 0, 1, 2)
        
        global_layout.addWidget(QLabel("指标更新间隔(秒):"), 1, 0)
        self.metrics_interval_spin = QDoubleSpinBox()
        self.metrics_interval_spin.setRange(1.0, 60.0)
        self.metrics_interval_spin.setSingleStep(1.0)
        self.metrics_interval_spin.setValue(10.0)
        global_layout.addWidget(self.metrics_interval_spin, 1, 1)
        
        global_layout.addWidget(QLabel("温度历史天数:"), 2, 0)
        self.temp_history_spin = QSpinBox()
        self.temp_history_spin.setRange(1, 90)
        self.temp_history_spin.setValue(30)
        global_layout.addWidget(self.temp_history_spin, 2, 1)
        
        layout.addWidget(global_group)
        
        # 变化率组
        rate_group = QGroupBox("变化率计算")
        rate_layout = QGridLayout(rate_group)
        
        rate_layout.addWidget(QLabel("时间窗口(秒):"), 0, 0)
        self.rate_window_spin = QDoubleSpinBox()
        self.rate_window_spin.setRange(10.0, 300.0)
        self.rate_window_spin.setSingleStep(10.0)
        self.rate_window_spin.setValue(60.0)
        rate_layout.addWidget(self.rate_window_spin, 0, 1)
        
        rate_layout.addWidget(QLabel("最小阈值:"), 1, 0)
        self.min_threshold_spin = QDoubleSpinBox()
        self.min_threshold_spin.setRange(0.001, 1.0)
        self.min_threshold_spin.setSingleStep(0.001)
        self.min_threshold_spin.setDecimals(3)
        self.min_threshold_spin.setValue(0.01)
        rate_layout.addWidget(self.min_threshold_spin, 1, 1)
        
        self.percentage_rates_check = QCheckBox("显示百分比变化率")
        rate_layout.addWidget(self.percentage_rates_check, 2, 0, 1, 2)
        
        layout.addWidget(rate_group)
        layout.addStretch()
        
        self.tab_widget.addTab(metrics_widget, "指标设置")
    
    def create_performance_tab(self):
        """创建性能设置选项卡"""
        perf_widget = QWidget()
        layout = QVBoxLayout(perf_widget)
        layout.setSpacing(15)
        
        # 性能控制组
        perf_group = QGroupBox("性能控制")
        perf_layout = QGridLayout(perf_group)
        
        self.realtime_calc_check = QCheckBox("启用实时计算")
        perf_layout.addWidget(self.realtime_calc_check, 0, 0, 1, 2)
        
        perf_layout.addWidget(QLabel("最大历史快照数:"), 1, 0)
        self.max_snapshots_spin = QSpinBox()
        self.max_snapshots_spin.setRange(100, 5000)
        self.max_snapshots_spin.setSingleStep(100)
        self.max_snapshots_spin.setValue(1000)
        perf_layout.addWidget(self.max_snapshots_spin, 1, 1)
        
        perf_layout.addWidget(QLabel("清理间隔(小时):"), 2, 0)
        self.cleanup_interval_spin = QDoubleSpinBox()
        self.cleanup_interval_spin.setRange(1.0, 168.0)
        self.cleanup_interval_spin.setSingleStep(1.0)
        self.cleanup_interval_spin.setValue(24.0)
        perf_layout.addWidget(self.cleanup_interval_spin, 2, 1)
        
        layout.addWidget(perf_group)
        layout.addStretch()
        
        self.tab_widget.addTab(perf_widget, "性能设置")
    
    def create_button_area(self, layout):
        """创建按钮区域"""
        button_layout = QHBoxLayout()
        
        # 重置按钮
        reset_btn = QPushButton("重置默认")
        reset_btn.clicked.connect(self.reset_to_defaults)
        button_layout.addWidget(reset_btn)
        
        button_layout.addStretch()
        
        # 取消按钮
        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)
        
        # 确定按钮
        ok_btn = QPushButton("确定")
        ok_btn.clicked.connect(self.accept_settings)
        ok_btn.setDefault(True)
        button_layout.addWidget(ok_btn)
        
        layout.addLayout(button_layout)
    
    def load_current_settings(self):
        """加载当前设置到控件"""
        # 显示设置
        self.max_sources_spin.setValue(self.current_settings['max_heat_sources_display'])
        self.update_interval_spin.setValue(self.current_settings['update_interval_ms'])
        self.auto_scroll_check.setChecked(self.current_settings['auto_scroll_enabled'])
        self.show_tracking_check.setChecked(self.current_settings['show_tracking_ids'])
        self.show_rates_check.setChecked(self.current_settings['show_change_rates'])
        
        # 指标设置
        self.enable_global_check.setChecked(self.current_settings['enable_global_metrics'])
        self.metrics_interval_spin.setValue(self.current_settings['metrics_update_interval'])
        self.temp_history_spin.setValue(self.current_settings['temperature_history_days'])
        self.rate_window_spin.setValue(self.current_settings['change_rate_window_seconds'])
        self.min_threshold_spin.setValue(self.current_settings['min_change_rate_threshold'])
        self.percentage_rates_check.setChecked(self.current_settings['show_percentage_rates'])
        
        # 性能设置
        self.realtime_calc_check.setChecked(self.current_settings['enable_real_time_calculation'])
        self.max_snapshots_spin.setValue(self.current_settings['max_history_snapshots'])
        self.cleanup_interval_spin.setValue(self.current_settings['cleanup_interval_hours'])
    
    def get_settings_from_controls(self):
        """从控件获取设置"""
        return {
            # 显示设置
            'max_heat_sources_display': self.max_sources_spin.value(),
            'update_interval_ms': self.update_interval_spin.value(),
            'auto_scroll_enabled': self.auto_scroll_check.isChecked(),
            'show_tracking_ids': self.show_tracking_check.isChecked(),
            'show_change_rates': self.show_rates_check.isChecked(),
            
            # 指标设置
            'enable_global_metrics': self.enable_global_check.isChecked(),
            'metrics_update_interval': self.metrics_interval_spin.value(),
            'temperature_history_days': self.temp_history_spin.value(),
            'change_rate_window_seconds': self.rate_window_spin.value(),
            'min_change_rate_threshold': self.min_threshold_spin.value(),
            'show_percentage_rates': self.percentage_rates_check.isChecked(),
            
            # 性能设置
            'enable_real_time_calculation': self.realtime_calc_check.isChecked(),
            'max_history_snapshots': self.max_snapshots_spin.value(),
            'cleanup_interval_hours': self.cleanup_interval_spin.value()
        }
    
    def reset_to_defaults(self):
        """重置为默认设置"""
        # 重新加载默认设置
        self.load_current_settings()
        self.logger.info("设置已重置为默认值")
    
    def accept_settings(self):
        """接受设置"""
        new_settings = self.get_settings_from_controls()
        self.current_settings.update(new_settings)
        
        # 发出设置变更信号
        self.settings_changed.emit(new_settings)
        
        self.logger.info("侧边栏设置已更新")
        self.accept()
    
    def set_current_settings(self, settings: dict):
        """设置当前配置"""
        self.current_settings.update(settings)
        self.load_current_settings()
    
    def get_current_settings(self) -> dict:
        """获取当前设置"""
        return self.current_settings.copy()
