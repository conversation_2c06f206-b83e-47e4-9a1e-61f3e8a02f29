#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
选项卡式侧边栏组件
将热源监控和配置工具分成独立的选项卡页面
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTabWidget,
                             QLabel, QPushButton, QFrame, QSizePolicy)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QIcon

from utils.logger import get_logger
from qt_ui.font_config import get_tab_style, get_button_style, get_label_style


class TabbedSidebar(QWidget):
    """选项卡式侧边栏主组件"""
    
    # 信号
    tab_changed = pyqtSignal(int, str)  # 选项卡切换信号 (index, tab_name)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger("TabbedSidebar")
        
        # 父窗口引用
        self.parent_window = parent
        
        # 选项卡组件引用
        self.monitoring_tab = None
        self.configuration_tab = None
        
        self.init_ui()
        
    def init_ui(self):
        """初始化用户界面"""
        # 设置尺寸策略
        self.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Expanding)
        
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 创建选项卡组件
        self.tab_widget = QTabWidget()
        self.tab_widget.setTabPosition(QTabWidget.North)
        self.tab_widget.setMovable(False)  # 不允许拖拽重排
        self.tab_widget.setTabsClosable(False)  # 不允许关闭选项卡

        # 设置选项卡组件的尺寸策略，确保它能够扩展
        self.tab_widget.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        
        # 设置选项卡样式
        self.tab_widget.setStyleSheet(get_tab_style())
        
        # 连接选项卡切换信号
        self.tab_widget.currentChanged.connect(self.on_tab_changed)
        
        main_layout.addWidget(self.tab_widget)
        
    def add_monitoring_tab(self, monitoring_widget):
        """添加监控页面选项卡"""
        self.monitoring_tab = monitoring_widget
        
        # 创建监控页面容器
        monitoring_container = QWidget()
        monitoring_container.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        monitoring_layout = QVBoxLayout(monitoring_container)
        monitoring_layout.setContentsMargins(5, 5, 5, 5)
        monitoring_layout.setSpacing(5)
        
        # 添加监控组件
        monitoring_layout.addWidget(monitoring_widget)
        # 移除弹性空间，让监控组件充分利用可用空间
        # monitoring_layout.addStretch()
        
        # 添加到选项卡
        tab_index = self.tab_widget.addTab(monitoring_container, " 监控")
        self.tab_widget.setTabToolTip(tab_index, "实时热源检测和监控数据")
        
        self.logger.info("添加热源监控选项卡")
        return tab_index
        
    def add_configuration_tab(self, configuration_widget):
        """添加配置页面选项卡"""
        self.configuration_tab = configuration_widget
        
        # 创建配置页面容器
        configuration_container = QWidget()
        configuration_container.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        configuration_layout = QVBoxLayout(configuration_container)
        configuration_layout.setContentsMargins(5, 5, 5, 5)
        configuration_layout.setSpacing(5)
        
        # 添加页面标题
        title_frame = QFrame()
        title_frame.setStyleSheet("""
            QFrame {
                background-color: #404040;
                border: 1px solid #606060;
                border-radius: 5px;
                padding: 5px;
            }
        """)
        title_frame.setFixedHeight(45)
        
        title_layout = QHBoxLayout(title_frame)
        title_layout.setContentsMargins(10, 5, 10, 5)
        
        # 标题文本
        title_label = QLabel("🛠️ 配置与工具")
        title_label.setStyleSheet(get_label_style('title_small', 'white', 'bold') + """
            background: none;
            border: none;
        """)
        title_layout.addWidget(title_label)
        
        # 弹性空间
        title_layout.addStretch()
        
        # 快速操作按钮 - 使用更短的文字避免重叠
        quick_expand_btn = QPushButton("展开")
        quick_expand_btn.setStyleSheet(get_button_style('button_small', '#505050', '#707070', '#606060'))
        quick_expand_btn.clicked.connect(self.expand_all_configuration)
        title_layout.addWidget(quick_expand_btn)
        
        configuration_layout.addWidget(title_frame)
        
        # 添加配置组件
        configuration_layout.addWidget(configuration_widget)
        # 移除弹性空间，让配置组件充分利用可用空间
        # configuration_layout.addStretch()
        
        # 添加到选项卡
        tab_index = self.tab_widget.addTab(configuration_container, " 配置")
        self.tab_widget.setTabToolTip(tab_index, "系统配置、手动标注和视频处理工具")
        
        self.logger.info("添加配置工具选项卡")
        return tab_index

    def on_tab_changed(self, index):
        """处理选项卡切换"""
        tab_text = self.tab_widget.tabText(index)
        tab_name = tab_text.split(' ', 1)[-1] if ' ' in tab_text else tab_text
        
        self.tab_changed.emit(index, tab_name)
        self.logger.info(f"切换到选项卡: {tab_name} (索引: {index})")
        
        # 根据选项卡调整窗口标题
        if hasattr(self.parent_window, 'sidebar_dock'):
            if index == 0:  # 监控页面
                self.parent_window.sidebar_dock.setWindowTitle("热源监控面板")
            elif index == 1:  # 配置页面
                self.parent_window.sidebar_dock.setWindowTitle("配置与工具面板")
                
    def set_current_tab(self, index):
        """设置当前选项卡"""
        if 0 <= index < self.tab_widget.count():
            self.tab_widget.setCurrentIndex(index)
            
    def get_current_tab_index(self):
        """获取当前选项卡索引"""
        return self.tab_widget.currentIndex()
        
    def get_current_tab_name(self):
        """获取当前选项卡名称"""
        index = self.tab_widget.currentIndex()
        if index >= 0:
            tab_text = self.tab_widget.tabText(index)
            return tab_text.split(' ', 1)[-1] if ' ' in tab_text else tab_text
        return ""
        
    def expand_all_configuration(self):
        """展开配置页面的所有区块"""
        if self.configuration_tab and hasattr(self.configuration_tab, 'accordion'):
            self.configuration_tab.accordion.expand_all()
            self.logger.info("展开配置页面所有区块")
            
    def collapse_all_configuration(self):
        """折叠配置页面的所有区块"""
        if self.configuration_tab and hasattr(self.configuration_tab, 'accordion'):
            self.configuration_tab.accordion.collapse_all()
            self.logger.info("折叠配置页面所有区块")
            
    def get_monitoring_widget(self):
        """获取监控组件"""
        return self.monitoring_tab
        
    def get_configuration_widget(self):
        """获取配置组件"""
        return self.configuration_tab
        
    def set_tab_enabled(self, index, enabled):
        """设置选项卡启用状态"""
        self.tab_widget.setTabEnabled(index, enabled)
        
    def set_tab_visible(self, index, visible):
        """设置选项卡可见性"""
        if visible:
            self.tab_widget.setTabText(index, self.tab_widget.tabText(index))
        else:
            # Qt没有直接隐藏选项卡的方法，这里可以考虑其他实现
            pass
            
    def add_tab_badge(self, index, badge_text):
        """为选项卡添加徽章（如未读数量）"""
        current_text = self.tab_widget.tabText(index)
        # 移除现有徽章
        if '(' in current_text and ')' in current_text:
            current_text = current_text.split('(')[0].strip()
        
        # 添加新徽章
        if badge_text:
            new_text = f"{current_text} ({badge_text})"
        else:
            new_text = current_text
            
        self.tab_widget.setTabText(index, new_text)
        
    def update_monitoring_data(self, **kwargs):
        """更新监控数据"""
        if self.monitoring_tab and hasattr(self.monitoring_tab, 'update_status_info'):
            self.monitoring_tab.update_status_info(**kwargs)
            
    def update_heat_sources_detail(self, heat_sources_detail):
        """更新热源详细信息"""
        print(f"🔥 TabbedSidebar收到热源详情更新: {len(heat_sources_detail) if heat_sources_detail else 0}个热源")

        if self.monitoring_tab and hasattr(self.monitoring_tab, 'update_heat_sources_detail'):
            print(f"🔄 TabbedSidebar转发给monitoring_tab")
            self.monitoring_tab.update_heat_sources_detail(heat_sources_detail)
        else:
            print(f"❌ monitoring_tab不存在或没有update_heat_sources_detail方法")

        # 更新监控选项卡的徽章
        if heat_sources_detail:
            self.add_tab_badge(0, str(len(heat_sources_detail)))
        else:
            self.add_tab_badge(0, "")
            
    def scroll_heat_sources(self, direction):
        """滚动热源详情"""
        if self.monitoring_tab and hasattr(self.monitoring_tab, 'scroll_heat_sources'):
            self.monitoring_tab.scroll_heat_sources(direction)
