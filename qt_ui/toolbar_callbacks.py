#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工具栏回调处理器
处理Qt工具栏的各种信号回调
"""

from utils.logger import get_logger


class ToolbarCallbackHandler:
    """工具栏回调处理器"""
    
    def __init__(self, system_instance):
        """
        初始化回调处理器
        
        Args:
            system_instance: 系统实例
        """
        self.system = system_instance
        self.logger = get_logger("ToolbarCallbackHandler")

    def change_detection_mode(self, mode: str):
        """切换检测模式"""
        try:
            if hasattr(self.system, 'set_detection_mode'):
                success = self.system.set_detection_mode(mode)
                if success:
                    self.logger.info(f"检测模式切换成功: {mode}")

                    # 显示模式切换信息
                    if mode == "video":
                        print("📹 已切换到视频检测模式")
                        print("   - 请点击'选择视频'按钮选择要检测的视频文件")
                        print("   - 无红外视频流")
                    elif mode == "camera":
                        print("📡 已切换到摄像头检测模式")
                        print("   - 使用实时摄像头流进行检测")
                        print("   - 包含红外和可见光双路视频")

                else:
                    self.logger.error(f"检测模式切换失败: {mode}")
                    print(f"❌ 检测模式切换失败: {mode}")
                    if mode == "camera":
                        print("🔧 请检查摄像头连接状态")
            else:
                self.logger.warning("系统不支持检测模式切换")
        except Exception as e:
            self.logger.error(f"切换检测模式失败: {e}")
            print(f"❌ 切换检测模式异常: {e}")

    def select_video_file(self, file_path: str):
        """选择视频文件"""
        try:
            if hasattr(self.system, 'set_video_file'):
                success = self.system.set_video_file(file_path)
                if success:
                    self.logger.info(f"视频文件设置成功: {file_path}")
                    print(f"📹 已选择视频文件: {file_path}")
                else:
                    self.logger.error(f"视频文件设置失败: {file_path}")
                    print(f"❌ 视频文件设置失败: {file_path}")
            else:
                self.logger.warning("系统不支持视频文件选择")
        except Exception as e:
            self.logger.error(f"选择视频文件失败: {e}")
            print(f"❌ 选择视频文件异常: {e}")

    def toggle_detection(self, enabled: bool):
        """切换热源检测"""
        try:
            if hasattr(self.system, 'toggle_heat_detection'):
                self.system.toggle_heat_detection(enabled)
                self.logger.info(f"热源检测: {'开启' if enabled else '关闭'}")
            else:
                self.logger.warning("系统不支持热源检测切换")
        except Exception as e:
            self.logger.error(f"切换热源检测失败: {e}")
    
    def toggle_human_detection(self, enabled: bool):
        """切换人体检测"""
        try:
            if hasattr(self.system, 'toggle_human_detection'):
                self.system.toggle_human_detection(enabled)
                self.logger.info(f"人体检测: {'开启' if enabled else '关闭'}")
            else:
                self.logger.warning("系统不支持人体检测切换")
        except Exception as e:
            self.logger.error(f"切换人体检测失败: {e}")
    
    def toggle_thermal_human_detection(self, enabled: bool):
        """切换热成像人体检测"""
        try:
            if hasattr(self.system, 'toggle_thermal_human_detection'):
                self.system.toggle_thermal_human_detection(enabled)
                self.logger.info(f"热成像人体检测: {'开启' if enabled else '关闭'}")
            else:
                self.logger.warning("系统不支持热成像人体检测切换")
        except Exception as e:
            self.logger.error(f"切换热成像人体检测失败: {e}")

    def toggle_fire_smoke_detection(self, enabled: bool):
        """切换火焰烟雾检测"""
        try:
            if hasattr(self.system, 'toggle_fire_smoke_detection'):
                self.system.toggle_fire_smoke_detection(enabled)
                self.logger.info(f"火焰烟雾检测: {'开启' if enabled else '关闭'}")
            elif hasattr(self.system, 'frame_processor') and self.system.frame_processor:
                # 直接通过帧处理器控制
                self.system.frame_processor.set_fire_smoke_detection_enabled(enabled)
                self.system.fire_smoke_detection_enabled = enabled
                self.logger.info(f"火焰烟雾检测: {'开启' if enabled else '关闭'}")
            else:
                self.logger.warning("系统不支持火焰烟雾检测切换")
        except Exception as e:
            self.logger.error(f"切换火焰烟雾检测失败: {e}")
    
    def change_threshold(self, value: float):
        """改变温度阈值"""
        try:
            if hasattr(self.system, 'set_temperature_threshold'):
                # 只在手动模式下允许调整阈值
                if not self.system.is_adaptive_mode():
                    success = self.system.set_temperature_threshold(value)
                    if success:
                        self.logger.info(f"温度阈值设置为: {value:.1f}°C")
                    else:
                        self.logger.warning("温度阈值设置失败")
                else:
                    self.logger.warning("自适应模式下无法手动调整阈值")
            else:
                self.logger.warning("系统不支持温度阈值设置")
        except Exception as e:
            self.logger.error(f"设置温度阈值失败: {e}")

    def change_adaptive_mode(self, is_adaptive: bool):
        """切换自适应模式"""
        try:
            if hasattr(self.system, 'set_adaptive_threshold'):
                success = self.system.set_adaptive_threshold(is_adaptive)
                if success:
                    mode_name = "自适应" if is_adaptive else "手动"
                    self.logger.info(f"阈值模式切换为: {mode_name}")

                    # 更新界面状态
                    if hasattr(self.system, 'qt_adapter') and self.system.qt_adapter:
                        main_window = self.system.qt_adapter.main_window
                        if main_window and hasattr(main_window, 'qt_toolbar'):
                            main_window.qt_toolbar.update_adaptive_mode(is_adaptive)
                else:
                    self.logger.warning("自适应模式切换失败")
            else:
                self.logger.warning("系统不支持自适应模式切换")
        except Exception as e:
            self.logger.error(f"切换自适应模式失败: {e}")
    
    def save_image(self):
        """保存图像"""
        try:
            if hasattr(self.system, 'save_current_frame'):
                filename = self.system.save_current_frame()
                if filename:
                    self.logger.info(f"图像已保存: {filename}")
                else:
                    self.logger.warning("图像保存失败")
            else:
                self.logger.warning("系统不支持图像保存")
        except Exception as e:
            self.logger.error(f"保存图像失败: {e}")
    
    def save_debug_info(self):
        """保存调试信息"""
        try:
            if hasattr(self.system, 'save_debug_info'):
                self.system.save_debug_info()
                self.logger.info("调试信息已保存")
            else:
                self.logger.warning("系统不支持调试信息保存")
        except Exception as e:
            self.logger.error(f"保存调试信息失败: {e}")

    def export_excel_data(self):
        """导出Excel数据 - 同时导出主系统和模块化深度分析数据"""
        try:
            # 导入Excel导出器
            from utils.excel_data_exporter import get_excel_exporter

            # 获取全局Excel导出器
            excel_exporter = get_excel_exporter()

            # 获取当前数据状态
            status = excel_exporter.get_export_status()

            # 计算总记录数（包括已写入文件的数据）
            cached_records = status['total_cached_records']
            database_file = status['database_file']

            # 检查数据库文件是否存在
            from pathlib import Path
            db_path = Path(database_file)
            has_database = db_path.exists()

            exported_files = []

            # 1. 导出主系统数据
            if has_database or cached_records > 0:
                # 执行手动导出（创建数据库副本）
                export_path = excel_exporter.get_current_database_copy()

                if export_path:
                    exported_files.append(("主系统监控数据", export_path))
                    self.logger.info(f"✅ 主系统Excel数据库导出成功: {export_path}")
                else:
                    self.logger.error("❌ 主系统Excel数据导出失败")

            # 2. 导出模块化系统的深度特征分析数据
            modular_export_path = self._export_modular_analysis_data()
            if modular_export_path:
                exported_files.append(("深度特征分析数据", modular_export_path))
                self.logger.info(f"✅ 模块化分析数据导出成功: {modular_export_path}")

            # 显示导出结果
            if exported_files:
                self._show_export_success_message(exported_files, cached_records)
            else:
                self._show_no_data_message()

        except Exception as e:
            self.logger.error(f"导出Excel数据失败: {e}")
            try:
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.critical(None, "导出错误", f"导出Excel数据时发生错误：\n{e}")
            except:
                pass

    def _export_modular_analysis_data(self):
        """导出模块化系统的深度特征分析数据"""
        try:
            # 检查是否有集成火焰烟雾检测器
            if (hasattr(self, 'system') and
                self.system and
                hasattr(self.system, 'fire_smoke_detector') and
                self.system.fire_smoke_detector):

                detector = self.system.fire_smoke_detector

                # 检查是否有数据引擎
                if hasattr(detector, 'data_engine') and detector.data_engine:
                    # 生成带时间戳的导出文件名
                    import time
                    timestamp = time.strftime('%Y%m%d_%H%M%S')
                    backup_path = f"data_exports/深度特征分析_{timestamp}.xlsx"

                    # 导出深度分析数据
                    export_path = detector.data_engine.save_current_data(backup_path)
                    return export_path
                else:
                    self.logger.info("⚠️ 模块化系统数据引擎未启用")
            else:
                self.logger.info("⚠️ 集成火焰烟雾检测器不可用")

            return None

        except Exception as e:
            self.logger.error(f"导出模块化分析数据失败: {e}")
            return None

    def _show_export_success_message(self, exported_files, cached_records):
        """显示导出成功消息"""
        try:
            from PyQt5.QtWidgets import QMessageBox
            from pathlib import Path

            message = f"数据导出成功！共导出 {len(exported_files)} 个文件：\n\n"

            for i, (file_type, file_path) in enumerate(exported_files, 1):
                file_name = Path(file_path).name
                try:
                    file_size = Path(file_path).stat().st_size / 1024  # KB
                    size_info = f" ({file_size:.1f} KB)"
                except:
                    size_info = ""

                message += f"{i}. {file_type}:\n"
                message += f"   文件: {file_name}{size_info}\n"
                message += f"   路径: {file_path}\n\n"

            if len(exported_files) >= 2:
                message += "📊 数据说明:\n"
                message += "• 主系统数据: 实时监控记录，包含9个专业工作表\n"
                message += "• 深度分析数据: 详细特征分析，包含几何、图像、运动特征\n\n"

            if cached_records > 0:
                message += f"缓存记录: {cached_records} 条\n"

            # 使用非模态消息框避免界面卡住
            msg_box = QMessageBox()
            msg_box.setWindowTitle("导出成功")
            msg_box.setText(message)
            msg_box.setModal(False)
            msg_box.show()

        except Exception as e:
            self.logger.error(f"显示导出消息失败: {e}")

    def _show_no_data_message(self):
        """显示无数据消息"""
        try:
            from PyQt5.QtWidgets import QMessageBox
            # 使用非模态消息框避免界面卡住
            msg_box = QMessageBox()
            msg_box.setWindowTitle("无数据")
            msg_box.setText("当前没有可导出的数据。\n\n请先运行系统收集数据后再尝试导出。\n\n系统启动后会自动创建数据库文件并开始记录数据。")
            msg_box.setModal(False)
            msg_box.show()
        except Exception as e:
            self.logger.error(f"显示无数据消息失败: {e}")

    def show_help(self):
        """显示帮助"""
        try:
            if hasattr(self.system, 'show_help'):
                self.system.show_help()
            else:
                self.logger.info("帮助功能暂未实现")
        except Exception as e:
            self.logger.error(f"显示帮助失败: {e}")
    
    def toggle_panel(self, enabled: bool):
        """切换侧边栏"""
        try:
            if hasattr(self.system, 'toggle_sidebar'):
                self.system.toggle_sidebar(enabled)
                self.logger.info(f"侧边栏: {'显示' if enabled else '隐藏'}")
            else:
                self.logger.warning("系统不支持侧边栏切换")
        except Exception as e:
            self.logger.error(f"切换侧边栏失败: {e}")
    
    def quit_application(self):
        """退出应用程序"""
        try:
            if hasattr(self.system, 'quit'):
                self.system.quit()
            else:
                self.logger.info("退出应用程序")
                import sys
                sys.exit(0)
        except Exception as e:
            self.logger.error(f"退出应用程序失败: {e}")
    
    def handle_key_press(self, key: str):
        """处理按键事件"""
        try:
            key_handlers = {
                'h': self.toggle_detection,
                'p': self.toggle_human_detection,
                't': self.toggle_thermal_human_detection,
                's': self.save_image,
                'd': self.save_debug_info,
                'f1': self.show_help,
                'tab': lambda: self.toggle_panel(True),
                'escape': self.quit_application
            }
            
            if key.lower() in key_handlers:
                handler = key_handlers[key.lower()]
                if callable(handler):
                    if key.lower() in ['h', 'p', 't']:
                        # 对于切换类型的按键，需要获取当前状态
                        current_state = self._get_current_state(key.lower())
                        handler(not current_state)
                    else:
                        handler()
                else:
                    handler
            else:
                self.logger.debug(f"未处理的按键: {key}")
                
        except Exception as e:
            self.logger.error(f"处理按键事件失败: {e}")
    
    def _get_current_state(self, key: str) -> bool:
        """获取当前状态"""
        try:
            if key == 'h' and hasattr(self.system, 'heat_detection_enabled'):
                return self.system.heat_detection_enabled
            elif key == 'p' and hasattr(self.system, 'human_detection_enabled'):
                return self.system.human_detection_enabled
            elif key == 't' and hasattr(self.system, 'thermal_human_detection_enabled'):
                return self.system.thermal_human_detection_enabled
            return False
        except Exception:
            return False
    
    def update_system_state(self, **kwargs):
        """更新系统状态"""
        try:
            for key, value in kwargs.items():
                if hasattr(self.system, key):
                    setattr(self.system, key, value)
                    self.logger.debug(f"系统状态更新: {key} = {value}")
        except Exception as e:
            self.logger.error(f"更新系统状态失败: {e}")
    
    def get_system_info(self) -> dict:
        """获取系统信息"""
        try:
            info = {
                'heat_detection_enabled': getattr(self.system, 'heat_detection_enabled', False),
                'human_detection_enabled': getattr(self.system, 'human_detection_enabled', False),
                'thermal_human_detection_enabled': getattr(self.system, 'thermal_human_detection_enabled', False),
                'fire_smoke_detection_enabled': getattr(self.system, 'fire_smoke_detection_enabled', False),
                'current_threshold': getattr(self.system, 'current_threshold', 35.0),
                'adaptive_mode': getattr(self.system, 'adaptive_threshold', True),

            }
            return info
        except Exception as e:
            self.logger.error(f"获取系统信息失败: {e}")
            return {}
    
    def sync_toolbar_state(self, toolbar):
        """同步工具栏状态"""
        try:
            system_info = self.get_system_info()
            
            if hasattr(toolbar, 'update_detection_status'):
                toolbar.update_detection_status(system_info.get('heat_detection_enabled', False))
            
            if hasattr(toolbar, 'update_human_detection_status'):
                toolbar.update_human_detection_status(system_info.get('human_detection_enabled', False))
            
            if hasattr(toolbar, 'update_thermal_human_status'):
                toolbar.update_thermal_human_status(system_info.get('thermal_human_detection_enabled', False))
            
            if hasattr(toolbar, 'update_threshold_value'):
                toolbar.update_threshold_value(system_info.get('current_threshold', 35.0))
            
            if hasattr(toolbar, 'update_adaptive_mode'):
                toolbar.update_adaptive_mode(system_info.get('adaptive_mode', True))

            if hasattr(toolbar, 'update_fire_smoke_detection_status'):
                toolbar.update_fire_smoke_detection_status(system_info.get('fire_smoke_detection_enabled', False))

            self.logger.debug("工具栏状态同步完成")
            
        except Exception as e:
            self.logger.error(f"同步工具栏状态失败: {e}")
