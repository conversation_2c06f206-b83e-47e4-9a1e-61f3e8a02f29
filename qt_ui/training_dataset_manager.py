#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
训练数据集管理器
管理抽取的帧和标注数据，组织成训练数据集
"""

import os
import json
import shutil
import random
from typing import Dict, List, Tuple, Optional
from datetime import datetime
from pathlib import Path
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton, 
                           QLabel, QListWidget, QListWidgetItem, QProgressBar,
                           QMessageBox, QFileDialog, QGroupBox, QSpinBox,
                           QTextEdit, QCheckBox)
from PyQt5.QtCore import Qt, pyqtSignal, QThread, pyqtSlot
from PyQt5.QtGui import QFont
from dataclasses import dataclass


@dataclass
class DatasetSplit:
    """数据集分割配置"""
    train_ratio: float = 0.7
    val_ratio: float = 0.2
    test_ratio: float = 0.1


class TrainingDatasetManager(QWidget):
    """训练数据集管理器"""
    
    # 信号
    dataset_created = pyqtSignal(str)  # 数据集路径
    progress_updated = pyqtSignal(int, int)  # (当前, 总数)
    
    def __init__(self, frame_extractor, parent=None):
        super().__init__(parent)
        self.frame_extractor = frame_extractor
        self.split_config = DatasetSplit()
        
        # 数据源目录（抽帧输出目录）
        self.source_dir = None
        
        # 数据集输出目录
        self.dataset_dir = Path("training_dataset")
        
        self.init_ui()
        self.connect_signals()
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)
        
        # 标题
        title_label = QLabel("📊 训练数据集")
        title_label.setStyleSheet("""
            font-size: 14px;
            font-weight: bold;
            color: #9C27B0;
            background-color: rgba(156, 39, 176, 0.15);
            padding: 8px;
            border-radius: 5px;
            border: 1px solid rgba(156, 39, 176, 0.3);
            margin-bottom: 5px;
        """)
        layout.addWidget(title_label)
        
        # 数据源组
        source_group = QGroupBox("数据源")
        source_group.setStyleSheet("QGroupBox { color: #ffffff; }")
        source_layout = QVBoxLayout(source_group)
        
        # 数据源选择
        source_select_layout = QHBoxLayout()
        self.source_button = QPushButton("选择数据源")
        self.source_button.clicked.connect(self.choose_source_dir)
        source_select_layout.addWidget(self.source_button)
        
        self.auto_source_button = QPushButton("使用抽帧目录")
        self.auto_source_button.clicked.connect(self.use_extractor_dir)
        source_select_layout.addWidget(self.auto_source_button)
        source_layout.addLayout(source_select_layout)
        
        # 数据源状态
        self.source_status_label = QLabel("未选择数据源")
        self.source_status_label.setStyleSheet("color: #aaaaaa; font-size: 10px;")
        self.source_status_label.setWordWrap(True)
        source_layout.addWidget(self.source_status_label)
        
        layout.addWidget(source_group)
        
        # 数据集配置组
        config_group = QGroupBox("数据集配置")
        config_group.setStyleSheet("QGroupBox { color: #ffffff; }")
        config_layout = QVBoxLayout(config_group)
        
        # 分割比例设置
        split_layout = QVBoxLayout()
        
        # 训练集比例
        train_layout = QHBoxLayout()
        train_layout.addWidget(QLabel("训练集:"))
        self.train_spinbox = QSpinBox()
        self.train_spinbox.setRange(50, 90)
        self.train_spinbox.setValue(int(self.split_config.train_ratio * 100))
        self.train_spinbox.valueChanged.connect(self.on_split_changed)
        train_layout.addWidget(self.train_spinbox)
        train_layout.addWidget(QLabel("%"))
        split_layout.addLayout(train_layout)
        
        # 验证集比例
        val_layout = QHBoxLayout()
        val_layout.addWidget(QLabel("验证集:"))
        self.val_spinbox = QSpinBox()
        self.val_spinbox.setRange(10, 40)
        self.val_spinbox.setValue(int(self.split_config.val_ratio * 100))
        self.val_spinbox.valueChanged.connect(self.on_split_changed)
        val_layout.addWidget(self.val_spinbox)
        val_layout.addWidget(QLabel("%"))
        split_layout.addLayout(val_layout)
        
        # 测试集比例（自动计算）
        test_layout = QHBoxLayout()
        test_layout.addWidget(QLabel("测试集:"))
        self.test_label = QLabel("10%")
        self.test_label.setStyleSheet("color: #aaaaaa;")
        test_layout.addWidget(self.test_label)
        split_layout.addLayout(test_layout)
        
        config_layout.addLayout(split_layout)
        
        # 选项设置
        options_layout = QVBoxLayout()
        
        self.shuffle_cb = QCheckBox("随机打乱数据")
        self.shuffle_cb.setChecked(True)
        options_layout.addWidget(self.shuffle_cb)
        
        self.copy_files_cb = QCheckBox("复制文件（否则移动）")
        self.copy_files_cb.setChecked(True)
        options_layout.addWidget(self.copy_files_cb)
        
        self.create_yaml_cb = QCheckBox("创建dataset.yaml")
        self.create_yaml_cb.setChecked(True)
        options_layout.addWidget(self.create_yaml_cb)
        
        config_layout.addLayout(options_layout)
        layout.addWidget(config_group)
        
        # 操作按钮
        button_layout = QHBoxLayout()
        
        self.scan_button = QPushButton("扫描数据")
        self.scan_button.clicked.connect(self.scan_source_data)
        button_layout.addWidget(self.scan_button)
        
        self.create_button = QPushButton("创建数据集")
        self.create_button.clicked.connect(self.create_dataset)
        self.create_button.setEnabled(False)
        button_layout.addWidget(self.create_button)
        
        self.open_button = QPushButton("打开目录")
        self.open_button.clicked.connect(self.open_dataset_dir)
        button_layout.addWidget(self.open_button)
        
        # 设置按钮样式
        for button in [self.source_button, self.auto_source_button, self.scan_button, 
                      self.create_button, self.open_button]:
            button.setStyleSheet("""
                QPushButton {
                    background-color: #404040;
                    border: 1px solid #606060;
                    border-radius: 3px;
                    padding: 4px 8px;
                    font-size: 10px;
                    color: white;
                }
                QPushButton:hover {
                    background-color: #505050;
                }
                QPushButton:pressed {
                    background-color: #303030;
                }
                QPushButton:disabled {
                    background-color: #2a2a2a;
                    color: #666666;
                }
            """)
        
        layout.addLayout(button_layout)
        
        # 数据统计
        stats_layout = QVBoxLayout()
        
        self.stats_label = QLabel("数据统计: 未扫描")
        self.stats_label.setStyleSheet("color: #ffffff; font-size: 11px;")
        stats_layout.addWidget(self.stats_label)
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        stats_layout.addWidget(self.progress_bar)
        
        layout.addLayout(stats_layout)
        
        # 输出目录设置
        output_layout = QHBoxLayout()
        output_layout.addWidget(QLabel("输出目录:"))
        
        self.output_button = QPushButton("选择目录")
        self.output_button.clicked.connect(self.choose_output_dir)
        self.output_button.setStyleSheet("""
            QPushButton {
                background-color: #404040;
                border: 1px solid #606060;
                border-radius: 3px;
                padding: 3px 8px;
                font-size: 9px;
                color: white;
            }
        """)
        output_layout.addWidget(self.output_button)
        layout.addLayout(output_layout)
        
        # 状态信息
        self.status_label = QLabel(f"输出: {self.dataset_dir}")
        self.status_label.setStyleSheet("color: #aaaaaa; font-size: 9px;")
        self.status_label.setWordWrap(True)
        layout.addWidget(self.status_label)
        
        # 添加弹性空间
        layout.addStretch()
    
    def connect_signals(self):
        """连接信号"""
        if self.frame_extractor:
            self.frame_extractor.frame_extracted.connect(self.on_frame_extracted)
    
    def on_split_changed(self):
        """分割比例改变"""
        train_ratio = self.train_spinbox.value() / 100.0
        val_ratio = self.val_spinbox.value() / 100.0
        test_ratio = 1.0 - train_ratio - val_ratio
        
        # 确保比例合理
        if test_ratio < 0.05:
            test_ratio = 0.1
            val_ratio = 1.0 - train_ratio - test_ratio
            self.val_spinbox.setValue(int(val_ratio * 100))
        
        self.split_config.train_ratio = train_ratio
        self.split_config.val_ratio = val_ratio
        self.split_config.test_ratio = test_ratio
        
        self.test_label.setText(f"{int(test_ratio * 100)}%")
    
    def choose_source_dir(self):
        """选择数据源目录"""
        dir_path = QFileDialog.getExistingDirectory(
            self, "选择数据源目录", str(self.source_dir or ".")
        )
        if dir_path:
            self.source_dir = Path(dir_path)
            self.update_source_status()
    
    def use_extractor_dir(self):
        """使用抽帧器的输出目录作为数据源"""
        if self.frame_extractor:
            self.source_dir = self.frame_extractor.output_dir
            self.update_source_status()
        else:
            QMessageBox.warning(self, "警告", "抽帧器不可用")
    
    def update_source_status(self):
        """更新数据源状态"""
        if self.source_dir and self.source_dir.exists():
            self.source_status_label.setText(f"数据源: {self.source_dir}")
            self.scan_button.setEnabled(True)
        else:
            self.source_status_label.setText("数据源目录不存在")
            self.scan_button.setEnabled(False)
    
    def choose_output_dir(self):
        """选择输出目录"""
        dir_path = QFileDialog.getExistingDirectory(
            self, "选择输出目录", str(self.dataset_dir.parent)
        )
        if dir_path:
            self.dataset_dir = Path(dir_path) / "training_dataset"
            self.status_label.setText(f"输出: {self.dataset_dir}")
    
    def scan_source_data(self):
        """扫描数据源"""
        if not self.source_dir or not self.source_dir.exists():
            QMessageBox.warning(self, "警告", "请先选择有效的数据源目录")
            return
        
        try:
            # 扫描图像和标签文件
            image_files = []
            label_files = []
            
            for ext in ['.jpg', '.jpeg', '.png', '.bmp']:
                image_files.extend(list(self.source_dir.glob(f"*{ext}")))
            
            for img_file in image_files:
                label_file = img_file.with_suffix('.txt')
                if label_file.exists():
                    label_files.append(label_file)
            
            # 统计信息
            total_images = len(image_files)
            total_labels = len(label_files)
            matched_pairs = len([f for f in image_files if f.with_suffix('.txt').exists()])
            
            self.stats_label.setText(
                f"图像: {total_images}, 标签: {total_labels}, 匹配: {matched_pairs}"
            )
            
            # 启用创建按钮
            if matched_pairs > 0:
                self.create_button.setEnabled(True)
                print(f"📊 数据扫描完成: {matched_pairs} 个有效样本")
            else:
                self.create_button.setEnabled(False)
                QMessageBox.warning(self, "警告", "没有找到匹配的图像-标签对")
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"扫描数据失败: {e}")
            print(f"❌ 数据扫描失败: {e}")
    
    def on_frame_extracted(self, filepath: str, metadata: dict):
        """处理新抽取的帧"""
        # 自动更新数据源状态
        if self.source_dir == self.frame_extractor.output_dir:
            # 如果使用的是抽帧器目录，自动更新统计
            pass  # 可以在这里实现实时更新

    def create_dataset(self):
        """创建训练数据集"""
        if not self.source_dir or not self.source_dir.exists():
            QMessageBox.warning(self, "警告", "请先选择有效的数据源目录")
            return

        try:
            # 确认操作
            reply = QMessageBox.question(
                self, "确认创建",
                f"将在 {self.dataset_dir} 创建训练数据集，是否继续？",
                QMessageBox.Yes | QMessageBox.No
            )

            if reply != QMessageBox.Yes:
                return

            # 显示进度条
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)

            # 收集所有有效的图像-标签对
            valid_pairs = []
            image_files = []

            for ext in ['.jpg', '.jpeg', '.png', '.bmp']:
                image_files.extend(list(self.source_dir.glob(f"*{ext}")))

            for img_file in image_files:
                label_file = img_file.with_suffix('.txt')
                if label_file.exists():
                    valid_pairs.append((img_file, label_file))

            if not valid_pairs:
                QMessageBox.warning(self, "警告", "没有找到有效的图像-标签对")
                self.progress_bar.setVisible(False)
                return

            # 随机打乱
            if self.shuffle_cb.isChecked():
                random.shuffle(valid_pairs)

            # 计算分割点
            total_count = len(valid_pairs)
            train_count = int(total_count * self.split_config.train_ratio)
            val_count = int(total_count * self.split_config.val_ratio)
            test_count = total_count - train_count - val_count

            # 分割数据
            train_pairs = valid_pairs[:train_count]
            val_pairs = valid_pairs[train_count:train_count + val_count]
            test_pairs = valid_pairs[train_count + val_count:]

            print(f"📊 数据分割: 训练{len(train_pairs)}, 验证{len(val_pairs)}, 测试{len(test_pairs)}")

            # 创建目录结构
            self.dataset_dir.mkdir(parents=True, exist_ok=True)

            splits = {
                'train': train_pairs,
                'val': val_pairs,
                'test': test_pairs
            }

            total_files = sum(len(pairs) * 2 for pairs in splits.values())  # 图像+标签
            processed_files = 0

            for split_name, pairs in splits.items():
                if not pairs:  # 跳过空分割
                    continue

                # 创建分割目录
                split_dir = self.dataset_dir / split_name
                images_dir = split_dir / "images"
                labels_dir = split_dir / "labels"

                images_dir.mkdir(parents=True, exist_ok=True)
                labels_dir.mkdir(parents=True, exist_ok=True)

                # 复制或移动文件
                for img_file, label_file in pairs:
                    # 处理图像文件
                    dst_img = images_dir / img_file.name
                    if self.copy_files_cb.isChecked():
                        shutil.copy2(img_file, dst_img)
                    else:
                        shutil.move(str(img_file), str(dst_img))

                    # 处理标签文件
                    dst_label = labels_dir / label_file.name
                    if self.copy_files_cb.isChecked():
                        shutil.copy2(label_file, dst_label)
                    else:
                        shutil.move(str(label_file), str(dst_label))

                    # 复制元数据文件（如果存在）
                    metadata_file = img_file.with_suffix('.json')
                    if metadata_file.exists():
                        dst_metadata = images_dir / metadata_file.name
                        if self.copy_files_cb.isChecked():
                            shutil.copy2(metadata_file, dst_metadata)
                        else:
                            shutil.move(str(metadata_file), str(dst_metadata))

                    processed_files += 2
                    progress = int((processed_files / total_files) * 100)
                    self.progress_bar.setValue(progress)
                    self.progress_updated.emit(processed_files, total_files)

            # 创建dataset.yaml文件
            if self.create_yaml_cb.isChecked():
                self.create_dataset_yaml()

            # 创建数据集信息文件
            self.create_dataset_info(splits)

            # 完成
            self.progress_bar.setValue(100)
            self.progress_bar.setVisible(False)

            QMessageBox.information(
                self, "成功",
                f"数据集创建完成！\n路径: {self.dataset_dir}\n"
                f"训练: {len(train_pairs)}, 验证: {len(val_pairs)}, 测试: {len(test_pairs)}"
            )

            self.dataset_created.emit(str(self.dataset_dir))
            print(f"✅ 训练数据集创建完成: {self.dataset_dir}")

        except Exception as e:
            self.progress_bar.setVisible(False)
            QMessageBox.critical(self, "错误", f"创建数据集失败: {e}")
            print(f"❌ 创建数据集失败: {e}")

    def create_dataset_yaml(self):
        """创建dataset.yaml配置文件"""
        try:
            yaml_content = f"""# 配电箱检测数据集配置
# 生成时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

# 数据集路径
path: {self.dataset_dir.absolute()}
train: train/images
val: val/images
test: test/images

# 类别数量
nc: 1

# 类别名称
names:
  0: electrical_box

# 数据集信息
dataset_info:
  name: "Electrical Box Detection Dataset"
  description: "从视频抽帧生成的配电箱检测数据集"
  created: "{datetime.now().isoformat()}"
  splits:
    train_ratio: {self.split_config.train_ratio}
    val_ratio: {self.split_config.val_ratio}
    test_ratio: {self.split_config.test_ratio}
"""

            yaml_file = self.dataset_dir / "dataset.yaml"
            with open(yaml_file, 'w', encoding='utf-8') as f:
                f.write(yaml_content)

            print(f"📄 dataset.yaml 已创建: {yaml_file}")

        except Exception as e:
            print(f"❌ 创建dataset.yaml失败: {e}")

    def create_dataset_info(self, splits: Dict):
        """创建数据集信息文件"""
        try:
            info = {
                'dataset_name': 'Electrical Box Detection Dataset',
                'created_time': datetime.now().isoformat(),
                'source_directory': str(self.source_dir),
                'total_samples': sum(len(pairs) for pairs in splits.values()),
                'splits': {
                    name: {
                        'count': len(pairs),
                        'ratio': len(pairs) / sum(len(p) for p in splits.values())
                    } for name, pairs in splits.items()
                },
                'configuration': {
                    'shuffle_data': self.shuffle_cb.isChecked(),
                    'copy_files': self.copy_files_cb.isChecked(),
                    'create_yaml': self.create_yaml_cb.isChecked()
                },
                'class_mapping': {
                    0: 'electrical_box'
                }
            }

            info_file = self.dataset_dir / "dataset_info.json"
            with open(info_file, 'w', encoding='utf-8') as f:
                json.dump(info, f, ensure_ascii=False, indent=2)

            print(f"📋 数据集信息已保存: {info_file}")

        except Exception as e:
            print(f"❌ 创建数据集信息失败: {e}")

    def open_dataset_dir(self):
        """打开数据集目录"""
        if self.dataset_dir.exists():
            os.startfile(str(self.dataset_dir))  # Windows
        else:
            QMessageBox.information(self, "提示", "数据集目录不存在，请先创建数据集")

    def get_dataset_stats(self) -> Dict:
        """获取数据集统计信息"""
        if not self.dataset_dir.exists():
            return {'exists': False}

        stats = {'exists': True, 'splits': {}}

        for split in ['train', 'val', 'test']:
            split_dir = self.dataset_dir / split
            if split_dir.exists():
                images_dir = split_dir / "images"
                labels_dir = split_dir / "labels"

                image_count = len(list(images_dir.glob("*"))) if images_dir.exists() else 0
                label_count = len(list(labels_dir.glob("*.txt"))) if labels_dir.exists() else 0

                stats['splits'][split] = {
                    'images': image_count,
                    'labels': label_count
                }

        return stats
