#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频抽帧器
从可见光画面抽取帧并保留手动标注，用于模型训练
"""

import cv2
import os
import json
import time
import numpy as np
from typing import Dict, List, Tuple, Optional
from datetime import datetime
from pathlib import Path
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton, 
                           QLabel, QSpinBox, QCheckBox, QProgressBar,
                           QFileDialog, QMessageBox, QComboBox, QLineEdit,
                           QTextEdit, QGroupBox)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer, QThread, pyqtSlot
from PyQt5.QtGui import QFont
from dataclasses import dataclass


@dataclass
class FrameExtractionConfig:
    """抽帧配置"""
    interval_seconds: float = 5.0  # 抽帧间隔（秒）
    min_quality_score: float = 0.7  # 最小质量分数
    max_frames_per_session: int = 100  # 每次会话最大抽帧数
    output_format: str = "jpg"  # 输出格式
    include_annotations: bool = True  # 是否包含标注
    auto_filter_duplicates: bool = True  # 自动过滤重复帧
    save_yolo_labels: bool = True  # 保存YOLO格式标签


class FrameQualityAnalyzer:
    """帧质量分析器"""
    
    def __init__(self):
        """初始化质量分析器"""
        self.last_frame = None
        self.similarity_threshold = 0.95  # 相似度阈值
    
    def analyze_quality(self, frame: np.ndarray) -> float:
        """
        分析帧质量
        
        Args:
            frame: 输入帧
            
        Returns:
            质量分数 (0-1)
        """
        if frame is None:
            return 0.0
        
        # 转换为灰度图
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        
        # 计算清晰度（拉普拉斯方差）
        laplacian_var = cv2.Laplacian(gray, cv2.CV_64F).var()
        sharpness_score = min(laplacian_var / 1000.0, 1.0)  # 归一化
        
        # 计算亮度分布
        hist = cv2.calcHist([gray], [0], None, [256], [0, 256])
        hist_norm = hist / hist.sum()
        brightness_score = 1.0 - abs(0.5 - np.mean(gray) / 255.0)  # 亮度适中得分更高
        
        # 计算对比度
        contrast_score = min(np.std(gray) / 128.0, 1.0)  # 归一化
        
        # 综合质量分数
        quality_score = (sharpness_score * 0.5 + 
                        brightness_score * 0.3 + 
                        contrast_score * 0.2)
        
        return min(quality_score, 1.0)
    
    def is_similar_to_last(self, frame: np.ndarray) -> bool:
        """检查是否与上一帧相似"""
        if self.last_frame is None:
            self.last_frame = frame.copy()
            return False
        
        # 计算结构相似性
        gray1 = cv2.cvtColor(self.last_frame, cv2.COLOR_BGR2GRAY)
        gray2 = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        
        # 调整尺寸以提高计算速度
        h, w = gray1.shape
        if h > 200 or w > 200:
            scale = min(200/h, 200/w)
            new_h, new_w = int(h*scale), int(w*scale)
            gray1 = cv2.resize(gray1, (new_w, new_h))
            gray2 = cv2.resize(gray2, (new_w, new_h))
        
        # 计算直方图相似度
        hist1 = cv2.calcHist([gray1], [0], None, [64], [0, 256])
        hist2 = cv2.calcHist([gray2], [0], None, [64], [0, 256])
        
        similarity = cv2.compareHist(hist1, hist2, cv2.HISTCMP_CORREL)
        
        self.last_frame = frame.copy()
        return similarity > self.similarity_threshold


class VideoFrameExtractor(QWidget):
    """视频抽帧控制面板"""
    
    # 信号
    frame_extracted = pyqtSignal(str, dict)  # (文件路径, 元数据)
    extraction_started = pyqtSignal()
    extraction_stopped = pyqtSignal()
    progress_updated = pyqtSignal(int, int)  # (当前, 总数)
    
    def __init__(self, manual_annotation_widget, parent=None):
        super().__init__(parent)
        self.manual_annotation_widget = manual_annotation_widget
        self.config = FrameExtractionConfig()
        self.quality_analyzer = FrameQualityAnalyzer()
        
        # 抽帧状态
        self.is_extracting = False
        self.extracted_count = 0
        self.last_extract_time = 0
        self.current_frame = None
        
        # 输出目录
        self.output_dir = Path("extracted_frames")
        self.output_dir.mkdir(exist_ok=True)
        
        # 定时器
        self.extract_timer = QTimer()
        self.extract_timer.timeout.connect(self.try_extract_frame)
        
        self.init_ui()
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)
        
        # 标题
        title_label = QLabel("🎬 视频抽帧")
        title_label.setStyleSheet("""
            font-size: 14px;
            font-weight: bold;
            color: #FF9800;
            background-color: rgba(255, 152, 0, 0.15);
            padding: 8px;
            border-radius: 5px;
            border: 1px solid rgba(255, 152, 0, 0.3);
            margin-bottom: 5px;
        """)
        layout.addWidget(title_label)
        
        # 抽帧控制组
        control_group = QGroupBox("抽帧控制")
        control_group.setStyleSheet("QGroupBox { color: #ffffff; }")
        control_layout = QVBoxLayout(control_group)
        
        # 抽帧间隔设置
        interval_layout = QHBoxLayout()
        interval_layout.addWidget(QLabel("间隔(秒):"))
        self.interval_spinbox = QSpinBox()
        self.interval_spinbox.setRange(1, 300)
        self.interval_spinbox.setValue(int(self.config.interval_seconds))
        self.interval_spinbox.valueChanged.connect(self.on_interval_changed)
        interval_layout.addWidget(self.interval_spinbox)
        control_layout.addLayout(interval_layout)
        
        # 质量阈值设置
        quality_layout = QHBoxLayout()
        quality_layout.addWidget(QLabel("质量阈值:"))
        self.quality_spinbox = QSpinBox()
        self.quality_spinbox.setRange(10, 100)
        self.quality_spinbox.setValue(int(self.config.min_quality_score * 100))
        self.quality_spinbox.valueChanged.connect(self.on_quality_changed)
        quality_layout.addWidget(self.quality_spinbox)
        quality_layout.addWidget(QLabel("%"))
        control_layout.addLayout(quality_layout)
        
        # 选项设置
        options_layout = QVBoxLayout()
        
        self.include_annotations_cb = QCheckBox("包含手动标注")
        self.include_annotations_cb.setChecked(self.config.include_annotations)
        self.include_annotations_cb.toggled.connect(self.on_options_changed)
        options_layout.addWidget(self.include_annotations_cb)
        
        self.filter_duplicates_cb = QCheckBox("过滤重复帧")
        self.filter_duplicates_cb.setChecked(self.config.auto_filter_duplicates)
        self.filter_duplicates_cb.toggled.connect(self.on_options_changed)
        options_layout.addWidget(self.filter_duplicates_cb)
        
        self.save_yolo_cb = QCheckBox("保存YOLO标签")
        self.save_yolo_cb.setChecked(self.config.save_yolo_labels)
        self.save_yolo_cb.toggled.connect(self.on_options_changed)
        options_layout.addWidget(self.save_yolo_cb)
        
        control_layout.addLayout(options_layout)
        layout.addWidget(control_group)
        
        # 操作按钮
        button_layout = QHBoxLayout()
        
        self.start_button = QPushButton("开始抽帧")
        self.start_button.clicked.connect(self.start_extraction)
        button_layout.addWidget(self.start_button)
        
        self.stop_button = QPushButton("停止抽帧")
        self.stop_button.clicked.connect(self.stop_extraction)
        self.stop_button.setEnabled(False)
        button_layout.addWidget(self.stop_button)
        
        self.manual_button = QPushButton("手动抽帧")
        self.manual_button.clicked.connect(self.manual_extract)
        button_layout.addWidget(self.manual_button)
        
        # 设置按钮样式
        for button in [self.start_button, self.stop_button, self.manual_button]:
            button.setStyleSheet("""
                QPushButton {
                    background-color: #404040;
                    border: 1px solid #606060;
                    border-radius: 3px;
                    padding: 5px 10px;
                    font-size: 10px;
                    color: white;
                }
                QPushButton:hover {
                    background-color: #505050;
                }
                QPushButton:pressed {
                    background-color: #303030;
                }
                QPushButton:disabled {
                    background-color: #2a2a2a;
                    color: #666666;
                }
            """)
        
        layout.addLayout(button_layout)
        
        # 进度显示
        progress_layout = QVBoxLayout()
        
        self.progress_label = QLabel("已抽取: 0 帧")
        self.progress_label.setStyleSheet("color: #ffffff; font-size: 11px;")
        progress_layout.addWidget(self.progress_label)
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        progress_layout.addWidget(self.progress_bar)
        
        layout.addLayout(progress_layout)
        
        # 输出目录设置
        output_layout = QHBoxLayout()
        output_layout.addWidget(QLabel("输出目录:"))
        
        self.output_button = QPushButton("选择目录")
        self.output_button.clicked.connect(self.choose_output_dir)
        self.output_button.setStyleSheet("""
            QPushButton {
                background-color: #404040;
                border: 1px solid #606060;
                border-radius: 3px;
                padding: 3px 8px;
                font-size: 9px;
                color: white;
            }
        """)
        output_layout.addWidget(self.output_button)
        layout.addLayout(output_layout)
        
        # 状态信息
        self.status_label = QLabel(f"输出: {self.output_dir}")
        self.status_label.setStyleSheet("color: #aaaaaa; font-size: 9px;")
        self.status_label.setWordWrap(True)
        layout.addWidget(self.status_label)
        
        # 添加弹性空间
        layout.addStretch()
    
    def on_interval_changed(self, value: int):
        """间隔时间改变"""
        self.config.interval_seconds = float(value)
    
    def on_quality_changed(self, value: int):
        """质量阈值改变"""
        self.config.min_quality_score = value / 100.0
    
    def on_options_changed(self):
        """选项改变"""
        self.config.include_annotations = self.include_annotations_cb.isChecked()
        self.config.auto_filter_duplicates = self.filter_duplicates_cb.isChecked()
        self.config.save_yolo_labels = self.save_yolo_cb.isChecked()
    
    def choose_output_dir(self):
        """选择输出目录"""
        dir_path = QFileDialog.getExistingDirectory(
            self, "选择输出目录", str(self.output_dir)
        )
        if dir_path:
            self.output_dir = Path(dir_path)
            self.output_dir.mkdir(exist_ok=True)
            self.status_label.setText(f"输出: {self.output_dir}")
    
    def start_extraction(self):
        """开始自动抽帧"""
        if self.is_extracting:
            return
        
        self.is_extracting = True
        self.extracted_count = 0
        self.last_extract_time = time.time()
        
        # 更新UI状态
        self.start_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        
        # 启动定时器
        self.extract_timer.start(1000)  # 每秒检查一次
        
        self.extraction_started.emit()
        print(f"🎬 开始自动抽帧 - 间隔: {self.config.interval_seconds}秒")
    
    def stop_extraction(self):
        """停止自动抽帧"""
        if not self.is_extracting:
            return
        
        self.is_extracting = False
        self.extract_timer.stop()
        
        # 更新UI状态
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        
        self.extraction_stopped.emit()
        print(f"⏹️ 停止抽帧 - 共抽取 {self.extracted_count} 帧")
    
    def manual_extract(self):
        """手动抽帧"""
        if self.current_frame is not None:
            self.extract_current_frame(manual=True)
        else:
            print("⚠️ 当前没有可用的帧")
    
    def set_current_frame(self, frame: np.ndarray):
        """设置当前帧"""
        self.current_frame = frame.copy() if frame is not None else None
    
    def try_extract_frame(self):
        """尝试抽取帧（定时器调用）"""
        if not self.is_extracting or self.current_frame is None:
            return

        current_time = time.time()
        if current_time - self.last_extract_time >= self.config.interval_seconds:
            self.extract_current_frame()
            self.last_extract_time = current_time

    def extract_current_frame(self, manual: bool = False):
        """抽取当前帧"""
        if self.current_frame is None:
            return

        try:
            # 质量检查
            quality_score = self.quality_analyzer.analyze_quality(self.current_frame)
            if quality_score < self.config.min_quality_score and not manual:
                print(f"⚠️ 帧质量不足: {quality_score:.2f} < {self.config.min_quality_score:.2f}")
                return

            # 重复检查
            if self.config.auto_filter_duplicates and not manual:
                if self.quality_analyzer.is_similar_to_last(self.current_frame):
                    print("⚠️ 跳过重复帧")
                    return

            # 准备保存的帧
            save_frame = self.current_frame.copy()

            # 添加手动标注
            annotations = []
            if self.config.include_annotations:
                visible_annotations = self.manual_annotation_widget.get_visible_annotations()
                if visible_annotations:
                    # 渲染标注到图像上
                    from .manual_annotation_handler import ManualAnnotationRenderer
                    renderer = ManualAnnotationRenderer()
                    save_frame = renderer.render_annotations(save_frame, visible_annotations)
                    annotations = visible_annotations

            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]  # 毫秒精度
            prefix = "manual" if manual else "auto"
            filename = f"{prefix}_{timestamp}.{self.config.output_format}"
            filepath = self.output_dir / filename

            # 保存图像
            success = cv2.imwrite(str(filepath), save_frame)
            if not success:
                print(f"❌ 保存图像失败: {filepath}")
                return

            # 保存YOLO格式标签
            if self.config.save_yolo_labels and annotations:
                self.save_yolo_labels(filepath, annotations, save_frame.shape)

            # 保存元数据
            metadata = {
                'timestamp': timestamp,
                'quality_score': quality_score,
                'manual': manual,
                'annotations_count': len(annotations),
                'image_size': save_frame.shape[:2],
                'annotations': [
                    {
                        'id': ann.id,
                        'label': ann.label,
                        'bbox': ann.bbox,
                        'confidence': ann.confidence
                    } for ann in annotations
                ]
            }

            metadata_file = filepath.with_suffix('.json')
            with open(metadata_file, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, ensure_ascii=False, indent=2)

            # 更新计数
            self.extracted_count += 1
            self.update_progress()

            # 发送信号
            self.frame_extracted.emit(str(filepath), metadata)

            extract_type = "手动" if manual else "自动"
            print(f"📸 {extract_type}抽帧成功: {filename} (质量: {quality_score:.2f}, 标注: {len(annotations)})")

        except Exception as e:
            print(f"❌ 抽帧失败: {e}")

    def save_yolo_labels(self, image_path: Path, annotations: List, image_shape: Tuple[int, int, int]):
        """保存YOLO格式标签文件"""
        try:
            label_file = image_path.with_suffix('.txt')
            height, width = image_shape[:2]

            with open(label_file, 'w', encoding='utf-8') as f:
                for annotation in annotations:
                    x, y, w, h = annotation.bbox

                    # 转换为YOLO格式 (center_x, center_y, width, height) 相对坐标
                    center_x = (x + w / 2) / width
                    center_y = (y + h / 2) / height
                    rel_width = w / width
                    rel_height = h / height

                    # 假设所有手动标注都是类别0（配电箱）
                    class_id = 0

                    f.write(f"{class_id} {center_x:.6f} {center_y:.6f} {rel_width:.6f} {rel_height:.6f}\n")

            print(f"💾 YOLO标签已保存: {label_file}")

        except Exception as e:
            print(f"❌ 保存YOLO标签失败: {e}")

    def update_progress(self):
        """更新进度显示"""
        self.progress_label.setText(f"已抽取: {self.extracted_count} 帧")

        if self.extracted_count > 0:
            self.progress_updated.emit(self.extracted_count, self.config.max_frames_per_session)

    def get_extraction_stats(self) -> Dict:
        """获取抽帧统计信息"""
        return {
            'total_extracted': self.extracted_count,
            'is_extracting': self.is_extracting,
            'output_dir': str(self.output_dir),
            'config': {
                'interval': self.config.interval_seconds,
                'quality_threshold': self.config.min_quality_score,
                'include_annotations': self.config.include_annotations,
                'save_yolo_labels': self.config.save_yolo_labels
            }
        }
