#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速演示脚本
展示PC端验证策略的核心功能
"""

import sys
import os
import time

# 强制使用模拟模式
os.environ['FORCE_MOCK_AI'] = '1'

from platform_detector import print_platform_info, get_ai_worker_instance
from utils.logger import get_logger

logger = get_logger("QuickDemo")


def demo_basic_functionality():
    """演示基本功能"""
    print("\n🎯 演示1: 基本功能测试")
    print("-" * 40)
    
    # 获取AI Worker实例
    ai_worker = get_ai_worker_instance()
    
    # 初始化检测器
    ai_worker.initialize_detectors(
        human_detector="MockHumanDetector",
        fire_smoke_detector="MockFireSmokeDetector",
        heat_detector="MockHeatDetector"
    )
    
    print("✅ AI Worker初始化完成")
    
    # 创建测试图像
    import numpy as np
    test_frame = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
    
    # 启动工作线程
    ai_worker.start()
    print("✅ 工作线程已启动")
    
    # 提交测试任务
    success1 = ai_worker.add_human_detection_task(test_frame)
    success2 = ai_worker.add_fire_smoke_detection_task(test_frame)
    success3 = ai_worker.add_heat_detection_task(test_frame)
    
    print(f"✅ 任务提交结果: 人体={success1}, 火焰={success2}, 热源={success3}")
    
    # 等待处理
    time.sleep(2)
    
    # 获取统计信息
    stats = ai_worker.get_statistics()
    print(f"📊 处理统计: 总任务={stats['total_tasks_processed']}, 队列={stats['queue_size']}")
    
    # 清理
    ai_worker.stop()
    ai_worker.wait(3000)
    print("✅ 工作线程已停止")


def demo_interface_compatibility():
    """演示接口兼容性"""
    print("\n🔧 演示2: 接口兼容性验证")
    print("-" * 40)
    
    from platform_detector import validate_ai_worker_interface
    
    ai_worker = get_ai_worker_instance()
    
    # 验证接口完整性
    is_valid = validate_ai_worker_interface(ai_worker)
    
    if is_valid:
        print("✅ 接口验证通过 - 与真实AI Worker完全兼容")
    else:
        print("❌ 接口验证失败")
    
    # 列出所有可用方法
    methods = [method for method in dir(ai_worker) if not method.startswith('_')]
    print(f"📋 可用方法数量: {len(methods)}")
    
    # 清理
    ai_worker.cleanup()


def demo_error_handling():
    """演示错误处理"""
    print("\n⚠️ 演示3: 错误处理机制")
    print("-" * 40)
    
    from PyQt5.QtWidgets import QApplication
    from PyQt5.QtCore import QObject, pyqtSignal
    
    # 创建Qt应用（如果不存在）
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    class ErrorHandler(QObject):
        def __init__(self):
            super().__init__()
            self.error_count = 0
        
        def on_error(self, task_id, error_message):
            self.error_count += 1
            print(f"🚨 捕获错误 #{self.error_count}: [{task_id}] {error_message}")
    
    ai_worker = get_ai_worker_instance()
    error_handler = ErrorHandler()
    
    # 连接错误信号
    ai_worker.processing_error.connect(error_handler.on_error)
    
    # 初始化并启动
    ai_worker.initialize_detectors(human_detector="MockHumanDetector")
    ai_worker.start()
    
    # 提交多个任务来触发可能的错误
    import numpy as np
    test_frame = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
    
    for i in range(10):
        ai_worker.add_human_detection_task(test_frame)
    
    print("✅ 已提交10个任务，等待处理...")
    
    # 等待处理完成
    time.sleep(3)
    
    print(f"📊 错误统计: 共捕获 {error_handler.error_count} 个错误")
    
    # 清理
    ai_worker.stop()
    ai_worker.wait(3000)


def demo_performance_monitoring():
    """演示性能监控"""
    print("\n📈 演示4: 性能监控")
    print("-" * 40)
    
    ai_worker = get_ai_worker_instance()
    ai_worker.initialize_detectors(
        human_detector="MockHumanDetector",
        fire_smoke_detector="MockFireSmokeDetector",
        heat_detector="MockHeatDetector"
    )
    
    ai_worker.start()
    
    import numpy as np
    test_frame = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
    
    # 提交大量任务进行性能测试
    start_time = time.time()
    
    for i in range(20):
        ai_worker.add_human_detection_task(test_frame)
        if i % 5 == 0:
            ai_worker.add_fire_smoke_detection_task(test_frame)
        if i % 7 == 0:
            ai_worker.add_heat_detection_task(test_frame)
    
    submission_time = time.time() - start_time
    print(f"⏱️ 任务提交耗时: {submission_time:.3f}秒")
    
    # 等待处理完成
    print("⏳ 等待任务处理完成...")
    
    while ai_worker.get_queue_size() > 0:
        time.sleep(0.1)
    
    total_time = time.time() - start_time
    print(f"⏱️ 总处理耗时: {total_time:.3f}秒")
    
    # 获取详细统计
    stats = ai_worker.get_statistics()
    print(f"📊 性能统计:")
    print(f"   总任务数: {stats['total_tasks_processed']}")
    print(f"   人体检测: {stats['human_detection_count']}")
    print(f"   火焰检测: {stats['fire_smoke_detection_count']}")
    print(f"   热源检测: {stats['heat_detection_count']}")
    print(f"   平均耗时: {stats['average_processing_time']:.3f}秒")
    print(f"   错误数量: {stats['error_count']}")
    
    # 清理
    ai_worker.stop()
    ai_worker.wait(3000)


def main():
    """主函数"""
    print("🚀 PC端验证策略快速演示")
    print("=" * 50)
    
    # 显示平台信息
    print_platform_info()
    
    try:
        # 运行演示
        demo_basic_functionality()
        demo_interface_compatibility()
        demo_error_handling()
        demo_performance_monitoring()
        
        print("\n🎉 所有演示完成！")
        print("\n💡 总结:")
        print("   ✅ 模拟AI Worker功能正常")
        print("   ✅ 接口完全兼容真实版本")
        print("   ✅ 错误处理机制有效")
        print("   ✅ 性能监控功能完整")
        print("\n🎯 您现在可以在PC端进行完整的逻辑验证！")
        
    except Exception as e:
        logger.error(f"演示过程中发生错误: {e}")
        print(f"\n❌ 演示失败: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
