# Hikvision Dual-Spectrum Thermal Imaging System - Dependencies
# Core GUI Framework
PyQt5>=5.15.0

# Computer Vision and Image Processing
opencv-python>=4.5.0
numpy>=1.21.0
Pillow>=8.0.0

# Deep Learning and YOLO Models
ultralytics>=8.0.0
torch>=1.9.0
torchvision>=0.10.0
dill>=0.3.4

# GPU Detection and Configuration
# PyTorch with CUDA support (for GPU detection)
# Note: For GPU systems, install PyTorch with CUDA:
# pip install torch torchvision --index-url https://download.pytorch.org/whl/cu118



# HTTP Requests and Network Communication
requests>=2.25.0

# Scientific Computing (for trend analysis)
scipy>=1.7.0

# System Tools and Hardware Detection
psutil>=5.8.0
# platform  # Built-in Python module for system info - no need to install
# re  # Built-in Python module for regex operations - no need to install



# Environment Variables Management
python-dotenv>=0.19.0

# Excel Data Export
pandas>=1.3.0
openpyxl>=3.0.0

# Additional dependencies for Linux compatibility
PyYAML>=6.0
matplotlib>=3.5.0

# Image processing and analysis (for fire/smoke detection features)
scikit-image>=0.19.0

# Packaging tool for creating exe (Windows only, optional for Linux)
# pyinstaller>=5.0.0  # 注释掉，Linux部署通常不需要打包成exe

# GPU Configuration and Detection Dependencies
# =====================================================
# All dependencies for GPU auto-detection are already included above:
# - torch: GPU detection and CUDA availability check
# - platform: System information detection (built-in)
# - re: Regular expressions for config file modification (built-in)
# - os: File operations (built-in)
# - sys: System-specific parameters (built-in)

# GPU Installation Notes:
# =====================================================
# For CPU-only systems (default):
#   pip install torch torchvision --index-url https://download.pytorch.org/whl/cpu
#
# For GPU systems with CUDA 11.8:
#   pip install torch torchvision --index-url https://download.pytorch.org/whl/cu118
#
# For GPU systems with CUDA 12.1:
#   pip install torch torchvision --index-url https://download.pytorch.org/whl/cu121
#
# Check CUDA version: nvidia-smi
# Check PyTorch CUDA support: python -c "import torch; print(torch.cuda.is_available())"

# Hikvision SDK Related (if needed)
# Note: Hikvision SDK needs to be installed separately, not available in PyPI
