# 海康威视双光谱热成像监控系统 - 目标设备依赖文件
# 适用于瑞芯微RK3588等ARM64 Linux设备
# 版本: 1.0
# 更新日期: 2025-08-03

# ==========================================
# 核心GUI框架
# ==========================================
PyQt5>=5.15.0,<6.0.0
PyQt5-Qt5>=5.15.0
PyQt5-sip>=12.8.0

# ==========================================
# 计算机视觉和图像处理
# ==========================================
opencv-python>=4.5.0,<5.0.0
numpy>=1.22.4,<2.0.0
Pillow>=8.0.0,<11.0.0

# ==========================================
# NPU推理引擎 (目标设备专用)
# ==========================================
# 注意: rknnlite需要从瑞芯微官方获取，通常预装在设备上
# 如果未安装，请联系设备供应商或从以下渠道获取:
# - 瑞芯微官方SDK
# - 设备厂商提供的软件包
# rknnlite>=1.4.0

# ==========================================
# 数据处理和分析
# ==========================================
pandas>=1.3.0,<3.0.0
openpyxl>=3.0.0,<4.0.0
xlsxwriter>=3.0.0,<4.0.0

# ==========================================
# 网络和HTTP请求
# ==========================================
requests>=2.25.0,<3.0.0
urllib3>=1.26.0,<3.0.0

# ==========================================
# 配置文件处理
# ==========================================
PyYAML>=5.4.0,<7.0.0
configparser>=5.0.0

# ==========================================
# 数学和科学计算
# ==========================================
scipy>=1.7.0,<2.0.0
scikit-learn>=1.0.0,<2.0.0

# ==========================================
# 时间和日期处理
# ==========================================
python-dateutil>=2.8.0,<3.0.0
pytz>=2021.1

# ==========================================
# 系统和路径处理
# ==========================================
pathlib2>=2.3.0; python_version<"3.4"
psutil>=5.8.0,<6.0.0

# ==========================================
# 日志和调试
# ==========================================
colorlog>=6.0.0,<7.0.0

# ==========================================
# 数据序列化
# ==========================================
pickle-mixin>=1.0.0

# ==========================================
# 线程和并发
# ==========================================
threading2>=0.1.0; python_version<"3.0"

# ==========================================
# 类型检查和验证
# ==========================================
typing-extensions>=3.10.0; python_version<"3.8"

# ==========================================
# 可选依赖 (用于增强功能)
# ==========================================
# 如果需要更好的图像处理性能
opencv-contrib-python>=4.5.0,<5.0.0

# 如果需要更多的数据分析功能
matplotlib>=3.3.0,<4.0.0
seaborn>=0.11.0,<1.0.0

# 如果需要更好的配置管理
python-dotenv>=0.19.0,<2.0.0

# 如果需要更好的日志管理
loguru>=0.6.0,<1.0.0

# ==========================================
# 系统级依赖说明
# ==========================================
# 以下是系统级依赖，需要通过系统包管理器安装:
#
# Ubuntu/Debian:
# sudo apt-get update
# sudo apt-get install -y \
#     python3-dev \
#     python3-pip \
#     libopencv-dev \
#     libqt5gui5 \
#     libqt5core5a \
#     libqt5widgets5 \
#     qt5-qmake \
#     qtbase5-dev \
#     libglib2.0-0 \
#     libsm6 \
#     libxext6 \
#     libxrender-dev \
#     libgl1-mesa-glx \
#     libglib2.0-0 \
#     libfontconfig1 \
#     libxrender1 \
#     libxtst6 \
#     libxi6 \
#     libxrandr2 \
#     libasound2-dev \
#     libpulse-dev
#
# CentOS/RHEL/Rocky Linux:
# sudo yum install -y \
#     python3-devel \
#     python3-pip \
#     opencv-devel \
#     qt5-qtbase-devel \
#     qt5-qtwidgets \
#     glib2-devel \
#     libSM-devel \
#     libXext-devel \
#     libXrender-devel \
#     mesa-libGL-devel \
#     fontconfig-devel \
#     libXtst \
#     libXi \
#     libXrandr \
#     alsa-lib-devel \
#     pulseaudio-libs-devel

# ==========================================
# 安装说明
# ==========================================
# 1. 确保Python版本 >= 3.8
# 2. 建议使用虚拟环境:
#    python3 -m venv thermal_camera_env
#    source thermal_camera_env/bin/activate
# 3. 安装依赖:
#    pip install -r requirements_target_device.txt
# 4. 验证安装:
#    python scripts/verify_dependencies.py

# ==========================================
# 特殊说明
# ==========================================
# 1. rknnlite库通常需要从设备厂商获取
# 2. 某些ARM64设备可能需要特定版本的OpenCV
# 3. 如果遇到Qt相关问题，可能需要设置环境变量:
#    export QT_QPA_PLATFORM=xcb
#    export DISPLAY=:0.0
# 4. 对于无头设备，可能需要虚拟显示:
#    sudo apt-get install xvfb
#    export DISPLAY=:99
#    Xvfb :99 -screen 0 1024x768x24 &

# ==========================================
# 性能优化建议
# ==========================================
# 1. 对于内存受限的设备，可以移除以下可选依赖:
#    - matplotlib
#    - seaborn
#    - scipy (如果不使用高级数学功能)
# 2. 对于存储受限的设备，使用轻量级版本:
#    - opencv-python-headless 替代 opencv-python
# 3. 对于CPU性能受限的设备:
#    - 确保numpy使用优化的BLAS库
#    - 考虑使用numba进行JIT编译

# ==========================================
# 故障排除
# ==========================================
# 如果安装过程中遇到问题:
# 1. 更新pip: pip install --upgrade pip
# 2. 清理缓存: pip cache purge
# 3. 使用国内镜像源:
#    pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ -r requirements_target_device.txt
# 4. 如果某个包安装失败，尝试单独安装:
#    pip install package_name --no-deps
# 5. 检查系统架构兼容性:
#    python -c "import platform; print(platform.machine())"

# ==========================================
# 版本兼容性
# ==========================================
# 测试环境:
# - Python 3.8+ on ARM64 Linux
# - 瑞芯微RK3588开发板
# - Ubuntu 20.04/22.04 ARM64
# - Debian 11 ARM64
#
# 已知兼容设备:
# - 瑞芯微RK3588系列
# - 瑞芯微RK3566系列
# - 其他支持RKNN的ARM64设备
