#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
依赖验证脚本
验证热成像火灾检测系统的所有依赖是否正确安装
"""

import sys
import importlib
import subprocess
import platform
from typing import Dict, List, Tuple, Optional


class DependencyVerifier:
    """依赖验证器"""
    
    def __init__(self):
        self.results = {
            'success': [],
            'warnings': [],
            'errors': []
        }
    
    def verify_python_version(self) -> bool:
        """验证Python版本"""
        print("🐍 Python版本检查")
        print("-" * 40)
        
        version = sys.version_info
        version_str = f"{version.major}.{version.minor}.{version.micro}"
        print(f"   当前Python版本: {version_str}")
        
        if version.major == 3 and version.minor >= 8:
            print("   ✅ Python版本符合要求 (>=3.8)")
            self.results['success'].append(f"Python {version_str}")
            return True
        else:
            print("   ❌ Python版本过低，需要Python 3.8+")
            self.results['errors'].append(f"Python版本过低: {version_str}")
            return False
    
    def verify_core_packages(self) -> bool:
        """验证核心包"""
        print("\n📦 核心包验证")
        print("-" * 40)
        
        core_packages = [
            ("PyQt5", "PyQt5界面框架", self._test_pyqt5),
            ("cv2", "OpenCV计算机视觉库", self._test_opencv),
            ("numpy", "数值计算库", self._test_numpy),
            ("PIL", "图像处理库", self._test_pillow),
        ]
        
        all_success = True
        for package, description, test_func in core_packages:
            success = self._verify_package(package, description, test_func)
            if not success:
                all_success = False
        
        return all_success
    
    def verify_ml_packages(self) -> bool:
        """验证机器学习包"""
        print("\n🧠 机器学习包验证")
        print("-" * 40)
        
        ml_packages = [
            ("torch", "PyTorch深度学习框架", self._test_torch),
            ("torchvision", "PyTorch视觉库", self._test_torchvision),
            ("ultralytics", "YOLO模型库", self._test_ultralytics),
            ("dill", "序列化库", self._test_dill),
            ("scipy", "科学计算库", self._test_scipy),
        ]
        
        all_success = True
        for package, description, test_func in ml_packages:
            success = self._verify_package(package, description, test_func)
            if not success:
                all_success = False
        
        return all_success
    
    def verify_communication_packages(self) -> bool:
        """验证通信包"""
        print("\n📡 通信包验证")
        print("-" * 40)

        comm_packages = [
            ("paho.mqtt", "MQTT客户端", self._test_mqtt),
            ("requests", "HTTP请求库", self._test_requests),
            ("yaml", "YAML解析库", self._test_yaml),
        ]
        
        all_success = True
        for package, description, test_func in comm_packages:
            success = self._verify_package(package, description, test_func)
            if not success:
                all_success = False
        
        return all_success
    
    def _verify_package(self, package_name: str, description: str, test_func) -> bool:
        """验证单个包"""
        try:
            # 尝试导入
            importlib.import_module(package_name)
            
            # 运行特定测试
            if test_func:
                test_result = test_func()
                if test_result:
                    print(f"   ✅ {package_name}: {description}")
                    self.results['success'].append(f"{package_name} - {description}")
                    return True
                else:
                    print(f"   ⚠️  {package_name}: {description} - 导入成功但功能测试失败")
                    self.results['warnings'].append(f"{package_name} - 功能测试失败")
                    return False
            else:
                print(f"   ✅ {package_name}: {description}")
                self.results['success'].append(f"{package_name} - {description}")
                return True
                
        except ImportError as e:
            print(f"   ❌ {package_name}: {description} - 未安装 ({e})")
            self.results['errors'].append(f"{package_name} - 未安装")
            return False
        except Exception as e:
            print(f"   ⚠️  {package_name}: {description} - 测试异常 ({e})")
            self.results['warnings'].append(f"{package_name} - 测试异常")
            return False
    
    def _test_pyqt5(self) -> bool:
        """测试PyQt5功能"""
        try:
            from PyQt5.QtWidgets import QApplication, QWidget
            from PyQt5.QtCore import QTimer
            return True
        except Exception:
            return False
    
    def _test_opencv(self) -> bool:
        """测试OpenCV功能"""
        try:
            import cv2
            import numpy as np
            # 创建一个简单的图像测试
            img = np.zeros((100, 100, 3), dtype=np.uint8)
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            return gray.shape == (100, 100)
        except Exception:
            return False
    
    def _test_numpy(self) -> bool:
        """测试NumPy功能"""
        try:
            import numpy as np
            arr = np.array([1, 2, 3])
            return arr.sum() == 6
        except Exception:
            return False
    
    def _test_pillow(self) -> bool:
        """测试Pillow功能"""
        try:
            from PIL import Image
            import numpy as np
            # 创建一个简单的图像
            img = Image.fromarray(np.zeros((100, 100, 3), dtype=np.uint8))
            return img.size == (100, 100)
        except Exception:
            return False
    
    def _test_torch(self) -> bool:
        """测试PyTorch功能"""
        try:
            import torch
            x = torch.tensor([1.0, 2.0, 3.0])
            return x.sum().item() == 6.0
        except Exception:
            return False
    
    def _test_torchvision(self) -> bool:
        """测试torchvision功能"""
        try:
            import torchvision
            return hasattr(torchvision, 'transforms')
        except Exception:
            return False
    
    def _test_ultralytics(self) -> bool:
        """测试ultralytics功能"""
        try:
            from ultralytics import YOLO
            return True
        except Exception:
            return False
    
    def _test_dill(self) -> bool:
        """测试dill功能"""
        try:
            import dill
            # 测试序列化功能
            data = {'test': [1, 2, 3]}
            serialized = dill.dumps(data)
            deserialized = dill.loads(serialized)
            return deserialized == data
        except Exception:
            return False
    
    def _test_scipy(self) -> bool:
        """测试scipy功能"""
        try:
            from scipy import fft
            import numpy as np
            x = np.array([1.0, 2.0, 3.0, 4.0])
            result = fft.fft(x)
            return len(result) == 4
        except Exception:
            return False
    
    def _test_mqtt(self) -> bool:
        """测试MQTT功能"""
        try:
            import paho.mqtt.client as mqtt
            client = mqtt.Client()
            return True
        except Exception:
            return False
    

    
    def _test_requests(self) -> bool:
        """测试requests功能"""
        try:
            import requests
            return hasattr(requests, 'get')
        except Exception:
            return False
    
    def _test_yaml(self) -> bool:
        """测试YAML功能"""
        try:
            import yaml
            data = {'test': 'value'}
            yaml_str = yaml.dump(data)
            loaded = yaml.safe_load(yaml_str)
            return loaded == data
        except Exception:
            return False
    
    def generate_report(self):
        """生成验证报告"""
        print("\n📋 依赖验证报告")
        print("=" * 50)
        
        total = len(self.results['success']) + len(self.results['warnings']) + len(self.results['errors'])
        success_rate = len(self.results['success']) / total * 100 if total > 0 else 0
        
        print(f"📊 总体成功率: {success_rate:.1f}%")
        print(f"✅ 成功: {len(self.results['success'])}")
        print(f"⚠️  警告: {len(self.results['warnings'])}")
        print(f"❌ 错误: {len(self.results['errors'])}")
        print()
        
        if self.results['errors']:
            print("❌ 需要解决的错误:")
            for error in self.results['errors']:
                print(f"   - {error}")
            print()
        
        if self.results['warnings']:
            print("⚠️  需要注意的警告:")
            for warning in self.results['warnings']:
                print(f"   - {warning}")
            print()
        
        # 提供安装建议
        if self.results['errors']:
            print("💡 安装建议:")
            self._provide_installation_suggestions()
    
    def _provide_installation_suggestions(self):
        """提供安装建议"""
        missing_packages = []
        for error in self.results['errors']:
            if " - 未安装" in error:
                package = error.split(" - ")[0]
                missing_packages.append(package)
        
        if missing_packages:
            print("   使用pip安装缺失的包:")
            pip_command = "pip3 install " + " ".join(missing_packages)
            print(f"   {pip_command}")
            print()
            
            print("   或者运行完整的安装脚本:")
            print("   ./scripts/install_linux_dependencies.sh")
    
    def run_verification(self) -> bool:
        """运行完整验证"""
        print("🔍 开始依赖验证")
        print("=" * 50)
        
        python_ok = self.verify_python_version()
        core_ok = self.verify_core_packages()
        ml_ok = self.verify_ml_packages()
        comm_ok = self.verify_communication_packages()
        
        self.generate_report()
        
        return python_ok and core_ok and ml_ok and comm_ok


def main():
    """主函数"""
    verifier = DependencyVerifier()
    success = verifier.run_verification()
    
    if success:
        print("🎉 所有依赖验证通过！系统可以正常运行。")
        sys.exit(0)
    else:
        print("❌ 依赖验证失败，请解决上述问题后重试。")
        sys.exit(1)


if __name__ == "__main__":
    main()
