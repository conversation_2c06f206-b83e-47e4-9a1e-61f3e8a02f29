#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门的数据交换类
包括帧数据交换、配置数据交换等专门用途的数据交换器
"""

import time
import numpy as np
from typing import Dict, List, Optional, Any, Callable
from thread_safe_data_exchange import ThreadSafeDataExchange, DataPacket
from utils.logger import get_logger


class FrameDataExchange:
    """专门用于图像帧数据交换的类"""
    
    def __init__(self, exchange: ThreadSafeDataExchange):
        """
        初始化帧数据交换器
        
        Args:
            exchange: 数据交换器实例
        """
        self.exchange = exchange
        self.logger = get_logger("FrameDataExchange")
        
        # 创建专用通道
        self.exchange.create_channel("frame_input", max_size=10)
        self.exchange.create_channel("frame_output", max_size=10)
        self.exchange.create_channel("detection_results", max_size=50)
        
        self.logger.info("帧数据交换器初始化完成")
    
    def send_frame_for_processing(self, frame: np.ndarray, 
                                 frame_id: str,
                                 detection_type: str = "human",
                                 metadata: Optional[Dict] = None) -> bool:
        """
        发送帧数据进行处理
        
        Args:
            frame: 图像帧
            frame_id: 帧ID
            detection_type: 检测类型
            metadata: 元数据
            
        Returns:
            是否成功发送
        """
        frame_data = {
            'frame_id': frame_id,
            'frame': frame.copy(),
            'detection_type': detection_type,
            'timestamp': time.time()
        }
        
        success = self.exchange.send_data(
            "frame_input",
            frame_data,
            data_type="frame_data",
            metadata=metadata,
            priority=1  # 帧数据优先级较高
        )
        
        if success:
            self.logger.debug(f"发送帧数据: {frame_id}, 类型: {detection_type}")
        
        return success
    
    def get_processed_frame(self, timeout: float = 1.0) -> Optional[Dict]:
        """
        获取处理后的帧数据
        
        Args:
            timeout: 超时时间
            
        Returns:
            处理后的帧数据或None
        """
        packet = self.exchange.receive_data("frame_output", timeout)
        if packet:
            self.logger.debug(f"接收处理后帧数据: {packet.payload.get('frame_id', 'unknown')}")
            return packet.payload
        return None
    
    def send_detection_result(self, frame_id: str,
                            detections: List[Dict],
                            annotated_frame: Optional[np.ndarray] = None,
                            processing_time: float = 0.0,
                            detection_type: str = "unknown",
                            metadata: Optional[Dict] = None) -> bool:
        """
        发送检测结果
        
        Args:
            frame_id: 帧ID
            detections: 检测结果
            annotated_frame: 标注后的帧
            processing_time: 处理时间
            detection_type: 检测类型
            metadata: 元数据
            
        Returns:
            是否成功发送
        """
        result_data = {
            'frame_id': frame_id,
            'detections': detections,
            'annotated_frame': annotated_frame,
            'processing_time': processing_time,
            'detection_type': detection_type,
            'timestamp': time.time()
        }
        
        success = self.exchange.send_data(
            "detection_results",
            result_data,
            data_type="detection_result",
            metadata=metadata,
            priority=2  # 检测结果优先级最高
        )
        
        if success:
            self.logger.debug(f"发送检测结果: {frame_id}, 检测数: {len(detections)}")
        
        return success
    
    def subscribe_to_detection_results(self, callback: Callable[[Dict], None]) -> bool:
        """
        订阅检测结果
        
        Args:
            callback: 回调函数
            
        Returns:
            是否成功订阅
        """
        def wrapper(packet: DataPacket):
            try:
                callback(packet.payload)
            except Exception as e:
                self.logger.error(f"检测结果回调失败: {e}")
        
        success = self.exchange.subscribe("detection_results", wrapper)
        if success:
            self.logger.info("订阅检测结果成功")
        
        return success
    
    def subscribe_to_frame_input(self, callback: Callable[[Dict], None]) -> bool:
        """
        订阅帧输入
        
        Args:
            callback: 回调函数
            
        Returns:
            是否成功订阅
        """
        def wrapper(packet: DataPacket):
            try:
                callback(packet.payload)
            except Exception as e:
                self.logger.error(f"帧输入回调失败: {e}")
        
        success = self.exchange.subscribe("frame_input", wrapper)
        if success:
            self.logger.info("订阅帧输入成功")
        
        return success
    
    def get_channel_statistics(self) -> Dict:
        """获取通道统计信息"""
        stats = {}
        
        for channel_name in ["frame_input", "frame_output", "detection_results"]:
            info = self.exchange.get_channel_info(channel_name)
            if info:
                stats[channel_name] = info
        
        return stats


class ConfigDataExchange:
    """专门用于配置数据交换的类"""
    
    def __init__(self, exchange: ThreadSafeDataExchange):
        """
        初始化配置数据交换器
        
        Args:
            exchange: 数据交换器实例
        """
        self.exchange = exchange
        self.logger = get_logger("ConfigDataExchange")
        
        # 创建配置通道
        self.exchange.create_channel("config_updates", max_size=20)
        self.exchange.create_channel("status_updates", max_size=50)
        self.exchange.create_channel("command_channel", max_size=30)
        
        self.logger.info("配置数据交换器初始化完成")
    
    def update_config(self, config_key: str, config_value: Any, 
                     target_thread: str = "all",
                     metadata: Optional[Dict] = None) -> bool:
        """
        更新配置
        
        Args:
            config_key: 配置键
            config_value: 配置值
            target_thread: 目标线程
            metadata: 元数据
            
        Returns:
            是否成功更新
        """
        config_data = {
            'key': config_key,
            'value': config_value,
            'target': target_thread,
            'timestamp': time.time()
        }
        
        success = self.exchange.send_data(
            "config_updates",
            config_data,
            data_type="config_update",
            priority=3,  # 配置更新优先级最高
            metadata=metadata
        )
        
        if success:
            self.logger.info(f"发送配置更新: {config_key} = {config_value}")
        
        return success
    
    def send_status_update(self, component: str, status: str, 
                          details: Optional[Dict] = None,
                          metadata: Optional[Dict] = None) -> bool:
        """
        发送状态更新
        
        Args:
            component: 组件名称
            status: 状态
            details: 详细信息
            metadata: 元数据
            
        Returns:
            是否成功发送
        """
        status_data = {
            'component': component,
            'status': status,
            'details': details or {},
            'timestamp': time.time()
        }
        
        success = self.exchange.send_data(
            "status_updates",
            status_data,
            data_type="status_update",
            metadata=metadata
        )
        
        if success:
            self.logger.debug(f"发送状态更新: {component} -> {status}")
        
        return success
    
    def send_command(self, command: str, parameters: Optional[Dict] = None,
                    target_thread: str = "all",
                    metadata: Optional[Dict] = None) -> bool:
        """
        发送命令
        
        Args:
            command: 命令名称
            parameters: 命令参数
            target_thread: 目标线程
            metadata: 元数据
            
        Returns:
            是否成功发送
        """
        command_data = {
            'command': command,
            'parameters': parameters or {},
            'target': target_thread,
            'timestamp': time.time()
        }
        
        success = self.exchange.send_data(
            "command_channel",
            command_data,
            data_type="command",
            priority=2,  # 命令优先级较高
            metadata=metadata
        )
        
        if success:
            self.logger.info(f"发送命令: {command} -> {target_thread}")
        
        return success
    
    def subscribe_to_config_updates(self, callback: Callable[[Dict], None]) -> bool:
        """
        订阅配置更新
        
        Args:
            callback: 回调函数
            
        Returns:
            是否成功订阅
        """
        def wrapper(packet: DataPacket):
            try:
                callback(packet.payload)
            except Exception as e:
                self.logger.error(f"配置更新回调失败: {e}")
        
        success = self.exchange.subscribe("config_updates", wrapper)
        if success:
            self.logger.info("订阅配置更新成功")
        
        return success
    
    def subscribe_to_status_updates(self, callback: Callable[[Dict], None]) -> bool:
        """
        订阅状态更新
        
        Args:
            callback: 回调函数
            
        Returns:
            是否成功订阅
        """
        def wrapper(packet: DataPacket):
            try:
                callback(packet.payload)
            except Exception as e:
                self.logger.error(f"状态更新回调失败: {e}")
        
        success = self.exchange.subscribe("status_updates", wrapper)
        if success:
            self.logger.info("订阅状态更新成功")
        
        return success
    
    def subscribe_to_commands(self, callback: Callable[[Dict], None]) -> bool:
        """
        订阅命令
        
        Args:
            callback: 回调函数
            
        Returns:
            是否成功订阅
        """
        def wrapper(packet: DataPacket):
            try:
                callback(packet.payload)
            except Exception as e:
                self.logger.error(f"命令回调失败: {e}")
        
        success = self.exchange.subscribe("command_channel", wrapper)
        if success:
            self.logger.info("订阅命令成功")
        
        return success
    
    def get_channel_statistics(self) -> Dict:
        """获取通道统计信息"""
        stats = {}
        
        for channel_name in ["config_updates", "status_updates", "command_channel"]:
            info = self.exchange.get_channel_info(channel_name)
            if info:
                stats[channel_name] = info
        
        return stats


class PerformanceDataExchange:
    """专门用于性能数据交换的类"""
    
    def __init__(self, exchange: ThreadSafeDataExchange):
        """
        初始化性能数据交换器
        
        Args:
            exchange: 数据交换器实例
        """
        self.exchange = exchange
        self.logger = get_logger("PerformanceDataExchange")
        
        # 创建性能通道
        self.exchange.create_channel("performance_metrics", max_size=100)
        self.exchange.create_channel("resource_usage", max_size=50)
        
        self.logger.info("性能数据交换器初始化完成")
    
    def send_performance_metric(self, metric_name: str, value: float,
                              component: str = "system",
                              metadata: Optional[Dict] = None) -> bool:
        """
        发送性能指标
        
        Args:
            metric_name: 指标名称
            value: 指标值
            component: 组件名称
            metadata: 元数据
            
        Returns:
            是否成功发送
        """
        metric_data = {
            'metric_name': metric_name,
            'value': value,
            'component': component,
            'timestamp': time.time()
        }
        
        success = self.exchange.send_data(
            "performance_metrics",
            metric_data,
            data_type="performance_metric",
            metadata=metadata
        )
        
        if success:
            self.logger.debug(f"发送性能指标: {component}.{metric_name} = {value}")
        
        return success
    
    def send_resource_usage(self, cpu_percent: float, memory_mb: float,
                          component: str = "system",
                          metadata: Optional[Dict] = None) -> bool:
        """
        发送资源使用情况
        
        Args:
            cpu_percent: CPU使用率
            memory_mb: 内存使用量(MB)
            component: 组件名称
            metadata: 元数据
            
        Returns:
            是否成功发送
        """
        usage_data = {
            'cpu_percent': cpu_percent,
            'memory_mb': memory_mb,
            'component': component,
            'timestamp': time.time()
        }
        
        success = self.exchange.send_data(
            "resource_usage",
            usage_data,
            data_type="resource_usage",
            metadata=metadata
        )
        
        if success:
            self.logger.debug(f"发送资源使用: {component} CPU={cpu_percent}% MEM={memory_mb}MB")
        
        return success
    
    def subscribe_to_performance_metrics(self, callback: Callable[[Dict], None]) -> bool:
        """
        订阅性能指标
        
        Args:
            callback: 回调函数
            
        Returns:
            是否成功订阅
        """
        def wrapper(packet: DataPacket):
            try:
                callback(packet.payload)
            except Exception as e:
                self.logger.error(f"性能指标回调失败: {e}")
        
        success = self.exchange.subscribe("performance_metrics", wrapper)
        if success:
            self.logger.info("订阅性能指标成功")
        
        return success
    
    def subscribe_to_resource_usage(self, callback: Callable[[Dict], None]) -> bool:
        """
        订阅资源使用情况
        
        Args:
            callback: 回调函数
            
        Returns:
            是否成功订阅
        """
        def wrapper(packet: DataPacket):
            try:
                callback(packet.payload)
            except Exception as e:
                self.logger.error(f"资源使用回调失败: {e}")
        
        success = self.exchange.subscribe("resource_usage", wrapper)
        if success:
            self.logger.info("订阅资源使用成功")
        
        return success
