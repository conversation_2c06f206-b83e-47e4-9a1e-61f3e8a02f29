#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
带缓存清理的启动脚本
提供选项在启动程序前清理所有缓存
"""

import sys
import os
import subprocess
from pathlib import Path

def show_banner():
    """显示启动横幅"""
    print("🔥 海康威视双光谱热成像监控系统")
    print("=" * 50)
    print("🚀 启动选项:")
    print("   1. 清理缓存后启动 (推荐)")
    print("   2. 直接启动")
    print("   3. 只清理缓存，不启动")
    print("   4. 退出")
    print("-" * 50)

def cleanup_cache_interactive():
    """交互式缓存清理"""
    try:
        from startup_cache_cleaner import clean_startup_cache
        print("\n🧹 开始清理缓存...")
        results = clean_startup_cache(verbose=True)
        
        print(f"\n📊 清理结果:")
        print(f"   清理项目: {results['total_cleaned']}")
        print(f"   释放空间: {format_size(results['total_size_freed'])}")
        
        if results['errors']:
            print(f"   ⚠️ 警告: {len(results['errors'])} 个")
        
        return True
        
    except Exception as e:
        print(f"❌ 缓存清理失败: {e}")
        return False

def format_size(size_bytes):
    """格式化文件大小"""
    for unit in ['B', 'KB', 'MB', 'GB']:
        if size_bytes < 1024.0:
            return f"{size_bytes:.1f} {unit}"
        size_bytes /= 1024.0
    return f"{size_bytes:.1f} TB"

def start_main_program():
    """启动主程序"""
    try:
        print("\n🚀 启动主程序...")
        
        # 检查虚拟环境
        venv_python = Path("tj/Scripts/python.exe")
        if venv_python.exists():
            # Windows虚拟环境
            python_cmd = str(venv_python)
        else:
            # Linux虚拟环境或系统Python
            venv_python = Path("tj/bin/python")
            if venv_python.exists():
                python_cmd = str(venv_python)
            else:
                python_cmd = sys.executable
        
        # 启动主程序
        result = subprocess.run([python_cmd, "main.py"], 
                              capture_output=False, 
                              text=True)
        
        return result.returncode == 0
        
    except Exception as e:
        print(f"❌ 启动主程序失败: {e}")
        return False

def get_cache_status():
    """获取缓存状态"""
    try:
        from startup_cache_cleaner import StartupCacheCleaner
        cleaner = StartupCacheCleaner(verbose=False)
        
        # 模拟清理以获取统计信息
        cache_info = {
            'python_cache': len(list(Path('.').glob('**/__pycache__'))),
            'log_files': len(list(Path('logs').glob('*.log'))) if Path('logs').exists() else 0,
            'temp_files': len(list(Path('.').glob('*.tmp'))) + len(list(Path('.').glob('*.temp'))),
            'export_files': len(list(Path('data_exports').glob('*_export_*.xlsx'))) if Path('data_exports').exists() else 0,
        }
        
        total_items = sum(cache_info.values())
        
        print(f"\n📋 当前缓存状态:")
        print(f"   Python缓存目录: {cache_info['python_cache']} 个")
        print(f"   日志文件: {cache_info['log_files']} 个")
        print(f"   临时文件: {cache_info['temp_files']} 个")
        print(f"   导出文件: {cache_info['export_files']} 个")
        print(f"   总计: {total_items} 项")
        
        return total_items > 0
        
    except Exception as e:
        print(f"⚠️ 无法获取缓存状态: {e}")
        return False

def main():
    """主函数"""
    show_banner()
    
    # 显示缓存状态
    has_cache = get_cache_status()
    
    if has_cache:
        print("💡 建议清理缓存以获得最佳性能")
    else:
        print("✨ 缓存状态良好")
    
    while True:
        try:
            choice = input("\n请选择操作 (1-4): ").strip()
            
            if choice == '1':
                # 清理缓存后启动
                print("\n🎯 选择: 清理缓存后启动")
                if cleanup_cache_interactive():
                    print("\n" + "="*30)
                    if start_main_program():
                        print("✅ 程序正常退出")
                    else:
                        print("❌ 程序异常退出")
                break
                
            elif choice == '2':
                # 直接启动
                print("\n🎯 选择: 直接启动")
                if start_main_program():
                    print("✅ 程序正常退出")
                else:
                    print("❌ 程序异常退出")
                break
                
            elif choice == '3':
                # 只清理缓存
                print("\n🎯 选择: 只清理缓存")
                cleanup_cache_interactive()
                print("\n✅ 缓存清理完成，程序未启动")
                break
                
            elif choice == '4':
                # 退出
                print("\n👋 退出程序")
                break
                
            else:
                print("❌ 无效选择，请输入 1-4")
                
        except KeyboardInterrupt:
            print("\n\n👋 用户取消操作")
            break
        except Exception as e:
            print(f"❌ 操作失败: {e}")
            break

def quick_start():
    """快速启动模式（命令行参数）"""
    if len(sys.argv) > 1:
        arg = sys.argv[1].lower()
        
        if arg in ['--clean', '-c']:
            print("🧹 快速清理缓存模式")
            cleanup_cache_interactive()
            return
            
        elif arg in ['--clean-start', '-cs']:
            print("🚀 清理缓存并启动模式")
            if cleanup_cache_interactive():
                start_main_program()
            return
            
        elif arg in ['--start', '-s']:
            print("🚀 直接启动模式")
            start_main_program()
            return
            
        elif arg in ['--help', '-h']:
            print("📖 使用说明:")
            print("   python start_with_cache_cleanup.py          # 交互模式")
            print("   python start_with_cache_cleanup.py -c       # 只清理缓存")
            print("   python start_with_cache_cleanup.py -cs      # 清理缓存并启动")
            print("   python start_with_cache_cleanup.py -s       # 直接启动")
            print("   python start_with_cache_cleanup.py -h       # 显示帮助")
            return
    
    # 默认交互模式
    main()

if __name__ == "__main__":
    try:
        quick_start()
    except KeyboardInterrupt:
        print("\n👋 程序被用户中断")
    except Exception as e:
        print(f"❌ 程序异常: {e}")
        sys.exit(1)
