#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能启动脚本
根据平台自动选择启动模式，支持PC端模拟和目标设备真实运行
"""

import sys
import os
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from platform_detector import (
    detect_target_platform, 
    print_platform_info,
    setup_mock_environment
)
from utils.logger import get_logger

logger = get_logger("SmartLauncher")


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="海康威视双光谱热成像监控系统智能启动器",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python start_with_platform_detection.py                    # 自动检测平台并启动
  python start_with_platform_detection.py --force-mock      # 强制使用模拟模式
  python start_with_platform_detection.py --force-real      # 强制使用真实模式
  python start_with_platform_detection.py --test            # 运行PC端模拟测试
  python start_with_platform_detection.py --info            # 仅显示平台信息
        """
    )
    
    parser.add_argument(
        '--force-mock', 
        action='store_true',
        help='强制使用模拟AI Worker（PC端测试模式）'
    )
    
    parser.add_argument(
        '--force-real', 
        action='store_true',
        help='强制使用真实AI Worker（目标设备模式）'
    )
    
    parser.add_argument(
        '--test', 
        action='store_true',
        help='运行PC端模拟测试'
    )
    
    parser.add_argument(
        '--info', 
        action='store_true',
        help='仅显示平台信息，不启动程序'
    )
    
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='详细输出模式'
    )
    
    return parser.parse_args()


def setup_environment(args):
    """设置环境变量"""
    if args.force_mock:
        os.environ['FORCE_MOCK_AI'] = '1'
        logger.info("设置环境变量: FORCE_MOCK_AI=1")
    
    if args.force_real:
        os.environ['FORCE_REAL_AI'] = '1'
        logger.info("设置环境变量: FORCE_REAL_AI=1")
    
    if args.verbose:
        os.environ['LOG_LEVEL'] = 'DEBUG'
        logger.info("设置环境变量: LOG_LEVEL=DEBUG")


def run_platform_test():
    """运行平台测试"""
    logger.info("运行PC端模拟测试...")
    
    try:
        from test_pc_simulation import main as test_main
        return test_main()
    except ImportError as e:
        logger.error(f"无法导入测试模块: {e}")
        return 1
    except Exception as e:
        logger.error(f"测试运行失败: {e}")
        return 1


def run_main_program():
    """运行主程序"""
    logger.info("启动主程序...")
    
    try:
        # 检测平台并显示信息
        is_target = detect_target_platform()
        
        if is_target:
            logger.info("🎯 目标设备模式启动")
        else:
            logger.info("🔧 PC端模拟模式启动")
            # 设置模拟环境
            mock_env = setup_mock_environment()
            logger.info(f"模拟环境已设置: {mock_env}")
        
        # 导入并运行主程序
        from main import main as main_program
        return main_program()
        
    except ImportError as e:
        logger.error(f"无法导入主程序: {e}")
        return 1
    except Exception as e:
        logger.error(f"主程序运行失败: {e}")
        return 1


def check_dependencies():
    """检查依赖项"""
    logger.info("检查依赖项...")
    
    required_modules = [
        'PyQt5',
        'cv2',
        'numpy',
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        logger.error(f"缺少必要的依赖项: {missing_modules}")
        print(f"\n❌ 缺少依赖项: {', '.join(missing_modules)}")
        print("请安装缺少的依赖项:")
        for module in missing_modules:
            if module == 'cv2':
                print(f"  pip install opencv-python")
            else:
                print(f"  pip install {module}")
        return False
    
    logger.info("✅ 所有依赖项检查通过")
    return True


def print_startup_banner():
    """打印启动横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                海康威视双光谱热成像监控系统                    ║
║                   智能平台检测启动器                         ║
╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)


def main():
    """主函数"""
    # 解析命令行参数
    args = parse_arguments()
    
    # 打印启动横幅
    print_startup_banner()
    
    # 设置环境变量
    setup_environment(args)
    
    # 显示平台信息
    print_platform_info()
    
    # 如果只是查看信息，直接返回
    if args.info:
        return 0
    
    # 检查依赖项
    if not check_dependencies():
        return 1
    
    # 根据参数选择运行模式
    if args.test:
        logger.info("运行测试模式")
        return run_platform_test()
    else:
        logger.info("运行主程序模式")
        return run_main_program()


if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n⚠️ 程序被用户中断")
        sys.exit(130)
    except Exception as e:
        print(f"\n❌ 程序异常退出: {e}")
        logger.error(f"程序异常: {e}", exc_info=True)
        sys.exit(1)
