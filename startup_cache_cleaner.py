#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动前缓存清理器
在程序启动前清理所有缓存文件，确保干净的运行环境
"""

import os
import sys
import shutil
import glob
from pathlib import Path
from datetime import datetime, timedelta
from typing import List, Dict, Tuple

class StartupCacheCleaner:
    """启动前缓存清理器"""
    
    def __init__(self, verbose: bool = True):
        """
        初始化清理器
        
        Args:
            verbose: 是否显示详细信息
        """
        self.verbose = verbose
        self.project_root = Path('.')
        self.cleaned_items = []
        self.errors = []
        
        # 定义需要清理的缓存类型
        self.cache_categories = {
            'python_cache': {
                'description': 'Python缓存文件',
                'patterns': ['**/__pycache__', '**/*.pyc', '**/*.pyo'],
                'directories': ['__pycache__']
            },
            'log_files': {
                'description': '旧日志文件',
                'patterns': ['logs/*.log'],
                'keep_days': 3  # 保留最近3天的日志
            },
            'temp_files': {
                'description': '临时文件',
                'patterns': ['*.tmp', '*.temp', '*~', '.DS_Store'],
                'directories': ['temp', 'tmp']
            },
            'annotation_cache': {
                'description': '标注缓存',
                'files': ['manual_annotations.json.backup', 'manual_annotations.json.tmp']
            },
            'export_cache': {
                'description': '导出缓存',
                'patterns': ['data_exports/*.tmp', 'data_exports/*_temp.xlsx'],
                'keep_latest': 5  # 保留最新的5个导出文件
            },
            'capture_cache': {
                'description': '截图缓存',
                'directories': ['captures'],
                'clear_contents': True  # 清空内容但保留目录
            },
            'test_files': {
                'description': '测试文件',
                'patterns': ['test_*.py'],
                'exclude_current': True  # 排除当前测试文件
            }
        }
    
    def clean_all_caches(self) -> Dict[str, any]:
        """
        清理所有缓存
        
        Returns:
            清理结果统计
        """
        if self.verbose:
            print("🧹 启动前缓存清理")
            print("=" * 50)
        
        results = {
            'total_cleaned': 0,
            'total_size_freed': 0,
            'categories': {},
            'errors': []
        }
        
        # 逐个清理缓存类型
        for category, config in self.cache_categories.items():
            if self.verbose:
                print(f"\n🔍 清理 {config['description']}...")
            
            category_result = self._clean_category(category, config)
            results['categories'][category] = category_result
            results['total_cleaned'] += category_result['items_cleaned']
            results['total_size_freed'] += category_result['size_freed']
        
        # 清理空目录
        empty_dirs_cleaned = self._clean_empty_directories()
        results['empty_directories_cleaned'] = empty_dirs_cleaned
        
        results['errors'] = self.errors
        
        if self.verbose:
            self._print_summary(results)
        
        return results
    
    def _clean_category(self, category: str, config: Dict) -> Dict[str, any]:
        """清理特定类型的缓存"""
        result = {
            'items_cleaned': 0,
            'size_freed': 0,
            'details': []
        }
        
        try:
            # 处理文件模式
            if 'patterns' in config:
                for pattern in config['patterns']:
                    items = list(self.project_root.glob(pattern))
                    for item in items:
                        if self._should_clean_item(item, config):
                            size = self._get_item_size(item)
                            if self._remove_item(item):
                                result['items_cleaned'] += 1
                                result['size_freed'] += size
                                result['details'].append(str(item))
            
            # 处理具体文件
            if 'files' in config:
                for file_path in config['files']:
                    item = self.project_root / file_path
                    if item.exists():
                        size = self._get_item_size(item)
                        if self._remove_item(item):
                            result['items_cleaned'] += 1
                            result['size_freed'] += size
                            result['details'].append(str(item))
            
            # 处理目录
            if 'directories' in config:
                for dir_name in config['directories']:
                    dir_path = self.project_root / dir_name
                    if dir_path.exists():
                        if config.get('clear_contents', False):
                            # 清空目录内容但保留目录
                            size = self._clear_directory_contents(dir_path)
                            if size > 0:
                                result['items_cleaned'] += 1
                                result['size_freed'] += size
                                result['details'].append(f"{dir_path} (内容已清空)")
                        else:
                            # 删除整个目录
                            size = self._get_item_size(dir_path)
                            if self._remove_item(dir_path):
                                result['items_cleaned'] += 1
                                result['size_freed'] += size
                                result['details'].append(str(dir_path))
            
            # 特殊处理：日志文件保留策略
            if category == 'log_files' and 'keep_days' in config:
                self._clean_old_logs(config['keep_days'], result)
            
            # 特殊处理：导出文件保留策略
            if category == 'export_cache' and 'keep_latest' in config:
                self._clean_old_exports(config['keep_latest'], result)
        
        except Exception as e:
            error_msg = f"清理 {category} 时出错: {e}"
            self.errors.append(error_msg)
            if self.verbose:
                print(f"   ❌ {error_msg}")
        
        if self.verbose and result['items_cleaned'] > 0:
            print(f"   ✅ 清理了 {result['items_cleaned']} 项，释放 {self._format_size(result['size_freed'])}")
        elif self.verbose:
            print(f"   ℹ️ 没有找到需要清理的项目")
        
        return result
    
    def _should_clean_item(self, item: Path, config: Dict) -> bool:
        """判断是否应该清理某个项目"""
        # 排除当前测试文件
        if config.get('exclude_current', False):
            current_script = Path(__file__).name
            if item.name == current_script:
                return False
        
        return True
    
    def _clean_old_logs(self, keep_days: int, result: Dict):
        """清理旧日志文件"""
        logs_dir = self.project_root / 'logs'
        if not logs_dir.exists():
            return
        
        cutoff_date = datetime.now() - timedelta(days=keep_days)
        
        for log_file in logs_dir.glob('*.log'):
            try:
                file_time = datetime.fromtimestamp(log_file.stat().st_mtime)
                if file_time < cutoff_date:
                    size = self._get_item_size(log_file)
                    if self._remove_item(log_file):
                        result['items_cleaned'] += 1
                        result['size_freed'] += size
                        result['details'].append(f"{log_file} (超过{keep_days}天)")
            except Exception as e:
                self.errors.append(f"处理日志文件 {log_file} 时出错: {e}")
    
    def _clean_old_exports(self, keep_latest: int, result: Dict):
        """清理旧的导出文件"""
        exports_dir = self.project_root / 'data_exports'
        if not exports_dir.exists():
            return
        
        # 获取所有导出文件并按修改时间排序
        export_files = []
        for pattern in ['fire_monitoring_export_*.xlsx', '*_export_*.xlsx']:
            export_files.extend(exports_dir.glob(pattern))
        
        # 按修改时间排序（最新的在前）
        export_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
        
        # 删除超出保留数量的文件
        for old_file in export_files[keep_latest:]:
            size = self._get_item_size(old_file)
            if self._remove_item(old_file):
                result['items_cleaned'] += 1
                result['size_freed'] += size
                result['details'].append(f"{old_file} (超出保留数量)")
    
    def _clear_directory_contents(self, dir_path: Path) -> int:
        """清空目录内容但保留目录"""
        total_size = 0
        try:
            for item in dir_path.iterdir():
                total_size += self._get_item_size(item)
                self._remove_item(item)
        except Exception as e:
            self.errors.append(f"清空目录 {dir_path} 时出错: {e}")
        return total_size
    
    def _clean_empty_directories(self) -> int:
        """清理空目录"""
        cleaned_count = 0
        
        # 常见的可能为空的目录
        potential_empty_dirs = [
            'temp', 'tmp', 'extracted_frames', 'output'
        ]
        
        for dir_name in potential_empty_dirs:
            dir_path = self.project_root / dir_name
            if dir_path.exists() and dir_path.is_dir():
                try:
                    if not any(dir_path.iterdir()):  # 目录为空
                        dir_path.rmdir()
                        cleaned_count += 1
                        if self.verbose:
                            print(f"   🗑️ 删除空目录: {dir_path}")
                except Exception as e:
                    self.errors.append(f"删除空目录 {dir_path} 时出错: {e}")
        
        return cleaned_count
    
    def _get_item_size(self, item: Path) -> int:
        """获取文件或目录的大小"""
        try:
            if item.is_file():
                return item.stat().st_size
            elif item.is_dir():
                total_size = 0
                for sub_item in item.rglob('*'):
                    if sub_item.is_file():
                        total_size += sub_item.stat().st_size
                return total_size
        except Exception:
            pass
        return 0
    
    def _remove_item(self, item: Path) -> bool:
        """删除文件或目录"""
        try:
            if item.is_file():
                item.unlink()
            elif item.is_dir():
                shutil.rmtree(item)
            return True
        except Exception as e:
            error_msg = f"删除 {item} 失败: {e}"
            self.errors.append(error_msg)
            return False
    
    def _format_size(self, size_bytes: int) -> str:
        """格式化文件大小"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size_bytes < 1024.0:
                return f"{size_bytes:.1f} {unit}"
            size_bytes /= 1024.0
        return f"{size_bytes:.1f} TB"
    
    def _print_summary(self, results: Dict):
        """打印清理总结"""
        print(f"\n📊 清理总结")
        print("-" * 30)
        print(f"总清理项目: {results['total_cleaned']}")
        print(f"释放空间: {self._format_size(results['total_size_freed'])}")
        print(f"清理的空目录: {results['empty_directories_cleaned']}")
        
        if results['errors']:
            print(f"\n⚠️ 错误 ({len(results['errors'])}):")
            for error in results['errors'][:5]:  # 只显示前5个错误
                print(f"   - {error}")
            if len(results['errors']) > 5:
                print(f"   ... 还有 {len(results['errors']) - 5} 个错误")
        
        print(f"\n🎉 缓存清理完成！")

def clean_startup_cache(verbose: bool = True) -> Dict:
    """
    清理启动缓存的便捷函数
    
    Args:
        verbose: 是否显示详细信息
        
    Returns:
        清理结果
    """
    cleaner = StartupCacheCleaner(verbose=verbose)
    return cleaner.clean_all_caches()

if __name__ == "__main__":
    # 支持命令行参数
    verbose = '--quiet' not in sys.argv and '-q' not in sys.argv
    
    print("🚀 程序启动前缓存清理")
    print("=" * 50)
    
    results = clean_startup_cache(verbose=verbose)
    
    # 返回适当的退出码
    exit_code = 1 if results['errors'] else 0
    sys.exit(exit_code)
