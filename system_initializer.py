#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统初始化器
负责热成像摄像头系统的各个组件初始化
"""

from typing import Optional
from utils.logger import get_logger

# OpenCV线程安全设置
import cv2
cv2.setNumThreads(1)  # 设置OpenCV使用单线程，避免多线程冲突

# 导入配置模块
from config.camera_config import CAMERA_CONFIG, VIDEO_CONFIG, UI_CONFIG
from config.detection_config import get_heat_detection_renderer, get_human_detection_config
# from config.fire_detection_config import get_fire_detection_config  # 已清理

# 导入核心模块
from core.video_capture import DualCameraCapture
from core.performance_monitor import PerformanceMonitor
from core.frame_processor import FrameProcessor

# 导入温度模块
from temperature.readers.base_reader import TemperatureReaderManager
from temperature.readers.real_temp_reader import RealTemperatureReader
from temperature.calculators.simple_calculator import HSVTemperatureCalculator

# 导入UI模块
# from ui.control_handler import UIControlHandler  # 已删除旧的UI模块

# 导入检测模块
from detection import ISAPIThermalDebugger

# 导入双光配准模块

# 导入Excel数据导出器 V3.0
from utils.excel_data_exporter import get_excel_exporter, create_v3_exporter

# 导入内存标注管理器
from detection.analysis.memory_annotation_manager import initialize_global_annotation_manager


class SystemInitializer:
    """系统初始化器类"""
    
    def __init__(self, system_instance):
        """
        初始化系统初始化器
        
        Args:
            system_instance: ThermalCameraSystem实例的引用
        """
        self.system = system_instance
        self.logger = get_logger("SystemInitializer")
    
    def initialize_camera_capture(self) -> bool:
        """初始化双摄像头捕获"""
        try:
            # 检查是否启用视频文件测试模式
            use_video_files = getattr(self.system, 'use_video_files_for_testing', False)
            video_folder = getattr(self.system, 'test_video_folder', None)

            if use_video_files and video_folder:
                print(f"🎬 启用视频文件测试模式: {video_folder}")
                # 视频模式：创建双摄像头捕获，但只使用可见光部分
                self.system.camera_capture = DualCameraCapture(
                    video_folder,  # 使用视频文件夹作为可见光源
                    None,  # 视频模式下没有红外摄像头
                    VIDEO_CONFIG,
                    use_video_files=True,
                    video_folder=video_folder
                )
                self.logger.info("✅ 双摄像头捕获初始化成功（视频文件模式，仅可见光）")
            else:
                print("📡 使用标准RTSP模式")
                self.system.camera_capture = DualCameraCapture(
                    CAMERA_CONFIG.visible_url,
                    CAMERA_CONFIG.thermal_url,
                    VIDEO_CONFIG
                )
                self.logger.info("✅ 双摄像头捕获初始化成功（RTSP模式）")

            return True
        except Exception as e:
            self.logger.exception(f"双摄像头捕获初始化失败: {e}")
            return False
    
    def initialize_display_manager(self) -> bool:
        """初始化显示管理器 - Qt版本不需要OpenCV显示管理器"""
        # Qt版本使用Qt界面，不需要OpenCV显示管理器
        self.logger.info("✅ 跳过OpenCV显示管理器初始化（使用Qt界面）")
        return True
    
    def initialize_temperature_manager(self) -> bool:
        """初始化温度管理器"""
        try:
            self.system.temperature_manager = TemperatureReaderManager()
            self._setup_temperature_readers()
            self.logger.info("✅ 温度管理器初始化成功")

            # 重新配置Qt界面的温度读取器
            self._reconfigure_qt_temperature_reader()

            return True
        except Exception as e:
            self.logger.exception(f"温度管理器初始化失败: {e}")
            return False
    
    def _setup_temperature_readers(self):
        """设置温度读取器"""
        try:
            # 添加真实温度读取器
            real_reader = RealTemperatureReader(
                CAMERA_CONFIG.ip,
                CAMERA_CONFIG.username,
                CAMERA_CONFIG.password
            )
            self.system.temperature_manager.add_reader("real_temp", real_reader, is_primary=True)

            # 初始化温度读取器
            if self.system.temperature_manager.initialize_all():
                self.system.temperature_manager.start_all_monitoring()
                self.logger.info("温度读取器初始化成功")
            else:
                self.logger.warning("温度读取器初始化失败，使用备用计算器")

        except Exception as e:
            self.logger.exception(f"温度读取器设置失败: {e}")
    
    def initialize_mouse_handler(self) -> bool:
        """初始化鼠标处理器 - Qt版本不需要OpenCV鼠标处理器"""
        # Qt版本使用Qt事件处理器，不需要OpenCV鼠标处理器
        self.logger.info("✅ 跳过OpenCV鼠标处理器初始化（使用Qt事件处理器）")
        return True
    
    def initialize_ui_control_handler(self) -> bool:
        """初始化UI控制处理器"""
        try:
            # self.system.ui_control_handler = UIControlHandler(self.system)  # 已删除旧的UI模块
            self.logger.info("✅ UI控制处理器初始化跳过（使用Qt界面）")
            return True
        except Exception as e:
            self.logger.exception(f"UI控制处理器初始化失败: {e}")
            return False
    
    def initialize_heat_detector(self) -> bool:
        """初始化ISAPI热源检测器"""
        try:
            camera_config = CAMERA_CONFIG
            self.system.heat_detector = ISAPIThermalDebugger(
                camera_ip=camera_config.ip,
                username=camera_config.username,
                password=camera_config.password
            )
            self.logger.info("✅ ISAPI热源检测器初始化成功（使用原始温度数据）")



            return True
        except Exception as e:
            self.logger.exception(f"ISAPI热源检测器初始化失败: {e}")
            self.system.heat_detector = None
            return False
    
    def initialize_human_detector(self) -> bool:
        """初始化人体检测器"""
        try:
            from detection.core.human_detector import HumanDetector
            from detection.utils.yolo_utils import YOLOModelManager

            # 获取人体检测配置
            human_config = get_human_detection_config()

            # 初始化模型管理器
            model_manager = YOLOModelManager()

            # 下载模型（如果需要）
            try:
                model_path = model_manager.download_model(human_config.model_name)
                self.logger.info(f"YOLO模型准备完成: {model_path}")
            except Exception as e:
                self.logger.warning(f"模型下载失败，将使用默认路径: {e}")
                model_path = human_config.model_name

            # 初始化人体检测器
            self.system.human_detector = HumanDetector(
                model_path=model_path,
                confidence_threshold=human_config.confidence_threshold,
                iou_threshold=human_config.iou_threshold,
                device=human_config.device
            )

            if self.system.human_detector.is_initialized:
                self.logger.info("✅ 人体检测器初始化成功")
                return True
            else:
                self.logger.warning("⚠️ 人体检测器初始化失败，功能将不可用")
                self.system.human_detector = None
                return False

        except Exception as e:
            self.logger.exception(f"人体检测器初始化失败: {e}")
            self.system.human_detector = None
            return False

    def initialize_fire_smoke_detector(self) -> bool:
        """初始化集成火焰烟雾检测器"""
        try:
            from detection.core.integrated_fire_smoke_detector import IntegratedFireSmokeDetector

            # 初始化集成火焰烟雾检测器
            self.system.fire_smoke_detector = IntegratedFireSmokeDetector()

            if self.system.fire_smoke_detector.is_available():
                self.logger.info("✅ 集成火焰烟雾检测器初始化成功")
                return True
            else:
                self.logger.warning("⚠️ 集成火焰烟雾检测器不可用（模块化系统未找到）")
                return False

        except Exception as e:
            self.logger.exception(f"火焰烟雾检测器初始化失败: {e}")
            self.system.fire_smoke_detector = None
            return False



    def initialize_performance_monitor(self) -> bool:
        """初始化性能监控器"""
        try:
            self.system.performance_monitor = PerformanceMonitor(VIDEO_CONFIG.fps_reset_interval)
            self.logger.info("✅ 性能监控器初始化成功")
            return True
        except Exception as e:
            self.logger.exception(f"性能监控器初始化失败: {e}")
            return False
    
    def initialize_frame_processor(self, heat_detection_applier) -> bool:
        """初始化帧处理器"""
        try:
            self.system.frame_processor = FrameProcessor(
                display_manager=None,  # Qt版本不使用OpenCV显示管理器
                mouse_handler=None,    # Qt版本不使用OpenCV鼠标处理器
                heat_detection_applier=heat_detection_applier,
                human_detector=self.system.human_detector,
                heat_detector=self.system.heat_detector,
                heat_detection_renderer=self.system.heat_detection_renderer,
                fire_smoke_detector=self.system.fire_smoke_detector  # 添加火焰烟雾检测器
            )

            # 检测器初始化完成，但不自动启用 - 等待用户手动控制
            print("🔧 检测器初始化状态:")

            if self.system.heat_detector and hasattr(self.system.heat_detector, 'is_initialized'):
                if self.system.heat_detector.is_initialized:
                    print("   ✅ 热源检测器已就绪（未启用）")
                    self.logger.info("🔧 热源检测器已就绪，等待用户启用")
            elif self.system.heat_detector:
                print("   ✅ 热源检测器已就绪（未启用）")
                self.logger.info("🔧 热源检测器已就绪，等待用户启用")

            if self.system.human_detector and self.system.human_detector.is_initialized:
                print("   ✅ 人体检测器已就绪（未启用）")
                self.logger.info("🔧 人体检测器已就绪，等待用户启用")

            if self.system.fire_smoke_detector and self.system.fire_smoke_detector.is_initialized:
                # 检查是否有模型加载成功
                has_models = False
                try:
                    if (hasattr(self.system.fire_smoke_detector, 'detection_engine') and
                        self.system.fire_smoke_detector.detection_engine):
                        engine = self.system.fire_smoke_detector.detection_engine
                        # 检查各种可能的模型属性
                        has_models = (
                            (hasattr(engine, 'fire_model') and engine.fire_model) or
                            (hasattr(engine, 'smoke_model') and engine.smoke_model) or
                            (hasattr(engine, 'single_model') and engine.single_model) or
                            (hasattr(engine, 'model') and engine.model)
                        )
                except Exception as e:
                    self.logger.warning(f"检查火焰烟雾检测器模型时出错: {e}")
                    has_models = False

                if has_models:
                    # 自动启用火焰烟雾检测
                    self.system.frame_processor.set_fire_smoke_detection_enabled(True)
                    print("   🔥 火焰烟雾检测器已就绪并自动启用")
                    self.logger.info("🔥 火焰烟雾检测器已自动启用（模型加载成功）")
                else:
                    print("   ✅ 火焰烟雾检测器已就绪（未启用 - 无模型）")
                    self.logger.info("🔧 火焰烟雾检测器已就绪，等待模型加载")

            print("ℹ️ 所有检测功能默认关闭，请通过界面按钮手动启用")



            # 连接性能监控器到帧处理器
            if hasattr(self.system, 'performance_monitor') and self.system.performance_monitor:
                self.system.frame_processor.set_performance_monitor(self.system.performance_monitor)
                self.logger.info("✅ 性能监控器已连接到帧处理器")

            # 设置Excel导出器到帧处理器（用于火焰烟雾检测结果导出）
            if hasattr(self.system, 'excel_exporter') and self.system.excel_exporter:
                self.system.frame_processor.set_excel_exporter(self.system.excel_exporter)
                self.logger.info("✅ 帧处理器Excel导出器已设置")

            self.logger.info("✅ 帧处理器初始化成功")
            return True
        except Exception as e:
            self.logger.exception(f"帧处理器初始化失败: {e}")
            return False

    def _reconfigure_qt_temperature_reader(self):
        """重新配置Qt界面的温度读取器"""
        try:
            # 检查是否使用Qt适配器
            if (hasattr(self.system, 'qt_adapter') and
                self.system.qt_adapter and
                hasattr(self.system.qt_adapter, 'main_window')):

                main_window = self.system.qt_adapter.main_window

                # 重新配置事件处理器
                if hasattr(main_window, '_configure_event_handlers'):
                    main_window._configure_event_handlers()
                    self.logger.info("✅ Qt界面温度读取器重新配置成功")

        except Exception as e:
            self.logger.warning(f"Qt界面温度读取器重新配置失败: {e}")



    def initialize_excel_data_exporter(self) -> bool:
        """初始化Excel数据导出器 V3.0 - 火灾监控数据库结构"""
        try:
            # 创建V3.0版本的Excel导出器实例
            excel_exporter = create_v3_exporter(
                export_dir="data_exports",
                database_filename="fire_monitoring_database.xlsx"
            )

            # 将导出器设置到系统中
            self.system.excel_exporter = excel_exporter

            # 初始化系统配置数据
            self._initialize_system_config_data(excel_exporter)

            # 同步手动标注数据到Excel数据库
            excel_exporter.sync_manual_annotations_to_assets()

            # 初始化内存标注管理器（高性能热源匹配）
            if initialize_global_annotation_manager():
                self.logger.info("✅ 内存标注管理器初始化成功")
            else:
                self.logger.warning("⚠️ 内存标注管理器初始化失败")

            # 连接到热源分析器
            if hasattr(self.system, 'heat_detector') and self.system.heat_detector:
                heat_detector = self.system.heat_detector
                if hasattr(heat_detector, 'detector'):
                    heat_detector = heat_detector.detector
                if hasattr(heat_detector, 'analyzer') and heat_detector.analyzer:
                    heat_detector.analyzer.set_excel_exporter(excel_exporter)
                    self.logger.info("✅ Excel导出器V3.0已连接到热源分析器")



            # 连接到性能监控器
            if hasattr(self.system, 'performance_monitor') and self.system.performance_monitor:
                self.system.performance_monitor.set_excel_exporter(excel_exporter)
                self.logger.info("✅ Excel导出器V3.0已连接到性能监控器")

            self.logger.info("✅ Excel数据导出器V3.0初始化成功 - 火灾监控数据库已就绪")
            return True

        except Exception as e:
            self.logger.exception(f"Excel数据导出器初始化失败: {e}")
            return False

    def _initialize_system_config_data(self, excel_exporter):
        """初始化系统配置数据到Excel数据库"""
        try:
            # 添加基础系统配置
            excel_exporter.add_system_config("System_Version", "V3.0", "火灾监控系统版本")
            excel_exporter.add_system_config("Database_Version", "V3.0", "Excel数据库结构版本")
            excel_exporter.add_system_config("Camera_Resolution", "1920x1080", "摄像头分辨率")
            excel_exporter.add_system_config("Detection_Threshold", "0.7", "检测置信度阈值")
            excel_exporter.add_system_config("Alert_Temperature_Threshold", "80.0", "预警温度阈值(°C)")
            excel_exporter.add_system_config("Auto_Export_Interval", "30", "自动导出间隔(分钟)")
            excel_exporter.add_system_config("Max_Cache_Size", "1000", "最大缓存记录数")

            # 添加坐标系统配置
            from config.coordinate_system_manager import get_coordinate_system_manager
            coord_manager = get_coordinate_system_manager()

            # 获取传感器尺寸信息
            thermal_dims, visible_dims = coord_manager.get_sensor_dimensions()
            display_thermal_dims, display_visible_dims, thermal_offset = coord_manager.get_display_dimensions()

            excel_exporter.add_system_config("Coordinate_System", "Sensor_Based", "坐标系统类型：基于传感器原始坐标")
            excel_exporter.add_system_config("Thermal_Sensor_Resolution", f"{thermal_dims[0]}x{thermal_dims[1]}", "热成像传感器分辨率")
            excel_exporter.add_system_config("Visible_Sensor_Resolution", f"{visible_dims[0]}x{visible_dims[1]}", "可见光传感器分辨率")
            excel_exporter.add_system_config("Thermal_Display_Resolution", f"{display_thermal_dims[0]}x{display_thermal_dims[1]}", "热成像显示分辨率")
            excel_exporter.add_system_config("Thermal_Display_Offset", str(thermal_offset), "热成像显示偏移量(像素)")

            # 计算转换比例
            thermal_scale_x = thermal_dims[0] / display_thermal_dims[0] if display_thermal_dims[0] > 0 else 1.0
            thermal_scale_y = thermal_dims[1] / display_thermal_dims[1] if display_thermal_dims[1] > 0 else 1.0
            excel_exporter.add_system_config("Thermal_Coordinate_Scale", f"X={thermal_scale_x:.3f}, Y={thermal_scale_y:.3f}", "热成像坐标转换比例")

            # 添加默认监控资产（如果需要） - 已禁用
            # default_assets = [
            #     {
            #         'asset_name': '默认监控区域',
            #         'asset_type': 'GENERAL',
            #         'alert_priority': 'MEDIUM'
            #     }
            # ]

            # for asset in default_assets:
            #     excel_exporter.add_monitored_asset(asset, "CAM_001")
            print("ℹ️ 默认监控资产创建已禁用，只使用用户手动标注的区域")

            self.logger.info("✅ 系统配置数据已初始化到Excel数据库")

        except Exception as e:
            self.logger.warning(f"⚠️ 初始化系统配置数据失败: {e}")
