#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单温度计算器
基于红外图像伪彩色映射估算温度
"""

import cv2
import numpy as np
from typing import Optional
from config.system_config import TEMPERATURE_CONFIG

class SimpleTemperatureCalculator:
    """简单温度计算器"""
    
    def __init__(self, min_temp: float = None, max_temp: float = None):
        self.min_temp = min_temp or TEMPERATURE_CONFIG.default_min_temp
        self.max_temp = max_temp or TEMPERATURE_CONFIG.default_max_temp
        self.config = TEMPERATURE_CONFIG
    
    def calculate_temperature(self, frame: np.ndarray, x: int, y: int) -> float:
        """
        计算指定位置的温度
        基于红外图像伪彩色映射估算温度
        """
        if frame is None or x < 0 or y < 0 or x >= frame.shape[1] or y >= frame.shape[0]:
            return 0.0
        
        # 获取BGR像素值
        b, g, r = frame[y, x]
        
        # 基于灰度值的线性映射
        gray = int(0.299 * r + 0.587 * g + 0.114 * b)
        
        # 线性映射：灰度值0-255对应温度范围
        temp_range = self.max_temp - self.min_temp
        base_temp = self.min_temp + (gray / 255.0) * temp_range
        
        # 基于颜色的微调
        red_ratio = r / 255.0
        blue_ratio = b / 255.0
        
        # 颜色调整
        if red_ratio > self.config.red_threshold:  # 红色区域 - 稍热
            color_adjustment = 1.0
        elif blue_ratio > self.config.blue_threshold:  # 蓝色区域 - 稍冷
            color_adjustment = -1.0
        else:  # 其他颜色
            color_adjustment = (red_ratio - blue_ratio) * self.config.color_adjustment_factor
        
        # 最终温度
        final_temp = base_temp + color_adjustment
        
        # 限制在合理范围内
        final_temp = max(self.min_temp - 5, min(self.max_temp + 5, final_temp))
        
        return round(final_temp, self.config.temperature_precision)
    
    def update_temperature_range(self, min_temp: float, max_temp: float):
        """更新温度范围"""
        self.min_temp = min_temp
        self.max_temp = max_temp
    
    def get_temperature_range(self) -> tuple:
        """获取当前温度范围"""
        return self.min_temp, self.max_temp

class HSVTemperatureCalculator:
    """基于HSV的温度计算器"""
    
    def __init__(self, min_temp: float = None, max_temp: float = None):
        self.min_temp = min_temp or TEMPERATURE_CONFIG.default_min_temp
        self.max_temp = max_temp or TEMPERATURE_CONFIG.default_max_temp
        self.config = TEMPERATURE_CONFIG
    
    def calculate_temperature(self, frame: np.ndarray, x: int, y: int) -> float:
        """
        基于HSV颜色空间计算温度
        更精确的海康威视伪彩色映射
        """
        if frame is None or x < 0 or y < 0 or x >= frame.shape[1] or y >= frame.shape[0]:
            return 0.0
        
        # 获取像素颜色
        b, g, r = frame[y, x]
        
        # 转换为HSV
        bgr_pixel = np.array([[[b, g, r]]], dtype=np.uint8)
        hsv_pixel = cv2.cvtColor(bgr_pixel, cv2.COLOR_BGR2HSV)[0, 0]
        h, s, v = hsv_pixel
        
        # 基于HSV计算温度比例
        temp_range = self.max_temp - self.min_temp
        
        # 海康威视伪彩色映射
        if s < 50:  # 低饱和度
            if v > 200:  # 白色 - 最热
                temp_ratio = 1.0
            elif v < 50:  # 黑色 - 最冷
                temp_ratio = 0.0
            else:  # 灰色 - 中等
                temp_ratio = v / 255.0
        else:  # 有颜色
            if h <= 15 or h >= 165:  # 红色 - 热
                temp_ratio = 0.8 + (v / 255.0) * 0.2
            elif 15 < h <= 45:  # 橙黄色 - 较热
                temp_ratio = 0.6 + (v / 255.0) * 0.2
            elif 45 < h <= 75:  # 黄绿色 - 中等
                temp_ratio = 0.4 + (v / 255.0) * 0.2
            elif 75 < h <= 120:  # 绿青色 - 较冷
                temp_ratio = 0.2 + (v / 255.0) * 0.2
            else:  # 蓝紫色 - 冷
                temp_ratio = 0.0 + (v / 255.0) * 0.2
        
        temperature = self.min_temp + temp_ratio * temp_range
        return round(temperature, self.config.temperature_precision)
    
    def update_temperature_range(self, min_temp: float, max_temp: float):
        """更新温度范围"""
        self.min_temp = min_temp
        self.max_temp = max_temp
    
    def get_temperature_range(self) -> tuple:
        """获取当前温度范围"""
        return self.min_temp, self.max_temp

def create_temperature_calculator(calculator_type: str = "hsv", 
                                min_temp: float = None, 
                                max_temp: float = None):
    """创建温度计算器工厂函数"""
    if calculator_type.lower() == "simple":
        return SimpleTemperatureCalculator(min_temp, max_temp)
    elif calculator_type.lower() == "hsv":
        return HSVTemperatureCalculator(min_temp, max_temp)
    else:
        raise ValueError(f"不支持的计算器类型: {calculator_type}")

# 默认计算器实例
default_calculator = HSVTemperatureCalculator()
