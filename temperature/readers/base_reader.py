#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
温度读取器基类
定义温度读取器的通用接口
"""

from abc import ABC, abstractmethod
from typing import Optional, Dict, Any
import time

class BaseTemperatureReader(ABC):
    """温度读取器基类"""
    
    def __init__(self):
        self.is_running = False
        self.last_update = 0
        self.update_interval = 5  # 默认5秒更新一次
        self.current_temp_range = {"min_temp": 20.0, "max_temp": 30.0}
    
    @abstractmethod
    def initialize(self) -> bool:
        """初始化温度读取器"""
        pass
    
    @abstractmethod
    def get_temperature_at_point(self, x: int, y: int) -> Optional[float]:
        """获取指定点的温度"""
        pass
    
    @abstractmethod
    def update_temperature_range(self) -> bool:
        """更新温度范围"""
        pass
    
    @abstractmethod
    def get_current_range(self) -> Dict[str, float]:
        """获取当前温度范围"""
        pass
    
    def start_monitoring(self):
        """启动监控（默认实现）"""
        self.is_running = True
        print(f"✅ {self.__class__.__name__} 监控已启动")
    
    def stop_monitoring(self):
        """停止监控（默认实现）"""
        self.is_running = False
        print(f"✅ {self.__class__.__name__} 监控已停止")
    
    def should_update(self) -> bool:
        """检查是否需要更新"""
        current_time = time.time()
        return current_time - self.last_update >= self.update_interval
    
    def mark_updated(self):
        """标记已更新"""
        self.last_update = time.time()

class TemperatureReaderManager:
    """温度读取器管理器"""
    
    def __init__(self):
        self.readers = {}
        self.primary_reader = None
    
    def add_reader(self, name: str, reader: BaseTemperatureReader, is_primary: bool = False):
        """添加温度读取器"""
        self.readers[name] = reader
        if is_primary:
            self.primary_reader = reader
    
    def get_reader(self, name: str) -> Optional[BaseTemperatureReader]:
        """获取指定的温度读取器"""
        return self.readers.get(name)
    
    def get_primary_reader(self) -> Optional[BaseTemperatureReader]:
        """获取主要的温度读取器"""
        return self.primary_reader
    
    def initialize_all(self) -> bool:
        """初始化所有读取器"""
        success_count = 0
        for name, reader in self.readers.items():
            try:
                if reader.initialize():
                    print(f"✅ {name} 初始化成功")
                    success_count += 1
                else:
                    print(f"❌ {name} 初始化失败")
            except Exception as e:
                print(f"❌ {name} 初始化异常: {e}")
        
        return success_count > 0
    
    def start_all_monitoring(self):
        """启动所有读取器的监控"""
        for reader in self.readers.values():
            try:
                reader.start_monitoring()
            except Exception as e:
                print(f"启动监控异常: {e}")
    
    def stop_all_monitoring(self):
        """停止所有读取器的监控"""
        for reader in self.readers.values():
            try:
                reader.stop_monitoring()
            except Exception as e:
                print(f"停止监控异常: {e}")
    
    def get_temperature_at_point(self, x: int, y: int) -> Optional[float]:
        """从主要读取器获取温度"""
        if self.primary_reader:
            return self.primary_reader.get_temperature_at_point(x, y)
        return None
    
    def get_current_range(self) -> Dict[str, float]:
        """从主要读取器获取温度范围"""
        if self.primary_reader:
            return self.primary_reader.get_current_range()
        return {"min_temp": 20.0, "max_temp": 30.0}
    
    def update_all_ranges(self) -> bool:
        """更新所有读取器的温度范围"""
        updated = False
        for reader in self.readers.values():
            try:
                if reader.update_temperature_range():
                    updated = True
            except Exception as e:
                print(f"更新温度范围异常: {e}")
        return updated
