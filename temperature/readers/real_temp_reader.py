#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实温度读取器
基于海康威视ISAPI获取真实的温度传感器数据
"""

import requests
import numpy as np
import json
import time
import threading
import re
from requests.auth import HTTPDigestAuth
from .base_reader import BaseTemperatureReader

class RealTemperatureReader(BaseTemperatureReader):
    """真实温度读取器"""
    
    def __init__(self, ip: str, username: str, password: str, port: int = 80):
        super().__init__()
        self.ip = ip
        self.username = username
        self.password = password
        self.port = port
        self.base_url = f"http://{ip}:{port}"
        
        self.session = requests.Session()
        self.session.auth = HTTPDigestAuth(username, password)
        
        # 温度数据缓存
        self.current_temp_matrix = None
        
        # 工作API
        self.working_api = None
        self.working_channel = None
        
    def initialize(self) -> bool:
        """初始化，找到工作的API"""
        print("🔍 初始化真实温度读取器...")
        
        # 测试不同通道的API
        test_configs = [
            (2, f'{self.base_url}/ISAPI/Thermal/channels/2/thermometry/jpegPicWithAppendData?format=json'),
            (1, f'{self.base_url}/ISAPI/Thermal/channels/1/thermometry/jpegPicWithAppendData?format=json'),
        ]
        
        for channel, api_url in test_configs:
            print(f"🔍 测试通道 {channel}...")
            
            try:
                response = self.session.get(api_url, timeout=10)
                
                if response.status_code == 200:
                    content_type = response.headers.get('Content-Type', '').lower()
                    
                    if 'multipart' in content_type:
                        # 尝试解析一次看是否有温度数据
                        result = self.parse_multipart_response(response)
                        
                        if result and result.get('temp_data'):
                            self.working_api = api_url
                            self.working_channel = channel
                            print(f"✅ 找到工作API: 通道 {channel}")
                            return True
                
            except Exception as e:
                print(f"❌ 通道 {channel} 测试失败: {e}")
        
        print("❌ 未找到可用的温度API")
        return False
    
    def get_real_temperature_data(self):
        """获取真实温度数据"""
        if not self.working_api:
            return None
        
        try:
            response = self.session.get(self.working_api, timeout=15)
            
            if response.status_code == 200:
                return self.parse_multipart_response(response)
            else:
                print(f"❌ API请求失败: HTTP {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ 获取温度数据异常: {e}")
            return None
    
    def parse_multipart_response(self, response):
        """解析multipart响应"""
        try:
            content = response.content
            content_type = response.headers.get('Content-Type', '')
            
            # 提取boundary
            boundary_match = re.search(r'boundary=([^;]+)', content_type)
            if not boundary_match:
                return None
            
            boundary = boundary_match.group(1).strip('"')
            boundary_bytes = f'--{boundary}'.encode()
            
            # 分割数据
            parts = content.split(boundary_bytes)
            
            # 查找JSON元数据
            metadata = None
            for part in parts:
                if b'JpegPictureWithAppendData' in part:
                    json_start = part.find(b'{')
                    json_end = part.rfind(b'}') + 1
                    
                    if json_start != -1 and json_end > json_start:
                        json_data = part[json_start:json_end]
                        metadata = json.loads(json_data.decode('utf-8'))
                        break
            
            if not metadata:
                return None
            
            # 提取温度数据
            return self.extract_temperature_from_multipart(content, boundary_bytes, metadata)
            
        except Exception as e:
            print(f"❌ 解析multipart响应异常: {e}")
            return None
    
    def extract_temperature_from_multipart(self, content, boundary_bytes, metadata):
        """从multipart数据中提取温度矩阵"""
        try:
            meta = metadata['JpegPictureWithAppendData']
            
            width = meta['jpegPicWidth']
            height = meta['jpegPicHeight']
            temp_len = meta['temperatureDataLength']
            p2p_len = meta['p2pDataLen']
            
            # 分割multipart数据
            parts = content.split(boundary_bytes)
            
            for part in parts:
                if len(part) < 10:
                    continue
                
                # 查找数据开始位置
                data_start = part.find(b'\r\n\r\n')
                if data_start == -1:
                    continue
                
                data_start += 4
                data = part[data_start:]
                
                # 检查是否是温度数据
                if abs(len(data) - p2p_len) <= 2:
                    temp_matrix = self.parse_temperature_matrix(data, width, height, temp_len)
                    
                    if temp_matrix is not None:
                        min_temp = float(np.min(temp_matrix))
                        max_temp = float(np.max(temp_matrix))
                        avg_temp = float(np.mean(temp_matrix))
                        
                        return {
                            'temp_data': {
                                'matrix': temp_matrix,
                                'width': width,
                                'height': height,
                                'min_temp': min_temp,
                                'max_temp': max_temp,
                                'avg_temp': avg_temp
                            },
                            'success': True
                        }
            
            return None
            
        except Exception as e:
            print(f"❌ 提取温度数据异常: {e}")
            return None
    
    def parse_temperature_matrix(self, data, width, height, temp_len):
        """解析温度矩阵"""
        try:
            expected_pixels = width * height
            expected_size = expected_pixels * temp_len
            
            if len(data) < expected_size:
                return None
            
            if temp_len == 4:
                # 32位浮点数
                temp_matrix = np.frombuffer(data[:expected_size], dtype=np.float32)
                temp_matrix = temp_matrix.reshape(height, width)
                
                min_val = np.min(temp_matrix)
                
                # 检查是否需要转换
                if min_val > 200:  # 可能是开氏度
                    temp_matrix = temp_matrix - 273.15
                
                return temp_matrix
            
            elif temp_len == 2:
                # 16位整数
                temp_raw = np.frombuffer(data[:expected_size], dtype=np.int16)
                temp_matrix = temp_raw.reshape(height, width).astype(np.float32)
                
                # 尝试不同的缩放
                for scale in [0.01, 0.1, 1.0]:
                    scaled = temp_matrix * scale
                    if -50 <= np.min(scaled) <= 150 and -50 <= np.max(scaled) <= 150:
                        return scaled
            
            return None
            
        except Exception as e:
            print(f"❌ 解析温度矩阵异常: {e}")
            return None
    
    def get_temperature_at_point(self, x: int, y: int) -> float:
        """获取指定点的温度"""
        try:
            if self.current_temp_matrix is None:
                # 获取最新数据
                result = self.get_real_temperature_data()
                if result and result.get('temp_data'):
                    self.current_temp_matrix = result['temp_data']['matrix']
                else:
                    return None
            
            temp_matrix = self.current_temp_matrix
            height, width = temp_matrix.shape
            
            # 坐标范围检查
            if 0 <= x < width and 0 <= y < height:
                return float(temp_matrix[y, x])
            else:
                return None
                
        except Exception as e:
            print(f"❌ 获取点温度异常: {e}")
            return None
    
    def update_temperature_range(self) -> bool:
        """更新温度范围"""
        if not self.should_update():
            return False
        
        try:
            result = self.get_real_temperature_data()
            
            if result and result.get('temp_data'):
                temp_data = result['temp_data']
                
                # 更新缓存
                self.current_temp_matrix = temp_data['matrix']
                self.current_temp_range = {
                    'min_temp': temp_data['min_temp'],
                    'max_temp': temp_data['max_temp'],
                    'avg_temp': temp_data['avg_temp']
                }
                
                print(f"🌡️ 真实温度更新: {temp_data['min_temp']:.1f}°C - {temp_data['max_temp']:.1f}°C")
                
                # 保存校准数据
                self.save_calibration()
                
                self.mark_updated()
                return True
            
        except Exception as e:
            print(f"❌ 更新温度范围异常: {e}")
        
        self.mark_updated()
        return False
    
    def save_calibration(self):
        """保存校准数据"""
        calibration_data = {
            "min_temp": self.current_temp_range["min_temp"],
            "max_temp": self.current_temp_range["max_temp"],
            "avg_temp": self.current_temp_range["avg_temp"],
            "calibration_type": "hikvision_real_sensor",
            "last_update": time.time(),
            "api_url": self.working_api,
            "channel": self.working_channel,
            "description": "海康威视真实温度传感器数据"
        }
        
        try:
            with open('thermal_scale_calibration.json', 'w') as f:
                json.dump(calibration_data, f, indent=2)
        except Exception as e:
            print(f"保存校准数据失败: {e}")
    
    def get_current_range(self) -> dict:
        """获取当前温度范围"""
        return self.current_temp_range.copy()
    
    def start_monitoring(self):
        """开始监控"""
        super().start_monitoring()
        
        def monitor_worker():
            while self.is_running:
                try:
                    self.update_temperature_range()
                    time.sleep(1)
                except Exception as e:
                    print(f"监控异常: {e}")
                    time.sleep(5)
        
        monitor_thread = threading.Thread(target=monitor_worker, daemon=True)
        monitor_thread.start()
        
        print("✅ 真实温度监控已启动")
    
    def stop_monitoring(self):
        """停止监控"""
        super().stop_monitoring()
        print("✅ 真实温度监控已停止")
