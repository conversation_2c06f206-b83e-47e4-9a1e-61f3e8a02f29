#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
线程安全数据交换模块
实现多线程环境下的安全数据交换机制
"""

import threading
import queue
import time
import copy
from typing import Dict, List, Optional, Any, Callable, TypeVar, Generic
from dataclasses import dataclass, field
from collections import defaultdict
import weakref
import numpy as np
from PyQt5.QtCore import QObject, pyqtSignal, QMutex, QReadWriteLock, QWaitCondition

from utils.logger import get_logger


class SimpleRWLock:
    """简单的读写锁实现"""

    def __init__(self):
        self._lock = threading.RLock()
        self._readers = 0
        self._writers = 0
        self._write_ready = threading.Condition(self._lock)
        self._read_ready = threading.Condition(self._lock)

    def lockForRead(self):
        """获取读锁"""
        self._lock.acquire()
        try:
            while self._writers > 0:
                self._read_ready.wait()
            self._readers += 1
        finally:
            self._lock.release()

    def lockForWrite(self):
        """获取写锁"""
        self._lock.acquire()
        try:
            while self._writers > 0 or self._readers > 0:
                self._write_ready.wait()
            self._writers += 1
        finally:
            self._lock.release()

    def unlock(self):
        """释放锁"""
        self._lock.acquire()
        try:
            if self._writers > 0:
                self._writers -= 1
                self._write_ready.notify_all()
                self._read_ready.notify_all()
            elif self._readers > 0:
                self._readers -= 1
                if self._readers == 0:
                    self._write_ready.notify_all()
        finally:
            self._lock.release()

T = TypeVar('T')


@dataclass
class DataPacket:
    """数据包结构"""
    packet_id: str
    data_type: str
    payload: Any
    timestamp: float
    metadata: Dict[str, Any] = field(default_factory=dict)
    priority: int = 0  # 优先级，数值越大优先级越高

    def __lt__(self, other):
        """用于优先级队列排序"""
        if isinstance(other, DataPacket):
            return self.priority < other.priority
        return NotImplemented

    def __eq__(self, other):
        """相等比较"""
        if isinstance(other, DataPacket):
            return self.packet_id == other.packet_id
        return NotImplemented


@dataclass
class ExchangeConfig:
    """数据交换配置"""
    max_queue_size: int = 100
    enable_compression: bool = False
    enable_encryption: bool = False
    timeout_seconds: float = 5.0
    enable_statistics: bool = True
    max_memory_usage_mb: float = 512.0
    cleanup_interval_seconds: float = 60.0


class ThreadSafeQueue(Generic[T]):
    """线程安全队列"""
    
    def __init__(self, maxsize: int = 0):
        """
        初始化线程安全队列
        
        Args:
            maxsize: 最大队列大小，0表示无限制
        """
        self._queue = queue.PriorityQueue(maxsize=maxsize)
        self._lock = threading.RLock()
        self._condition = threading.Condition(self._lock)
        self._closed = False
        self.logger = get_logger("ThreadSafeQueue")
    
    def put(self, item: T, priority: int = 0, timeout: Optional[float] = None) -> bool:
        """
        放入数据项
        
        Args:
            item: 数据项
            priority: 优先级
            timeout: 超时时间
            
        Returns:
            是否成功放入
        """
        if self._closed:
            return False
        
        try:
            # 使用负优先级，因为PriorityQueue是最小堆
            self._queue.put((-priority, time.time(), item), timeout=timeout)
            
            with self._condition:
                self._condition.notify_all()
            
            return True
            
        except queue.Full:
            self.logger.warning("队列已满，无法放入数据")
            return False
        except Exception as e:
            self.logger.error(f"放入数据失败: {e}")
            return False
    
    def get(self, timeout: Optional[float] = None) -> Optional[T]:
        """
        获取数据项
        
        Args:
            timeout: 超时时间
            
        Returns:
            数据项或None
        """
        if self._closed:
            return None
        
        try:
            priority, timestamp, item = self._queue.get(timeout=timeout)
            return item
            
        except queue.Empty:
            return None
        except Exception as e:
            self.logger.error(f"获取数据失败: {e}")
            return None
    
    def get_nowait(self) -> Optional[T]:
        """非阻塞获取数据项"""
        return self.get(timeout=0)
    
    def put_nowait(self, item: T, priority: int = 0) -> bool:
        """非阻塞放入数据项"""
        return self.put(item, priority, timeout=0)
    
    def size(self) -> int:
        """获取队列大小"""
        return self._queue.qsize()
    
    def empty(self) -> bool:
        """检查队列是否为空"""
        return self._queue.empty()
    
    def full(self) -> bool:
        """检查队列是否已满"""
        return self._queue.full()
    
    def clear(self):
        """清空队列"""
        with self._lock:
            while not self._queue.empty():
                try:
                    self._queue.get_nowait()
                except queue.Empty:
                    break
    
    def close(self):
        """关闭队列"""
        self._closed = True
        with self._condition:
            self._condition.notify_all()


class ThreadSafeCache:
    """线程安全缓存"""
    
    def __init__(self, max_size: int = 1000, ttl_seconds: float = 300.0):
        """
        初始化线程安全缓存
        
        Args:
            max_size: 最大缓存大小
            ttl_seconds: 生存时间（秒）
        """
        self.max_size = max_size
        self.ttl_seconds = ttl_seconds
        self._cache = {}  # key -> (value, timestamp)
        self._access_order = []  # LRU顺序
        self._lock = SimpleRWLock()
        self.logger = get_logger("ThreadSafeCache")
    
    def get(self, key: str) -> Optional[Any]:
        """
        获取缓存值
        
        Args:
            key: 缓存键
            
        Returns:
            缓存值或None
        """
        self._lock.lockForRead()
        try:
            if key not in self._cache:
                return None
            
            value, timestamp = self._cache[key]
            
            # 检查是否过期
            if time.time() - timestamp > self.ttl_seconds:
                self._lock.unlock()
                self._lock.lockForWrite()
                try:
                    if key in self._cache:
                        del self._cache[key]
                        if key in self._access_order:
                            self._access_order.remove(key)
                finally:
                    self._lock.unlock()
                    self._lock.lockForRead()
                return None
            
            return copy.deepcopy(value)
            
        finally:
            self._lock.unlock()
    
    def put(self, key: str, value: Any):
        """
        放入缓存值
        
        Args:
            key: 缓存键
            value: 缓存值
        """
        self._lock.lockForWrite()
        try:
            # 如果缓存已满，移除最久未访问的项
            if len(self._cache) >= self.max_size and key not in self._cache:
                if self._access_order:
                    oldest_key = self._access_order.pop(0)
                    if oldest_key in self._cache:
                        del self._cache[oldest_key]
            
            # 更新缓存
            self._cache[key] = (copy.deepcopy(value), time.time())
            
            # 更新访问顺序
            if key in self._access_order:
                self._access_order.remove(key)
            self._access_order.append(key)
            
        finally:
            self._lock.unlock()
    
    def remove(self, key: str) -> bool:
        """
        移除缓存项
        
        Args:
            key: 缓存键
            
        Returns:
            是否成功移除
        """
        self._lock.lockForWrite()
        try:
            if key in self._cache:
                del self._cache[key]
                if key in self._access_order:
                    self._access_order.remove(key)
                return True
            return False
        finally:
            self._lock.unlock()
    
    def clear(self):
        """清空缓存"""
        self._lock.lockForWrite()
        try:
            self._cache.clear()
            self._access_order.clear()
        finally:
            self._lock.unlock()
    
    def size(self) -> int:
        """获取缓存大小"""
        self._lock.lockForRead()
        try:
            return len(self._cache)
        finally:
            self._lock.unlock()
    
    def cleanup_expired(self):
        """清理过期项"""
        current_time = time.time()
        expired_keys = []
        
        self._lock.lockForRead()
        try:
            for key, (value, timestamp) in self._cache.items():
                if current_time - timestamp > self.ttl_seconds:
                    expired_keys.append(key)
        finally:
            self._lock.unlock()
        
        if expired_keys:
            self._lock.lockForWrite()
            try:
                for key in expired_keys:
                    if key in self._cache:
                        del self._cache[key]
                        if key in self._access_order:
                            self._access_order.remove(key)
            finally:
                self._lock.unlock()


class ThreadSafeDataExchange(QObject):
    """线程安全数据交换器"""
    
    # Qt信号定义
    data_received = pyqtSignal(str, object)  # channel, data_packet
    channel_created = pyqtSignal(str)  # channel_name
    channel_closed = pyqtSignal(str)  # channel_name
    statistics_updated = pyqtSignal(dict)  # statistics
    
    def __init__(self, config: ExchangeConfig = None, parent=None):
        """
        初始化线程安全数据交换器
        
        Args:
            config: 交换配置
            parent: 父对象
        """
        super().__init__(parent)
        
        self.config = config or ExchangeConfig()
        self.logger = get_logger("ThreadSafeDataExchange")
        
        # 数据通道
        self._channels = {}  # channel_name -> ThreadSafeQueue
        self._subscribers = defaultdict(list)  # channel_name -> [callback_list]
        self._channel_lock = SimpleRWLock()
        
        # 共享缓存
        self.shared_cache = ThreadSafeCache(
            max_size=1000,
            ttl_seconds=300.0
        )
        
        # 统计信息
        self._statistics = {
            'total_packets_sent': 0,
            'total_packets_received': 0,
            'total_bytes_transferred': 0,
            'active_channels': 0,
            'active_subscribers': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'errors': 0
        }
        self._stats_lock = threading.Lock()
        
        # 清理定时器
        self._cleanup_timer = threading.Timer(
            self.config.cleanup_interval_seconds,
            self._cleanup_expired_data
        )
        self._cleanup_timer.daemon = True
        self._cleanup_timer.start()
        
        self.logger.info("线程安全数据交换器初始化完成")
    
    def create_channel(self, channel_name: str, max_size: int = None) -> bool:
        """
        创建数据通道
        
        Args:
            channel_name: 通道名称
            max_size: 最大队列大小
            
        Returns:
            是否成功创建
        """
        if max_size is None:
            max_size = self.config.max_queue_size
        
        self._channel_lock.lockForWrite()
        try:
            if channel_name in self._channels:
                self.logger.warning(f"通道已存在: {channel_name}")
                return False
            
            self._channels[channel_name] = ThreadSafeQueue(maxsize=max_size)
            
            with self._stats_lock:
                self._statistics['active_channels'] += 1
            
            self.logger.info(f"创建数据通道: {channel_name}")
            self.channel_created.emit(channel_name)
            
            return True
            
        finally:
            self._channel_lock.unlock()
    
    def close_channel(self, channel_name: str) -> bool:
        """
        关闭数据通道
        
        Args:
            channel_name: 通道名称
            
        Returns:
            是否成功关闭
        """
        self._channel_lock.lockForWrite()
        try:
            if channel_name not in self._channels:
                return False
            
            # 关闭队列
            self._channels[channel_name].close()
            del self._channels[channel_name]
            
            # 清理订阅者
            if channel_name in self._subscribers:
                del self._subscribers[channel_name]
            
            with self._stats_lock:
                self._statistics['active_channels'] -= 1
            
            self.logger.info(f"关闭数据通道: {channel_name}")
            self.channel_closed.emit(channel_name)
            
            return True
            
        finally:
            self._channel_lock.unlock()
    
    def send_data(self, channel_name: str, data: Any, 
                  data_type: str = "generic", 
                  priority: int = 0,
                  metadata: Optional[Dict] = None,
                  timeout: Optional[float] = None) -> bool:
        """
        发送数据到通道
        
        Args:
            channel_name: 通道名称
            data: 数据
            data_type: 数据类型
            priority: 优先级
            metadata: 元数据
            timeout: 超时时间
            
        Returns:
            是否成功发送
        """
        # 获取通道
        self._channel_lock.lockForRead()
        try:
            if channel_name not in self._channels:
                self.logger.error(f"通道不存在: {channel_name}")
                return False
            
            channel = self._channels[channel_name]
        finally:
            self._channel_lock.unlock()
        
        # 创建数据包
        packet = DataPacket(
            packet_id=f"{channel_name}_{int(time.time() * 1000000)}",
            data_type=data_type,
            payload=data,
            timestamp=time.time(),
            metadata=metadata or {},
            priority=priority
        )
        
        # 发送数据包
        if timeout is None:
            timeout = self.config.timeout_seconds
        
        success = channel.put(packet, priority, timeout)
        
        if success:
            with self._stats_lock:
                self._statistics['total_packets_sent'] += 1
                # 估算数据大小
                if isinstance(data, np.ndarray):
                    self._statistics['total_bytes_transferred'] += data.nbytes
                elif isinstance(data, (str, bytes)):
                    self._statistics['total_bytes_transferred'] += len(data)
            
            # 通知订阅者
            self._notify_subscribers(channel_name, packet)
            
        return success
    
    def receive_data(self, channel_name: str, 
                    timeout: Optional[float] = None) -> Optional[DataPacket]:
        """
        从通道接收数据
        
        Args:
            channel_name: 通道名称
            timeout: 超时时间
            
        Returns:
            数据包或None
        """
        # 获取通道
        self._channel_lock.lockForRead()
        try:
            if channel_name not in self._channels:
                self.logger.error(f"通道不存在: {channel_name}")
                return None
            
            channel = self._channels[channel_name]
        finally:
            self._channel_lock.unlock()
        
        # 接收数据包
        if timeout is None:
            timeout = self.config.timeout_seconds
        
        packet = channel.get(timeout)
        
        if packet:
            with self._stats_lock:
                self._statistics['total_packets_received'] += 1
        
        return packet
    
    def subscribe(self, channel_name: str, callback: Callable[[DataPacket], None]) -> bool:
        """
        订阅通道数据
        
        Args:
            channel_name: 通道名称
            callback: 回调函数
            
        Returns:
            是否成功订阅
        """
        # 使用弱引用避免循环引用
        weak_callback = weakref.ref(callback) if hasattr(callback, '__self__') else callback
        
        self._channel_lock.lockForRead()
        try:
            if channel_name not in self._channels:
                self.logger.error(f"通道不存在: {channel_name}")
                return False
            
            self._subscribers[channel_name].append(weak_callback)
            
            with self._stats_lock:
                self._statistics['active_subscribers'] += 1
            
            self.logger.info(f"订阅通道: {channel_name}")
            return True
            
        finally:
            self._channel_lock.unlock()
    
    def unsubscribe(self, channel_name: str, callback: Callable[[DataPacket], None]) -> bool:
        """
        取消订阅通道
        
        Args:
            channel_name: 通道名称
            callback: 回调函数
            
        Returns:
            是否成功取消订阅
        """
        if channel_name not in self._subscribers:
            return False
        
        # 查找并移除回调
        subscribers = self._subscribers[channel_name]
        for i, sub in enumerate(subscribers):
            if (hasattr(sub, '__self__') and sub() == callback) or sub == callback:
                subscribers.pop(i)
                
                with self._stats_lock:
                    self._statistics['active_subscribers'] -= 1
                
                self.logger.info(f"取消订阅通道: {channel_name}")
                return True
        
        return False

    def _notify_subscribers(self, channel_name: str, packet: DataPacket):
        """通知订阅者"""
        if channel_name not in self._subscribers:
            return

        # 清理失效的弱引用
        valid_subscribers = []
        for subscriber in self._subscribers[channel_name]:
            if hasattr(subscriber, '__self__'):
                # 弱引用
                callback = subscriber()
                if callback is not None:
                    valid_subscribers.append(subscriber)
                    try:
                        callback(packet)
                    except Exception as e:
                        self.logger.error(f"订阅者回调失败: {e}")
                        with self._stats_lock:
                            self._statistics['errors'] += 1
            else:
                # 普通函数引用
                valid_subscribers.append(subscriber)
                try:
                    subscriber(packet)
                except Exception as e:
                    self.logger.error(f"订阅者回调失败: {e}")
                    with self._stats_lock:
                        self._statistics['errors'] += 1

        # 更新有效订阅者列表
        self._subscribers[channel_name] = valid_subscribers

        # 发送Qt信号
        self.data_received.emit(channel_name, packet)

    def get_channel_info(self, channel_name: str) -> Optional[Dict]:
        """
        获取通道信息

        Args:
            channel_name: 通道名称

        Returns:
            通道信息字典或None
        """
        self._channel_lock.lockForRead()
        try:
            if channel_name not in self._channels:
                return None

            channel = self._channels[channel_name]
            return {
                'name': channel_name,
                'size': channel.size(),
                'empty': channel.empty(),
                'full': channel.full(),
                'subscribers': len(self._subscribers.get(channel_name, []))
            }
        finally:
            self._channel_lock.unlock()

    def list_channels(self) -> List[str]:
        """获取所有通道名称列表"""
        self._channel_lock.lockForRead()
        try:
            return list(self._channels.keys())
        finally:
            self._channel_lock.unlock()

    def get_statistics(self) -> Dict:
        """获取统计信息"""
        with self._stats_lock:
            stats = self._statistics.copy()
            stats['cache_size'] = self.shared_cache.size()
            return stats

    def clear_channel(self, channel_name: str) -> bool:
        """
        清空通道数据

        Args:
            channel_name: 通道名称

        Returns:
            是否成功清空
        """
        self._channel_lock.lockForRead()
        try:
            if channel_name not in self._channels:
                return False

            self._channels[channel_name].clear()
            self.logger.info(f"清空通道: {channel_name}")
            return True
        finally:
            self._channel_lock.unlock()

    def _cleanup_expired_data(self):
        """清理过期数据"""
        try:
            # 清理缓存中的过期项
            self.shared_cache.cleanup_expired()

            # 重新启动清理定时器
            self._cleanup_timer = threading.Timer(
                self.config.cleanup_interval_seconds,
                self._cleanup_expired_data
            )
            self._cleanup_timer.daemon = True
            self._cleanup_timer.start()

        except Exception as e:
            self.logger.error(f"清理过期数据失败: {e}")

    def shutdown(self):
        """关闭数据交换器"""
        self.logger.info("正在关闭线程安全数据交换器...")

        # 停止清理定时器
        if hasattr(self, '_cleanup_timer'):
            self._cleanup_timer.cancel()

        # 关闭所有通道
        self._channel_lock.lockForWrite()
        try:
            for channel_name in list(self._channels.keys()):
                self._channels[channel_name].close()
            self._channels.clear()
            self._subscribers.clear()
        finally:
            self._channel_lock.unlock()

        # 清空缓存
        self.shared_cache.clear()

        self.logger.info("线程安全数据交换器已关闭")
