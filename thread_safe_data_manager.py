#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
线程安全数据管理器
统一管理所有线程间的数据交换
"""

import time
import threading
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
from PyQt5.QtCore import QObject, pyqtSignal, QTimer

from thread_safe_data_exchange import ThreadSafeDataExchange, ExchangeConfig
from specialized_data_exchanges import FrameDataExchange, ConfigDataExchange, PerformanceDataExchange
from utils.logger import get_logger


@dataclass
class DataManagerConfig:
    """数据管理器配置"""
    enable_frame_exchange: bool = True
    enable_config_exchange: bool = True
    enable_performance_exchange: bool = True
    enable_monitoring: bool = True
    monitoring_interval: float = 5.0
    enable_auto_cleanup: bool = True
    cleanup_interval: float = 60.0
    max_total_memory_mb: float = 1024.0


class ThreadSafeDataManager(QObject):
    """线程安全数据管理器"""
    
    # Qt信号定义
    statistics_updated = pyqtSignal(dict)  # 统计信息更新
    memory_warning = pyqtSignal(float)  # 内存警告
    channel_status_changed = pyqtSignal(str, str)  # 通道状态变化
    error_occurred = pyqtSignal(str, str)  # 错误发生
    
    def __init__(self, config: DataManagerConfig = None, parent=None):
        """
        初始化线程安全数据管理器
        
        Args:
            config: 管理器配置
            parent: 父对象
        """
        super().__init__(parent)
        
        self.config = config or DataManagerConfig()
        self.logger = get_logger("ThreadSafeDataManager")
        
        # 创建核心数据交换器
        exchange_config = ExchangeConfig(
            max_queue_size=100,
            enable_statistics=True,
            max_memory_usage_mb=self.config.max_total_memory_mb,
            cleanup_interval_seconds=self.config.cleanup_interval
        )
        
        self.core_exchange = ThreadSafeDataExchange(exchange_config, self)
        
        # 创建专门的数据交换器
        self.frame_exchange = None
        self.config_exchange = None
        self.performance_exchange = None
        
        if self.config.enable_frame_exchange:
            self.frame_exchange = FrameDataExchange(self.core_exchange)
        
        if self.config.enable_config_exchange:
            self.config_exchange = ConfigDataExchange(self.core_exchange)
        
        if self.config.enable_performance_exchange:
            self.performance_exchange = PerformanceDataExchange(self.core_exchange)
        
        # 连接信号
        self._connect_signals()
        
        # 监控定时器
        if self.config.enable_monitoring:
            self.monitoring_timer = QTimer()
            self.monitoring_timer.timeout.connect(self._update_statistics)
            self.monitoring_timer.start(int(self.config.monitoring_interval * 1000))
        
        # 统计信息
        self.global_statistics = {
            'total_channels': 0,
            'total_packets_sent': 0,
            'total_packets_received': 0,
            'total_bytes_transferred': 0,
            'active_subscribers': 0,
            'memory_usage_mb': 0.0,
            'uptime_seconds': 0.0,
            'start_time': time.time()
        }
        
        self.logger.info("线程安全数据管理器初始化完成")
    
    def _connect_signals(self):
        """连接信号槽"""
        self.core_exchange.channel_created.connect(self._on_channel_created)
        self.core_exchange.channel_closed.connect(self._on_channel_closed)
        self.core_exchange.statistics_updated.connect(self._on_core_statistics_updated)
    
    def _on_channel_created(self, channel_name: str):
        """处理通道创建事件"""
        self.logger.info(f"通道已创建: {channel_name}")
        self.channel_status_changed.emit(channel_name, "created")
    
    def _on_channel_closed(self, channel_name: str):
        """处理通道关闭事件"""
        self.logger.info(f"通道已关闭: {channel_name}")
        self.channel_status_changed.emit(channel_name, "closed")
    
    def _on_core_statistics_updated(self, stats: Dict):
        """处理核心统计信息更新"""
        # 检查内存使用
        memory_usage = stats.get('total_bytes_transferred', 0) / (1024 * 1024)  # 转换为MB
        if memory_usage > self.config.max_total_memory_mb * 0.8:  # 80%警告阈值
            self.memory_warning.emit(memory_usage)
    
    def get_frame_exchange(self) -> Optional[FrameDataExchange]:
        """获取帧数据交换器"""
        return self.frame_exchange
    
    def get_config_exchange(self) -> Optional[ConfigDataExchange]:
        """获取配置数据交换器"""
        return self.config_exchange
    
    def get_performance_exchange(self) -> Optional[PerformanceDataExchange]:
        """获取性能数据交换器"""
        return self.performance_exchange
    
    def get_core_exchange(self) -> ThreadSafeDataExchange:
        """获取核心数据交换器"""
        return self.core_exchange
    
    def create_custom_channel(self, channel_name: str, max_size: int = 50) -> bool:
        """
        创建自定义通道
        
        Args:
            channel_name: 通道名称
            max_size: 最大队列大小
            
        Returns:
            是否成功创建
        """
        success = self.core_exchange.create_channel(channel_name, max_size)
        if success:
            self.logger.info(f"创建自定义通道: {channel_name}")
        return success
    
    def send_data_to_channel(self, channel_name: str, data: Any,
                           data_type: str = "custom",
                           priority: int = 0,
                           metadata: Optional[Dict] = None) -> bool:
        """
        发送数据到指定通道
        
        Args:
            channel_name: 通道名称
            data: 数据
            data_type: 数据类型
            priority: 优先级
            metadata: 元数据
            
        Returns:
            是否成功发送
        """
        return self.core_exchange.send_data(
            channel_name, data, data_type, priority, metadata
        )
    
    def receive_data_from_channel(self, channel_name: str, 
                                timeout: Optional[float] = None):
        """
        从指定通道接收数据
        
        Args:
            channel_name: 通道名称
            timeout: 超时时间
            
        Returns:
            数据包或None
        """
        return self.core_exchange.receive_data(channel_name, timeout)
    
    def subscribe_to_channel(self, channel_name: str, 
                           callback: Callable) -> bool:
        """
        订阅指定通道
        
        Args:
            channel_name: 通道名称
            callback: 回调函数
            
        Returns:
            是否成功订阅
        """
        return self.core_exchange.subscribe(channel_name, callback)
    
    def _update_statistics(self):
        """更新统计信息"""
        try:
            # 获取核心统计信息
            core_stats = self.core_exchange.get_statistics()
            
            # 更新全局统计信息
            self.global_statistics.update({
                'total_channels': len(self.core_exchange.list_channels()),
                'total_packets_sent': core_stats.get('total_packets_sent', 0),
                'total_packets_received': core_stats.get('total_packets_received', 0),
                'total_bytes_transferred': core_stats.get('total_bytes_transferred', 0),
                'active_subscribers': core_stats.get('active_subscribers', 0),
                'memory_usage_mb': core_stats.get('total_bytes_transferred', 0) / (1024 * 1024),
                'uptime_seconds': time.time() - self.global_statistics['start_time']
            })
            
            # 添加专门交换器的统计信息
            detailed_stats = self.global_statistics.copy()
            
            if self.frame_exchange:
                detailed_stats['frame_channels'] = self.frame_exchange.get_channel_statistics()
            
            if self.config_exchange:
                detailed_stats['config_channels'] = self.config_exchange.get_channel_statistics()
            
            if self.performance_exchange:
                detailed_stats['performance_channels'] = self.performance_exchange.get_channel_statistics()
            
            # 发送统计信息更新信号
            self.statistics_updated.emit(detailed_stats)
            
        except Exception as e:
            self.logger.error(f"更新统计信息失败: {e}")
            self.error_occurred.emit("statistics_update", str(e))
    
    def get_global_statistics(self) -> Dict:
        """获取全局统计信息"""
        self._update_statistics()
        return self.global_statistics.copy()
    
    def get_channel_list(self) -> List[str]:
        """获取所有通道列表"""
        return self.core_exchange.list_channels()
    
    def get_channel_info(self, channel_name: str) -> Optional[Dict]:
        """获取通道信息"""
        return self.core_exchange.get_channel_info(channel_name)
    
    def clear_channel(self, channel_name: str) -> bool:
        """清空指定通道"""
        success = self.core_exchange.clear_channel(channel_name)
        if success:
            self.logger.info(f"清空通道: {channel_name}")
        return success
    
    def close_channel(self, channel_name: str) -> bool:
        """关闭指定通道"""
        success = self.core_exchange.close_channel(channel_name)
        if success:
            self.logger.info(f"关闭通道: {channel_name}")
        return success
    
    def get_system_health(self) -> Dict:
        """获取系统健康状态"""
        stats = self.get_global_statistics()
        channels = self.get_channel_list()
        
        health = {
            'overall_status': True,
            'memory_status': 'normal',
            'channel_status': 'normal',
            'performance_status': 'normal',
            'details': {
                'total_channels': len(channels),
                'memory_usage_mb': stats.get('memory_usage_mb', 0),
                'uptime_hours': stats.get('uptime_seconds', 0) / 3600,
                'packet_throughput': stats.get('total_packets_sent', 0) / max(stats.get('uptime_seconds', 1), 1)
            }
        }
        
        # 检查内存状态
        memory_usage = stats.get('memory_usage_mb', 0)
        if memory_usage > self.config.max_total_memory_mb * 0.9:
            health['memory_status'] = 'critical'
            health['overall_status'] = False
        elif memory_usage > self.config.max_total_memory_mb * 0.7:
            health['memory_status'] = 'warning'
        
        # 检查通道状态
        if len(channels) == 0:
            health['channel_status'] = 'warning'
        
        # 检查性能状态
        packet_rate = health['details']['packet_throughput']
        if packet_rate < 1.0:  # 每秒少于1个包可能有问题
            health['performance_status'] = 'warning'
        
        return health
    
    def optimize_performance(self):
        """优化性能"""
        try:
            # 清理过期缓存
            self.core_exchange.shared_cache.cleanup_expired()
            
            # 获取通道信息并优化
            channels = self.get_channel_list()
            for channel_name in channels:
                info = self.get_channel_info(channel_name)
                if info and info.get('size', 0) > 50:  # 队列过大时清理
                    self.logger.warning(f"通道 {channel_name} 队列过大，进行清理")
                    self.clear_channel(channel_name)
            
            self.logger.info("性能优化完成")
            
        except Exception as e:
            self.logger.error(f"性能优化失败: {e}")
            self.error_occurred.emit("performance_optimization", str(e))
    
    def shutdown(self):
        """关闭数据管理器"""
        self.logger.info("正在关闭线程安全数据管理器...")
        
        # 停止监控定时器
        if hasattr(self, 'monitoring_timer'):
            self.monitoring_timer.stop()
        
        # 关闭核心交换器
        self.core_exchange.shutdown()
        
        self.logger.info("线程安全数据管理器已关闭")
