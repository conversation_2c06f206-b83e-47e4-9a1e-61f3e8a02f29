#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型文件引用更新脚本
将所有.pt/.pth模型文件引用更改为.rknn格式
"""

import os
import re
import shutil
from pathlib import Path
from typing import Dict, List, Tuple
import json

from utils.logger import get_logger


class ModelReferenceUpdater:
    """模型引用更新器"""
    
    def __init__(self, dry_run: bool = False):
        """
        初始化更新器
        
        Args:
            dry_run: 是否为试运行模式
        """
        self.logger = get_logger("ModelReferenceUpdater")
        self.dry_run = dry_run
        
        # 模型路径映射规则
        self.model_path_mappings = {
            # 人体检测模型
            "yolov8n.pt": "models/human_detection/yolov8n_human.rknn",
            "yolov8s.pt": "models/human_detection/yolov8s_human.rknn",
            "yolov8m.pt": "models/human_detection/yolov8m_human.rknn",
            "yolov8l.pt": "models/human_detection/yolov8l_human.rknn",
            "yolov8x.pt": "models/human_detection/yolov8x_human.rknn",
            
            # 火焰烟雾检测模型
            "best.pt": "models/fire_detection/fire_smoke.rknn",
            "best-1.pt": "models/fire_detection/fire.rknn",
            "best-2.pt": "models/fire_detection/smoke.rknn",
            "fire_model.pt": "models/fire_detection/fire.rknn",
            "smoke_model.pt": "models/fire_detection/smoke.rknn",
            "flame_yolo.pt": "models/fire_detection/fire.rknn",
            "smoke_yolo.pt": "models/fire_detection/smoke.rknn",
            "fire_combined_yolo.pt": "models/fire_detection/fire_smoke.rknn",
            "fire_smoke_yolov8.pt": "models/fire_detection/fire_smoke.rknn",
            "fire_smoke_yolov8n.pt": "models/fire_detection/fire_smoke_n.rknn",
            "fire_smoke_yolov8s.pt": "models/fire_detection/fire_smoke_s.rknn",
            
            # 通用模型路径模式
            "models/best.pt": "models/fire_detection/fire_smoke.rknn",
            "models/fire_detection/best.pt": "models/fire_detection/fire_smoke.rknn",
            "models/human_detection/yolov8n.pt": "models/human_detection/yolov8n_human.rknn",
            "2/models/best-1.pt": "models/fire_detection/fire.rknn",
            "models/fs/best-1.pt": "models/fire_detection/fire.rknn",
            "models/fs/best-2.pt": "models/fire_detection/smoke.rknn",
        }
        
        # 需要更新的文件列表
        self.target_files = [
            "2/config/config.yaml",
            "2/modules/config_module.py",
            "config/human_detection_config.py",
            "config/new_object_detection_config.py",
            "core/frame_processor.py",
            "detection/core/adaptive_fire_smoke_detector.py",
            "detection/core/integrated_fire_smoke_detector.py",
            "detection/utils/fire_yolo_utils.py",
            "detection/utils/yolo_utils.py",
        ]
        
        # 更新统计
        self.updated_files = []
        self.updated_references = []
        self.backup_dir = Path("model_reference_backup")
    
    def update_all_references(self) -> bool:
        """更新所有模型引用"""
        try:
            self.logger.info("开始更新模型文件引用")
            
            # 创建备份目录
            if not self.dry_run:
                self.backup_dir.mkdir(exist_ok=True)
            
            # 更新每个文件
            for file_path in self.target_files:
                if Path(file_path).exists():
                    self._update_file(file_path)
                else:
                    self.logger.warning(f"文件不存在，跳过: {file_path}")
            
            # 生成报告
            self._generate_report()
            
            self.logger.info(f"模型引用更新完成，共更新 {len(self.updated_files)} 个文件")
            return True
            
        except Exception as e:
            self.logger.error(f"更新模型引用失败: {e}")
            return False
    
    def _update_file(self, file_path: str):
        """更新单个文件"""
        try:
            self.logger.info(f"正在更新文件: {file_path}")
            
            # 读取文件内容
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            file_updated = False
            
            # 应用模型路径映射
            for old_path, new_path in self.model_path_mappings.items():
                if old_path in content:
                    content = content.replace(old_path, new_path)
                    file_updated = True
                    self.updated_references.append(f"{file_path}: {old_path} -> {new_path}")
                    self.logger.info(f"  替换: {old_path} -> {new_path}")
            
            # 使用正则表达式处理更复杂的模式
            content, regex_updated = self._apply_regex_replacements(content, file_path)
            if regex_updated:
                file_updated = True
            
            # 如果文件有更新，写回文件
            if file_updated:
                if not self.dry_run:
                    # 备份原文件
                    backup_path = self.backup_dir / Path(file_path).name
                    shutil.copy2(file_path, backup_path)
                    
                    # 写入更新后的内容
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                
                self.updated_files.append(file_path)
                self.logger.info(f"  ✅ 文件已更新: {file_path}")
            else:
                self.logger.info(f"  ⚪ 文件无需更新: {file_path}")
                
        except Exception as e:
            self.logger.error(f"更新文件失败 [{file_path}]: {e}")
    
    def _apply_regex_replacements(self, content: str, file_path: str) -> Tuple[str, bool]:
        """应用正则表达式替换"""
        updated = False
        
        # 模式1: 匹配 .pt 和 .pth 扩展名，但保留路径结构
        patterns = [
            # 匹配引号内的 .pt 文件
            (r'"([^"]*?)\.pt"', lambda m: f'"{self._convert_pt_path(m.group(1))}.rknn"'),
            (r'"([^"]*?)\.pth"', lambda m: f'"{self._convert_pt_path(m.group(1))}.rknn"'),
            
            # 匹配单引号内的 .pt 文件
            (r"'([^']*?)\.pt'", lambda m: f"'{self._convert_pt_path(m.group(1))}.rknn'"),
            (r"'([^']*?)\.pth'", lambda m: f"'{self._convert_pt_path(m.group(1))}.rknn'"),
            
            # 匹配配置文件中的路径
            (r'weights_path:\s*"([^"]*?)\.pt"', lambda m: f'weights_path: "{self._convert_pt_path(m.group(1))}.rknn"'),
            (r'model_path:\s*"([^"]*?)\.pt"', lambda m: f'model_path: "{self._convert_pt_path(m.group(1))}.rknn"'),
        ]
        
        for pattern, replacement in patterns:
            new_content = re.sub(pattern, replacement, content)
            if new_content != content:
                updated = True
                content = new_content
                self.logger.info(f"  应用正则替换: {pattern}")
        
        return content, updated
    
    def _convert_pt_path(self, path: str) -> str:
        """转换PyTorch路径为RKNN路径"""
        # 如果已经在映射表中，直接返回
        full_pt_path = f"{path}.pt"
        if full_pt_path in self.model_path_mappings:
            return self.model_path_mappings[full_pt_path].replace('.rknn', '')
        
        # 根据路径特征推断RKNN路径
        if 'human' in path.lower() or 'yolo' in path.lower():
            # 人体检测模型
            base_name = Path(path).name
            return f"models/human_detection/{base_name}_human"
        elif any(keyword in path.lower() for keyword in ['fire', 'smoke', 'flame']):
            # 火焰烟雾检测模型
            base_name = Path(path).name
            if 'fire' in base_name.lower() and 'smoke' not in base_name.lower():
                return f"models/fire_detection/fire"
            elif 'smoke' in base_name.lower() and 'fire' not in base_name.lower():
                return f"models/fire_detection/smoke"
            else:
                return f"models/fire_detection/fire_smoke"
        else:
            # 通用模型
            base_name = Path(path).name
            return f"models/general/{base_name}"
    
    def _generate_report(self):
        """生成更新报告"""
        report = {
            "update_summary": {
                "total_files_updated": len(self.updated_files),
                "total_references_updated": len(self.updated_references),
                "dry_run": self.dry_run
            },
            "updated_files": self.updated_files,
            "updated_references": self.updated_references,
            "model_path_mappings": self.model_path_mappings
        }
        
        report_path = "model_reference_update_report.json"
        if not self.dry_run:
            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
        
        # 打印摘要
        print("\n" + "="*60)
        print("模型文件引用更新报告")
        print("="*60)
        print(f"更新模式: {'试运行' if self.dry_run else '实际更新'}")
        print(f"更新文件数: {len(self.updated_files)}")
        print(f"更新引用数: {len(self.updated_references)}")
        
        if self.updated_files:
            print("\n更新的文件:")
            for file_path in self.updated_files:
                print(f"  ✅ {file_path}")
        
        if self.updated_references:
            print("\n更新的引用:")
            for ref in self.updated_references[:10]:  # 只显示前10个
                print(f"  🔄 {ref}")
            if len(self.updated_references) > 10:
                print(f"  ... 还有 {len(self.updated_references) - 10} 个更新")
        
        print("="*60)


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="模型文件引用更新工具")
    parser.add_argument("--dry-run", action="store_true", 
                       help="试运行模式，不实际修改文件")
    
    args = parser.parse_args()
    
    print("🔄 开始更新模型文件引用")
    
    updater = ModelReferenceUpdater(dry_run=args.dry_run)
    success = updater.update_all_references()
    
    if success:
        print("\n🎉 模型引用更新完成!")
        if args.dry_run:
            print("💡 这是试运行模式，没有实际修改文件")
            print("💡 要执行实际更新，请运行: python update_model_references.py")
    else:
        print("\n❌ 模型引用更新失败")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
