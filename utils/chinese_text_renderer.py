#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中文文字渲染工具
使用 Pillow 库绘制中文文字，然后转换回 OpenCV 格式
"""

import cv2
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import os
from typing import Tuple, Optional

class ChineseTextRenderer:
    """中文文字渲染器"""
    
    def __init__(self, font_path: Optional[str] = None, default_size: int = 20):
        """
        初始化中文文字渲染器
        
        Args:
            font_path: 字体文件路径，如果为None则使用系统默认字体
            default_size: 默认字体大小
        """
        self.default_size = default_size
        self.font_cache = {}  # 字体缓存
        
        # 尝试找到合适的中文字体
        self.font_path = self._find_chinese_font(font_path)
        
    def _find_chinese_font(self, font_path: Optional[str] = None) -> Optional[str]:
        """查找可用的中文字体"""
        if font_path and os.path.exists(font_path):
            return font_path

        import platform
        system = platform.system()

        if system == "Windows":
            # Windows 系统常见中文字体路径 - 使用环境变量避免硬编码
            import os
            # 获取Windows目录，避免硬编码路径
            windows_dir = os.environ.get('WINDIR')
            if not windows_dir:
                # 如果环境变量不存在，尝试常见位置
                import platform
                if platform.machine().endswith('64'):
                    windows_dir = os.environ.get('SystemRoot', '/Windows')
                else:
                    windows_dir = '/Windows'
            fonts_dir = f"{windows_dir}/Fonts"
            font_paths = [
                f"{fonts_dir}/msyh.ttc",      # 微软雅黑
                f"{fonts_dir}/simhei.ttf",    # 黑体
                f"{fonts_dir}/simsun.ttc",    # 宋体
                f"{fonts_dir}/simkai.ttf",    # 楷体
            ]
        elif system == "Linux":
            # Linux 系统常见中文字体路径
            font_paths = [
                "/usr/share/fonts/truetype/wqy/wqy-microhei.ttc",     # 文泉驿微米黑
                "/usr/share/fonts/truetype/wqy/wqy-zenhei.ttc",       # 文泉驿正黑
                "/usr/share/fonts/truetype/arphic/ukai.ttc",          # AR PL UKai CN
                "/usr/share/fonts/truetype/arphic/uming.ttc",         # AR PL UMing CN
                "/usr/share/fonts/opentype/noto/NotoSansCJK-Regular.ttc",  # Noto Sans CJK
                "/usr/share/fonts/truetype/noto-cjk/NotoSansCJK-Regular.ttc",  # Noto Sans CJK (另一个位置)
                "/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf",  # Liberation Sans
                "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",    # DejaVu Sans (备用)
                "/usr/share/fonts/TTF/DejaVuSans.ttf",               # DejaVu Sans (Arch Linux)
                "/usr/share/fonts/truetype/droid/DroidSansFallback.ttf",  # Droid Sans Fallback
                "/System/Library/Fonts/PingFang.ttc",                # macOS PingFang (如果存在)
            ]
        elif system == "Darwin":  # macOS
            # macOS 系统常见中文字体路径
            font_paths = [
                "/System/Library/Fonts/PingFang.ttc",                # 苹方
                "/System/Library/Fonts/STHeiti Light.ttc",           # 华文黑体
                "/System/Library/Fonts/Hiragino Sans GB.ttc",        # 冬青黑体
                "/Library/Fonts/Arial Unicode MS.ttf",               # Arial Unicode MS
            ]
        else:
            font_paths = []

        for font in font_paths:
            if os.path.exists(font):
                print(f"✅ 找到中文字体: {font}")
                return font

        print("⚠️ 未找到中文字体文件，将使用默认字体")
        return None
    
    def _get_font(self, size: int) -> ImageFont.ImageFont:
        """获取指定大小的字体对象"""
        if size in self.font_cache:
            return self.font_cache[size]
            
        try:
            if self.font_path:
                font = ImageFont.truetype(self.font_path, size)
            else:
                font = ImageFont.load_default()
        except Exception as e:
            print(f"⚠️ 加载字体失败: {e}，使用默认字体")
            font = ImageFont.load_default()
            
        self.font_cache[size] = font
        return font
    
    def get_text_size(self, text: str, font_size: int) -> Tuple[int, int]:
        """获取文字尺寸"""
        font = self._get_font(font_size)
        
        # 创建临时图像来测量文字尺寸
        temp_img = Image.new('RGB', (1, 1))
        draw = ImageDraw.Draw(temp_img)
        
        # 获取文字边界框
        bbox = draw.textbbox((0, 0), text, font=font)
        width = bbox[2] - bbox[0]
        height = bbox[3] - bbox[1]
        
        return width, height
    
    def draw_text_on_opencv_image(self, 
                                  cv_image: np.ndarray,
                                  text: str,
                                  position: Tuple[int, int],
                                  font_size: int = None,
                                  color: Tuple[int, int, int] = (255, 255, 255),
                                  background_color: Optional[Tuple[int, int, int]] = None,
                                  padding: int = 5) -> np.ndarray:
        """
        在OpenCV图像上绘制中文文字
        
        Args:
            cv_image: OpenCV图像 (BGR格式)
            text: 要绘制的文字
            position: 文字位置 (x, y)
            font_size: 字体大小
            color: 文字颜色 (BGR格式)
            background_color: 背景颜色 (BGR格式)，None表示透明
            padding: 背景边距
            
        Returns:
            绘制文字后的图像
        """
        if font_size is None:
            font_size = self.default_size
            
        # 转换BGR到RGB
        rgb_color = (color[2], color[1], color[0])
        
        # 获取文字尺寸
        text_width, text_height = self.get_text_size(text, font_size)
        
        # 计算文字区域
        x, y = position
        text_area_width = text_width + 2 * padding
        text_area_height = text_height + 2 * padding
        
        # 边界检查
        img_height, img_width = cv_image.shape[:2]
        if x + text_area_width > img_width:
            x = img_width - text_area_width
        if y + text_area_height > img_height:
            y = img_height - text_area_height
        if x < 0:
            x = 0
        if y < 0:
            y = 0
            
        # 提取文字区域
        text_region = cv_image[y:y+text_area_height, x:x+text_area_width].copy()
        
        # 转换为PIL图像
        pil_image = Image.fromarray(cv2.cvtColor(text_region, cv2.COLOR_BGR2RGB))
        draw = ImageDraw.Draw(pil_image)
        
        # 绘制背景
        if background_color is not None:
            bg_rgb = (background_color[2], background_color[1], background_color[0])
            draw.rectangle([0, 0, text_area_width, text_area_height], fill=bg_rgb)
        
        # 绘制文字
        font = self._get_font(font_size)
        draw.text((padding, padding), text, font=font, fill=rgb_color)
        
        # 转换回OpenCV格式
        text_region_with_text = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
        
        # 将文字区域复制回原图像
        result_image = cv_image.copy()
        result_image[y:y+text_area_height, x:x+text_area_width] = text_region_with_text
        
        return result_image
    
    def create_text_image(self, 
                         text: str,
                         font_size: int = None,
                         color: Tuple[int, int, int] = (255, 255, 255),
                         background_color: Tuple[int, int, int] = (0, 0, 0),
                         padding: int = 10) -> np.ndarray:
        """
        创建只包含文字的图像
        
        Args:
            text: 要绘制的文字
            font_size: 字体大小
            color: 文字颜色 (BGR格式)
            background_color: 背景颜色 (BGR格式)
            padding: 边距
            
        Returns:
            包含文字的OpenCV图像
        """
        if font_size is None:
            font_size = self.default_size
            
        # 转换BGR到RGB
        rgb_color = (color[2], color[1], color[0])
        bg_rgb = (background_color[2], background_color[1], background_color[0])
        
        # 获取文字尺寸
        text_width, text_height = self.get_text_size(text, font_size)
        
        # 创建图像
        img_width = text_width + 2 * padding
        img_height = text_height + 2 * padding
        
        pil_image = Image.new('RGB', (img_width, img_height), bg_rgb)
        draw = ImageDraw.Draw(pil_image)
        
        # 绘制文字
        font = self._get_font(font_size)
        draw.text((padding, padding), text, font=font, fill=rgb_color)
        
        # 转换为OpenCV格式
        cv_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
        
        return cv_image


# 全局渲染器实例
_chinese_renderer = None

def get_chinese_renderer() -> ChineseTextRenderer:
    """获取全局中文渲染器实例"""
    global _chinese_renderer
    if _chinese_renderer is None:
        _chinese_renderer = ChineseTextRenderer()
    return _chinese_renderer

def draw_chinese_text(cv_image: np.ndarray,
                     text: str,
                     position: Tuple[int, int],
                     font_size: int = 20,
                     color: Tuple[int, int, int] = (255, 255, 255),
                     background_color: Optional[Tuple[int, int, int]] = None,
                     padding: int = 5) -> np.ndarray:
    """
    便捷函数：在OpenCV图像上绘制中文文字
    
    Args:
        cv_image: OpenCV图像
        text: 要绘制的文字
        position: 文字位置 (x, y)
        font_size: 字体大小
        color: 文字颜色 (BGR格式)
        background_color: 背景颜色 (BGR格式)，None表示透明
        padding: 背景边距
        
    Returns:
        绘制文字后的图像
    """
    renderer = get_chinese_renderer()
    return renderer.draw_text_on_opencv_image(
        cv_image, text, position, font_size, color, background_color, padding
    )
