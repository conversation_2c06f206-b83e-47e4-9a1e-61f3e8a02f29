#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
跨平台字体配置工具
为不同操作系统提供合适的字体配置
"""

import platform
import os
from typing import List, Optional


class CrossPlatformFontManager:
    """跨平台字体管理器"""
    
    def __init__(self):
        self.system = platform.system()
        self.font_families = self._get_system_font_families()
    
    def _get_system_font_families(self) -> List[str]:
        """获取系统适用的字体族列表"""
        if self.system == "Windows":
            return [
                "Microsoft YaHei",  # 微软雅黑
                "SimHei",           # 黑体
                "SimSun",           # 宋体
                "Arial",            # 备用英文字体
                "sans-serif"        # 系统默认无衬线字体
            ]
        elif self.system == "Linux":
            return [
                "WenQuanYi Micro Hei",  # 文泉驿微米黑
                "WenQuanYi Zen Hei",    # 文泉驿正黑
                "Noto Sans CJK SC",     # Noto Sans 中文简体
                "DejaVu Sans",          # DejaVu Sans
                "Liberation Sans",      # Liberation Sans
                "Arial",                # Arial (如果安装)
                "sans-serif"            # 系统默认
            ]
        elif self.system == "Darwin":  # macOS
            return [
                "PingFang SC",          # 苹方简体
                "Hiragino Sans GB",     # 冬青黑体简体中文
                "STHeiti",              # 华文黑体
                "Arial",                # Arial
                "Helvetica",            # Helvetica
                "sans-serif"            # 系统默认
            ]
        else:
            # 其他系统使用通用字体
            return [
                "Arial",
                "Helvetica",
                "DejaVu Sans",
                "Liberation Sans",
                "sans-serif"
            ]
    
    def get_font_family_string(self) -> str:
        """获取CSS样式的字体族字符串"""
        return ", ".join(f'"{font}"' if " " in font else font 
                        for font in self.font_families)
    
    def get_primary_font(self) -> str:
        """获取主要字体名称"""
        return self.font_families[0] if self.font_families else "Arial"
    
    def get_fallback_fonts(self) -> List[str]:
        """获取备用字体列表"""
        return self.font_families[1:] if len(self.font_families) > 1 else ["Arial"]
    
    def check_font_availability(self) -> dict:
        """检查字体可用性（简单检查）"""
        result = {
            "system": self.system,
            "recommended_fonts": self.font_families,
            "primary_font": self.get_primary_font()
        }
        
        # 对于Linux系统，检查常见字体包是否安装
        if self.system == "Linux":
            font_packages = {
                "fonts-wqy-microhei": "文泉驿微米黑",
                "fonts-wqy-zenhei": "文泉驿正黑", 
                "fonts-noto-cjk": "Noto CJK字体",
                "fonts-arphic-ukai": "AR PL UKai",
                "fonts-arphic-uming": "AR PL UMing"
            }
            result["suggested_packages"] = font_packages
        
        return result
    
    @staticmethod
    def get_installation_instructions() -> dict:
        """获取字体安装说明"""
        system = platform.system()
        
        if system == "Linux":
            return {
                "system": "Linux",
                "instructions": [
                    "安装中文字体包:",
                    "Ubuntu/Debian: sudo apt-get install fonts-wqy-microhei fonts-wqy-zenhei",
                    "CentOS/RHEL: sudo yum install wqy-microhei-fonts wqy-zenhei-fonts",
                    "Fedora: sudo dnf install wqy-microhei-fonts wqy-zenhei-fonts",
                    "Arch Linux: sudo pacman -S wqy-microhei wqy-zenhei"
                ]
            }
        elif system == "Darwin":
            return {
                "system": "macOS", 
                "instructions": [
                    "macOS通常已包含中文字体",
                    "如需额外字体，可通过App Store安装或手动安装字体文件"
                ]
            }
        elif system == "Windows":
            return {
                "system": "Windows",
                "instructions": [
                    "Windows通常已包含中文字体",
                    "如需额外字体，可通过控制面板->字体进行安装"
                ]
            }
        else:
            return {
                "system": "Unknown",
                "instructions": ["请根据您的操作系统安装相应的中文字体"]
            }


# 全局字体管理器实例
font_manager = CrossPlatformFontManager()


def get_ui_font_family() -> str:
    """获取UI使用的字体族字符串"""
    return font_manager.get_font_family_string()


def get_primary_ui_font() -> str:
    """获取主要UI字体"""
    return font_manager.get_primary_font()


def print_font_info():
    """打印字体信息（用于调试）"""
    info = font_manager.check_font_availability()
    print(f"🖥️  操作系统: {info['system']}")
    print(f"🔤 主要字体: {info['primary_font']}")
    print(f"📝 推荐字体: {', '.join(info['recommended_fonts'])}")
    
    if "suggested_packages" in info:
        print("📦 建议安装的字体包:")
        for package, description in info["suggested_packages"].items():
            print(f"   - {package}: {description}")
    
    instructions = CrossPlatformFontManager.get_installation_instructions()
    if instructions["instructions"]:
        print(f"\n💡 {instructions['system']} 字体安装说明:")
        for instruction in instructions["instructions"]:
            print(f"   {instruction}")


if __name__ == "__main__":
    print_font_info()
