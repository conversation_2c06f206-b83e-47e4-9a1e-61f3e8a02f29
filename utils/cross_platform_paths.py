#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
跨平台路径管理工具
确保所有路径操作都是跨平台兼容的
"""

import os
import platform
from pathlib import Path
from typing import Union, Optional, List
import sys


class CrossPlatformPathManager:
    """跨平台路径管理器"""
    
    def __init__(self, project_root: Optional[Union[str, Path]] = None):
        """
        初始化路径管理器
        
        Args:
            project_root: 项目根目录，如果为None则自动检测
        """
        self.system = platform.system()
        self.project_root = self._detect_project_root(project_root)
        
    def _detect_project_root(self, project_root: Optional[Union[str, Path]] = None) -> Path:
        """检测项目根目录"""
        if project_root:
            return Path(project_root).resolve()
        
        # 尝试从当前文件位置推断项目根目录
        current_file = Path(__file__).resolve()
        
        # 向上查找包含main.py的目录
        for parent in current_file.parents:
            if (parent / 'main.py').exists():
                return parent
        
        # 如果找不到，使用当前工作目录
        return Path.cwd()
    
    def get_absolute_path(self, relative_path: Union[str, Path]) -> Path:
        """
        将相对路径转换为绝对路径
        
        Args:
            relative_path: 相对路径
            
        Returns:
            绝对路径
        """
        path = Path(relative_path)
        if path.is_absolute():
            return path
        return (self.project_root / path).resolve()
    
    def get_config_path(self, config_file: str) -> Path:
        """获取配置文件路径"""
        return self.get_absolute_path(f"config/{config_file}")
    
    def get_model_path(self, model_file: str) -> Path:
        """获取模型文件路径"""
        return self.get_absolute_path(f"models/{model_file}")
    
    def get_log_path(self, log_file: str) -> Path:
        """获取日志文件路径"""
        return self.get_absolute_path(f"logs/{log_file}")
    
    def get_output_path(self, output_file: str) -> Path:
        """获取输出文件路径"""
        return self.get_absolute_path(f"output/{output_file}")
    
    def get_data_path(self, data_file: str) -> Path:
        """获取数据文件路径"""
        return self.get_absolute_path(f"data/{data_file}")
    
    def ensure_directory(self, path: Union[str, Path]) -> Path:
        """
        确保目录存在
        
        Args:
            path: 目录路径
            
        Returns:
            目录的绝对路径
        """
        abs_path = self.get_absolute_path(path)
        abs_path.mkdir(parents=True, exist_ok=True)
        return abs_path
    
    def get_temp_dir(self) -> Path:
        """获取临时目录"""
        if self.system == "Linux":
            temp_dir = Path("/tmp") / "thermal_camera"
        elif self.system == "Windows":
            # 使用系统临时目录，避免硬编码路径
            import tempfile
            temp_dir = Path(tempfile.gettempdir()) / "thermal_camera"
        else:  # macOS
            temp_dir = Path("/tmp") / "thermal_camera"
        
        temp_dir.mkdir(parents=True, exist_ok=True)
        return temp_dir
    
    def get_font_paths(self) -> List[str]:
        """获取系统字体路径"""
        if self.system == "Windows":
            # 使用环境变量获取Windows字体目录，避免硬编码
            windows_dir = os.environ.get('WINDIR')
            if not windows_dir:
                # 如果环境变量不存在，使用相对路径
                windows_dir = os.environ.get('SystemRoot', '/Windows')
            fonts_dir = f"{windows_dir}/Fonts"
            return [
                f"{fonts_dir}/msyh.ttc",      # 微软雅黑
                f"{fonts_dir}/simhei.ttf",    # 黑体
                f"{fonts_dir}/simsun.ttc",    # 宋体
                f"{fonts_dir}/simkai.ttf",    # 楷体
            ]
        elif self.system == "Linux":
            return [
                "/usr/share/fonts/truetype/wqy/wqy-microhei.ttc",
                "/usr/share/fonts/truetype/wqy/wqy-zenhei.ttc",
                "/usr/share/fonts/truetype/arphic/ukai.ttc",
                "/usr/share/fonts/truetype/arphic/uming.ttc",
                "/usr/share/fonts/opentype/noto/NotoSansCJK-Regular.ttc",
                "/usr/share/fonts/truetype/noto-cjk/NotoSansCJK-Regular.ttc",
                "/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf",
                "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
                "/usr/share/fonts/TTF/DejaVuSans.ttf",
                "/usr/share/fonts/truetype/droid/DroidSansFallback.ttf",
            ]
        elif self.system == "Darwin":  # macOS
            return [
                "/System/Library/Fonts/PingFang.ttc",
                "/System/Library/Fonts/STHeiti Light.ttc",
                "/System/Library/Fonts/Hiragino Sans GB.ttc",
                "/Library/Fonts/Arial Unicode MS.ttf",
            ]
        else:
            return []
    
    def find_available_font(self) -> Optional[str]:
        """查找可用的中文字体"""
        for font_path in self.get_font_paths():
            if Path(font_path).exists():
                print(f"✅ 找到中文字体: {font_path}")
                return font_path
        
        print("⚠️ 未找到中文字体文件，将使用默认字体")
        return None
    
    def get_python_executable(self) -> Path:
        """获取Python可执行文件路径"""
        return Path(sys.executable)
    
    def get_virtual_env_path(self) -> Optional[Path]:
        """获取虚拟环境路径"""
        venv_path = os.environ.get('VIRTUAL_ENV')
        if venv_path:
            return Path(venv_path)
        return None
    
    def normalize_path(self, path: Union[str, Path]) -> str:
        """
        标准化路径格式
        
        Args:
            path: 输入路径
            
        Returns:
            标准化后的路径字符串
        """
        return str(Path(path).resolve())
    
    def get_system_info(self) -> dict:
        """获取系统信息"""
        return {
            'system': self.system,
            'project_root': str(self.project_root),
            'python_executable': str(self.get_python_executable()),
            'virtual_env': str(self.get_virtual_env_path()) if self.get_virtual_env_path() else None,
            'temp_dir': str(self.get_temp_dir()),
            'available_font': self.find_available_font()
        }


# 全局路径管理器实例
_global_path_manager = None


def get_path_manager() -> CrossPlatformPathManager:
    """获取全局路径管理器实例"""
    global _global_path_manager
    if _global_path_manager is None:
        _global_path_manager = CrossPlatformPathManager()
    return _global_path_manager


def safe_path(path: Union[str, Path]) -> Path:
    """
    安全的路径转换函数
    
    Args:
        path: 输入路径
        
    Returns:
        跨平台兼容的Path对象
    """
    return get_path_manager().get_absolute_path(path)


def ensure_dir(path: Union[str, Path]) -> Path:
    """
    确保目录存在的便捷函数
    
    Args:
        path: 目录路径
        
    Returns:
        目录的绝对路径
    """
    return get_path_manager().ensure_directory(path)


if __name__ == "__main__":
    # 测试代码
    pm = CrossPlatformPathManager()
    print("系统信息:")
    for key, value in pm.get_system_info().items():
        print(f"  {key}: {value}")
