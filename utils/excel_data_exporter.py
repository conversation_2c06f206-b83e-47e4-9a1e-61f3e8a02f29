#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel数据导出器 V3.1
基于火灾监控数据库结构设计，支持6个核心工作表
用于将热源检测数据、预警信息、系统性能等关键数据导出到Excel文件
火焰和烟雾的详细数据由模块化系统单独处理
"""

import os
import pandas as pd
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Any, Union
from pathlib import Path
import threading
import time
from dataclasses import asdict
import uuid
import shutil

# 优先使用openpyxl进行直接操作
try:
    from openpyxl import Workbook, load_workbook
    from openpyxl.utils.dataframe import dataframe_to_rows
    from openpyxl.styles import Font, PatternFill, Alignment
    OPENPYXL_AVAILABLE = True
except ImportError:
    OPENPYXL_AVAILABLE = False

from utils.logger import get_logger


class ExcelDataExporter:
    """Excel数据导出器 V3.0 - 火灾监控数据库结构"""

    def __init__(self, export_dir: str = "data_exports", database_filename: str = "fire_monitoring_database.xlsx"):
        """
        初始化Excel数据导出器

        Args:
            export_dir: 导出文件目录
            database_filename: 数据库文件名
        """
        self.export_dir = Path(export_dir)
        self.export_dir.mkdir(exist_ok=True)
        self.database_filename = database_filename
        self.database_filepath = self.export_dir / database_filename

        self.logger = get_logger("ExcelDataExporter_V3")

        # V3.1 数据缓存 - 6个核心工作表对应的数据缓存
        self.heat_source_log = []           # 热源事件日志
        # 移除火焰和烟雾日志 - 由模块化系统处理
        # self.flame_log = []                 # 火焰事件日志 (已移除)
        # self.smoke_log = []                 # 烟雾事件日志 (已移除)
        # self.flame_analysis_log = []        # 火焰分析详情日志 (已移除)
        self.alert_log = []                 # 预警事件日志
        self.system_performance_log = []    # 系统性能与全局状态日志
        self.monitored_assets = []          # 重点监控资产
        self.annotation_log = []            # 标注日志
        self.system_config = []             # 系统与设备配置

        # 导出配置
        self.auto_export_enabled = True
        self.export_interval_minutes = 30  # 30分钟自动导出一次
        self.max_cache_size = 1000  # 最大缓存条数
        self.real_time_write = True  # 启用实时写入
        self.use_openpyxl_direct = OPENPYXL_AVAILABLE  # 优先使用openpyxl直接操作

        # 线程锁，确保文件操作安全
        self._file_lock = threading.Lock()

        # 初始化数据库文件
        self._initialize_database()

        # 启动自动导出线程（用于定期备份和清理）
        self._start_auto_export_thread()

        self.logger.info(f"✅ Excel数据导出器V3.1初始化完成，数据库文件: {self.database_filepath}")
        self.logger.info(f"🔧 使用引擎: {'openpyxl直接操作' if self.use_openpyxl_direct else 'pandas+openpyxl'}")
        self.logger.info(f"⚡ 实时写入: {'启用' if self.real_time_write else '禁用'}")
        self.logger.info(f"📊 核心工作表: 6个 (热源、预警、性能、资产、标注、配置)")
    
    def _initialize_database(self):
        """初始化数据库文件，创建6个核心工作表的基础结构"""
        try:
            # 如果数据库文件不存在，创建初始结构
            if not self.database_filepath.exists():
                self._create_initial_database_structure()
                self.logger.info(f"📊 创建新的数据库文件: {self.database_filepath}")
            else:
                self.logger.info(f"📊 使用现有数据库文件: {self.database_filepath}")
                # 检查并更新现有数据库的表结构
                self._check_and_update_database_structure()

        except Exception as e:
            self.logger.error(f"❌ 初始化数据库失败: {e}")

    def _create_initial_database_structure(self):
        """创建初始数据库结构"""
        with pd.ExcelWriter(self.database_filepath, engine='openpyxl') as writer:
            # 创建空的工作表结构
            self._create_empty_worksheets(writer)

    def _create_empty_worksheets(self, writer):
        """创建空的工作表 - V3.1版本，6个核心工作表"""
        # 1. HeatSource_Log (热源事件日志)
        heat_source_columns = ['Event_ID', 'Timestamp', 'Camera_ID', 'Tracking_ID', 'Max_Temperature',
                              'Min_Temperature', 'Avg_Temperature', 'Area', 'Avg_Temp_Change_Rate', 'Thermal_Bbox',
                              'Confidence', 'In_Asset_ID', 'Snapshot_Filename']
        pd.DataFrame(columns=heat_source_columns).to_excel(writer, sheet_name='HeatSource_Log', index=False)

        # 移除火焰和烟雾日志 - 由模块化系统处理
        # 2. Flame_Log (火焰事件日志) - 已移除，由模块化系统处理
        # 3. Smoke_Log (烟雾事件日志) - 已移除，由模块化系统处理
        # 4. Flame_Analysis_Log (火焰分析详情日志) - 已移除，由模块化系统处理

        # 2. Alert_Log (预警事件日志)
        alert_columns = ['Alert_ID', 'Trigger_Event_ID', 'Tracking_ID', 'Alert_Level', 'Alert_Asset_ID',
                        'Alert_Asset_Name', 'Alert_Start_Time', 'Alert_End_Time', 'Alert_Duration_Sec',
                        'Trigger_Reason', 'Verification_Status', 'Verifier', 'Verification_Time', 'Verification_Notes']
        pd.DataFrame(columns=alert_columns).to_excel(writer, sheet_name='Alert_Log', index=False)

        # 3. System_Performance_Log (系统性能与全局状态日志)
        performance_columns = ['Timestamp', 'Camera_ID', 'FPS', 'Processing_Latency', 'Memory_Usage',
                              'Total_Heat_Sources', 'Potential_Flame_Count', 'Smoke_Count', 'Human_Count', 'Active_Alerts']
        pd.DataFrame(columns=performance_columns).to_excel(writer, sheet_name='System_Performance_Log', index=False)

        # 4. Monitored_Assets (重点监控资产)
        assets_columns = ['Asset_ID', 'Asset_Name', 'Asset_Type', 'Camera_ID', 'Alert_Priority',
                         'Creation_Time', 'Is_Active']
        pd.DataFrame(columns=assets_columns).to_excel(writer, sheet_name='Monitored_Assets', index=False)

        # 5. Annotation_Log (标注日志)
        annotation_columns = ['Annotation_ID', 'Asset_ID', 'Spectrum_Type', 'Annotation_Bbox', 'Timestamp']
        pd.DataFrame(columns=annotation_columns).to_excel(writer, sheet_name='Annotation_Log', index=False)

        # 6. System_Config (系统与设备配置)
        config_columns = ['Item', 'Content', 'Notes']
        pd.DataFrame(columns=config_columns).to_excel(writer, sheet_name='System_Config', index=False)

    def _check_and_update_database_structure(self):
        """检查并更新现有数据库的表结构"""
        try:
            from openpyxl import load_workbook

            # 加载现有数据库
            wb = load_workbook(self.database_filepath)

            # 检查 HeatSource_Log 表是否需要添加 Min_Temperature 列
            if 'HeatSource_Log' in wb.sheetnames:
                ws = wb['HeatSource_Log']

                # 获取第一行的列名
                if ws.max_row > 0:
                    headers = [cell.value for cell in ws[1]]

                    # 检查是否缺少 Min_Temperature 列
                    if 'Min_Temperature' not in headers:
                        self.logger.info("🔧 检测到 HeatSource_Log 表缺少 Min_Temperature 列，正在添加...")

                        # 找到 Max_Temperature 列的位置
                        if 'Max_Temperature' in headers:
                            max_temp_index = headers.index('Max_Temperature')
                            # 在 Max_Temperature 之后插入 Min_Temperature 列
                            insert_col = max_temp_index + 2  # +1 for 0-based index, +1 for after Max_Temperature

                            # 插入新列
                            ws.insert_cols(insert_col)

                            # 设置列标题
                            ws.cell(row=1, column=insert_col, value='Min_Temperature')

                            # 为现有数据行设置合理的估算值
                            avg_temp_col = None
                            if 'Avg_Temperature' in headers:
                                avg_temp_col = headers.index('Avg_Temperature') + 1

                            for row in range(2, ws.max_row + 1):
                                # 获取该行的最高温度
                                max_temp_cell = ws.cell(row=row, column=max_temp_index + 1)
                                max_temp = max_temp_cell.value if max_temp_cell.value is not None else 0.0

                                # 估算最低温度（假设最低温度约为最高温度的60-80%）
                                if max_temp > 0:
                                    # 使用一个合理的估算：最低温度 = 最高温度 * 0.7 + 环境温度偏移
                                    estimated_min_temp = max(max_temp * 0.7, max_temp - 15.0)
                                    # 确保最低温度不会太低（至少15°C）
                                    estimated_min_temp = max(estimated_min_temp, 15.0)
                                else:
                                    estimated_min_temp = 0.0

                                ws.cell(row=row, column=insert_col, value=round(estimated_min_temp, 2))

                                # 同时更新平均温度（如果平均温度列存在且当前值为0）
                                if avg_temp_col and max_temp > 0:
                                    avg_temp_cell = ws.cell(row=row, column=avg_temp_col)
                                    if avg_temp_cell.value is None or avg_temp_cell.value == 0:
                                        estimated_avg_temp = (max_temp + estimated_min_temp) / 2.0
                                        avg_temp_cell.value = round(estimated_avg_temp, 2)

                            # 保存更改
                            wb.save(self.database_filepath)
                            self.logger.info("✅ 成功添加 Min_Temperature 列到 HeatSource_Log 表")
                        else:
                            self.logger.warning("⚠️ 无法找到 Max_Temperature 列，跳过添加 Min_Temperature 列")
                    else:
                        self.logger.info("✅ HeatSource_Log 表结构已是最新版本")

            wb.close()

        except Exception as e:
            self.logger.error(f"❌ 更新数据库结构失败: {e}")

    # ==================== V3.0 数据添加方法 ====================

    def add_heat_source_event(self, heat_source: Any, camera_id: str = "CAM_001", asset_id: Optional[str] = None):
        """添加热源事件到HeatSource_Log"""
        try:
            event_id = str(uuid.uuid4())
            tracking_id = f"T-{getattr(heat_source, 'tracking_id', 'unknown')}"

            # 构建热成像边界框字符串
            bbox = getattr(heat_source, 'bbox', (0, 0, 0, 0))
            thermal_bbox = f"({bbox[0]},{bbox[1]},{bbox[2]},{bbox[3]})"

            # 获取温度数据
            max_temp = getattr(heat_source, 'max_temperature', 0.0)
            min_temp = getattr(heat_source, 'min_temperature', 0.0)

            # 计算平均温度（如果没有avg_temperature属性，则自动计算）
            avg_temp = getattr(heat_source, 'avg_temperature', None)
            if avg_temp is None and max_temp > 0 and min_temp > 0:
                avg_temp = (max_temp + min_temp) / 2.0
            elif avg_temp is None:
                avg_temp = max_temp if max_temp > 0 else 0.0

            data_row = {
                'Event_ID': event_id,
                'Timestamp': datetime.now(),
                'Camera_ID': camera_id,
                'Tracking_ID': tracking_id,
                'Max_Temperature': max_temp,
                'Min_Temperature': min_temp,
                'Avg_Temperature': avg_temp,
                'Area': getattr(heat_source, 'area', 0),
                'Avg_Temp_Change_Rate': getattr(heat_source, 'temp_change_rate', 0.0),
                'Thermal_Bbox': thermal_bbox,
                'Confidence': getattr(heat_source, 'confidence', 1.0),
                'In_Asset_ID': asset_id,
                'Snapshot_Filename': getattr(heat_source, 'snapshot_filename', None)
            }
            self.heat_source_log.append(data_row)

            # 实时写入到Excel文件
            if self.real_time_write:
                self._write_single_record_to_excel('HeatSource_Log', data_row)
            else:
                self._check_cache_size()

        except Exception as e:
            self.logger.error(f"❌ 添加热源事件失败: {e}")

    def add_flame_event(self, flame: Any, camera_id: str = "CAM_001", asset_id: Optional[str] = None):
        """添加火焰事件到Flame_Log - 已禁用，由模块化系统处理"""
        # 方法已禁用 - 火焰数据由模块化系统单独处理
        # 移除频繁的日志输出，避免日志污染
        # self.logger.debug("火焰事件添加已禁用，由模块化系统处理")
        return
        # 原有的火焰事件处理代码已注释
        # 火焰数据现在由模块化系统单独处理

    def add_smoke_event(self, smoke: Any, camera_id: str = "CAM_001", asset_id: Optional[str] = None):
        """添加烟雾事件到Smoke_Log - 已禁用，由模块化系统处理"""
        # 方法已禁用 - 烟雾数据由模块化系统单独处理
        # 移除频繁的日志输出，避免日志污染
        # self.logger.debug("烟雾事件添加已禁用，由模块化系统处理")
        return
        # 原有的烟雾事件处理代码已注释
        # 烟雾数据现在由模块化系统单独处理
    
    def add_flame_analysis(self, flame_event_id: str, analysis_result: Dict):
        """添加火焰分析详情到Flame_Analysis_Log - 已禁用，由模块化系统处理"""
        # 方法已禁用 - 火焰分析数据由模块化系统单独处理
        # 移除频繁的日志输出，避免日志污染
        # self.logger.debug("火焰分析添加已禁用，由模块化系统处理")
        return
        # 原有的火焰分析处理代码已注释
        # 火焰分析数据现在由模块化系统单独处理

    def add_flame_analysis_event(self, analysis_result):
        """添加火焰分析结果到Flame_Analysis_Log表 - 已禁用，由模块化系统处理"""
        # 方法已禁用 - 火焰分析数据由模块化系统单独处理
        # 移除频繁的日志输出，避免日志污染
        # self.logger.debug("火焰分析事件添加已禁用，由模块化系统处理")
        return
        # 原有的火焰分析事件处理代码已注释
        # 火焰分析数据现在由模块化系统单独处理

    def add_alert_event(self, alert_info: Dict, trigger_event_id: Optional[str] = None,
                       tracking_id: Optional[str] = None):
        """添加预警事件到Alert_Log"""
        try:
            alert_id = str(uuid.uuid4())

            data_row = {
                'Alert_ID': alert_id,
                'Trigger_Event_ID': trigger_event_id,
                'Tracking_ID': tracking_id,
                'Alert_Level': alert_info.get('alert_level', 'UNKNOWN'),
                'Alert_Asset_ID': alert_info.get('asset_id', None),
                'Alert_Asset_Name': alert_info.get('asset_name', None),
                'Alert_Start_Time': alert_info.get('start_time', datetime.now()),
                'Alert_End_Time': alert_info.get('end_time', None),
                'Alert_Duration_Sec': alert_info.get('duration_sec', None),
                'Trigger_Reason': alert_info.get('trigger_reason', ''),
                'Verification_Status': alert_info.get('verification_status', 'PENDING'),
                'Verifier': alert_info.get('verifier', None),
                'Verification_Time': alert_info.get('verification_time', None),
                'Verification_Notes': alert_info.get('verification_notes', None)
            }
            self.alert_log.append(data_row)

            # 实时写入到Excel文件
            if self.real_time_write:
                self._write_single_record_to_excel('Alert_Log', data_row)
            else:
                self._check_cache_size()

        except Exception as e:
            self.logger.error(f"❌ 添加预警事件失败: {e}")

    def add_system_performance(self, performance_data: Dict, camera_id: str = "CAM_001"):
        """添加系统性能数据到System_Performance_Log"""
        try:
            data_row = {
                'Timestamp': datetime.now(),
                'Camera_ID': camera_id,
                'FPS': performance_data.get('fps', 0.0),
                'Processing_Latency': performance_data.get('processing_latency', 0.0),
                'Memory_Usage': performance_data.get('memory_usage', 0.0),
                'Total_Heat_Sources': performance_data.get('total_heat_sources', 0),
                'Potential_Flame_Count': performance_data.get('potential_flame_count', 0),
                'Smoke_Count': performance_data.get('smoke_count', 0),
                'Human_Count': performance_data.get('human_count', 0),
                'Active_Alerts': performance_data.get('active_alerts', 0)
            }
            self.system_performance_log.append(data_row)

            # 实时写入到Excel文件
            if self.real_time_write:
                self._write_single_record_to_excel('System_Performance_Log', data_row)

        except Exception as e:
            self.logger.error(f"❌ 添加系统性能数据失败: {e}")

    def add_monitored_asset(self, asset_info: Dict, camera_id: str = "CAM_001"):
        """添加监控资产到Monitored_Assets"""
        try:
            asset_id = asset_info.get('asset_id', str(uuid.uuid4()))

            data_row = {
                'Asset_ID': asset_id,
                'Asset_Name': asset_info.get('asset_name', ''),
                'Asset_Type': asset_info.get('asset_type', 'UNKNOWN'),
                'Camera_ID': camera_id,
                'Alert_Priority': asset_info.get('alert_priority', 'MEDIUM'),
                'Creation_Time': asset_info.get('creation_time', datetime.now()),
                'Is_Active': asset_info.get('is_active', True)
            }
            self.monitored_assets.append(data_row)

            # 实时写入到Excel文件
            if self.real_time_write:
                self._write_single_record_to_excel('Monitored_Assets', data_row)

        except Exception as e:
            self.logger.error(f"❌ 添加监控资产失败: {e}")

    def sync_manual_annotations_to_assets(self, force_update=False):
        """同步手动标注到监控资产表"""
        try:
            # 读取监控区域文件
            from pathlib import Path
            import json

            zone_file = Path("config/monitoring_zones.json")
            if zone_file.exists():
                with open(zone_file, 'r', encoding='utf-8') as f:
                    zones_data = json.load(f)

                # 处理手动标注区域
                for zone in zones_data.get('zones', []):
                    if zone.get('zone_type') == 'manual':
                        asset_info = {
                            'asset_id': zone['id'],
                            'asset_name': zone['name'],
                            'asset_type': 'MANUAL_ANNOTATION',
                            'alert_priority': 'HIGH' if zone.get('priority', 3) <= 2 else 'MEDIUM',
                            'creation_time': datetime.fromisoformat(zone['created_time']) if zone.get('created_time') else datetime.now(),
                            'is_active': zone.get('enabled', True)
                        }

                        # 检查是否已存在（包括Excel文件中的数据）
                        existing_in_memory = any(asset['Asset_ID'] == asset_info['asset_id'] for asset in self.monitored_assets)
                        existing_in_excel = self._check_asset_exists_in_excel(asset_info['asset_id'])

                        if not (existing_in_memory or existing_in_excel) or force_update:
                            if not (existing_in_memory or existing_in_excel):
                                self.add_monitored_asset(asset_info, zone.get('camera_id', 'CAM_001'))

                                # 同时添加标注记录
                                annotation_info = {
                                    'asset_id': zone['id'],
                                    'spectrum_type': 'Thermal',  # 手动标注通常在热成像上
                                    'bbox': tuple(zone['bbox']),
                                    'timestamp': datetime.fromisoformat(zone['created_time']) if zone.get('created_time') else datetime.now()
                                }
                                self.add_annotation(annotation_info)

                                self.logger.info(f"✅ 同步手动标注区域到资产表: {zone['name']}")

            # 同时处理 manual_annotations.json 中的标注
            annotation_file = Path("manual_annotations.json")
            if annotation_file.exists():
                with open(annotation_file, 'r', encoding='utf-8') as f:
                    annotations_data = json.load(f)

                for ann_id, ann_data in annotations_data.items():
                    # 处理所有手动标注（热成像和可见光）
                    asset_info = {
                        'asset_id': ann_id,
                        'asset_name': ann_data['label'],
                        'asset_type': 'MANUAL_ANNOTATION',
                        'alert_priority': 'MEDIUM',
                        'creation_time': datetime.fromisoformat(ann_data['timestamp']) if ann_data.get('timestamp') else datetime.now(),
                        'is_active': ann_data.get('visible', True)
                    }

                    # 检查是否已存在（包括Excel文件中的数据）
                    existing_in_memory = any(asset['Asset_ID'] == asset_info['asset_id'] for asset in self.monitored_assets)
                    existing_in_excel = self._check_asset_exists_in_excel(asset_info['asset_id'])

                    if not (existing_in_memory or existing_in_excel):
                        self.add_monitored_asset(asset_info, 'CAM_001')

                        # 同时添加标注记录，根据 is_thermal 字段确定光谱类型
                        spectrum_type = 'Thermal' if ann_data.get('is_thermal', False) else 'Visible'
                        annotation_info = {
                            'asset_id': ann_id,
                            'spectrum_type': spectrum_type,
                            'bbox': tuple(ann_data['bbox']),
                            'timestamp': datetime.fromisoformat(ann_data['timestamp']) if ann_data.get('timestamp') else datetime.now()
                        }
                        self.add_annotation(annotation_info)

                        spectrum_desc = "热成像" if spectrum_type == 'Thermal' else "可见光"
                        self.logger.info(f"✅ 同步{spectrum_desc}手动标注到资产表: {ann_data['label']}")

        except Exception as e:
            self.logger.warning(f"⚠️ 同步手动标注失败: {e}")

    def _check_asset_exists_in_excel(self, asset_id: str) -> bool:
        """检查资产是否已存在于Excel文件中"""
        try:
            if self.database_filepath.exists():
                import pandas as pd
                df = pd.read_excel(self.database_filepath, sheet_name='Monitored_Assets')
                return asset_id in df['Asset_ID'].values
        except Exception as e:
            self.logger.debug(f"检查Excel文件中的资产失败: {e}")
        return False

    def add_annotation(self, annotation_info: Dict):
        """添加标注到Annotation_Log（使用传感器坐标系统）"""
        try:
            annotation_id = str(uuid.uuid4())

            # 构建标注边界框字符串（传感器坐标）
            bbox = annotation_info.get('bbox', (0, 0, 0, 0))
            bbox_str = f"({bbox[0]},{bbox[1]},{bbox[2]},{bbox[3]})"

            # 添加坐标系统标识
            spectrum_type = annotation_info.get('spectrum_type', 'Thermal')
            coord_system_note = f"{spectrum_type}_Sensor_Coords"

            data_row = {
                'Annotation_ID': annotation_id,
                'Asset_ID': annotation_info.get('asset_id', ''),
                'Spectrum_Type': spectrum_type,  # Thermal或Visible
                'Annotation_Bbox': bbox_str,  # 传感器坐标系
                'Timestamp': annotation_info.get('timestamp', datetime.now())
            }
            self.annotation_log.append(data_row)

            # 实时写入到Excel文件
            if self.real_time_write:
                self._write_single_record_to_excel('Annotation_Log', data_row)

            self.logger.debug(f"📝 添加{coord_system_note}标注: {bbox_str}")

        except Exception as e:
            self.logger.error(f"❌ 添加标注数据失败: {e}")

    def add_system_config(self, config_item: str, content: str, notes: str = ""):
        """添加系统配置到System_Config"""
        try:
            data_row = {
                'Item': config_item,
                'Content': content,
                'Notes': notes
            }
            self.system_config.append(data_row)

        except Exception as e:
            self.logger.error(f"❌ 添加系统配置失败: {e}")

    def _write_single_record_to_excel(self, sheet_name: str, data_row: Dict):
        """实时写入单条记录到Excel文件（使用openpyxl直接操作）"""
        if not self.use_openpyxl_direct:
            return  # 如果不使用openpyxl直接操作，跳过

        try:
            with self._file_lock:
                # 确保文件存在
                if not self.database_filepath.exists():
                    self._create_initial_database_structure()

                # 加载工作簿
                wb = load_workbook(self.database_filepath)

                # 确保工作表存在
                if sheet_name not in wb.sheetnames:
                    self._create_worksheet_if_not_exists(wb, sheet_name)

                ws = wb[sheet_name]

                # 找到下一个空行
                next_row = ws.max_row + 1

                # 获取列标题
                headers = [cell.value for cell in ws[1]]

                # 写入数据行
                for col_idx, header in enumerate(headers, 1):
                    if header in data_row:
                        value = data_row[header]
                        # 处理datetime对象
                        if isinstance(value, datetime):
                            value = value.strftime("%Y-%m-%d %H:%M:%S")
                        ws.cell(row=next_row, column=col_idx, value=value)

                # 保存文件
                wb.save(self.database_filepath)
                wb.close()

        except Exception as e:
            self.logger.warning(f"⚠️ 实时写入失败，将使用缓存模式: {e}")
            # 如果实时写入失败，回退到缓存模式
            self.real_time_write = False

    def _create_worksheet_if_not_exists(self, wb: Workbook, sheet_name: str):
        """如果工作表不存在则创建"""
        if sheet_name not in wb.sheetnames:
            ws = wb.create_sheet(sheet_name)

            # 根据工作表名称添加对应的列标题
            if sheet_name == 'HeatSource_Log':
                headers = ['Event_ID', 'Timestamp', 'Camera_ID', 'Tracking_ID', 'Max_Temperature',
                          'Avg_Temperature', 'Area', 'Avg_Temp_Change_Rate', 'Thermal_Bbox',
                          'Confidence', 'In_Asset_ID', 'Snapshot_Filename']
            elif sheet_name == 'Flame_Log':
                headers = ['Event_ID', 'Timestamp', 'Camera_ID', 'Tracking_ID', 'Centroid_X', 'Centroid_Y',
                          'Bbox_X1', 'Bbox_Y1', 'Bbox_X2', 'Bbox_Y2', 'Area', 'Width', 'Height',
                          'Aspect_Ratio', 'Perimeter', 'Compactness', 'Area_Change_Rate', 'Confidence',
                          'Duration', 'Frame_Count', 'Total_Distance', 'In_Asset_ID', 'Snapshot_Filename']
            elif sheet_name == 'Smoke_Log':
                headers = ['Event_ID', 'Timestamp', 'Camera_ID', 'Tracking_ID', 'Centroid_X', 'Centroid_Y',
                          'Bbox_X1', 'Bbox_Y1', 'Bbox_X2', 'Bbox_Y2', 'Area', 'Width', 'Height',
                          'Aspect_Ratio', 'Perimeter', 'Compactness', 'Area_Change_Rate', 'Confidence',
                          'Duration', 'Frame_Count', 'Total_Distance', 'In_Asset_ID', 'Snapshot_Filename']
            elif sheet_name == 'Flame_Analysis_Log':
                headers = ['Flame_Event_ID', 'Flicker_Frequency', 'Flicker_Intensity',
                          'Morphology_AspectRatio', 'Analysis_Timestamp']
            elif sheet_name == 'Alert_Log':
                headers = ['Alert_ID', 'Trigger_Event_ID', 'Tracking_ID', 'Alert_Level', 'Alert_Asset_ID',
                          'Alert_Asset_Name', 'Alert_Start_Time', 'Alert_End_Time', 'Alert_Duration_Sec',
                          'Trigger_Reason', 'Verification_Status', 'Verifier', 'Verification_Time', 'Verification_Notes']
            elif sheet_name == 'System_Performance_Log':
                headers = ['Timestamp', 'Camera_ID', 'FPS', 'Processing_Latency', 'Memory_Usage',
                          'Total_Heat_Sources', 'Potential_Flame_Count', 'Smoke_Count', 'Human_Count', 'Active_Alerts']
            elif sheet_name == 'Monitored_Assets':
                headers = ['Asset_ID', 'Asset_Name', 'Asset_Type', 'Camera_ID', 'Alert_Priority',
                          'Creation_Time', 'Is_Active']
            elif sheet_name == 'Annotation_Log':
                headers = ['Annotation_ID', 'Asset_ID', 'Spectrum_Type', 'Annotation_Bbox', 'Timestamp']
            elif sheet_name == 'System_Config':
                headers = ['Item', 'Content', 'Notes']
            else:
                headers = ['ID', 'Timestamp', 'Data']  # 默认列

            # 写入标题行
            for col_idx, header in enumerate(headers, 1):
                cell = ws.cell(row=1, column=col_idx, value=header)
                # 设置标题样式
                cell.font = Font(bold=True)
                cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
                cell.alignment = Alignment(horizontal="center")

    def export_to_excel(self, filename: Optional[str] = None) -> str:
        """导出数据到Excel文件 - V3.0版本，支持9个工作表"""
        try:
            # 使用固定的数据库文件名或指定文件名
            if filename is None:
                filepath = self.database_filepath
            else:
                filepath = self.export_dir / filename

            # 读取现有数据（如果文件存在）
            existing_data = self._read_existing_data(filepath)

            # 创建Excel写入器
            with pd.ExcelWriter(filepath, engine='openpyxl', mode='w') as writer:
                # 1. HeatSource_Log (热源事件日志)
                self._export_heat_source_log(writer, existing_data.get('HeatSource_Log', []))

                # 移除火焰和烟雾相关的导出 - 由模块化系统处理
                # 2. Flame_Log (火焰事件日志) - 已移除
                # 3. Smoke_Log (烟雾事件日志) - 已移除
                # 4. Flame_Analysis_Log (火焰分析详情日志) - 已移除

                # 2. Alert_Log (预警事件日志)
                self._export_alert_log(writer, existing_data.get('Alert_Log', []))

                # 3. System_Performance_Log (系统性能与全局状态日志)
                self._export_system_performance_log(writer, existing_data.get('System_Performance_Log', []))

                # 4. Monitored_Assets (重点监控资产)
                self._export_monitored_assets(writer, existing_data.get('Monitored_Assets', []))

                # 5. Annotation_Log (标注日志)
                self._export_annotation_log(writer, existing_data.get('Annotation_Log', []))

                # 6. System_Config (系统与设备配置)
                self._export_system_config(writer, existing_data.get('System_Config', []))

            # 清空已导出的数据缓存
            self._clear_exported_data()

            self.logger.info(f"✅ 数据已导出到Excel数据库文件: {filepath}")
            return str(filepath)

        except Exception as e:
            self.logger.error(f"❌ 导出Excel文件失败: {e}")
            return ""

    def _read_existing_data(self, filepath: Path) -> Dict:
        """读取现有Excel文件中的数据"""
        existing_data = {}
        try:
            if filepath.exists():
                # 读取6个核心工作表 (移除火焰和烟雾相关表)
                sheet_names = ['HeatSource_Log', 'Alert_Log', 'System_Performance_Log',
                              'Monitored_Assets', 'Annotation_Log', 'System_Config']

                for sheet_name in sheet_names:
                    try:
                        df = pd.read_excel(filepath, sheet_name=sheet_name)
                        existing_data[sheet_name] = df.to_dict('records')
                    except Exception:
                        existing_data[sheet_name] = []

        except Exception as e:
            self.logger.warning(f"⚠️ 读取现有数据失败: {e}")

        return existing_data
    
    def _export_heat_source_log(self, writer, existing_data: List[Dict]):
        """导出热源事件日志"""
        try:
            # 合并现有数据和新数据
            all_data = existing_data + self.heat_source_log
            if all_data:
                df = pd.DataFrame(all_data)
                df.to_excel(writer, sheet_name='HeatSource_Log', index=False)
            else:
                # 创建空表结构
                columns = ['Event_ID', 'Timestamp', 'Camera_ID', 'Tracking_ID', 'Max_Temperature',
                          'Avg_Temperature', 'Area', 'Avg_Temp_Change_Rate', 'Thermal_Bbox',
                          'Confidence', 'In_Asset_ID', 'Snapshot_Filename']
                pd.DataFrame(columns=columns).to_excel(writer, sheet_name='HeatSource_Log', index=False)
        except Exception as e:
            self.logger.error(f"❌ 导出热源日志失败: {e}")

    def _export_flame_log(self, writer, existing_data: List[Dict]):
        """导出火焰事件日志 - 已禁用，由模块化系统处理"""
        # 方法已禁用 - 火焰数据由模块化系统单独处理
        self.logger.info("⚠️ 火焰日志导出已禁用，请使用模块化系统")
        return
        # 原有的火焰日志导出代码已注释
        # 火焰数据现在由模块化系统单独处理

    def _export_smoke_log(self, writer, existing_data: List[Dict]):
        """导出烟雾事件日志 - 已禁用，由模块化系统处理"""
        # 方法已禁用 - 烟雾数据由模块化系统单独处理
        self.logger.info("⚠️ 烟雾日志导出已禁用，请使用模块化系统")
        return
        # 原有的烟雾日志导出代码已注释
        # 烟雾数据现在由模块化系统单独处理

    def _export_flame_analysis_log(self, writer, existing_data: List[Dict]):
        """导出火焰分析详情日志 - 已禁用，由模块化系统处理"""
        # 方法已禁用 - 火焰分析数据由模块化系统单独处理
        self.logger.info("⚠️ 火焰分析日志导出已禁用，请使用模块化系统")
        return
        # 原有的火焰分析日志导出代码已注释
        # 火焰分析数据现在由模块化系统单独处理

    def _export_alert_log(self, writer, existing_data: List[Dict]):
        """导出预警事件日志"""
        try:
            all_data = existing_data + self.alert_log
            if all_data:
                df = pd.DataFrame(all_data)
                df.to_excel(writer, sheet_name='Alert_Log', index=False)
            else:
                columns = ['Alert_ID', 'Trigger_Event_ID', 'Tracking_ID', 'Alert_Level', 'Alert_Asset_ID',
                          'Alert_Asset_Name', 'Alert_Start_Time', 'Alert_End_Time', 'Alert_Duration_Sec',
                          'Trigger_Reason', 'Verification_Status', 'Verifier', 'Verification_Time', 'Verification_Notes']
                pd.DataFrame(columns=columns).to_excel(writer, sheet_name='Alert_Log', index=False)
        except Exception as e:
            self.logger.error(f"❌ 导出预警日志失败: {e}")

    def _export_system_performance_log(self, writer, existing_data: List[Dict]):
        """导出系统性能与全局状态日志"""
        try:
            all_data = existing_data + self.system_performance_log
            if all_data:
                df = pd.DataFrame(all_data)
                df.to_excel(writer, sheet_name='System_Performance_Log', index=False)
            else:
                columns = ['Timestamp', 'Camera_ID', 'FPS', 'Processing_Latency', 'Memory_Usage',
                          'Total_Heat_Sources', 'Potential_Flame_Count', 'Smoke_Count', 'Human_Count', 'Active_Alerts']
                pd.DataFrame(columns=columns).to_excel(writer, sheet_name='System_Performance_Log', index=False)
        except Exception as e:
            self.logger.error(f"❌ 导出系统性能日志失败: {e}")

    def _export_monitored_assets(self, writer, existing_data: List[Dict]):
        """导出重点监控资产"""
        try:
            all_data = existing_data + self.monitored_assets
            if all_data:
                df = pd.DataFrame(all_data)
                df.to_excel(writer, sheet_name='Monitored_Assets', index=False)
            else:
                columns = ['Asset_ID', 'Asset_Name', 'Asset_Type', 'Camera_ID', 'Alert_Priority',
                          'Creation_Time', 'Is_Active']
                pd.DataFrame(columns=columns).to_excel(writer, sheet_name='Monitored_Assets', index=False)
        except Exception as e:
            self.logger.error(f"❌ 导出监控资产失败: {e}")

    def _export_annotation_log(self, writer, existing_data: List[Dict]):
        """导出标注日志"""
        try:
            all_data = existing_data + self.annotation_log
            if all_data:
                df = pd.DataFrame(all_data)
                df.to_excel(writer, sheet_name='Annotation_Log', index=False)
            else:
                columns = ['Annotation_ID', 'Asset_ID', 'Spectrum_Type', 'Annotation_Bbox', 'Timestamp']
                pd.DataFrame(columns=columns).to_excel(writer, sheet_name='Annotation_Log', index=False)
        except Exception as e:
            self.logger.error(f"❌ 导出标注日志失败: {e}")

    def _export_system_config(self, writer, existing_data: List[Dict]):
        """导出系统与设备配置"""
        try:
            all_data = existing_data + self.system_config
            if all_data:
                df = pd.DataFrame(all_data)
                df.to_excel(writer, sheet_name='System_Config', index=False)
            else:
                columns = ['Item', 'Content', 'Notes']
                pd.DataFrame(columns=columns).to_excel(writer, sheet_name='System_Config', index=False)
        except Exception as e:
            self.logger.error(f"❌ 导出系统配置失败: {e}")

    def _clear_exported_data(self):
        """清空已导出的数据缓存 - V3.1版本，6个核心工作表"""
        self.heat_source_log.clear()
        # 移除火焰和烟雾相关缓存清理 - 由模块化系统处理
        # self.flame_log.clear()  # 已移除
        # self.smoke_log.clear()  # 已移除
        # self.flame_analysis_log.clear()  # 已移除
        self.alert_log.clear()
        self.system_performance_log.clear()
        # 注意：monitored_assets, annotation_log, system_config 通常不清空，因为它们是配置性数据

    def _check_cache_size(self):
        """检查缓存大小，超过限制时自动导出 - V3.1版本"""
        total_size = (len(self.heat_source_log) + len(self.alert_log) + len(self.system_performance_log))
        # 移除火焰和烟雾相关缓存计算 - 由模块化系统处理

        if total_size >= self.max_cache_size:
            self.logger.info(f"📊 缓存数据达到上限({total_size})，自动导出Excel文件")
            self.export_to_excel()
    
    def _start_auto_export_thread(self):
        """启动自动导出线程"""
        def auto_export_worker():
            while self.auto_export_enabled:
                try:
                    time.sleep(self.export_interval_minutes * 60)  # 转换为秒

                    # 检查是否有数据需要导出 - V3.1版本
                    total_data = (len(self.heat_source_log) + len(self.alert_log) + len(self.system_performance_log))
                    # 移除火焰和烟雾相关数据计算 - 由模块化系统处理

                    if total_data > 0:
                        self.logger.info(f"⏰ 定时导出Excel数据库，当前数据量: {total_data}")
                        self.export_to_excel()

                except Exception as e:
                    self.logger.error(f"❌ 自动导出线程异常: {e}")

        if self.auto_export_enabled:
            export_thread = threading.Thread(target=auto_export_worker, daemon=True)
            export_thread.start()
            self.logger.info(f"🕒 自动导出线程已启动，间隔: {self.export_interval_minutes}分钟")

    def manual_export(self) -> str:
        """手动导出数据 - 创建当前数据库的完整副本"""
        try:
            # 生成带时间戳的导出文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            export_filename = f"fire_monitoring_export_{timestamp}.xlsx"
            export_filepath = self.export_dir / export_filename

            # 如果使用实时写入，直接复制当前数据库文件
            if self.real_time_write and self.database_filepath.exists():
                with self._file_lock:
                    shutil.copy2(self.database_filepath, export_filepath)
                    self.logger.info(f"✅ 数据库文件已导出到: {export_filepath}")
                    return str(export_filepath)
            else:
                # 使用传统方法导出
                return self.export_to_excel(export_filename)

        except Exception as e:
            self.logger.error(f"❌ 手动导出失败: {e}")
            return ""

    def get_current_database_copy(self) -> str:
        """获取当前数据库的即时副本"""
        return self.manual_export()

    def get_export_status(self) -> Dict:
        """获取导出状态 - V3.1版本，6个核心工作表"""
        return {
            'database_file': str(self.database_filepath),
            'export_dir': str(self.export_dir),
            'auto_export_enabled': self.auto_export_enabled,
            'export_interval_minutes': self.export_interval_minutes,
            'cached_heat_source_events': len(self.heat_source_log),
            # 移除火焰和烟雾相关状态 - 由模块化系统处理
            # 'cached_flame_events': len(self.flame_log),  # 已移除
            # 'cached_smoke_events': len(self.smoke_log),  # 已移除
            # 'cached_flame_analysis': len(self.flame_analysis_log),  # 已移除
            'cached_alerts': len(self.alert_log),
            'cached_performance_data': len(self.system_performance_log),
            'cached_assets': len(self.monitored_assets),
            'cached_annotations': len(self.annotation_log),
            'cached_configs': len(self.system_config),
            'total_cached_records': (len(self.heat_source_log) + len(self.alert_log) + len(self.system_performance_log))
        }

    def stop_auto_export(self):
        """停止自动导出"""
        self.auto_export_enabled = False
        self.logger.info("🛑 自动导出已停止")

    # ==================== 兼容性方法 ====================
    # 为了保持与现有代码的兼容性，保留一些旧的方法名

    def add_heat_source_data(self, heat_sources: List[Any], camera_id: str = "CAM_001"):
        """兼容性方法：添加热源数据（集成内存标注管理器）"""
        try:
            # 导入内存标注管理器
            from detection.analysis.memory_annotation_manager import find_heat_source_asset_id

            for heat_source in heat_sources:
                # 获取热源边界框
                bbox = getattr(heat_source, 'bbox', (0, 0, 0, 0))

                # 使用内存标注管理器查找匹配的资产ID（高性能）
                asset_id = find_heat_source_asset_id(bbox, 'Thermal')

                # 添加热源事件，包含资产ID
                self.add_heat_source_event(heat_source, camera_id, asset_id)

                # 调试信息
                if asset_id:
                    heat_source_id = getattr(heat_source, 'id', 'unknown')
                    self.logger.debug(f"🔗 热源{heat_source_id}匹配到资产: {asset_id}")

        except Exception as e:
            self.logger.error(f"❌ 添加热源数据失败: {e}")
            # 降级处理：不使用资产匹配
            for heat_source in heat_sources:
                self.add_heat_source_event(heat_source, camera_id)

    def add_alert_data(self, alert_level: str, alert_info: Dict):
        """兼容性方法：添加预警数据"""
        alert_info['alert_level'] = alert_level
        self.add_alert_event(alert_info)

    def add_detection_stats(self, stats: Dict, camera_id: str = "CAM_001"):
        """兼容性方法：添加检测统计数据"""
        # 将检测统计数据转换为系统性能数据格式
        performance_data = {
            'fps': stats.get('detection_fps', 0.0),
            'processing_latency': stats.get('processing_time_ms', 0.0),
            'total_heat_sources': stats.get('total_heat_sources', 0),
            'potential_flame_count': stats.get('total_flames', 0),
            'smoke_count': stats.get('total_smoke', 0),
            'human_count': stats.get('total_humans', 0)
        }
        self.add_system_performance(performance_data, camera_id)


# ==================== 全局导出器实例 ====================
_global_exporter = None

def get_excel_exporter() -> ExcelDataExporter:
    """获取全局Excel导出器实例 - V3.0版本"""
    global _global_exporter
    if _global_exporter is None:
        _global_exporter = ExcelDataExporter()
    return _global_exporter

def set_excel_exporter(exporter: ExcelDataExporter):
    """设置全局Excel导出器实例"""
    global _global_exporter
    _global_exporter = exporter

def create_v3_exporter(export_dir: str = "data_exports",
                      database_filename: str = "fire_monitoring_database.xlsx") -> ExcelDataExporter:
    """创建V3.0版本的Excel导出器"""
    return ExcelDataExporter(export_dir, database_filename)

# ==================== 示例用法 ====================
# 测试代码已移除，避免主程序运行时的干扰
