#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志管理模块
提供统一的日志记录功能
"""

import logging
import os
from datetime import datetime
from typing import Optional
from config.system_config import SYSTEM_CONFIG

class Logger:
    """日志管理器"""
    
    def __init__(self, name: str = "ThermalCamera", log_file: Optional[str] = None):
        self.name = name
        self.logger = logging.getLogger(name)
        
        # 避免重复添加处理器
        if not self.logger.handlers:
            self._setup_logger(log_file)
    
    def _setup_logger(self, log_file: Optional[str] = None):
        """设置日志器"""
        self.logger.setLevel(getattr(logging, SYSTEM_CONFIG.log_level))
        
        # 创建格式器
        formatter = logging.Formatter(SYSTEM_CONFIG.log_format)
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)
        
        # 文件处理器
        if log_file is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            # 使用pathlib确保跨平台路径兼容性
            from pathlib import Path
            log_file = str(Path(SYSTEM_CONFIG.logs_dir) / f"thermal_camera_{timestamp}.log")
        
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(formatter)
        self.logger.addHandler(file_handler)
        
        self.logger.info(f"日志系统初始化完成，日志文件: {log_file}")
    
    def debug(self, message: str):
        """调试信息"""
        self.logger.debug(message)
    
    def info(self, message: str):
        """一般信息"""
        self.logger.info(message)
    
    def warning(self, message: str):
        """警告信息"""
        self.logger.warning(message)
    
    def error(self, message: str):
        """错误信息"""
        self.logger.error(message)
    
    def critical(self, message: str):
        """严重错误"""
        self.logger.critical(message)
    
    def exception(self, message: str):
        """异常信息（包含堆栈跟踪）"""
        self.logger.exception(message)

# 全局日志器实例
_global_logger = None

def get_logger(name: str = "ThermalCamera") -> Logger:
    """获取日志器实例"""
    global _global_logger
    if _global_logger is None:
        _global_logger = Logger(name)
    return _global_logger

def log_system_info():
    """记录系统信息"""
    logger = get_logger()
    logger.info("=" * 50)
    logger.info("海康威视热成像监控系统启动")
    logger.info(f"配置目录: {SYSTEM_CONFIG.config_dir}")
    logger.info(f"捕获目录: {SYSTEM_CONFIG.captures_dir}")
    logger.info(f"日志目录: {SYSTEM_CONFIG.logs_dir}")
    logger.info("=" * 50)

def log_camera_info(camera_info: dict):
    """记录摄像头信息"""
    logger = get_logger()
    logger.info("摄像头信息:")
    
    visible_info = camera_info.get('visible', {})
    thermal_info = camera_info.get('thermal', {})
    
    logger.info(f"可见光: {visible_info.get('width')}x{visible_info.get('height')} @ {visible_info.get('fps')}FPS")
    logger.info(f"红外: {thermal_info.get('width')}x{thermal_info.get('height')} @ {thermal_info.get('fps')}FPS")

def log_temperature_range(min_temp: float, max_temp: float, source: str = ""):
    """记录温度范围"""
    logger = get_logger()
    source_text = f"[{source}] " if source else ""
    logger.info(f"{source_text}温度范围更新: {min_temp:.1f}°C - {max_temp:.1f}°C")

def log_error_with_context(error: Exception, context: str = ""):
    """记录带上下文的错误"""
    logger = get_logger()
    context_text = f"[{context}] " if context else ""
    logger.error(f"{context_text}发生异常: {str(error)}")
    logger.exception("异常详情:")
