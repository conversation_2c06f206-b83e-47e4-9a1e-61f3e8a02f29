#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
弹窗集成工具
为主程序提供简单的弹窗集成方案
"""

import sys
import os
import threading
import time
from typing import Optional

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer, QObject, pyqtSignal

from detection.analysis.alert_response_handler import AlertResponseHandler
from detection.analysis.smart_alert_evaluator import EvaluationResult
from config.fire_alert_config import AlertLevel
from utils.logger import get_logger


class PopupIntegrationManager(QObject):
    """弹窗集成管理器"""
    
    # 信号定义
    popup_requested = pyqtSignal(object)  # 弹窗请求信号
    
    def __init__(self):
        super().__init__()
        self.logger = get_logger("PopupIntegrationManager")
        self.app = None
        self.response_handler = None
        self.app_thread = None
        self.is_running = False
        
        # 连接信号
        self.popup_requested.connect(self._handle_popup_request)
    
    def initialize(self):
        """初始化弹窗系统"""
        try:
            # 检查是否已经有QApplication实例
            self.app = QApplication.instance()
            
            if self.app is None:
                # 在单独线程中创建QApplication
                self.app_thread = threading.Thread(target=self._run_qt_app, daemon=True)
                self.app_thread.start()
                
                # 等待应用启动
                time.sleep(1)
                self.app = QApplication.instance()
            
            if self.app:
                self.app.setQuitOnLastWindowClosed(False)
                self.response_handler = AlertResponseHandler()
                self.is_running = True
                self.logger.info("弹窗系统初始化成功")
                return True
            else:
                self.logger.error("弹窗系统初始化失败")
                return False
                
        except Exception as e:
            self.logger.error(f"弹窗系统初始化失败: {e}")
            return False
    
    def _run_qt_app(self):
        """在单独线程中运行Qt应用"""
        try:
            if QApplication.instance() is None:
                app = QApplication(sys.argv)
                app.setQuitOnLastWindowClosed(False)
                
                # 创建一个定时器来保持事件循环活跃
                keep_alive_timer = QTimer()
                keep_alive_timer.timeout.connect(lambda: None)
                keep_alive_timer.start(1000)  # 每秒触发一次
                
                self.logger.info("Qt应用程序在后台线程中启动")
                app.exec_()
        except Exception as e:
            self.logger.error(f"Qt应用程序启动失败: {e}")
    
    def show_popup(self, evaluation_result: EvaluationResult) -> bool:
        """显示弹窗"""
        if not self.is_running or not self.response_handler:
            self.logger.warning("弹窗系统未初始化")
            return False
        
        try:
            # 发送弹窗请求信号
            self.popup_requested.emit(evaluation_result)
            return True
        except Exception as e:
            self.logger.error(f"弹窗请求失败: {e}")
            return False
    
    def _handle_popup_request(self, evaluation_result: EvaluationResult):
        """处理弹窗请求"""
        try:
            # 处理预警响应（包括弹窗）
            response_records = self.response_handler.handle_alert(evaluation_result)
            
            # 查找弹窗响应记录
            popup_record = None
            for record in response_records:
                if record.response_action.value == 'popup_alert':
                    popup_record = record
                    break
            
            if popup_record:
                if popup_record.success:
                    self.logger.info(f"弹窗显示成功: {evaluation_result.alert_level.value}")
                else:
                    self.logger.warning(f"弹窗显示失败: {popup_record.message}")
            else:
                self.logger.debug("没有弹窗响应记录")
                
        except Exception as e:
            self.logger.error(f"处理弹窗请求失败: {e}")
    
    def shutdown(self):
        """关闭弹窗系统"""
        try:
            self.is_running = False
            if self.app:
                self.app.quit()
            self.logger.info("弹窗系统已关闭")
        except Exception as e:
            self.logger.error(f"关闭弹窗系统失败: {e}")


# 全局弹窗管理器实例
_popup_manager = None


def get_popup_integration_manager() -> PopupIntegrationManager:
    """获取全局弹窗集成管理器（单例模式）"""
    global _popup_manager
    if _popup_manager is None:
        _popup_manager = PopupIntegrationManager()
    return _popup_manager


def initialize_popup_system() -> bool:
    """初始化弹窗系统"""
    manager = get_popup_integration_manager()
    return manager.initialize()


def show_alert_popup(evaluation_result: EvaluationResult) -> bool:
    """显示预警弹窗"""
    manager = get_popup_integration_manager()
    return manager.show_popup(evaluation_result)


def shutdown_popup_system():
    """关闭弹窗系统"""
    global _popup_manager
    if _popup_manager:
        _popup_manager.shutdown()
        _popup_manager = None


# 简化的弹窗函数
def quick_show_popup(alert_level: AlertLevel, 
                    risk_score: float = 0.5,
                    confidence_score: float = 0.5,
                    conditions: list = None,
                    actions: list = None) -> bool:
    """快速显示弹窗"""
    from datetime import datetime
    
    if conditions is None:
        conditions = [f"{alert_level.value} 预警触发"]
    
    if actions is None:
        actions = ["记录日志", "弹窗提示"]
    
    # 创建简单的评估结果
    evaluation_result = EvaluationResult(
        alert_level=alert_level,
        risk_score=risk_score,
        confidence_score=confidence_score,
        confidence_dimension_score=confidence_score,
        temperature_dimension_score=0.5,
        area_dimension_score=0.5,
        duration_dimension_score=0.5,
        triggered_conditions=conditions,
        recommended_actions=actions,
        evaluation_details={},
        timestamp=datetime.now()
    )
    
    return show_alert_popup(evaluation_result)


# 测试函数
def test_popup_integration():
    """测试弹窗集成"""
    print("测试弹窗集成...")
    
    # 初始化弹窗系统
    if not initialize_popup_system():
        print("❌ 弹窗系统初始化失败")
        return
    
    print("✅ 弹窗系统初始化成功")
    
    # 等待系统准备就绪
    time.sleep(2)
    
    # 显示测试弹窗
    success = quick_show_popup(
        AlertLevel.LEVEL_1,
        risk_score=0.3,
        confidence_score=0.4,
        conditions=["检测到异常热源", "温度上升7°C"],
        actions=["记录日志", "提醒关注", "弹窗提示"]
    )
    
    if success:
        print("✅ 弹窗请求发送成功")
        print("弹窗应该已经显示，请查看屏幕")
        
        # 等待用户交互
        input("按回车键继续...")
    else:
        print("❌ 弹窗请求失败")
    
    # 关闭弹窗系统
    shutdown_popup_system()
    print("弹窗系统已关闭")


if __name__ == "__main__":
    test_popup_integration()
