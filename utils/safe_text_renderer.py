#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安全文字渲染器
自动处理中文和英文文字显示，避免乱码问题
"""

import cv2
import numpy as np
from typing import Tuple, Optional, Union
import re

from utils.logger import get_logger


class SafeTextRenderer:
    """安全文字渲染器 - 自动处理中文和英文"""
    
    def __init__(self):
        self.logger = get_logger("SafeTextRenderer")
        self.chinese_renderer = None
        self._init_chinese_renderer()
    
    def _init_chinese_renderer(self):
        """初始化中文渲染器"""
        try:
            from utils.chinese_text_renderer import get_chinese_renderer
            self.chinese_renderer = get_chinese_renderer()
            self.logger.info("✅ 中文渲染器初始化成功")
        except Exception as e:
            self.logger.warning(f"⚠️ 中文渲染器初始化失败: {e}")
            self.chinese_renderer = None
    
    def has_chinese(self, text: str) -> bool:
        """检查文本是否包含中文字符"""
        if not text:
            return False
        return bool(re.search(r'[\u4e00-\u9fff]', text))
    
    def draw_text(self, 
                  image: np.ndarray,
                  text: str,
                  position: Tuple[int, int],
                  font_size: int = 20,
                  color: Tuple[int, int, int] = (255, 255, 255),
                  background_color: Optional[Tuple[int, int, int]] = None,
                  padding: int = 3) -> np.ndarray:
        """
        安全绘制文字（自动处理中文和英文）
        
        Args:
            image: 输入图像
            text: 要绘制的文字
            position: 文字位置 (x, y)
            font_size: 字体大小
            color: 文字颜色 (BGR)
            background_color: 背景颜色 (BGR)，None表示透明
            padding: 背景边距
            
        Returns:
            绘制文字后的图像
        """
        if not text or image is None:
            return image
        
        try:
            # 检查是否包含中文
            if self.has_chinese(text):
                return self._draw_chinese_text(image, text, position, font_size, 
                                             color, background_color, padding)
            else:
                return self._draw_english_text(image, text, position, font_size, 
                                             color, background_color, padding)
        except Exception as e:
            self.logger.warning(f"⚠️ 文字绘制失败: {e}")
            # 回退到基础OpenCV绘制
            return self._draw_fallback_text(image, text, position, font_size, color)
    
    def _draw_chinese_text(self, 
                          image: np.ndarray,
                          text: str,
                          position: Tuple[int, int],
                          font_size: int,
                          color: Tuple[int, int, int],
                          background_color: Optional[Tuple[int, int, int]],
                          padding: int) -> np.ndarray:
        """绘制中文文字"""
        if self.chinese_renderer:
            try:
                return self.chinese_renderer.draw_text_on_opencv_image(
                    image, text, position, font_size, color, background_color, padding
                )
            except Exception as e:
                self.logger.warning(f"⚠️ 中文渲染器绘制失败: {e}")
        
        # 回退方案：替换为英文或拼音
        english_text = self._chinese_to_english_fallback(text)
        return self._draw_english_text(image, english_text, position, font_size, 
                                     color, background_color, padding)
    
    def _draw_english_text(self, 
                          image: np.ndarray,
                          text: str,
                          position: Tuple[int, int],
                          font_size: int,
                          color: Tuple[int, int, int],
                          background_color: Optional[Tuple[int, int, int]],
                          padding: int) -> np.ndarray:
        """绘制英文文字"""
        result_image = image.copy()
        
        # 计算OpenCV字体参数
        font_face = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = font_size / 30.0  # 转换为OpenCV字体比例
        font_thickness = max(1, int(font_size / 20))
        
        # 如果有背景色，先绘制背景
        if background_color is not None:
            text_size = cv2.getTextSize(text, font_face, font_scale, font_thickness)[0]
            x, y = position
            
            # 绘制背景矩形
            cv2.rectangle(result_image,
                         (x - padding, y - text_size[1] - padding),
                         (x + text_size[0] + padding, y + padding),
                         background_color, -1)
        
        # 绘制文字
        cv2.putText(result_image, text, position, font_face, font_scale, color, font_thickness)
        
        return result_image
    
    def _draw_fallback_text(self, 
                           image: np.ndarray,
                           text: str,
                           position: Tuple[int, int],
                           font_size: int,
                           color: Tuple[int, int, int]) -> np.ndarray:
        """回退文字绘制（最基础的OpenCV绘制）"""
        result_image = image.copy()
        
        # 如果是中文，替换为英文
        if self.has_chinese(text):
            text = self._chinese_to_english_fallback(text)
        
        # 使用最基础的OpenCV绘制
        font_scale = max(0.4, font_size / 30.0)
        cv2.putText(result_image, text, position, cv2.FONT_HERSHEY_SIMPLEX, 
                   font_scale, color, 1)
        
        return result_image
    
    def _chinese_to_english_fallback(self, chinese_text: str) -> str:
        """中文到英文的回退映射"""
        chinese_to_english = {
            # 火焰烟雾检测相关
            "火焰": "Fire",
            "烟雾": "Smoke", 
            "检测": "Detection",
            "识别": "Recognition",
            "监控": "Monitor",
            "报警": "Alarm",
            
            # 热源相关
            "热源": "Heat",
            "温度": "Temp",
            "面积": "Area",
            "位置": "Pos",
            "尺寸": "Size",
            
            # 系统状态
            "系统": "System",
            "状态": "Status",
            "开启": "ON",
            "关闭": "OFF",
            "运行": "Running",
            "停止": "Stopped",
            
            # 数值单位
            "像素": "px",
            "度": "°",
            "秒": "s",
            "分钟": "min",
            "小时": "h",
            
            # 常用词汇
            "数量": "Count",
            "总数": "Total",
            "最大": "Max",
            "最小": "Min",
            "平均": "Avg",
            "当前": "Current",
            "实时": "Live",
            "历史": "History",
            
            # 完整短语
            "火焰检测": "Fire Detection",
            "烟雾识别": "Smoke Detection", 
            "热源监控": "Heat Monitor",
            "系统状态": "System Status",
            "检测结果": "Detection Result",
            "人体检测": "Human Detection",
            "人体": "Human",
        }
        
        # 尝试完整匹配
        if chinese_text in chinese_to_english:
            return chinese_to_english[chinese_text]
        
        # 尝试部分匹配
        for chinese, english in chinese_to_english.items():
            if chinese in chinese_text:
                chinese_text = chinese_text.replace(chinese, english)
        
        # 如果仍有中文字符，返回简化版本
        if self.has_chinese(chinese_text):
            return "Text"  # 通用回退
        
        return chinese_text
    
    def get_text_size(self, text: str, font_size: int) -> Tuple[int, int]:
        """获取文字尺寸"""
        try:
            if self.has_chinese(text) and self.chinese_renderer:
                return self.chinese_renderer.get_text_size(text, font_size)
            else:
                # 使用OpenCV计算英文文字尺寸
                font_scale = font_size / 30.0
                font_thickness = max(1, int(font_size / 20))
                text_size = cv2.getTextSize(text, cv2.FONT_HERSHEY_SIMPLEX, 
                                          font_scale, font_thickness)[0]
                return text_size[0], text_size[1]
        except Exception as e:
            self.logger.warning(f"⚠️ 获取文字尺寸失败: {e}")
            # 回退估算
            return len(text) * font_size // 2, font_size


# 全局安全渲染器实例
_safe_renderer = None

def get_safe_renderer() -> SafeTextRenderer:
    """获取全局安全渲染器实例"""
    global _safe_renderer
    if _safe_renderer is None:
        _safe_renderer = SafeTextRenderer()
    return _safe_renderer

def safe_draw_text(image: np.ndarray,
                  text: str,
                  position: Tuple[int, int],
                  font_size: int = 20,
                  color: Tuple[int, int, int] = (255, 255, 255),
                  background_color: Optional[Tuple[int, int, int]] = None,
                  padding: int = 3) -> np.ndarray:
    """
    便捷函数：安全绘制文字
    
    Args:
        image: 输入图像
        text: 要绘制的文字
        position: 文字位置 (x, y)
        font_size: 字体大小
        color: 文字颜色 (BGR)
        background_color: 背景颜色 (BGR)，None表示透明
        padding: 背景边距
        
    Returns:
        绘制文字后的图像
    """
    renderer = get_safe_renderer()
    return renderer.draw_text(image, text, position, font_size, color, 
                            background_color, padding)

def safe_get_text_size(text: str, font_size: int) -> Tuple[int, int]:
    """
    便捷函数：安全获取文字尺寸
    
    Args:
        text: 文字内容
        font_size: 字体大小
        
    Returns:
        文字尺寸 (width, height)
    """
    renderer = get_safe_renderer()
    return renderer.get_text_size(text, font_size)
