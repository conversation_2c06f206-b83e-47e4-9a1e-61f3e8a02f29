#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统诊断工具
用于检查系统环境和依赖是否正确配置
"""

import sys
import os
import platform
import subprocess
from pathlib import Path
from typing import Dict, List, Tuple


class SystemDiagnostics:
    """系统诊断类"""
    
    def __init__(self):
        self.results = []
        self.errors = []
        self.warnings = []
    
    def log_result(self, category: str, item: str, status: str, details: str = ""):
        """记录检查结果"""
        self.results.append({
            'category': category,
            'item': item,
            'status': status,
            'details': details
        })
        
        if status == 'ERROR':
            self.errors.append(f"{category} - {item}: {details}")
        elif status == 'WARNING':
            self.warnings.append(f"{category} - {item}: {details}")
    
    def check_python_environment(self):
        """检查Python环境"""
        print("🐍 检查Python环境...")
        
        # Python版本
        version = sys.version_info
        if version.major == 3 and version.minor >= 8:
            self.log_result("Python", "版本", "OK", f"{version.major}.{version.minor}.{version.micro}")
        else:
            self.log_result("Python", "版本", "ERROR", f"需要Python 3.8+，当前: {version.major}.{version.minor}.{version.micro}")
        
        # 虚拟环境
        venv = os.environ.get('VIRTUAL_ENV')
        if venv:
            self.log_result("Python", "虚拟环境", "OK", venv)
        else:
            self.log_result("Python", "虚拟环境", "WARNING", "未检测到虚拟环境")
        
        # Python路径
        self.log_result("Python", "可执行文件", "OK", sys.executable)
    
    def check_system_info(self):
        """检查系统信息"""
        print("💻 检查系统信息...")
        
        # 操作系统
        system = platform.system()
        self.log_result("系统", "操作系统", "OK", f"{system} {platform.release()}")
        
        # 架构
        arch = platform.machine()
        self.log_result("系统", "架构", "OK", arch)
        
        # 发行版信息 (Linux)
        if system == "Linux":
            try:
                with open('/etc/os-release', 'r') as f:
                    for line in f:
                        if line.startswith('PRETTY_NAME='):
                            distro = line.split('=')[1].strip().strip('"')
                            self.log_result("系统", "发行版", "OK", distro)
                            break
            except:
                self.log_result("系统", "发行版", "WARNING", "无法检测")
    
    def check_python_packages(self):
        """检查Python包"""
        print("📦 检查Python包...")
        
        required_packages = [
            ('cv2', 'OpenCV'),
            ('PyQt5', 'PyQt5'),
            ('numpy', 'NumPy'),
            ('PIL', 'Pillow'),
            ('pathlib', 'pathlib'),
            ('ultralytics', 'Ultralytics'),
        ]
        
        for module, name in required_packages:
            try:
                __import__(module)
                # 尝试获取版本
                try:
                    if module == 'cv2':
                        import cv2
                        version = cv2.__version__
                    elif module == 'PyQt5':
                        from PyQt5.QtCore import QT_VERSION_STR
                        version = QT_VERSION_STR
                    elif module == 'numpy':
                        import numpy
                        version = numpy.__version__
                    elif module == 'PIL':
                        from PIL import Image
                        version = Image.__version__
                    elif module == 'ultralytics':
                        import ultralytics
                        version = ultralytics.__version__
                    else:
                        version = "已安装"
                    
                    self.log_result("Python包", name, "OK", version)
                except:
                    self.log_result("Python包", name, "OK", "已安装")
            except ImportError:
                self.log_result("Python包", name, "ERROR", "未安装")
    
    def check_qt_environment(self):
        """检查Qt环境"""
        print("🖥️ 检查Qt环境...")
        
        # Qt平台插件路径
        qt_plugin_path = os.environ.get('QT_QPA_PLATFORM_PLUGIN_PATH')
        if qt_plugin_path:
            if Path(qt_plugin_path).exists():
                self.log_result("Qt", "插件路径", "OK", qt_plugin_path)
            else:
                self.log_result("Qt", "插件路径", "ERROR", f"路径不存在: {qt_plugin_path}")
        else:
            self.log_result("Qt", "插件路径", "WARNING", "未设置QT_QPA_PLATFORM_PLUGIN_PATH")
        
        # 显示环境
        display = os.environ.get('DISPLAY')
        if display:
            self.log_result("Qt", "显示环境", "OK", display)
        else:
            if platform.system() == "Linux":
                self.log_result("Qt", "显示环境", "WARNING", "未设置DISPLAY变量")
            else:
                self.log_result("Qt", "显示环境", "OK", "Windows/macOS")
        
        # Qt平台
        qt_platform = os.environ.get('QT_QPA_PLATFORM')
        if qt_platform:
            self.log_result("Qt", "平台设置", "OK", qt_platform)
        else:
            self.log_result("Qt", "平台设置", "OK", "使用默认")
    
    def check_fonts(self):
        """检查字体"""
        print("🔤 检查字体...")
        
        try:
            from utils.cross_platform_paths import get_path_manager
            pm = get_path_manager()
            font = pm.find_available_font()
            
            if font:
                self.log_result("字体", "中文字体", "OK", font)
            else:
                self.log_result("字体", "中文字体", "WARNING", "未找到中文字体")
        except Exception as e:
            self.log_result("字体", "中文字体", "ERROR", str(e))
    
    def check_project_structure(self):
        """检查项目结构"""
        print("📁 检查项目结构...")
        
        required_files = [
            'main.py',
            'config/system_config.py',
            'utils/logger.py',
            'requirements.txt'
        ]
        
        required_dirs = [
            'config',
            'utils',
            'core',
            'detection',
            'qt_ui'
        ]
        
        for file_path in required_files:
            if Path(file_path).exists():
                self.log_result("项目文件", file_path, "OK", "存在")
            else:
                self.log_result("项目文件", file_path, "ERROR", "缺失")
        
        for dir_path in required_dirs:
            if Path(dir_path).exists():
                self.log_result("项目目录", dir_path, "OK", "存在")
            else:
                self.log_result("项目目录", dir_path, "ERROR", "缺失")
    
    def check_permissions(self):
        """检查权限"""
        print("🔐 检查权限...")
        
        # 检查当前目录写权限
        try:
            test_file = Path("test_write_permission.tmp")
            test_file.write_text("test")
            test_file.unlink()
            self.log_result("权限", "当前目录写入", "OK", "可写")
        except:
            self.log_result("权限", "当前目录写入", "ERROR", "无写入权限")
        
        # 检查日志目录
        logs_dir = Path("logs")
        if logs_dir.exists():
            if os.access(logs_dir, os.W_OK):
                self.log_result("权限", "日志目录", "OK", "可写")
            else:
                self.log_result("权限", "日志目录", "ERROR", "无写入权限")
        else:
            try:
                logs_dir.mkdir(exist_ok=True)
                self.log_result("权限", "日志目录", "OK", "已创建")
            except:
                self.log_result("权限", "日志目录", "ERROR", "无法创建")
    
    def check_network(self):
        """检查网络连接"""
        print("🌐 检查网络连接...")
        
        # 检查基本网络连接
        try:
            import urllib.request
            urllib.request.urlopen('https://www.google.com', timeout=5)
            self.log_result("网络", "互联网连接", "OK", "正常")
        except:
            self.log_result("网络", "互联网连接", "WARNING", "无法连接")
    
    def run_all_checks(self):
        """运行所有检查"""
        print("🔍 开始系统诊断...\n")
        
        self.check_system_info()
        self.check_python_environment()
        self.check_python_packages()
        self.check_qt_environment()
        self.check_fonts()
        self.check_project_structure()
        self.check_permissions()
        self.check_network()
        
        print("\n" + "="*60)
        print("📊 诊断结果汇总")
        print("="*60)
        
        # 统计结果
        ok_count = sum(1 for r in self.results if r['status'] == 'OK')
        warning_count = sum(1 for r in self.results if r['status'] == 'WARNING')
        error_count = sum(1 for r in self.results if r['status'] == 'ERROR')
        
        print(f"✅ 正常: {ok_count}")
        print(f"⚠️ 警告: {warning_count}")
        print(f"❌ 错误: {error_count}")
        
        # 显示详细结果
        if self.errors:
            print(f"\n❌ 错误详情:")
            for error in self.errors:
                print(f"  - {error}")
        
        if self.warnings:
            print(f"\n⚠️ 警告详情:")
            for warning in self.warnings:
                print(f"  - {warning}")
        
        # 给出建议
        if error_count > 0:
            print(f"\n💡 建议:")
            print("  - 请解决上述错误后再运行系统")
            print("  - 参考Linux部署指南.md获取详细解决方案")
            return False
        elif warning_count > 0:
            print(f"\n💡 建议:")
            print("  - 警告项目可能影响系统功能")
            print("  - 建议在部署前解决警告问题")
            return True
        else:
            print(f"\n🎉 系统环境检查通过！可以正常运行。")
            return True


def main():
    """主函数"""
    diagnostics = SystemDiagnostics()
    success = diagnostics.run_all_checks()
    
    # 返回适当的退出码
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
