#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证模型引用更新脚本
检查所有模型文件引用是否已正确更新为RKNN格式
"""

import os
import re
from pathlib import Path
from typing import List, Dict, Tuple
import json

from utils.logger import get_logger


class ModelReferenceVerifier:
    """模型引用验证器"""
    
    def __init__(self):
        """初始化验证器"""
        self.logger = get_logger("ModelReferenceVerifier")
        
        # 验证结果
        self.verification_results = {
            'total_files_checked': 0,
            'files_with_pytorch_refs': [],
            'files_with_rknn_refs': [],
            'pytorch_references_found': [],
            'rknn_references_found': [],
            'verification_passed': False
        }
        
        # 需要检查的文件
        self.target_files = [
            "2/config/config.yaml",
            "2/modules/config_module.py",
            "config/human_detection_config.py",
            "config/new_object_detection_config.py",
            "core/frame_processor.py",
            "detection/core/adaptive_fire_smoke_detector.py",
            "detection/core/integrated_fire_smoke_detector.py",
            "detection/utils/fire_yolo_utils.py",
            "detection/utils/yolo_utils.py",
            "detection/core/human_detector.py",
            "2/modules/detection_module.py"
        ]
    
    def verify_all_references(self) -> bool:
        """验证所有模型引用"""
        try:
            self.logger.info("开始验证模型文件引用")
            
            for file_path in self.target_files:
                if Path(file_path).exists():
                    self._verify_file(file_path)
                else:
                    self.logger.warning(f"文件不存在，跳过验证: {file_path}")
            
            # 生成验证报告
            self._generate_verification_report()
            
            # 判断验证是否通过
            self.verification_results['verification_passed'] = (
                len(self.verification_results['pytorch_references_found']) == 0
            )
            
            return self.verification_results['verification_passed']
            
        except Exception as e:
            self.logger.error(f"验证模型引用失败: {e}")
            return False
    
    def _verify_file(self, file_path: str):
        """验证单个文件"""
        try:
            self.logger.info(f"正在验证文件: {file_path}")
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            self.verification_results['total_files_checked'] += 1
            
            # 检查PyTorch模型引用
            pytorch_refs = self._find_pytorch_references(content, file_path)
            if pytorch_refs:
                self.verification_results['files_with_pytorch_refs'].append(file_path)
                self.verification_results['pytorch_references_found'].extend(pytorch_refs)
                self.logger.warning(f"  ⚠️ 发现PyTorch引用: {len(pytorch_refs)} 个")
            
            # 检查RKNN模型引用
            rknn_refs = self._find_rknn_references(content, file_path)
            if rknn_refs:
                self.verification_results['files_with_rknn_refs'].append(file_path)
                self.verification_results['rknn_references_found'].extend(rknn_refs)
                self.logger.info(f"  ✅ 发现RKNN引用: {len(rknn_refs)} 个")
            
            if not pytorch_refs and not rknn_refs:
                self.logger.info(f"  ⚪ 无模型引用: {file_path}")
            
        except Exception as e:
            self.logger.error(f"验证文件失败 [{file_path}]: {e}")
    
    def _find_pytorch_references(self, content: str, file_path: str) -> List[Dict]:
        """查找PyTorch模型引用"""
        pytorch_refs = []

        # 定义PyTorch模型文件模式，但排除代码逻辑中的扩展名检查
        patterns = [
            r'["\']([^"\']*[^\.][^p][^t][^h]?\.pt)["\']',  # .pt 文件，但不是 .endswith('.pt')
            r'["\']([^"\']*[^\.][^p][^t][^h]\.pth)["\']',  # .pth 文件，但不是 .endswith('.pth')
        ]

        # 更精确的模式：查找实际的模型文件路径，而不是代码中的扩展名检查
        file_path_patterns = [
            r'["\']([^"\']*(?:models?|weights?|checkpoints?)[^"\']*\.pt)["\']',  # 包含模型路径的.pt文件
            r'["\']([^"\']*(?:models?|weights?|checkpoints?)[^"\']*\.pth)["\']',  # 包含模型路径的.pth文件
            r'["\']([a-zA-Z0-9_\-/\\]+\.pt)["\']',  # 具体的.pt文件名
            r'["\']([a-zA-Z0-9_\-/\\]+\.pth)["\']',  # 具体的.pth文件名
        ]

        for pattern in file_path_patterns:
            matches = re.finditer(pattern, content)
            for match in matches:
                line_context = self._get_line_context(content, match.start())

                # 排除代码逻辑中的扩展名检查
                if not any(keyword in line_context.lower() for keyword in [
                    'endswith', 'extension', 'suffix', 'format', 'type'
                ]):
                    pytorch_refs.append({
                        'file': file_path,
                        'reference': match.group(1),
                        'line_context': line_context,
                        'position': match.start()
                    })

        return pytorch_refs
    
    def _find_rknn_references(self, content: str, file_path: str) -> List[Dict]:
        """查找RKNN模型引用"""
        rknn_refs = []
        
        # 定义RKNN模型文件模式
        patterns = [
            r'["\']([^"\']*\.rknn)["\']',  # .rknn 文件
        ]
        
        for pattern in patterns:
            matches = re.finditer(pattern, content)
            for match in matches:
                rknn_refs.append({
                    'file': file_path,
                    'reference': match.group(1),
                    'line_context': self._get_line_context(content, match.start()),
                    'position': match.start()
                })
        
        return rknn_refs
    
    def _get_line_context(self, content: str, position: int) -> str:
        """获取指定位置的行上下文"""
        lines = content[:position].split('\n')
        line_number = len(lines)
        current_line = content.split('\n')[line_number - 1] if line_number <= len(content.split('\n')) else ""
        return f"Line {line_number}: {current_line.strip()}"
    
    def _generate_verification_report(self):
        """生成验证报告"""
        # 保存详细报告到JSON文件
        report_path = "model_reference_verification_report.json"
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(self.verification_results, f, indent=2, ensure_ascii=False)
        
        # 打印摘要报告
        self._print_summary_report()
    
    def _print_summary_report(self):
        """打印摘要报告"""
        print("\n" + "="*60)
        print("模型文件引用验证报告")
        print("="*60)
        
        print(f"检查文件总数: {self.verification_results['total_files_checked']}")
        print(f"包含PyTorch引用的文件: {len(self.verification_results['files_with_pytorch_refs'])}")
        print(f"包含RKNN引用的文件: {len(self.verification_results['files_with_rknn_refs'])}")
        print(f"发现的PyTorch引用: {len(self.verification_results['pytorch_references_found'])}")
        print(f"发现的RKNN引用: {len(self.verification_results['rknn_references_found'])}")
        
        # 显示验证状态
        if self.verification_results['verification_passed']:
            print("\n🎉 验证通过: 所有PyTorch模型引用已成功更新为RKNN格式!")
        else:
            print("\n⚠️ 验证未通过: 仍有PyTorch模型引用需要更新")
        
        # 显示剩余的PyTorch引用
        if self.verification_results['pytorch_references_found']:
            print("\n❌ 剩余的PyTorch引用:")
            for ref in self.verification_results['pytorch_references_found']:
                print(f"  📁 {ref['file']}")
                print(f"     🔗 {ref['reference']}")
                print(f"     📍 {ref['line_context']}")
                print()
        
        # 显示RKNN引用
        if self.verification_results['rknn_references_found']:
            print("✅ 已更新的RKNN引用:")
            for ref in self.verification_results['rknn_references_found'][:10]:  # 只显示前10个
                print(f"  📁 {ref['file']}")
                print(f"     🔗 {ref['reference']}")
            
            if len(self.verification_results['rknn_references_found']) > 10:
                remaining = len(self.verification_results['rknn_references_found']) - 10
                print(f"  ... 还有 {remaining} 个RKNN引用")
        
        print("="*60)


def check_model_directory_structure():
    """检查模型目录结构"""
    print("\n🏗️ 检查模型目录结构")
    print("-" * 30)
    
    required_dirs = [
        "models",
        "models/human_detection",
        "models/fire_detection",
        "models/general"
    ]
    
    all_dirs_exist = True
    for dir_path in required_dirs:
        if Path(dir_path).exists():
            print(f"  ✅ {dir_path}")
        else:
            print(f"  ❌ {dir_path} (不存在)")
            all_dirs_exist = False
    
    # 检查README文件
    readme_files = [
        "models/README.md",
        "models/CONVERSION_GUIDE.md",
        "models/model_mappings.json"
    ]
    
    for file_path in readme_files:
        if Path(file_path).exists():
            print(f"  ✅ {file_path}")
        else:
            print(f"  ❌ {file_path} (不存在)")
    
    return all_dirs_exist


def main():
    """主函数"""
    print("🔍 开始验证模型文件引用更新")
    
    # 验证模型引用
    verifier = ModelReferenceVerifier()
    verification_passed = verifier.verify_all_references()
    
    # 检查目录结构
    structure_ok = check_model_directory_structure()
    
    # 总结
    print("\n" + "="*60)
    print("任务1.5完成状态总结")
    print("="*60)
    
    if verification_passed and structure_ok:
        print("🎉 任务1.5: 更新模型文件引用 - 完全成功!")
        print("✅ 所有PyTorch模型引用已更新为RKNN格式")
        print("✅ RKNN模型目录结构已创建")
        print("✅ 相关文档和指南已生成")
        return 0
    elif verification_passed:
        print("✅ 模型引用更新成功，但目录结构需要完善")
        return 0
    else:
        print("❌ 模型引用更新不完整，需要进一步处理")
        return 1


if __name__ == "__main__":
    exit(main())
